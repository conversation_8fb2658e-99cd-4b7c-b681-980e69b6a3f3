<?php

'yii2-only`;

	namespace app\models;
	use app\components\ActiveRecords\MyHistoryActiveRecord;
	use app\components\App;
	use app\components\Dict;
	use app\components\Helpers\AnyCache;
	use app\models\EmployeeGroup;
	use app\models\EmployeeGroupConfig;
	use app\models\Status;
	use Yang;

`/yii2-only';


/**
 * This is the model class for table "employee_group".
 *
 * The followings are the available columns in table 'employee_group':
 * @property string $row_id
 * @property string $employee_contract_id
 * @property string $group_id
 * @property string $group_value
 * @property string $note
 * @property string $valid_from
 * @property string $valid_to
 * @property integer $status
 * @property string $created_by
 * @property string $created_on
 * @property string $modified_by
 * @property string $modified_on
 * @property integer $pre_row_id
 */
class EmployeeGroup extends MyHistoryActiveRecord
{

	public function __construct($scenario = 'insert')
	{
		parent::__construct($scenario);
		if ($scenario === 'insert') {
			$this->status = Status::PUBLISHED;
		}

		$this->identifyColumn = "employee_contract_id";
		$this->autoSplitIntervals = true;
		$this->autoSplitColumns = ["employee_contract_id", "group_id",];
		$this->historyValidationColumns = [["employee_contract_id","group_id",],];
	}

	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'employee_group';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
		if(isYii1()) $rules = [
			[ 'employee_contract_id, group_id, group_value, valid_from, status, created_by, created_on', 'required', 'message'=>"error_field_required" ],
			[ 'status, pre_row_id', 'numerical', 'integerOnly'=>true ],
			[ 'employee_contract_id, group_id, created_by, modified_by', 'length', 'max'=>32 ],
            [ 'group_value', 'length', 'max'=>64 ],
			[ 'note', 'length', 'max'=>512 ],
			[ 'valid_to, modified_on', 'safe' ],
			[ 'row_id, employee_contract_id, group_id, group_value, note, valid_from, valid_to, status, created_by, created_on, modified_by, modified_on, pre_row_id', 'safe', 'on'=>'search' ],
		];
		if(isYii2()) $rules = [
			[["employee_contract_id", "group_id", "group_value", "valid_from", "status", "created_by", "created_on", ],"required", "message" => "error_field_required", ],
			[["status", "pre_row_id", ],"integer", ],
			[["employee_contract_id", "group_id", "created_by", "modified_by", ],"string", "max" => "32", ],
            [["group_value", ],"string", "max" => "64", ],
			[["note", ],"string", "max" => "512", ],
			[["valid_to", "modified_on", ],"safe", ],
			[["row_id", "employee_contract_id", "group_id", "group_value", "note", "valid_from", "valid_to", "status", "created_by", "created_on", "modified_by", "modified_on", "pre_row_id", ],"safe", "on" => "search", ],
		];
		return $rules;
	}

	/**
	 * @return array relational rules.
	 */
	public function relations()
	{
		// NOTE: you may need to adjust the relation name and the related
		// class name for the relations automatically generated below.
		return array(
		);
	}

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return array(
			'row_id'				=> Dict::getValue("note"),
			'employee_contract_id'	=> Dict::getValue("employee_contract_number"),
			'group_id'				=> Dict::getValue("group_id"),
			'group_value'			=> Dict::getValue("group_value"),
			'note'					=> Dict::getValue("note"),
			'valid_from'			=> Dict::getValue("valid_from"),
			'valid_to'				=> Dict::getValue("valid_to"),
			'status'                => Dict::getValue("status"),
            'created_by'            => Dict::getValue("created_by"),
            'created_on'            => Dict::getValue("created_on"),
            'modified_by'			=> Dict::getValue("modified_by"),
            'modified_on'			=> Dict::getValue("modified_on"),
			'pre_row_id'			=> Dict::getValue("pre_row_id"),
		);
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		// @todo Please modify the following code to remove attributes that should not be searched.

		$criteria=new CDbCriteria;

		$criteria->compare('row_id',$this->row_id,true);
		$criteria->compare('employee_contract_id',$this->employee_contract_id,true);
		$criteria->compare('group_id',$this->group_id,true);
		$criteria->compare('group_value',$this->group_value,true);
		$criteria->compare('note',$this->note,true);
		$criteria->compare('valid_from',$this->valid_from,true);
		$criteria->compare('valid_to',$this->valid_to,true);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_by',$this->created_by,true);
		$criteria->compare('created_on',$this->created_on,true);
		$criteria->compare('modified_by',$this->modified_by,true);
		$criteria->compare('modified_on',$this->modified_on,true);
		$criteria->compare('pre_row_id',$this->pre_row_id);

		return new CActiveDataProvider($this, array(
			'criteria'=>$criteria,
		));
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return EmployeeGroup the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

	public static function getLeftJoinSQL($type,$employee_contract_name="employee_contract",$date_field="cal.`date`",$alias="")
	{
		$dateFieldisDate = \FALSE;
		if(strtotime($date_field)){
			$dateFieldisDate = \TRUE;
		}
		$sql="LEFT JOIN `employee_group` employee_group_$type$alias ON
						employee_group_$type$alias.`employee_contract_id`=$employee_contract_name.`employee_contract_id`
					AND employee_group_$type$alias.`group_id`='$type'
					AND employee_group_$type$alias.`status`=".Status::PUBLISHED."
                    AND ".($dateFieldisDate===\TRUE?"'$date_field'":$date_field)." BETWEEN employee_group_$type$alias.`valid_from` AND IFNULL(employee_group_$type$alias.`valid_to`, '".App::getSetting("defaultEnd")."')
			";

		return $sql;
	}

	public static function getLeftJoinSQLWithoutCal($type,$employee_contract_name="employee_contract",$date_field="",$alias="",$employee_name="employee",$validFrom="",$validTo="")
	{
		$dateFieldisDate = \FALSE;
		if(strtotime($date_field)){
			$dateFieldisDate = \TRUE;
		}
		$employee_table_name = ($type === 'unit_id' ? $employee_name : $employee_contract_name);

		$sql = "LEFT JOIN `employee_group` employee_group_$type$alias ON
						employee_group_$type$alias.`employee_contract_id`=$employee_contract_name.`employee_contract_id`
					AND employee_group_$type$alias.`group_id`='$type'
					AND employee_group_$type$alias.`status`=".Status::PUBLISHED;
		if($dateFieldisDate===\TRUE){
			$sql .=	"
                    AND '$date_field' BETWEEN employee_group_$type$alias.`valid_from` AND IFNULL(employee_group_$type$alias.`valid_to`, '".App::getSetting("defaultEnd")."')
			";
		} else if (!empty($date_field)) {
			$sql .=	"
                    AND $date_field BETWEEN employee_group_$type$alias.`valid_from` AND IFNULL(employee_group_$type$alias.`valid_to`, '".App::getSetting("defaultEnd")."')
			";
		}else{
			$sql .=	"
					AND	( $employee_table_name.`valid_from` <= IFNULL(employee_group_$type$alias.`valid_to`, '".App::getSetting("defaultEnd")."')
						AND $employee_table_name.`valid_to` >= employee_group_$type$alias.`valid_from`)
					";
			if($type !== 'unit_id'){
				$sql .= "
						AND ( $employee_table_name.`ec_valid_from` <= IFNULL(employee_group_$type$alias.`valid_to`, '".App::getSetting("defaultEnd")."')
						AND $employee_table_name.`ec_valid_to` >= employee_group_$type$alias.`valid_from`)

			";}
			if($validFrom !== "" && $validTo !== ""){
				$sql .= "
					AND $validFrom <= IFNULL(employee_group_$type$alias.`valid_to`, '".App::getSetting("defaultEnd")."')
					AND $validTo >= employee_group_$type$alias.`valid_from`
				";
			}
		}

		return $sql;
	}

	public static function getLeftJoinSQLWithoutCalAndate($type,$employee_contract_name="employee_contract",$alias="")
	{
		$sql = "
				LEFT JOIN `employee_group` employee_group_$type$alias ON
						employee_group_$type$alias.`employee_contract_id`=$employee_contract_name.`employee_contract_id`
					AND employee_group_$type$alias.`group_id`='$type'
					AND employee_group_$type$alias.`status`=".Status::PUBLISHED;

		return $sql;
	}

	public static function getAllActiveLeftJoinSQL($employee_contract_name="employee_contract",$date="cal.`date`",$alias="",$employee_name="employee",$valid_from="",$valid_to="")
	{
		// $acKey = AnyCache::key("getAllActiveLeftJoinSQL",[func_get_args(),userID()]);
		// if(AnyCache::has($acKey)) return AnyCache::get($acKey);

		$sql="";

		$employee_group=EmployeeGroupConfig::getActiveGroups();
		for($i=0;$i<count($employee_group);$i++)
		{
			$sql.=EmployeeGroup::getLeftJoinSQLWithoutCal( $employee_group[$i],$employee_contract_name,$date,$alias,$employee_name,$valid_from,$valid_to);
		}

		// AnyCache::set($acKey,$sql,"garg sql string");
		return $sql;
	}

	public static function getActiveGroupSQL($type,$default_table_name,$alias="")
	{
		if(EmployeeGroupConfig::isActiveGroup($type))
		{

			return "employee_group_$type$alias.`group_value`";
		}
		else
		{
			return "$default_table_name.`$type` ";
		}
	}

	public static function getActiveGroupValidFromSQL($type, $alias="")
	{
		if(EmployeeGroupConfig::isActiveGroup($type))
		{

			return "IFNULL(employee_group_$type$alias.`valid_from`, '".	App::getSetting("defaultStart")."'),";
		}
	}

	public static function getActiveGroupValidToSQL($type, $alias="")
	{
		if(EmployeeGroupConfig::isActiveGroup($type))
		{

			return "IFNULL(employee_group_$type$alias.`valid_to`, '".	App::getSetting("defaultEnd")."'),";
		}
	}

	public static function getActiveGroupCheck($type,$filter='')
	{
		if (empty($filter))
		{
			$filter=substr($type,0,  strlen($type)-3);
		}
		if(EmployeeGroupConfig::isActiveGroup($type))
		{
			$searchInput = Yang::getRequestParam("searchInput")['workgroup'] ?? null;
			if (is_array($searchInput)) {
				$filter="AND (employee_group_$type.`group_value` IN {$filter} OR 'ALL' IN {$filter} OR '' IN {$filter})
				";
			} else {
				return " AND (employee_group_$type.`group_value` IS NOT NULL OR '{".$filter."}'='ALL' OR '{".$filter."}'='' OR $filter.$type='ALL')
						";
			}
		}
		else
		{
			return "";
		}
	}

	public function getAllActiveGroupCheck()
	{
		$sql="";
		$employee_group=EmployeeGroupConfig::getActiveGroups();
		for($i=0;$i<count($employee_group);$i++)
		{
			$sql.=EmployeeGroup::getActiveGroupCheck($employee_group[$i]);
		}
		return $sql;
	}

	public static function getActiveGroupTable($type,$default_table_name,$alias="")
	{
		if(EmployeeGroupConfig::isActiveGroup($type)) {
			return "employee_group_$type$alias";
		} else {
			return "$default_table_name";
		}
	}

	/**
	 * Visszaadja Gargosítva az alap csoportosítás tábláinkat: company, payroll, workgroup, unit, cog1-3, cost, cost_center, employee_position
	 * GARG Where-nek employee és employee_contract kell! Azaz a default tábla nevek paraméterbe!
	 * @param string $employeeTableId
	 * @param string $contractTableId
	 * @param string $date
	 * @return string
	 */
	public static function getAllBaseTablesWithGroup($employeeTableId = "employee", $contractTableId = "employee_contract", $date = "cal.`date`")
	{
		$pub = Status::PUBLISHED;
		$end = App::getSetting("defaultEnd");
		$SQL = "
			LEFT JOIN `company` ON
					`company`.`company_id` = " . EmployeeGroup::getActiveGroupSQL("company_id", $employeeTableId) . "
				AND `company`.`status` = {$pub}
				AND {$date} BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
			LEFT JOIN `payroll` ON
					`payroll`.`payroll_id` = " . EmployeeGroup::getActiveGroupSQL("payroll_id", $employeeTableId) . "
				AND `payroll`.`status` = {$pub}
				AND {$date} BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}')
			LEFT JOIN `workgroup` ON
					`workgroup`.`workgroup_id` = " . EmployeeGroup::getActiveGroupSQL("workgroup_id", $contractTableId) . "
				AND `workgroup`.`status` = {$pub}
				AND {$date} BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '{$end}')
			LEFT JOIN `unit` ON
					`unit`.`unit_id` = " . EmployeeGroup::getActiveGroupSQL("unit_id", $employeeTableId) . "
				AND `unit`.`status` = {$pub}
				AND {$date} BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '{$end}')
			LEFT JOIN `company_org_group1` ON
					`company_org_group1`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group1_id", $employeeTableId) . "
				AND `company_org_group1`.`status` = {$pub}
				AND {$date} BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '{$end}')
			LEFT JOIN `company_org_group2` ON
					`company_org_group2`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group2_id", $employeeTableId) . "
				AND `company_org_group2`.`status` = {$pub}
				AND {$date} BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '{$end}')
			LEFT JOIN `company_org_group3` ON
					`company_org_group3`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group3_id", $employeeTableId) . "
				AND `company_org_group3`.`status` = {$pub}
				AND {$date} BETWEEN `company_org_group3`.`valid_from` AND IFNULL(`company_org_group3`.`valid_to`, '{$end}')
			LEFT JOIN `employee_cost` ON
					`employee_cost`.`employee_contract_id` = {$contractTableId}.`employee_contract_id`
				AND `employee_cost`.`status` = {$pub}
				AND {$date} BETWEEN `employee_cost`.`valid_from` AND IFNULL(`employee_cost`.`valid_to`, '{$end}')
			LEFT JOIN `cost` ON
					`cost`.`cost_id` = `employee_cost`.`cost_id`
				AND `cost`.`status` = {$pub}
				AND {$date} BETWEEN `cost`.`valid_from` AND IFNULL(`cost`.`valid_to`, '{$end}')
			LEFT JOIN `cost_center` ON
					`cost_center`.`cost_center_id` = `employee_cost`.`cost_center_id`
				AND `cost_center`.`status` = {$pub}
				AND {$date} BETWEEN `cost_center`.`valid_from` AND IFNULL(`cost_center`.`valid_to`, '{$end}')
			LEFT JOIN `employee_position` ON
					`employee_position`.`employee_position_id` = " . EmployeeGroup::getActiveGroupSQL("employee_position_id", $contractTableId) . "
				AND `employee_position`.`status` = {$pub}
				AND {$date} BETWEEN `employee_position`.`valid_from` AND IFNULL(`employee_position`.`valid_to`, '{$end}')
		";
		return $SQL;
	}

	/**
	 * Visszaadja Gargosítva az alap csoportosítás tábláinkat: company, payroll, workgroup, unit, cog1-3, cost, cost_center, employee_position
	 * GARG Where-nek employee és employee_contract kell! Azaz a default tábla nevek paraméterbe!
	 * @param string $employeeTableId
	 * @param string $contractTableId
	 * @param string $vFrom
	 * @param string $vTo
	 * @return string
	 */
	public static function getAllBaseTablesWithGroupFromTo($employeeTableId = "employee", $contractTableId = "employee_contract", $vFrom = "", $vTo = "")
	{
		$pub = Status::PUBLISHED;
		$end = App::getSetting("defaultEnd");
		if (empty($vFrom) || empty($vTo)) {
			return " -- getAllBaseTablesWithGroupFromTo() vFrom or vTo missing!";
		}
		if (strtotime($vFrom)) {
			$vFrom = "'" . $vFrom . "'";
		}
		if (strtotime($vTo)) {
			$vTo = "'" . $vTo . "'";
		}
		$SQL = "
			LEFT JOIN `company` ON
					`company`.`company_id` = " . EmployeeGroup::getActiveGroupSQL("company_id", $employeeTableId) . "
				AND `company`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= {$vTo}
			LEFT JOIN `payroll` ON
					`payroll`.`payroll_id` = " . EmployeeGroup::getActiveGroupSQL("payroll_id", $employeeTableId) . "
				AND `payroll`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`valid_from` <= {$vTo}
			LEFT JOIN `workgroup` ON
					`workgroup`.`workgroup_id` = " . EmployeeGroup::getActiveGroupSQL("workgroup_id", $contractTableId) . "
				AND `workgroup`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`workgroup`.`valid_to`, '{$end}')
				AND `workgroup`.`valid_from` <= {$vTo}
			LEFT JOIN `unit` ON
					`unit`.`unit_id` = " . EmployeeGroup::getActiveGroupSQL("unit_id", $employeeTableId) . "
				AND `unit`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`unit`.`valid_to`, '{$end}')
				AND `unit`.`valid_from` <= {$vTo}
			LEFT JOIN `company_org_group1` ON
					`company_org_group1`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group1_id", $employeeTableId) . "
				AND `company_org_group1`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`company_org_group1`.`valid_to`, '{$end}')
				AND `company_org_group1`.`valid_from` <= {$vTo}
			LEFT JOIN `company_org_group2` ON
					`company_org_group2`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group2_id", $employeeTableId) . "
				AND `company_org_group2`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`company_org_group2`.`valid_to`, '{$end}')
				AND `company_org_group2`.`valid_from` <= {$vTo}
			LEFT JOIN `company_org_group3` ON
					`company_org_group3`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group3_id", $employeeTableId) . "
				AND `company_org_group3`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`company_org_group3`.`valid_to`, '{$end}')
				AND `company_org_group3`.`valid_from` <= {$vTo}
			LEFT JOIN `employee_cost` ON
					`employee_cost`.`employee_contract_id` = {$contractTableId}.`employee_contract_id`
				AND `employee_cost`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`employee_cost`.`valid_to`, '{$end}')
				AND `employee_cost`.`valid_from` <= {$vTo}
			LEFT JOIN `cost` ON
					`cost`.`cost_id` = `employee_cost`.`cost_id`
				AND `cost`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`cost`.`valid_to`, '{$end}')
				AND `cost`.`valid_from` <= {$vTo}
			LEFT JOIN `cost_center` ON
					`cost_center`.`cost_center_id` = `employee_cost`.`cost_center_id`
				AND `cost_center`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`cost_center`.`valid_to`, '{$end}')
				AND `cost_center`.`valid_from` <= {$vTo}
			LEFT JOIN `employee_position` ON
					`employee_position`.`employee_position_id` = " . EmployeeGroup::getActiveGroupSQL("employee_position_id", $contractTableId) . "
				AND `employee_position`.`status` = {$pub}
				AND {$vFrom} <= IFNULL(`employee_position`.`valid_to`, '{$end}')
				AND `employee_position`.`valid_from` <= {$vTo}
		";
		return $SQL;
	}

    public function afterSave()
    {
        if (App::getSetting('automaticLoadExt2FromEmployeePosition') && $this->group_id === 'employee_position_id') {
            $condition = "valid_from <= '{$this->valid_to}' AND valid_to >= '{$this->valid_from}'";
            $employeeId = \EmployeeContract::model()->findAllByAttributes(['employee_contract_id' => $this->employee_contract_id, 'status' => Status::PUBLISHED], $condition)[0]->employee_id;
            $employeeContractArray = EmployeeContract::model()->findAllByAttributes(['employee_id' => $employeeId, 'status' => Status::PUBLISHED]);
            $contractIds = [];
            foreach ($employeeContractArray as $employeeContract) {
                $contractIds[] = $employeeContract->employee_contract_id;
            }
            $employeeExt2Array = \EmployeeExt2::model()->findAllByAttributes(['employee_id' => $employeeId, 'status' => Status::PUBLISHED]);

            $employeeGroups= new EmployeeGroup();
            $criteria = new CDbCriteria;
            $end = App::getSetting("defaultEnd");
            $criteria->alias = "eg";
            $criteria->join = "JOIN `employee_contract` ec 
                                        ON eg.`employee_contract_id` = ec.`employee_contract_id`
                                            AND ec.status = " . Status::PUBLISHED . "
                                                AND ec.valid_from <= IFNULL(eg.valid_to, '{$end}') AND ec.valid_to >= eg.valid_from
                                                    AND ec.ec_valid_from <= IFNULL(eg.valid_to,'{$end}') AND ec.ec_valid_to >= eg.valid_from
                                                        AND ec.employee_contract_id IN ('" . join("','", $contractIds) . "')";
            $criteria->condition = "`eg`.status = " . Status::PUBLISHED . "
                                        AND eg.group_id = 'employee_position_id'";

            $employeeGroupPositionArray = $employeeGroups->findAll($criteria);
            foreach ($employeeExt2Array as $employeeExt2) {
                $employeeExt2->delete();
            }
            foreach ($employeeGroupPositionArray as $employeeGroupPosition) {
                    $employeePositionDetails = \EmployeePositionDetails::model()->findAllByAttributes(['employee_position_id' => $employeeGroupPosition->group_value, 'status' => Status::PUBLISHED])[0];
                    if (!isset($employeePositionDetails)) {
                        $employeePosition = \EmployeePosition::model()->findAllByAttributes(['employee_position_id' => $employeeGroupPosition->group_value, 'status' => Status::PUBLISHED])[0];
                    }
                    $employeeExt2 = new \EmployeeExt2();
                    $employeeExt2->ext2_option1 = $employeePositionDetails->position_code ?? '';
                    $employeeExt2->ext2_option2 = $employeePositionDetails->position ?? $employeePosition->employee_position_name;
                    $employeeExt2->ext2_option3 = $employeePositionDetails->position_english_name ?? '';
                    $employeeExt2->ext2_option4 = $employeePositionDetails->service_group ?? '';
                    $employeeExt2->ext2_option5 = $employeePositionDetails->activity_description ?? '';
                    $employeeExt2->ext2_option6 = $employeePositionDetails->feor_code ?? '';
                    $employeeExt2->ext2_option7 = $employeePositionDetails->position_family ?? '';
                    $employeeExt2->ext2_option8 = $employeePositionDetails->sub_position_family_code ?? '';
                    $employeeExt2->ext2_option9 = $employeePositionDetails->sub_position_family_description ?? '';
                    $employeeExt2->ext2_option10 = $employeePositionDetails->hay_grade ?? '';
                    $employeeExt2->ext2_option11 = $employeePositionDetails->min_salary ?? '';
                    $employeeExt2->ext2_option12 = $employeePositionDetails->classification_category ?? '';
                    $employeeExt2->ext2_option13 = $employeePositionDetails->monitor_in_front_of_work ?? '';
                    $employeeExt2->ext2_option14 = $employeePositionDetails->referral_type ?? '';
                    $employeeExt2->ext2_option15 = $employeePositionDetails->ho_eligibility ?? '';
                    $employeeExt2->ext2_option16 = $employeePositionDetails->basic_it_profile ?? '';
                    $employeeExt2->employee_id = $employeeId;
                    $employeeExt2->valid_from = $employeeGroupPosition->valid_from;
                    $employeeExt2->valid_to = $employeeGroupPosition->valid_to;
                    $employeeExt2->status = Status::PUBLISHED;
                    $employeeExt2->created_by = 'system';
                    $employeeExt2->created_on = date('Y-m-d H:i:s');
                    $employeeExt2->save();
            }
        }
        return parent::afterSave();
    }
}

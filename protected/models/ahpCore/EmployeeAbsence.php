<?php

use Components\Core\Enum\AppSettingsIdEnum;
use Components\Core\Enum\ModuleNameEnum;
use Components\Core\Enum\DailyWorktimePlaceEnum;
use Components\ProcessAcknowledgment\Enum\ProcessAcknowledgmentEnum;
use Components\SchedulingAssistantCore\Builder\LockableWorkScheduleWithEmployeeRawDataBuilder;
use Components\SchedulingAssistantCore\Handler\UnlockingHandler;
use Components\WorkScheduleCore\Enum\WorkScheduleUsedTypeOfDayTypeSourceEnum;
use Components\WorkScheduleCore\Enum\EmployeeWorkScheduleEnum;

Yang::loadComponentNamespaces('ProcessAcknowledgment');
Yang::loadComponentNamespaces('SchedulingAssistantWorkScheduleEdit');
Yang::loadComponentNamespaces('SchedulingAssistantRotation');
Yang::loadComponentNamespaces('SchedulingAssistantCore');
Yang::loadComponentNamespaces('SchedulingAssistant');
Yang::loadComponentNamespaces('WorkScheduleCore');
Yang::loadComponentNamespaces('Employee');
Yang::loadComponentNamespaces('Core');

/**
 * This is the model class for table "employee_absence".
 *
 * The followings are the available columns in table 'employee_absence':
 * @property string $row_id
 * @property string $employee_absence_id
 * @property string $company_id
 * @property string $employee_contract_id
 * @property string $employee_id
 * @property string $day
 * @property double(10,2) $absence_hour
 * @property int $full_day_absence
 * @property string $state_type_id
 * @property string $note
 * @property int $score
 * @property int $status
 * @property string $created_by
 * @property string $created_on
 * @property string $modified_by
 * @property string $modified_on
 * @property int $pre_row_id
 */
class EmployeeAbsence extends MyActiveRecord
{
	public $bgcolor, $name_dict_id, $company_org_group2_id;
	public $published = Status::PUBLISHED;
	public const CREATED_BY_BEFORESAVE = 'EA_beforeSave';
	public const MODIFIED_BY_AFTERSAVE_REJECTING = 'EA_afterSaveRejecting';
	public const DAYTYPE_ID = 'daytype_id';
	public const DAYTYPE_NAME = 'daytype_name';
	public const FULL_WORK_TIME = 'full_work_time';
	public const EMPLOYEE_CONTRACT_DAILY_WORKTIME = 'ec_daily_worktime';
	public const WORKGROUP_DAILY_WORKTIME = 'wg_daily_worktime';
	/**
	 * @return string the associated database table name
	 */
	public function tableName()
	{
		return 'employee_absence';
	}

	/**
	 * @return array validation rules for model attributes.
	 */
	public function rules()
	{
        return [
            [ 'employee_absence_id, company_id, employee_contract_id, day, state_type_id, status, created_by, created_on', 'required' ],
            [ 'score, status, pre_row_id, full_day_absence', 'numerical', 'integerOnly'=>true ],
            [ 'absence_hour', 'numerical' ],
            [ 'employee_absence_id, company_id, employee_contract_id, employee_id, state_type_id, created_by, modified_by', 'length', 'max'=>32 ],
            [ 'note', 'length', 'max'=>512 ],
            [ 'modified_on', 'safe' ],
            [ 'row_id, employee_absence_id, company_id, employee_contract_id, employee_id, day, absence_hour, full_day_absence, state_type_id, note, score, status, created_by, created_on, modified_by, modified_on, pre_row_id', 'safe', 'on'=>'search' ],
        ];
	}
	
	/**
	 * @return array relational rules.
	 */
    public function relations()
    {
        return [
            'state_type' => [self::HAS_ONE, 'StateType',
                ['state_type_id' => 'state_type_id'],
            ],
        ];
    }

	/**
	 * @return array customized attribute labels (name=>label)
	 */
	public function attributeLabels()
	{
		return [
			'row_id' => 'Row',
			'employee_absence_id' => 'Employee Absence',
			'company_id' => 'Company',
			'employee_contract_id' => 'Employee Contract',
			'employee_id' => 'Employee',
			'day' => 'Day',
			'state_type_id' => 'State Type',
			'note' => 'Note',
			'score' => 'Score',
			'status' => 'Status',
			'created_by' => 'Created By',
			'created_on' => 'Created On',
			'modified_by' => 'Modified By',
			'modified_on' => 'Modified On',
			'pre_row_id' => 'Pre Row',
        ];
	}

	/**
	 * Retrieves a list of models based on the current search/filter conditions.
	 *
	 * Typical usecase:
	 * - Initialize the model fields with values from filter form.
	 * - Execute this method to get CActiveDataProvider instance which will filter
	 * models according to data in model fields.
	 * - Pass data provider to CGridView, CListView or any similar widget.
	 *
	 * @return CActiveDataProvider the data provider that can return the models
	 * based on the search/filter conditions.
	 */
	public function search()
	{
		$criteria= new CDbCriteria();

		$criteria->compare('row_id',$this->row_id,true);
		$criteria->compare('employee_absence_id',$this->employee_absence_id,true);
		$criteria->compare('company_id',$this->company_id,true);
		$criteria->compare('employee_contract_id',$this->employee_contract_id,true);
		$criteria->compare('employee_id',$this->employee_id,true);
		$criteria->compare('day',$this->day,true);
		$criteria->compare('state_type_id',$this->state_type_id,true);
		$criteria->compare('note',$this->note,true);
		$criteria->compare('score',$this->score);
		$criteria->compare('status',$this->status);
		$criteria->compare('created_by',$this->created_by,true);
		$criteria->compare('created_on',$this->created_on,true);
		$criteria->compare('modified_by',$this->modified_by,true);
		$criteria->compare('modified_on',$this->modified_on,true);
		$criteria->compare('pre_row_id',$this->pre_row_id);

		return new CActiveDataProvider($this, [
			'criteria'=>$criteria,
        ]);
	}

	/**
	 * Returns the static model of the specified AR class.
	 * Please note that you should have this exact method in all your CActiveRecord descendants!
	 * @param string $className active record class name.
	 * @return EmployeeAbsence the static model class
	 */
	public static function model($className=__CLASS__)
	{
		return parent::model($className);
	}

    public function beforeSave()
    {
        $this->handleAutoFillAndScheduleUnlock();

        return parent::beforeSave();
    }

    public function afterSave()
    {
        $this->handleAutoFillAfterSave();
        $this->invalidateProcessAcknowledgmentRow();

        return parent::afterSave();
    }

	private function autoFillWorkScheduleByWorkgroup(): void
	{
		$absenceCalcInHour = App::getSetting('absence_calculation_hour');
		$dailyWorktimePlace = App::getSetting('dailyWorktimePlace');
        $absenceDayType = $this->getAbsenceDayType($this->day, $this->employee_contract_id);

        if (empty($absenceDayType)
            || empty($absenceDayType[self::DAYTYPE_ID])
        ) {
            return;
        }

        $getEmployeeWorkSchedule = new GetEmployeeWorkSchedule(
            $this->day,
            $this->day,
            [$this->employee_contract_id],
            true,
            'workSchedule',
            [Status::DRAFT, Status::PUBLISHED, Status::WS_OUT_OF_TIME_CHANGE_DRAFT]
        );
        $employeeWorkScheduleRow = $getEmployeeWorkSchedule->get($this->employee_contract_id, $this->day);
        $fullWorkTime = $employeeWorkScheduleRow[EmployeeWorkScheduleEnum::USED_FULL_WORK_TIME] ?: ($absenceDayType[self::FULL_WORK_TIME] ?? 28800);

        $attrs = [
            'employee_contract_id' => $this->employee_contract_id,
            'day' => $this->day,
            'daytype_id' => $absenceDayType[self::DAYTYPE_ID],
            'daytype_name' => $absenceDayType[self::DAYTYPE_NAME],
            'full_work_time' => $fullWorkTime,
            'type_of_daytype' => WorkScheduleUsedTypeOfDayTypeSourceEnum::WORKDAY,
            'created_by' => self::CREATED_BY_BEFORESAVE,
            'created_on' => date('Y-m-d H:i:s')
        ];

        if (empty($employeeWorkScheduleRow[EmployeeWorkScheduleEnum::USED_DAYTYPE_ID])) {
            $wsu = new WorkScheduleUsed();
            $wsu->attributes = $attrs;
            $wsu->status = Status::PUBLISHED;

            if ($wsu->validate()) {
                $wsu->save();
            }
        }

        if (empty($employeeWorkScheduleRow[EmployeeWorkScheduleEnum::DRAFT_WSU_DAYTYPE_ID])) {
            $wsuDraft = new WorkScheduleUsedDraft();
            $wsuDraft->attributes = $attrs;
            $wsuDraft->status = Status::LOCKED;

            if ($wsuDraft->validate()) {
                $wsuDraft->save();
            }
        }

        if ($absenceCalcInHour) {
            if ($dailyWorktimePlace == DailyWorktimePlaceEnum::CONTRACT) {
                if (empty($absenceDayType[self::EMPLOYEE_CONTRACT_DAILY_WORKTIME])) {
                   Yang::log('No daily worktime is defined in the employee’s contract. ' .
                       'The system has applied the standard 8-hour workday for absence tracking.' .
                       'Employee ID: ' . $this->employee_contract_id . ' Day: ' . $this->day,
                       Yang::LOGLEVEL_ERROR
                   );
                }
                $this->absence_hour =
                    $absenceDayType[self::EMPLOYEE_CONTRACT_DAILY_WORKTIME] ?? 8;
            } elseif ($dailyWorktimePlace == DailyWorktimePlaceEnum::WORKGROUP) {
                if (empty($absenceDayType[self::WORKGROUP_DAILY_WORKTIME])) {
                    Yang::log('No daily worktime is defined in the employee’s workgroup. ' .
                        'The system has applied the standard 8-hour workday for absence tracking.' .
                        'Employee ID: ' . $this->employee_contract_id . ' Day: '.$this->day,
                        Yang::LOGLEVEL_ERROR
                    );
                }
                $this->absence_hour =
                    $absenceDayType[self::WORKGROUP_DAILY_WORKTIME] ?? 8;
            } elseif ($dailyWorktimePlace == DailyWorktimePlaceEnum::WORK_SCHEDULE) {
                $correction = $employeeWorkScheduleRow[
                              EmployeeWorkScheduleEnum::USED_PAID_BREAKTIME_AFTER_WORKTIME_CORRECTION
                ] ?? 0;
                if (empty($absenceDayType[self::FULL_WORK_TIME])) {
                    Yang::log('No daily worktime is defined in the employee’s workschedule. ' .
                        'The system has applied the standard 8-hour workday for absence tracking.' .
                        'Employee ID: '.$this->employee_contract_id.' Day: '.$this->day,
                        Yang::LOGLEVEL_ERROR
                    );
                    $correction = 0;
                }
                $this->absence_hour = ($fullWorkTime - $correction) / 3600;
            }
        }
	}

	private function unlockWorkSchedule($absenceDay, $contractID): void
	{
		$modifiedDate = date("Y-m-d H:i:s");
		$deletedStatus = Status::DELETED;

		$criteria = new CDbCriteria();
		$criteria->condition = "
						employee_contract_id = '{$contractID}'
					AND day = '{$absenceDay}'
					AND status IN ('" . Status::PUBLISHED . "', '" . Status::LOCKED . "')
					AND created_by = '" . self::CREATED_BY_BEFORESAVE . "'
					AND modified_on IS NULL
				";

		$wsu = WorkScheduleUsed::model()->find($criteria);
		if (!empty($wsu)) {
			$wsu->status = $deletedStatus;
			$wsu->modified_on = $modifiedDate;
			$wsu->modified_by = self::MODIFIED_BY_AFTERSAVE_REJECTING;
			if ($wsu->validate()) {
				$wsu->save();
			}
		}

		$wsuDraft = WorkScheduleUsedDraft::model()->find($criteria);
		if (!empty($wsuDraft)) {
			$wsuDraft->status = $deletedStatus;
			$wsuDraft->modified_on = $modifiedDate;
			$wsuDraft->modified_by = self::MODIFIED_BY_AFTERSAVE_REJECTING;
			if ($wsuDraft->validate()) {
				$wsuDraft->save();
			}
		}
	}

	private function getAbsenceDayType($day, $employee_contract_id): array
	{
        $employeeGroupWorkGroupJoin = (EmployeeGroupConfig::isActiveGroup('workgroup_id')) ?
            EmployeeGroup::getLeftJoinSQLWithoutCal(
                "workgroup_id",
                "employee_contract",
                $day
            ) :
            '';

		$SQL = "
			SELECT
				daytype.`daytype_id`  AS " . self::DAYTYPE_ID . ",
				daytype.`name`  AS " . self::DAYTYPE_NAME . ",
				daytype.`full_work_time` AS " . self::FULL_WORK_TIME . ",
				employee_contract.`daily_worktime` AS " . self::EMPLOYEE_CONTRACT_DAILY_WORKTIME . ",
				workgroup.`daily_worktime` AS " . self::WORKGROUP_DAILY_WORKTIME . "
			FROM `employee_contract`
			
			{$employeeGroupWorkGroupJoin}
			
			LEFT JOIN `workgroup` ON
				workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
					AND '$day' BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '".App::getSetting("defaultEnd")."')
					AND workgroup.`status` = ".Status::PUBLISHED."
			LEFT JOIN
				`absence_daytype_by_workgroup` ON
					absence_daytype_by_workgroup.`workgroup_id` = workgroup.`workgroup_id`
						AND absence_daytype_by_workgroup.`status` = ".Status::PUBLISHED."
			LEFT JOIN
				`daytype` ON
					daytype.`daytype_id` = absence_daytype_by_workgroup.`daytype_id`
						AND '$day' BETWEEN daytype.`valid_from` AND IFNULL(daytype.`valid_to`, '".App::getSetting("defaultEnd")."')
						AND daytype.`status` = ".Status::PUBLISHED."
			WHERE
				employee_contract.`employee_contract_id` = '$employee_contract_id'
					AND '$day' BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '".App::getSetting("defaultEnd")."')
					AND employee_contract.`status` = ".Status::PUBLISHED."
					AND absence_daytype_by_workgroup.`row_id` IS NOT NULL
					AND daytype.`row_id` IS NOT NULL
			LIMIT 1
		";

        $results = dbFetchAll($SQL);
        if (count($results) && isset(reset($results)[self::DAYTYPE_ID])) {
            return reset($results);
        }

        return [];
	}

    private function schedulingAssistantUnlockWorkSchedule(\DateTime $absenceDay, string $contractID): void
    {
        $lockableWorkScheduleDescriptor = (new LockableWorkScheduleWithEmployeeRawDataBuilder())
            ->reset()
            ->setEmployeeContractIds([$contractID])
            ->setStartDate($absenceDay)
            ->setEndDate($absenceDay)
            ->enableSetUsedSavedsWithoutStatus()
            ->enableSetWorkSchedule()
            ->enableSetAbsences()
            ->enableSetWorkScheduleDraft()
            ->setInvalidStatus()
            ->setOnlyIfUsedSavedExists()
            ->setManualAbsences([
                $this
            ])
            ->build();

        (new UnlockingHandler())($lockableWorkScheduleDescriptor);
    }

	public static function isAbsence ($employeeContractId, $day)
	{
		$sql = "
		SELECT
			`employee_absence`.`status` as `status`,
			`employee_absence`.`full_day_absence` as `full_day_absence`
		FROM `employee_absence`
		WHERE 
			`employee_absence`.`employee_contract_id` = '".$employeeContractId."'
			AND `employee_absence`.`day` = '".$day."'
			AND `employee_absence`.`status` IN  (".Status::PUBLISHED.", ".Status::DRAFT.")
		";

		$command = dbFetchAll($sql);
		if (empty($command)) { $command[0]['status'] = NULL; } 
		return $command;

	}

	/**
     * Egy a dolgozói azonosítóval indexelt tömbben adja vissza a megadott dolgozók
     * távollét adatait a kért időintervallumra.
     *
     * @param  array $employeeContractIds szerződés-azonosító(k)
     * @param  string $from kezdő dátum "YYYY-MM-DD" formátumban
     * @param  string $to végdátum "YYYY-MM-DD" formátumban
     * @return array Dolgozói azonosítóval indexelt tömb, vagy üres tömb,
     * ha nincsenek távollétek a megadott időintervallumra.
     */
    public static function getEmployeeAbsencesByInterval(array $employeeContractIds, string $from, string $to): array
    {
        $sql = "
        SELECT
            `employee_contract_id`,
            `day`,
            `absence_hour`,
            `full_day_absence`,
            `employee_absence_id`,
            `state_type_id`,
            `status`
        FROM
            `employee_absence`
        WHERE
            `employee_contract_id` IN('".implode("', '", $employeeContractIds)."')
            AND `day` BETWEEN '".$from."' AND '".$to."'
            AND `status` = ".Status::PUBLISHED."
        ";

        $absences = dbFetchAll($sql);
		$result	  = [];

        if (!empty($absences)) {
            foreach ($absences as $a) {
                $result[$a['employee_contract_id']][$a['day']]['absence_hour']        = $a['absence_hour'];
                $result[$a['employee_contract_id']][$a['day']]['full_day_absence']    = $a['full_day_absence'];
                $result[$a['employee_contract_id']][$a['day']]['employee_absence_id'] = $a['employee_absence_id'];
                $result[$a['employee_contract_id']][$a['day']]['state_type_id']       = $a['state_type_id'];
                $result[$a['employee_contract_id']][$a['day']]['status']              = $a['status'];
            }
        }
		
		return $result;
    }

	public function employeeAbsenceHourCalculate($data)
	{
		$return = 0;

		$sql = "SELECT daily_worktime
				FROM employee_contract ec
				WHERE ec.`status` = ".Status::PUBLISHED."
				AND ec.valid_from <= '".$data['valid_from']."'
				AND ec.valid_to >= '".$data['valid_to']."'
				AND ec.ec_valid_from <= '".$data['valid_from']."'
				AND ec.ec_valid_to >= '".$data['valid_to']."'
				AND ec.employee_contract_id = '".$data['employee_contract_id']."'
				ORDER BY ec.row_id DESC
				LIMIT 1; ";

		$employee_data = dbFetchRow($sql);

		if($employee_data){
			$return = ($employee_data['daily_worktime'] * $data['quantity']);
		}

		return $return;

	}

	public function getAbsencesForMonth($employeeContractId, $month = '', $baseAbsenceType = '', $sum = false)
	{
		$SQL = "
		SELECT
			ea.row_id,
			ea.employee_contract_id,
			link_at_to_bat.base_absence_type_id,
			IF('".$month."' <> '',DATE_FORMAT(ea.day,'%Y-%m'),ea.day) as day,
		    IF(
		    	'".$sum."' = '1',
		    	SUM(IF('".App::getSetting('showAbsencesInHours')."' = '1', ea.absence_hour, ea.full_day_absence)),
		    	IF('".App::getSetting('showAbsencesInHours')."' = '1', ea.absence_hour, ea.full_day_absence)
		    ) as absence
		FROM employee_absence  ea
		LEFT JOIN link_at_to_bat
			ON ea.state_type_id = link_at_to_bat.state_type_id
		WHERE
			ea.employee_contract_id = '".$employeeContractId."'
			AND ea.status = ".$this->published."
			AND (
				CASE
					WHEN '".$baseAbsenceType."' <> ''
					THEN link_at_to_bat.base_absence_type_id = '".$baseAbsenceType."'
				END
			)
			AND (
				CASE
					WHEN '".$month."' <> ''
					THEN Month(ea.day) = Month('".$month."')
				END
			)
		";

		if( !empty($month) ){
			$SQL .= 'GROUP BY Month(ea.day)';
			$result = dbFetchRow($SQL);
		}else{
			$SQL .= 'GROUP BY ea.day';
			$result = dbFetchAll($SQL);
		}

		return $result;
	}

    private function handleAutoFillAndScheduleUnlock(): void
    {
        if (!(int)App::getSetting(AppSettingsIdEnum::AUTOFILL_WSBG_BY_WORKGROUP)) {
            return;
        }

        $stateTypeFocusedConfig = App::getSetting(AppSettingsIdEnum::AUTOFILL_WSBG_BY_WORKGROUP_STATE_TYPE_FOCUS);
        $focusedStateTypes = json_decode($stateTypeFocusedConfig ?? '{}', true);

        if (!empty($focusedStateTypes) && !$this->isStateTypeInFocusedList($this->state_type_id, $focusedStateTypes)) {
            return;
        }

        if ((int)$this->status !== Status::PUBLISHED && (int)$this->status !== Status::DRAFT) {
            return;
        }

        if (weHaveModule(ModuleNameEnum::SCHEDULING_ASSISTANT)) {
            $absenceDay = DateTime::createFromFormat('Y-m-d', $this->day);
            $this->schedulingAssistantUnlockWorkSchedule($absenceDay, $this->employee_contract_id);
        }

        $this->autoFillWorkScheduleByWorkgroup();
    }

    private function handleAutoFillAfterSave(): void
    {
        if (!(int)App::getSetting(AppSettingsIdEnum::AUTOFILL_WSBG_BY_WORKGROUP)) {
            return;
        }

        $stateTypeFocusedConfig = App::getSetting(AppSettingsIdEnum::AUTOFILL_WSBG_BY_WORKGROUP_STATE_TYPE_FOCUS);
        $focusedStateTypes = json_decode($stateTypeFocusedConfig ?? '{}', true);

        if (!empty($focusedStateTypes) && !$this->isStateTypeInFocusedList($this->state_type_id, $focusedStateTypes)) {
            return;
        }

        if ((int)$this->status !== Status::DELETED) {
            return;
        }

        $absenceDay = DateTime::createFromFormat('Y-m-d', $this->day);
        if (weHaveModule(ModuleNameEnum::SCHEDULING_ASSISTANT)) {
            $this->schedulingAssistantUnlockWorkSchedule($absenceDay, $this->employee_contract_id);
        } else {
            $this->unlockWorkSchedule($this->day, $this->employee_contract_id);
        }
    }

    private function isStateTypeInFocusedList(string $stateTypeId, array $focusedStateTypes): bool
    {
        return in_array($stateTypeId, $focusedStateTypes);
    }

    private function invalidateProcessAcknowledgmentRow(): void
    {
        $processAcknowledgmentSettings = json_decode(App::getSetting('feature_processAcknowledgment_settings'), true);
        if ((isset($processAcknowledgmentSettings['acknowledgment-status']) && $processAcknowledgmentSettings['acknowledgment-status'] === true)) {
            ProcessAcknowledgment::model()->updateAll(
                [
                    ProcessAcknowledgmentEnum::STATUS => Status::DELETED,
                    'modified_on' => date('Y-m-d H:i:s'),
                ],
                'process_identifier = "' . $this->row_id . '" AND process_id = "' . ProcessAcknowledgmentEnum::IDENTIFIER_ABSENCE . '"'
            );
        }
    }

}

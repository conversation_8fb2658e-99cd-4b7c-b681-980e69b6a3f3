<?php

/**
 * Felhasználói tevékenységek megjelenítésére szolgáló kimutatás.
 * Kimutatás alapjául a következő 2 log táblánk szolgál: admin_log & main_log --> gyo<PERSON><PERSON><PERSON><PERSON> érdekében a user_activity_log új táblából konkrét eredmény jön
 *
 * Innote link: report-user-activity
 */
class ReportUserActivityController extends Grid2Controller
{
	// Osztályváltozók
	public $pub;
	public $req;
	public $rej;
	public $del;
	public $inv;
	public $delreq;
	public $lock;
	public $end;
	public $costTable;
	public $costName;
	public $costIdentifier;
	public $regGridEditable;
	public $mainLogAdminLogInterval;
	public $extraHoursMainLogInterval;
	public $empAbsenceMainLogInterval;
	public $maxInterval;
	public $lang;
	public $stateTypes	= [];
	public $employees	= [];
	public $employeeIds	= [];
	public $empIds		= [];
	public $wsuFields	= [];
	public $standbyUpgradedVersion;
	public $overTimeUpgradedVersion;
	public $overTimeRestdayUseInterval;
	public $ptrDefaultMode;
	public $ptrMultiApprove;
	public $useApprovalWithEndorsement;
	public $useStandbyAndDaytypeChangeRequest;
	public $ptrIds	= [];
	public $dicts	= [];
	public $costs	= [];
	public $adminLog	= [];
	public $daytypes	= [];
	public $mainGroups	= [];
	public $extraHours	= [];
	public $delAbsences	= [];
	public $dropdowns	= [];
	public $allAbsences	= [];
	public $eehOtStandbyDutyDrafts	= [];
	public $workSchedOvertimes		= [];
	public $calcBalanceDrafts		= [];
	public $workSchedApproves		= [];
	public $workSchedUsedDrafts		= [];
	public $bulkGroupChangeByCogReqDrafts = [];
	public $ruaSpeedUpRunWeekly;
	public $workWithFiles	= false;
	public $fullPath		= "";
	public $mainPath		= "";
	public $processByMainLogRowId;
	public $processLogins;
	public $processGridDataRows;
	public $showGridDataRows;
	public $memoryLimit;

	/**
	 * Mai nap felhasználó tevékenységeinek betöltése a táblába ( / vagy előző 6 hónap ha még üres a tábla / vagy paraméterben megadott X napra visszamenőleg)
	 * @return void
	 */
	public function actionUploadPrevDataToDb()
	{
		Yang::log("User activity upload to db started.", "log", "system.RUA");
		echo 'Betöltés elkezdődött <br />';

		// Vars + ini
		$this->setVars();
		$this->workWithFiles	= true;
		$customerDbPatchName	= Yang::getParam('customerDbPatchName');
		$this->fullPath			= Yang::getBasePath() . '/../webroot/file_storage/ruaCron/' . $customerDbPatchName;
		$this->mainPath			= Yang::getBasePath() . '/../webroot/file_storage/ruaCron';
		$this->deleteWrittenFiles();
		$mem = ini_get("memory_limit");
		ini_set('memory_limit', $this->memoryLimit);
		$orig = ini_get('max_execution_time');
		ini_set('max_execution_time', '108000');

		// Szükséges adatok lekérése 1x
		$ahp = weHaveModule('ttwa-ahp');
		$wfm = weHaveModule('ttwa-wfm');
		$this->getEmployees();
		$this->getPtrIds();
		if ($ahp) {
			$this->stateTypes = $this->getStateTypes();
			$this->getDeletedAbsences();
			$this->getAllAbsences();
		}
		if ($wfm)
		{
			$this->getCosts();
			$this->getDaytypes();
			$this->getExtraHours();
			$this->getAllEehDrafts();
			$this->getAllWorkSchedOvertimes();
			$this->getAllBalanceDrafts();
			$this->getAllWorkSchedApproves();
			$this->getAllWorkSchedUsedDrafts();
			$this->getAllBulkGroupChangeReqDrafts();
		}

		// Aznap, vagy 6 hónapra visszamenőleg, vagy paraméterbe napban megadott visszamenőlegesen
		$run6Months			= false;
		$runGivenDaysBefore	= false;
		$givenDaysBefore	= 0;
		$rowId				= "";
		$isPrev6MonthSetSQL = "SELECT COUNT(row_id) AS rows_nr FROM `user_activity_log`";
		$rows = dbFetchRow($isPrev6MonthSetSQL);
		if ($rows["rows_nr"] == "0") {
			$run6Months = true;
		}
		if (!is_null(requestParam("days"))) {
			$runGivenDaysBefore	= true;
			$givenDaysBefore	= requestParam("days");
		}

		// Kereső paraméterek beállítása
		$filter				= [];
		$filter["page"]		= ["ALL"];
		$filter["username"]	= "ALL";
		if ($run6Months || $runGivenDaysBefore || $this->processByMainLogRowId)
		{
			// Valid_from és valid_to
			$dates = new DateTime("now");
			$dates->modify("-1 days");
			$filter["valid_to"] = $dates->format("Y-m-d");
			if ($run6Months && !$runGivenDaysBefore) {
				$dates->modify("-6 months");
				$dates->modify("first day of this month");
			} else if ($runGivenDaysBefore) {
				$dates->modify("-" . $givenDaysBefore . " days");
			} else if ($this->processByMainLogRowId)
			{
				// Beolvassa az utolsó feldolgozott main log sor azonosítót fájlból
				$rowIdNotFound = true;
				if (file_exists($this->mainPath . "/" . Yang::customerDbPatchName() . "_mainLogLastProcessedRowId.txt"))
				{
					$data = readFromFile($this->mainPath, Yang::customerDbPatchName() . "_mainLogLastProcessedRowId.txt", false);
					if (count($data) >= 1 && isset($data[0]) && is_numeric($data[0])) {
						$rowIdNotFound	= false;
						$rowId			= $data[0];
						$time			= dbFetchValue("SELECT `event_time` FROM `main_log` WHERE `row_id` = " . $rowId);
						if ($time === false) { $time = "error"; }
					}
				}
				// Ha nincs meg akkor megpróbálja a user_activity_log táblából
				if ($rowIdNotFound)
				{
					$getLatestSQL = "
						SELECT
							m_log.`row_id`,
							m_log.`event_time`
						FROM `user_activity_log` ua_log
						JOIN `main_log` m_log ON
								m_log.`username` = ua_log.`modifier_username`
							AND m_log.`controller_id` = ua_log.`controller_id`
							AND m_log.`event_time` = (SELECT MAX(`changed_datetime`) FROM `user_activity_log`)
						LIMIT 1
					";
					$latest	= dbFetchRow($getLatestSQL);
					$rowId	= $latest["row_id"] ?? "error";
					$time	= $latest["event_time"] ?? "error";
				}

				if ($rowId === "error" || $time === "error") {
					echo "Hiba - Nem található az utoljára betöltött main log sor azonosító vagy időpont!";
					Yang::log("User activity upload error - Last processed main log row id or time not found!", "log", "system.RUA");
					exit();
				}
				// Ha egyezik a jelenlegi intervallum végével -> -1 nap
				$latestDay = explode(" ", $time)[0];
				if ($latestDay == $dates->format("Y-m-d")) {
					$dates->modify("-1 days");
				} else {
					$dates = new DateTime($latestDay);
				}
			}
			$filter["valid_from"] = $dates->format("Y-m-d");

			// Heti vagy napi bontás
			if ($this->ruaSpeedUpRunWeekly)
			{
				// Heti bontás
				$startDate	= $filter["valid_from"];
				$endDate	= $filter["valid_to"];
				$startTime	= strtotime($startDate);
				$endTime	= strtotime($endDate);
				$periods	= [];
				$date		= new DateTime();
				$i			= 0;
				while ($startTime < $endTime)
				{
					$week	= date('W', $startTime);
					$year	= date('Y', $startTime);
					$date->setISODate($year, $week);
					$periods[$i]['start']	= $date->format('Y-m-d');
					$periods[$i]['end']		= date('Y-m-d', strtotime($periods[$i]['start'] . "+6 days"));
					$startTime += strtotime('+1 week', 0);
					$i++;
				}
				// Utsó dátum fix
				$count = count($periods);
				if ($periods[$count-1]["end"] != $endDate) { $periods[$count-1]["end"] = $endDate; }
				if ($runGivenDaysBefore && isset($periods[0]["start"]) && $periods[0]["start"] != $startDate) { $periods[0]["start"] = $startDate; }
			} else {
				// Napi bontás
				$periods = new DatePeriod(
					new DateTime($filter["valid_from"]),
					new DateInterval('P1D'),
					new DateTime(date('Y-m-d', strtotime("+1 day", strtotime($filter["valid_to"]))))
				);
			}

			// Admin log és dropdownok és main log
			$this->getMainLogs($filter["valid_from"], $filter["valid_to"], $filter["username"], " AND 1=1 ", 1, $periods, $rowId);
			$this->getAllAdminLog($filter["valid_from"], $filter["valid_to"], $ahp, true);
			$this->getDropdowns($filter["valid_from"], $filter["valid_to"], $filter["username"], true);

			// Adatok betöltése
			if ($this->processByMainLogRowId) {
				$file = new SplFileInfo($this->mainPath . "/" . Yang::customerDbPatchName() . "_mainLogLastProcessedRowId.txt");
			}
			$insertStartSQL = "INSERT INTO `user_activity_log` (`modifier_username`, `fullname`, `rolegroup`, `event`, `changed_column`, `changed_from`, `changed_to`, `changed_datetime`, `controller_id`, `status`, `created_by`, `created_on`) VALUES ";
			foreach ($periods as $period)
			{
				$filter["valid_from"]	= ($this->ruaSpeedUpRunWeekly) ? $period["start"] : $period->format("Y-m-d");
				$filter["valid_to"]		= ($this->ruaSpeedUpRunWeekly) ? $period["end"] : $period->format("Y-m-d");

				$data		= $this->dataArray("reportUserActivity", $filter, false, true);
				$counter	= 0;
				$insertSQL	= "";
				foreach ($data as $d)
				{
					if ($counter == 0) {
						$insertSQL = $insertStartSQL . "('{$d["modifier_username"]}', '{$d["fullname"]}', '{$d["rolegroup"]}', '{$d["event"]}', '{$d["changed_column"]}', '{$d["changed_from"]}', '{$d["changed_to"]}', '{$d["changed_datetime"]}', '{$d["controller_id"]}', 2, 'rua_upload_cron', NOW()),";
						$counter++;
					} else if ($counter % 499 == 0) {
						$insertSQL = substr($insertSQL, 0, -1) . "; " . $insertStartSQL . "('{$d["modifier_username"]}', '{$d["fullname"]}', '{$d["rolegroup"]}', '{$d["event"]}', '{$d["changed_column"]}', '{$d["changed_from"]}', '{$d["changed_to"]}', '{$d["changed_datetime"]}', '{$d["controller_id"]}', 2, 'rua_upload_cron', NOW()),";
						$counter++;
					} else {
						$insertSQL .= "('{$d["modifier_username"]}', '{$d["fullname"]}', '{$d["rolegroup"]}', '{$d["event"]}', '{$d["changed_column"]}', '{$d["changed_from"]}', '{$d["changed_to"]}', '{$d["changed_datetime"]}', '{$d["controller_id"]}', 2, 'rua_upload_cron', NOW()),";
						$counter++;
					}
					if ($this->processByMainLogRowId && isset($d["row_id"]) && is_numeric($d["row_id"])) {
						$fileobj = $file->openFile('w');
						writeLineToFile($fileobj, $d["row_id"]);
						$fileobj = null;
					}
				}
				if ($counter > 0) {
					dbExecute(substr($insertSQL, 0, -1) . "; ");
				}
			}
		} else {
			// Egy nap
			$filter["valid_from"]	= date("Y-m-d", strtotime("-1 days"));
			$filter["valid_to"]		= $filter["valid_from"];

			// Admin log és dropdownok és main log
			$this->getMainLogs($filter["valid_from"], $filter["valid_to"], $filter["username"], " AND 1=1 ", 1);
			$this->getAllAdminLog($filter["valid_from"], $filter["valid_to"], $ahp, true);
			$this->getDropdowns($filter["valid_from"], $filter["valid_to"], $filter["username"], true);

			// Adatok betöltése
			$data			= $this->dataArray("reportUserActivity", $filter, false, true);
			$counter		= 0;
			$insertSQL		= "";
			$insertStartSQL = "INSERT INTO `user_activity_log` (`modifier_username`, `fullname`, `rolegroup`, `event`, `changed_column`, `changed_from`, `changed_to`, `changed_datetime`, `controller_id`, `status`, `created_by`, `created_on`) VALUES ";
			foreach ($data as $d)
			{
				if ($counter == 0) {
					$insertSQL = $insertStartSQL . "('{$d["modifier_username"]}', '{$d["fullname"]}', '{$d["rolegroup"]}', '{$d["event"]}', '{$d["changed_column"]}', '{$d["changed_from"]}', '{$d["changed_to"]}', '{$d["changed_datetime"]}', '{$d["controller_id"]}', 2, 'rua_upload_cron', NOW()),";
					$counter++;
				} else if ($counter % 499 == 0) {
					$insertSQL = substr($insertSQL, 0, -1) . "; " . $insertStartSQL . "('{$d["modifier_username"]}', '{$d["fullname"]}', '{$d["rolegroup"]}', '{$d["event"]}', '{$d["changed_column"]}', '{$d["changed_from"]}', '{$d["changed_to"]}', '{$d["changed_datetime"]}', '{$d["controller_id"]}', 2, 'rua_upload_cron', NOW()),";
					$counter++;
				} else {
					$insertSQL .= "('{$d["modifier_username"]}', '{$d["fullname"]}', '{$d["rolegroup"]}', '{$d["event"]}', '{$d["changed_column"]}', '{$d["changed_from"]}', '{$d["changed_to"]}', '{$d["changed_datetime"]}', '{$d["controller_id"]}', 2, 'rua_upload_cron', NOW()),";
					$counter++;
				}
			}
			if ($counter > 0) {
				dbExecute(substr($insertSQL, 0, -1) . "; ");
			}
		}

		$this->deleteWrittenFiles();

		// Ini var az eredetire
		ini_set('max_execution_time', $orig);
		ini_set('memory_limit', $mem);

		echo 'Betöltés befejeződött';
		Yang::log("User activity upload to db ended.", "log", "system.RUA");
	}

	/**
	 * Grid2 konstruktor meghívása, controller id beállítás, osztályváltozó def
	 */
	public function __construct()
	{
		$this->setVars();
		ini_set('memory_limit', $this->memoryLimit);
		parent::__construct("reportUserActivity");
		//parent::enableLAGrid();
	}

	/**
	 * Beállítja az alapértékeket és osztályváltozókat
	 */
	private function setVars()
	{
		$this->pub		= Status::PUBLISHED;
		$this->req		= Status::ABAP_REQUESTED;
		$this->rej		= Status::REJECTED;
		$this->del		= Status::DELETED;
		$this->inv		= Status::INVALID;
		$this->lock		= Status::LOCKED;
		$this->delreq	= Status::STATUS_REMOVED;
		$this->end		= App::getSetting("defaultEnd");
		$this->lang		= Dict::getLang();

		$costMode				= App::getSetting("costMode");
		$this->costTable		= ($costMode == "cost") ? "cost" : "cost_center";
		$this->costName			= ($costMode == "cost") ? "cost_name" : "cost_center_name";
		$this->costIdentifier	= ($costMode == "cost") ? "cost_id" : "cost_center_id";
		$this->regGridEditable	= (int)App::getSetting("summarySheet_regsGrid_noncalc_rows_editable");

		$this->mainLogAdminLogInterval		= 8;
		$this->extraHoursMainLogInterval	= 3;
		$this->empAbsenceMainLogInterval	= 3;
		$this->maxInterval					= 1800;

		$this->standbyUpgradedVersion				= App::getSetting("standbyUpgradedVersion");
		$this->overTimeUpgradedVersion				= App::getSetting("otMgmtUpgradedVersion");
		$this->overTimeRestdayUseInterval			= App::getSetting("otMgmtRestdayUseInterval");
		$this->ptrDefaultMode						= App::getSetting("ptr_default_mode");
		$this->ptrMultiApprove						= App::getSetting("ptrMultiLevelApprove");
		$this->useApprovalWithEndorsement			= App::getSetting("useApprovalWithEndorsement");
		$this->useStandbyAndDaytypeChangeRequest	= App::getSetting("useStandbyAndDaytypeChangeRequest");

		$this->getDicts();
		$this->getMainGroups();

		$this->ruaSpeedUpRunWeekly		= (Yang::customerDbPatchName() !== "flex") ? true : false;
		$this->processByMainLogRowId	= (int)App::getSetting("userActivityProcessByMainLogRowId");
		$this->processLogins			= (int)App::getSetting("userActivityProcessLogins");
		$this->processGridDataRows		= (int)App::getSetting("userActivityProcessGridDataRows");
		$this->showGridDataRows			= (int)App::getSetting("userActivityShowGridDataRows");
		$this->memoryLimit				= App::getSetting("userActivityMemoryLimit");
	}

	/**
	 * Beállítja az esemény labeleket & egyéb többször használatos dict-eket & a wsu mezőket
	 * @return void
	 */
	private function getDicts()
	{
		// Dicts
		$this->dicts["company"]						= Dict::getValue("company");
		$this->dicts["payroll"]						= Dict::getValue("Payroll");
		$this->dicts["workgroup"]					= Dict::getValue("workgroup");
		$this->dicts["unit"]						= Dict::getValue("unit");
		$this->dicts["employee_position"]			= Dict::getValue("employee_position_id");
		$this->dicts["company_org_group1"]			= Dict::getValue("company_org_group1");
		$this->dicts["company_org_group2"]			= Dict::getValue("company_org_group2");
		$this->dicts["company_org_group3"]			= Dict::getValue("company_org_group3");
		$this->dicts["all"]							= Dict::getValue("all");
		$this->dicts["balance_transmission"]		= Dict::getValue("balance_transmission");
		$this->dicts["signedIn"]					= Dict::getValue("login");
		$this->dicts["signedOut"]					= Dict::getValue("logout");
		$this->dicts["userChange"]					= Dict::getValue("userswitch_menu");
		$this->dicts["openedSwitchMenu"]			= Dict::getValue("LOG_AUTO_site_open") . " - " . Dict::getValue("userswitch_menu");
		$this->dicts["sumSheet"]					= Dict::getValue("summary_sheet");
		$this->dicts["singleLock"]					= Dict::getValue("rua_singleLock");
		$this->dicts["singleUnlock"]				= Dict::getValue("rua_singleUnlock");
		$this->dicts["singleDelData"]				= Dict::getValue("rua_singleDelData");
		$this->dicts["regTimeChanged"]				= Dict::getValue("rua_regTimeChanged");
		$this->dicts["regTypeChanged"]				= Dict::getValue("rua_regTypeChanged");
		$this->dicts["regCostChanged"]				= Dict::getValue("rua_regCostChanged");
		$this->dicts["regReaderChanged"]			= Dict::getValue("rua_regReaderChanged");
		$this->dicts["regAdded"]					= Dict::getValue("rua_regAdded");
		$this->dicts["regDeleted"]					= Dict::getValue("rua_regDeleted");
		$this->dicts["insideTypeChanged"]			= Dict::getValue("rua_insideTypeChanged");
		$this->dicts["calcValueChanged"]			= Dict::getValue("rua_calcValueChanged");
		$this->dicts["calcAdded"]					= Dict::getValue("rua_calcAdded");
		$this->dicts["calcDeleted"]					= Dict::getValue("rua_calcDeleted");
		$this->dicts["groupLock"]					= Dict::getValue("rua_groupLock");
		$this->dicts["groupUnlock"]					= Dict::getValue("rua_groupUnlock");
		$this->dicts["groupDelData"]				= Dict::getValue("rua_groupDelData");
		$this->dicts["daytypeChanged"]				= Dict::getValue("rua_daytypeChanged");
		$this->dicts["standbyChanged"]				= Dict::getValue("rua_standbyChanged");
		$this->dicts["overtimeChanged"]				= Dict::getValue("rua_overtimeChanged");
		$this->dicts["dutyChanged"]					= Dict::getValue("rua_dutyChanged");
		$this->dicts["absenceAdded"]				= Dict::getValue("rua_absenceAdded");
		$this->dicts["absenceDeleted"]				= Dict::getValue("rua_absenceDeleted");
		$this->dicts["overtimeApproved"]			= Dict::getValue("rua_overtimeApproved");
		$this->dicts["overtimeToPremium"]			= Dict::getValue("rua_overtimeToPremium");
		$this->dicts["overtimeToAbsredempt"]		= Dict::getValue("rua_overtimeToAbsredempt");
		$this->dicts["frameManagement"]				= Dict::getValue("menu_item_frame_management");
		$this->dicts["balanceManagement"]			= Dict::getValue("page_title_balance_management");
		$this->dicts["groupLock"]					= Dict::getValue("rua_groupLock");
		$this->dicts["groupDelData"]				= Dict::getValue("rua_groupDelData");
		$this->dicts["groupSave"]					= Dict::getValue("rua_groupSave");
		$this->dicts["lock"]						= Dict::getValue("status_locked");
		$this->dicts["save"]						= Dict::getValue("sumsheet_calc_saved");
		$this->dicts["del"]							= Dict::getValue("operation_delete");
		$this->dicts["valueSaved"]					= Dict::getValue("rua_valueSaved");
		$this->dicts["wsbu"]						= Dict::getValue("page_title_work_schedule_by_unit");
		$this->dicts["wsbe"]						= Dict::getValue("page_title_edit_work_schedule_by_employee");
		$this->dicts["wsu"]							= Dict::getValue("page_title_work_schedule_by_group");
		$this->dicts["otMgmt"]						= Dict::getValue("page_title_overtime_management");
		$this->dicts["save"]						= Dict::getValue("save");
		$this->dicts["daytypeChanged"]				= Dict::getValue("rua_daytypeChanged");
		$this->dicts["workStartChanged"]			= Dict::getValue("rua_workStartChanged");
		$this->dicts["workEndChanged"]				= Dict::getValue("rua_workEndChanged");
		$this->dicts["otStartChanged"]				= Dict::getValue("rua_otStartChanged");
		$this->dicts["otEndChanged"]				= Dict::getValue("rua_otEndChanged");
		$this->dicts["standbyStartChanged"]			= Dict::getValue("rua_standbyStartChanged");
		$this->dicts["standbyEndChanged"]			= Dict::getValue("rua_standbyEndChanged");
		$this->dicts["dutyStartChanged"]			= Dict::getValue("rua_dutyStartChanged");
		$this->dicts["dutyEndChanged"]				= Dict::getValue("rua_dutyEndChanged");
		$this->dicts["pauseStartChanged"]			= Dict::getValue("rua_pauseStartChanged");
		$this->dicts["pauseEndChanged"]				= Dict::getValue("rua_pauseEndChanged");
		$this->dicts["rua_dayCopied"]				= Dict::getValue("rua_dayCopied");
		$this->dicts["dayCopied"]					= Dict::getValue("rua_dayCopied");
		$this->dicts["wsFullCopied"]				= Dict::getValue("rua_wsFullCopied");
		$this->dicts["wsSaved"]						= Dict::getValue("rua_wsSaved");
		$this->dicts["otOrdered"]					= Dict::getValue("rua_otOrdered");
		$this->dicts["otAccepted"]					= Dict::getValue("rua_otAccepted");
		$this->dicts["otRejected"]					= Dict::getValue("rua_otRejeceted");
		$this->dicts["otDeleted"]					= Dict::getValue("rua_otDeleted");
		$this->dicts["yes"]							= Dict::getValue("yes");
		$this->dicts["no"]							= Dict::getValue("no");
		$this->dicts["number_of_repeated_days"]		= Dict::getValue("number_of_repeated_days");
		$this->dicts["publicHolidayAsRestday"]		= Dict::getValue("publicHolidayAsRestday");
		$this->dicts["wsuByUnitPhRestdayType"]		= Dict::getValue("wsuByUnitPhRestdayType");
		$this->dicts["phCountry"]					= Dict::getValue("phCountry");
		$this->dicts["selected_employees"]			= Dict::getValue("selected_employees");
		$this->dicts["daytype"]						= Dict::getValue("daytype");
		$this->dicts["saveWorkSchedDiffAsPaid"]		= Dict::getValue("saveWorkSchedDiffAsPaid");
		$this->dicts["overtime"]					= Dict::getValue("overtime");
		$this->dicts["type"]						= Dict::getValue("type");
		$this->dicts["daytype_type_of_day_restday"]	= Dict::getValue("daytype_type_of_day_restday");
		$this->dicts["before_worktime"]				= Dict::getValue("before_worktime");
		$this->dicts["after_worktime"]				= Dict::getValue("after_worktime");
		$this->dicts["time"]						= Dict::getValue("time");
		$this->dicts["break_between_wt_ot"]			= Dict::getValue("break_between_wt_ot");
		$this->dicts["ptr"]							= Dict::getValue("page_title_payroll_transfer");
		$this->dicts["ptrApprove"]					= Dict::getValue("ptrApprove");
		$this->dicts["ptrDeny"]						= Dict::getValue("ptrDeny");
		$this->dicts["ptrCancel"]					= Dict::getValue("ptrCancel");
		$this->dicts["ptrSendToApprove"]			= Dict::getValue("ptrSendToApprove");
		$this->dicts["ptrUpload"]					= Dict::getValue("ptrUpload");
		$this->dicts["ptr_target"]					= Dict::getValue("ptr_target");
		$this->dicts["ptrLocked"]					= Dict::getValue("ptrLocked");
		$this->dicts["message"]						= Dict::getValue("message");
		$this->dicts["created_by"]					= Dict::getValue("created_by");
		$this->dicts["created_on"]					= Dict::getValue("created_on");
		$this->dicts["yearView"]					= Dict::getValue("menu_item_ahp_year");
		$this->dicts["absenceApproval"]				= Dict::getValue("page_title_absence_approval");
		$this->dicts["absenceAdded"]				= Dict::getValue("rua_absenceAdded");
		$this->dicts["absenceDeleted"]				= Dict::getValue("rua_absenceDeleted");
		$this->dicts["absenceDeleteRequested"]		= Dict::getValue("absence_delete_required");
		$this->dicts["date"]						= Dict::getValue("date");
		$this->dicts["absence_type_id"]				= Dict::getValue("absence_type_id");
		$this->dicts["approved"]					= Dict::getValue("approved");
		$this->dicts["fullDayAbsence"]				= Dict::getValue("fullDayAbsence");
		$this->dicts["absence_hours"]				= Dict::getValue("absence_hours");
		$this->dicts["absence_accepted"]			= Dict::getValue("absence_accepted");
		$this->dicts["absence_delete_required"]		= Dict::getValue("absence_delete_required");
		$this->dicts["absence_rejected"]			= Dict::getValue("absence_rejected");
		$this->dicts["gridData"]					= Dict::getValue("rua_actionGridData");
		$this->dicts["exportXLS"]					= Dict::getValue("rua_actionExportXLS");
		$this->dicts["saveData"]					= Dict::getValue("rua_actionSave");
		$this->dicts["updateData"]					= Dict::getValue("rua_actionSaveWithPK");
		$this->dicts["deleteData"]					= Dict::getValue("rua_actionDelete");
		$this->dicts["employeeMgmt"]				= Dict::getValue("menu_item_employee_management");
		$this->dicts["absenceCalc"]					= Dict::getValue("absence_calculation");
		$this->dicts["employeeLocked"]				= Dict::getValue("rua_employeeLocked");
		$this->dicts["employeeDeleted"]				= Dict::getValue("rua_employeeDeleted");
		$this->dicts["employeeDeleteLastHistory"]	= Dict::getValue("rua_employeeDeleteLastHistory");
		$this->dicts["employeeDataChanged"]			= Dict::getValue("rua_employeeDataChanged");
		$this->dicts["employeeDataAdded"]			= Dict::getValue("rua_employeeDataAdded");
		$this->dicts["employeeValidityExtended"]	= Dict::getValue("rua_employeeValidityExtended");
		$this->dicts["newAdded"]					= Dict::getValue("rua_newAdded");
		$this->dicts["edited"]						= Dict::getValue("rua_edited");
		$this->dicts["deleted"]						= Dict::getValue("rua_deleted");
		$this->dicts["validityAdded"]				= Dict::getValue("rua_validityAdded");
		$this->dicts["validityEdited"]				= Dict::getValue("rua_validityEdited");
		$this->dicts["validityDeleted"]				= Dict::getValue("rua_validityDeleted");
		$this->dicts["inlinEdited"]					= Dict::getValue("rua_inlinEdited");
		$this->dicts["stDeleted"]					= Dict::getValue("status_deleted");
		$this->dicts["absenceType"]					= Dict::getValue("absence_type");
		$this->dicts["pageTitleAbsenceType"]		= Dict::getValue("page_title_absence_type");
		$this->dicts["modifiedEaseIdentifier"]		= Dict::getValue("rua_modifiedEaseIdentifier");
		$this->dicts["approveController"]			= Dict::getValue("menu_item_approve");
		$this->dicts["approveAbsence"]				= Dict::getValue("rua_approveAbsence");
		$this->dicts["rejectAbsence"]				= Dict::getValue("rua_rejectAbsence");
		$this->dicts["approveRegistration"]			= Dict::getValue("rua_approveRegistration");
		$this->dicts["rejectRegistration"]			= Dict::getValue("rua_rejectRegistration");
		$this->dicts["approveOvertime"]				= Dict::getValue("rua_approveOvertime");
		$this->dicts["rejectOvertime"]				= Dict::getValue("rua_rejectOvertime");
		$this->dicts["approveOvertimeEndorse"]		= Dict::getValue("rua_approveOvertimeEndorse");
		$this->dicts["rejectOvertimeEndorse"]		= Dict::getValue("rua_rejectOvertimeEndorse");
		$this->dicts["approveOvertimeSeen"]			= Dict::getValue("rua_approveOvertimeSeen");
		$this->dicts["rejectOvertimeSeen"]			= Dict::getValue("rua_rejectOvertimeSeen");
		$this->dicts["approveStandby"]				= Dict::getValue("rua_approveStandby");
		$this->dicts["rejectStandby"]				= Dict::getValue("rua_rejectStandby");
		$this->dicts["approveDuty"]					= Dict::getValue("rua_approveDuty");
		$this->dicts["rejectDuty"]					= Dict::getValue("rua_rejectDuty");
		$this->dicts["approveBalance"]				= Dict::getValue("rua_approveBalance");
		$this->dicts["rejectBalance"]				= Dict::getValue("rua_rejectBalance");
		$this->dicts["approveWorkschedule"]			= Dict::getValue("rua_approveWorkschedule");
		$this->dicts["rejectWorkschedule"]			= Dict::getValue("rua_rejectWorkschedule");
		$this->dicts["approveDaytypeChange"]		= Dict::getValue("rua_approveDaytypeChange");
		$this->dicts["rejectDaytypeChange"]			= Dict::getValue("rua_rejectDaytypeChange");
		$this->dicts["approveCogChange"]			= Dict::getValue("rua_approveCogChange");
		$this->dicts["rejectCogChange"]				= Dict::getValue("rua_rejectCogChange");
		$this->dicts["requestWithdrawn"]			= Dict::getValue("request_withdrawn");
		$this->dicts["cardNum"]						= Dict::getValue("cardnum");
		$this->dicts["beforeWorktime"]				= Dict::getValue("before_worktime");
		$this->dicts["afterWorktime"]				= Dict::getValue("after_worktime");
		$this->dicts["restday"]						= Dict::getValue("daytype_type_of_day_restday");

		// WSU fields
		$this->wsuFields =
		[
			"work_start"		=> $this->dicts["workStartChanged"],
			"work_end"			=> $this->dicts["workEndChanged"],
			"overtime_start"	=> $this->dicts["otStartChanged"],
			"overtime_end"		=> $this->dicts["otEndChanged"],
			"standby_start"		=> $this->dicts["standbyStartChanged"],
			"standby_end"		=> $this->dicts["standbyEndChanged"],
			"duty_start"		=> $this->dicts["dutyStartChanged"],
			"duty_end"			=> $this->dicts["dutyEndChanged"],
			"pause_start"		=> $this->dicts["pauseStartChanged"],
			"pause_end"			=> $this->dicts["pauseEndChanged"]
		];
	}

	/**
	 * Rendszerbe található alap csoportok történetiséggel
	 * @return void
	 */
	private function getMainGroups()
	{
		$searchFields = ["company", "payroll", "workgroup", "unit", "employee_position", "company_org_group1", "company_org_group2", "company_org_group3"];
		foreach ($searchFields as $sf)
		{
			if (strpos($sf, "org_group") !== false) {
				$name	= mb_substr($sf, 0, -1) . "_name";
				$id		= mb_substr($sf, 0, -1) . "_id";
			} else {
				$name	= $sf . "_name";
				$id		= $sf . "_id";
			}

			$SQL = "
				SELECT
					`{$id}` AS id,
					`{$name}` AS name,
					`valid_from`,
					`valid_to`
				FROM `{$sf}`
				WHERE `status` = {$this->pub}
			";
			$res = dbFetchAll($SQL);
			foreach ($res as $r) {
				$this->mainGroups[$sf][$r["id"]][$r["valid_from"] . ":" . $r["valid_to"]] = $r["name"];
			}
		}
	}

	/**
	 * Grid inicializálás, használt mód: ARRAY
	 * @return void
	 */
	protected function G2BInit()
    {
		parent::setControllerPageTitleId("page_title_report_user_activity");

		$this->LAGridRights->overrideInitRights("paging",				true);
		$this->LAGridRights->overrideInitRights("search",				true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",				false);
		$this->LAGridRights->overrideInitRights("multi_select",			false);
		$this->LAGridRights->overrideInitRights("column_move",			true);
		$this->LAGridRights->overrideInitRights("reload_sortings",		true);
		$this->LAGridRights->overrideInitRights("details",				false);
		$this->LAGridRights->overrideInitRights("init_open_search",		true);
		$this->LAGridRights->overrideInitRights("reload",				true);
		$this->LAGridRights->overrideInitRights("export_xlsx",			true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",		false);
		$this->maxDays = 93;

		$this->LAGridDB->enableArrMode();
		$this->LAGridDB->setPrimaryKey("primary_key");
		parent::setExportFileName(Dict::getValue("page_title_report_user_activity"));

		parent::G2BInit();
    }

	/**
	 * Oszlop elnevezések definiálása
	 * @return array
	 */
	public function attributeLabels()
    {
        $return =
		[
			'modifier_username'	=> Dict::getValue("modifier_username"),
            'fullname'			=> Dict::getValue("name"),
            'rolegroup'			=> Dict::getValue("rolegroup"),
			'event'				=> Dict::getValue("event"),
            'changed_column'	=> Dict::getValue("changed_column"),
            'changed_from'		=> Dict::getValue("changed_from"),
			'changed_to'		=> Dict::getValue("changed_to"),
			'changed_datetime'	=> Dict::getModuleValue("ttwa-base", "time"),
		];

		return $return;
    }

	/**
	 * Oszlopok definiálása
	 * @return array
	 */
	public function columns()
    {
		$columns =
		[
			'modifier_username'	=> $this->getColDetails(),
			'fullname'			=> $this->getColDetails(200, 'dialogTitle'),
			'rolegroup'			=> $this->getColDetails(),
			'event'				=> $this->getColDetails(350),
			'changed_column'	=> $this->getColDetails(350),
			'changed_from'		=> $this->getColDetails(250),
			'changed_to'		=> $this->getColDetails(250),
			'changed_datetime'	=> $this->getColDetails()
		];

		$columns = $this->columnRights($columns);
		return $columns;
    }

	/**
	 * Grid2 oszlop def helper
	 * @param int $width
	 * @return array
	 */
	private function getColDetails($width = 150, $class = null)
	{
		$col					= [];
		$col["export"]			= true;
		$col["report_width"]	= 20;
		$col["col_type"]		= 'ed';
		$col["edit"]			= false;
		$col["width"]			= $width;
		$col["wrap_text"]	    = true;
		if (!is_null($class)) { $col["class"] = $class; }

		return $col;
	}

	/**
	 * Kereső definiálása
	 * @return array
	 */
	public function search()
	{
		$pages =
		[
			["id" => "ALL", "value" => Dict::getValue("all")],
			["id" => "login", "value" => Dict::getValue("cplatform_login")],
			["id" => "sumSheet", "value" => Dict::getValue("menu_item_summary_sheet")],
			["id" => "balanceManagementWithFrame", "value" => Dict::getValue("menu_item_frame_management") . " & " . Dict::getValue("menu_item_balance_management")],
			["id" => "workScheduleWithOtMgmt", "value" => Dict::getValue("menu_item_work_schedule") . " & " . Dict::getValue("menu_item_overtime_management")],
			["id" => "payrollTransfer", "value" => Dict::getValue("menu_item_payroll_transfer")],
			["id" => "yearlyAbsenceWithApproveReject", "value" => Dict::getValue("menu_item_ahp_year") . " & " . Dict::getValue("menu_item_absence_approval")],
			["id" => "approve", "value" => Dict::getValue("menu_item_approve")],
			["id" => "employeeMgmt", "value" => Dict::getValue("menu_item_employee_management")]
		];

		$search =
		[
			'valid_from' =>
			[
				'label_text'	=> Dict::getModuleValue("ttwa-base", 'valid_from'),
				'model'			=> 'SQL',
				'columnId'		=> 'valid_from',
				'default_value'	=> date('Y-m-01'),
				'col_type'		=> 'ed',
				'dPicker'		=> true,
				'onchange'		=> ['username']
			],
			'valid_to' =>
			[
				'label_text'	=> Dict::getModuleValue("ttwa-base", 'valid_to'),
				'model'			=> 'SQL',
				'columnId'		=> 'valid_to',
				'default_value'	=> date('Y-m-t'),
				'col_type'		=> 'ed',
				'dPicker'		=> true,
				'onchange'		=> ['username']
			],
			'page' =>
			[
				'multiple'		=> 1,
				'class'			=> 'customSelect2Class',
				'label_text'	=> Dict::getValue("controller"),
				'default_value'	=> 'ALL',
				'columnId'		=> 'controller',
				'col_type'		=> 'combo',
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'	=> $pages
				]
			],
			'username' =>
			[
				'label_text'	=> Dict::getValue("username"),
				'columnId'		=> 'username',
				'col_type'		=> 'combo',
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							u.`username` as id,
							u.`username` AS value
						FROM `user` u
						WHERE
								`u`.`status` = {$this->pub}
							AND '{valid_from}' <= IFNULL(u.`valid_to`, '{$this->end}') AND u.`valid_from` <= '{valid_to}'
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				]
			],
			'submit' =>
			[
				'col_type'		=> 'searchBarReloadGrid',
				'width'			=> '*',
				'label_text'	=> ''
			]
		];

		return $search;
	}

	/**
	 * Adathalmaz összeállítása a Grid2 feltöltéséhez
	 * @param string $gridID
	 * @param array $filter
	 * @param boolean $isExport
	 * @return array
	 */
	protected function dataArray($gridID, $filter, $isExport = false, $cronRun = false)
	{
		$results	= [];
		$tableRes	= [];
		$pages		= $filter["page"];
		if ($cronRun) {
			$logData = $this->getAllLogData($filter["valid_from"], $filter["valid_to"], $filter["username"], $pages, $cronRun);
		} else {
			$gridDataWhereSQL	= ($this->showGridDataRows) ? " AND 1=1" : " AND log.`event` NOT LIKE '%Adat%lekérdez%'";
			$whereSQL			= $this->getAllLogData($filter["valid_from"], $filter["valid_to"], $filter["username"], $pages, $cronRun, true /* where SQL condition needed */);
			$tableDataSQL		= "
				SELECT
					log.`modifier_username`,
					log.`fullname`,
					log.`rolegroup`,
					log.`event`,
					log.`changed_column`,
					log.`changed_from`,
					log.`changed_to`,
					log.`changed_datetime`
				FROM `user_activity_log` AS log
				WHERE
						log.`changed_datetime` BETWEEN '{$filter["valid_from"]}' AND DATE_ADD('{$filter["valid_to"]}', INTERVAL 1 DAY)
					AND log.`status` = {$this->pub}
					AND (log.`modifier_username` = '{$filter["username"]}' OR 'ALL' = '{$filter["username"]}')
					{$whereSQL["whereSQL"]}
					{$gridDataWhereSQL}
				ORDER BY `changed_datetime` DESC";
			$tableRes	= dbFetchAll($tableDataSQL);
			$from		= new DateTime($filter["valid_from"]);
			$to			= new DateTime($filter["valid_to"]);
			$today		= new DateTime(date("Y-m-d"));
			if (($today >= $from && $today <= $to) || empty($tableRes))
			{
				$maxSQL	= "SELECT MAX(`changed_datetime`) AS max_dt FROM `user_activity_log` WHERE `status` = {$this->pub}";
				$max	= dbFetchRow($maxSQL);
				if ($max["max_dt"] == "" || is_null($max["max_dt"])) {
					$logData = $this->getAllLogData($filter["valid_from"], $filter["valid_to"], $filter["username"], $pages);
				} else {
					$logData = $this->getAllLogData($max["max_dt"], $filter["valid_to"], $filter["username"], $pages);
				}
			}
		}

		for ($i = 0; $i < count($logData); $i++)
		{
			$event = [];

			try {
				$event = $this->getEvent($logData[$i]);
			} catch (Exception $exception) {
				continue;
			}

			if (!empty($event))
			{
				unset($event["page_title"]);
				unset($event["log_message"]);
				unset($event["log_params"]);
				unset($event["action_id"]);
				unset($event["params"]);
				if (!$cronRun) { unset($event["controller_id"]); }
				$results[] = $event;
			}
		}

		if (!empty($tableRes)) { $results = array_merge($results, $tableRes); }

		return $results;
	}

	/**
	 * A main_log tábla adatai az érvényesség dátumok között, felhasználóra és a kiválasztott oldalakra
	 * Az összes adatos SQL-k csak akkor futnak, ha szükség van rájuk az oldal szűrő alapján
	 * @param string $validFrom
	 * @param string $validTo
	 * @param string $username
	 * @param array $pages
	 * @param boolean $cronRun
	 * @param boolean $getWHERESQLOnly
	 * @return array
	 */
	private function getAllLogData($validFrom, $validTo, $username, $pages, $cronRun = false, $getWHERESQLOnly = false)
	{
		if (count($pages) > 1 || ($pages[0] != "login" && count($pages) == 1)) {
			if (!$cronRun) { $this->getEmployees(); }
		}
		$username	= !is_null($username) && !empty($username) ? $username : "ALL";
		$wfm		= weHaveModule('ttwa-wfm');
		$ahp		= weHaveModule('ttwa-ahp');

		if (!$cronRun)
		{
			if (in_array("ALL", $pages))
			{
				$whereSQL = " AND 1=1";
				if (!$getWHERESQLOnly)
				{
					$this->getAllAdminLog($validFrom, $validTo, $ahp);
					if ($ahp) {
						$this->stateTypes = $this->getStateTypes();
						$this->getDeletedAbsences();
						$this->getAllAbsences();
					}
					if ($wfm)
					{
						$this->getCosts();
						$this->getDaytypes();
						$this->getExtraHours();
						$this->getAllEehDrafts();
						$this->getAllWorkSchedOvertimes();
						$this->getAllBalanceDrafts();
						$this->getAllWorkSchedApproves();
						$this->getAllWorkSchedUsedDrafts();
						$this->getAllBulkGroupChangeReqDrafts();
					}
					$this->getPtrIds();
					$this->getDropDowns($validFrom, $validTo, $username);
				}
			} else {
				$controllerIds = [];
				if (in_array("login", $pages)) {
					$controllerIds[] = "login";
					$controllerIds[] = "userSwitchMenu";
				}
				if (in_array("sumSheet", $pages) && $wfm)
				{
					$controllerIds[] = "wfm/summarySheet";
					if (empty($this->adminLog)) {
						$this->getAllAdminLog($validFrom, $validTo);
					}
					if (empty($this->stateTypes)) {
						$this->stateTypes = $this->getStateTypes();
					}
					if (empty($this->costs)) {
						$this->getCosts();
					}
					if (empty($this->daytypes)) {
						$this->getDaytypes();
					}
					if (empty($this->extraHours)) {
						$this->getExtraHours();
					}
					if (empty($this->delAbsences)) {
						$this->getDeletedAbsences();
					}
				}

				if (in_array("balanceManagementWithFrame", $pages) && $wfm) {
					$whereSQL2 = " OR (log.`controller_id` LIKE '%balanceManagement%' OR log.`controller_id` LIKE '%frameManagement%')";
				} else {
					$whereSQL2 = "";
				}

				if (in_array("workScheduleWithOtMgmt", $pages) && $wfm)
				{
					$controllerIds[] = "wfm/workScheduleByUnit";
					$controllerIds[] = "wfm/editWorkScheduleByEmployee";
					$controllerIds[] = "wfm/workScheduleByGroup";
					$controllerIds[] = "wfm/overtimeManagement";
					if (empty($this->daytypes)) {
						$this->getDaytypes();
					}
				}
				if(in_array("payrollTransfer", $pages)) {
					$controllerIds[] = "payrollTransfer";
					if (empty($this->ptrIds)) {
						$this->getPtrIds();
					}
				}
				if (in_array("yearlyAbsenceWithApproveReject", $pages) && $ahp)
				{
					$controllerIds[] = "ahp/absenceplanneryearview";
					$controllerIds[] = "ahp/absenceapproval";
					if (empty($this->stateTypes)) {
						$this->stateTypes = $this->getStateTypes();
					}
					if (empty($this->adminLog)) {
						$this->getAllAdminLog($validFrom, $validTo);
					}
				}
				if (in_array("approve", $pages)) {
					$controllerIds[] = "approve";
					if (empty($this->stateTypes)) {
						$this->stateTypes = $this->getStateTypes();
					}
					if (empty($this->allAbsences) && $ahp) {
						$this->getAllAbsences();
					}
					if (empty($this->eehOtStandbyDutyDrafts) && $wfm) {
						$this->getAllEehDrafts();
					}
					if (empty($this->workSchedOvertimes) && $wfm) {
						$this->getAllWorkSchedOvertimes();
					}
					if (empty($this->daytypes) && $wfm) {
						$this->getDaytypes();
					}
					if (empty($this->calcBalanceDrafts) && $wfm) {
						$this->getAllBalanceDrafts();
					}
					if (empty($this->workSchedApproves) && $wfm) {
						$this->getAllWorkSchedApproves();
					}
					if (empty($this->workSchedUsedDrafts) && $wfm) {
						$this->getAllWorkSchedUsedDrafts();
					}
					if (empty($this->bulkGroupChangeByCogReqDrafts) && $wfm) {
						$this->getAllBulkGroupChangeReqDrafts();
					}
				}
				if (in_array("employeeMgmt", $pages)) {
					$controllerIds[] = "employee";
					$controllerIds[] = "employeecontrol";
					if (empty($this->adminLog)) {
						$this->getAllAdminLog($validFrom, $validTo, false);
					}
				}

				$whereSQL = " AND (log.`controller_id` IN ('" . implode("', '", $controllerIds) . "') {$whereSQL2})";
			}
		} else {
			$whereSQL = " AND 1=1";
		}

		if ($getWHERESQLOnly) { return ["whereSQL" => $whereSQL]; }

		if (!$cronRun) {
			return $this->getMainLogs($validFrom, $validTo, $username, $whereSQL, 0);
		} else {
			return $this->getMainLogs($validFrom, $validTo, $username, $whereSQL, 2);
		}
	}

	/**
	 * Összes contract id, valid from & to páros névvel
	 * @return void
	 */
	private function getEmployees()
	{
		$SQL = "
			SELECT
				" . Employee::getParam("fullname_with_emp_id", "e") . " AS name,
				ec.`employee_contract_id`,
				ec.`valid_from`,
				IFNULL(ec.`valid_to`, '{$this->end}') AS valid_to,
				e.`employee_id`,
				e.`valid_from` AS empValidFrom,
				IFNULL(e.`valid_to`, '{$this->end}') AS empValidTo,
				e.`emp_id`
			FROM `employee` e
			JOIN `employee_contract` ec ON
					ec.`employee_id` = e.`employee_id`
				AND ec.`status` = {$this->pub}
				AND e.`valid_from` <= IFNULL(ec.`valid_to`, '{$this->end}') AND ec.`valid_from` <= IFNULL(e.`valid_to`, '{$this->end}')
				AND e.`valid_from` <= IFNULL(ec.`ec_valid_to`, '{$this->end}') AND ec.`ec_valid_from` <= IFNULL(e.`valid_to`, '{$this->end}')
			WHERE e.`status` = {$this->pub}
			GROUP BY ec.`employee_contract_id`, ec.`valid_from`
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r) {
			$this->employees[$r["employee_contract_id"]][$r["valid_from"] . ":" . $r["valid_to"]]	= $r["name"];
			$this->employeeIds[$r["employee_id"]][$r["empValidFrom"] . ":" . $r["empValidTo"]]		= $r["name"];
			$this->empIds[$r["emp_id"]][$r["empValidFrom"] . ":" . $r["empValidTo"]]				= $r["name"];
		}
	}

	/**
	 * Összes admin log esemény az szűrt időszakban + interval
	 * @param string $vFrom
	 * @param string $vTo
	 * @param boolean $absence
	 * @param boolean $cronRun
	 * @return void
	 */
	private function getAllAdminLog($vFrom, $vTo, $absence = true, $cronRun = false)
	{
		if ($absence)
		{
			$SQLSelect = ",
				ea.`employee_contract_id`,
				ea.`day`,
				ea.`state_type_id`,
				ea.`employee_absence_id`,
				ea.`del_employee_absence_id`
			";
			$SQLJoin = "
			LEFT JOIN `employee_absence` ea ON ea.`row_id` = IF(al.`model` = 'EmployeeAbsence', al.`model_pk`, 0)
			";
		} else {
			$SQLSelect	= "";
			$SQLJoin	= "";
		}

		$SQL = "
			SELECT
				al.`model`,
				al.`model_pk`,
				al.`created_on`,
				al.`field`,
				al.`old_value`,
				al.`new_value`,
				al.`created_by`
				{$SQLSelect}
			FROM `admin_log` al
			{$SQLJoin}
			WHERE
					al.`created_on` BETWEEN DATE_SUB('{$vFrom}', INTERVAL {$this->mainLogAdminLogInterval} SECOND) AND DATE_ADD('{$vTo}', INTERVAL {$this->mainLogAdminLogInterval} SECOND)
				AND al.`status` = {$this->pub}
		";
		$res = dbFetchAll($SQL);
		if (!$cronRun)
		{
			foreach ($res as $r)
			{
				if (empty($r["model_pk"])) {
					$this->adminLog[$r["model"] . "|empty|" . $r["field"] . "|" . $r["created_on"]] = [
						"old_value"					=> $r["old_value"],
						"new_value"					=> $r["new_value"],
						"employee_contract_id"		=> $r["employee_contract_id"],
						"day"						=> $r["day"],
						"state_type_id"				=> $r["state_type_id"],
						"employee_absence_id"		=> $r["employee_absence_id"],
						"del_employee_absence_id"	=> $r["del_employee_absence_id"],
						"created_by"                => $r["created_by"]
					];
				} else {
					$this->adminLog[$r["model"] . "|" . $r["model_pk"] . "|" . $r["field"] . "|" . $r["created_on"]] = [
						"old_value"					=> $r["old_value"],
						"new_value"					=> $r["new_value"],
						"employee_contract_id"		=> $r["employee_contract_id"],
						"day"						=> $r["day"],
						"state_type_id"				=> $r["state_type_id"],
						"employee_absence_id"		=> $r["employee_absence_id"],
						"del_employee_absence_id"	=> $r["del_employee_absence_id"],
						"created_by"                => $r["created_by"]
					];
				}
			}
		} else {
			$this->writeAdminLogFiles($res);
		}
	}

	/**
	 * Távollétek lekérdezése
	 * @return array
	 */
	private function getStateTypes()
	{
		$SQL = "
			SELECT
				d.`dict_value`,
				st.`state_type_id`
			FROM `state_type` st
			LEFT JOIN `dictionary` d ON
					d.`dict_id` = st.`name_dict_id`
				AND d.`valid` = 1
				AND d.`module` = 'ttwa-ahp'
				AND d.`lang` = '{$this->lang}'
			WHERE st.`status` = {$this->pub}
		";
		$res = dbFetchAll($SQL, "state_type_id", "dict_value");
		return $res;
	}

	/**
	 * Ha aktív a bérátadás jóváhagyás akkor lekéri az összes transfer azonosítót, és hozzá tartozó approve tábla row_idkat
	 * @return void
	 */
	private function getPtrIds()
	{
		if ($this->ptrMultiApprove == "1")
		{
			$SQL = "
				SELECT
					IFNULL(pta.`row_id`, '0') AS row_id,
					pth.`payroll_transfer_id`,
					IF(e.`employee_id` IS NOT NULL, " . Employee::getParam("fullname_with_emp_id", "e") . ", u.`username`) AS creator,
					pth.`created_on`
				FROM `payroll_transfer_history` pth
				LEFT JOIN `payroll_transfer_approve` pta ON
						pta.`payroll_transfer_id` = pth.`payroll_transfer_id`
					AND pta.`score` NOT IN ('-1', '1')
				LEFT JOIN `user` u ON
						u.`user_id` = pth.`created_by`
					AND u.`status` = {$this->pub}
					AND pth.`created_on` BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->end}')
				LEFT JOIN `employee` e ON
						e.`employee_id` = u.`employee_id`
					AND e.`status` = {$this->pub}
					AND pth.`created_on` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->end}')
				WHERE pth.`status` = {$this->pub}
			";
			$res = dbFetchAll($SQL);
			foreach ($res as $r) {
				if ($r["row_id"] == "0") {
					$this->ptrIds[$r["payroll_transfer_id"]] = ["creator" => $r["creator"], "created" => $r["created_on"]];
				} else {
					$this->ptrIds[$r["row_id"]] = ["creator" => $r["creator"], "created" => $r["created_on"]];
				}
			}
		}
	}

	/**
	 * Összes költséghely/viselő lekérdezése
	 * @return void
	 */
	private function getCosts()
	{
		$SQL = "
			SELECT
				`{$this->costIdentifier}` AS id,
				`{$this->costName}` AS name,
				`valid_from`,
				IFNULL(`valid_to`, '{$this->end}') AS valid_to
			FROM `{$this->costTable}`
			WHERE `status` = {$this->pub}
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r) {
			$this->costs[$r["id"] . ":" . $r["name"]][$r["valid_from"] . ":" . $r["valid_to"]] = true;
		}
	}

	/**
	 * Összes naptípus lekérdezése
	 * @return void
	 */
	private function getDaytypes()
	{
		$SQL = "
			SELECT
				`daytype_id` AS id,
				`name` AS name,
				`valid_from`,
				IFNULL(`valid_to`, '{$this->end}') AS valid_to
			FROM `daytype`
			WHERE `status` = {$this->pub}
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r) {
			$this->daytypes[$r["id"]][$r["valid_from"] . ":" . $r["valid_to"]] = $r["name"];
		}
	}

	/**
	 * Visszaadja az összes extra hourst az adatbázisból szerződás, nap, bentlét típus szerint
	 * @return void
	 */
	private function getExtraHours()
	{
		$SQL = "
			SELECT
				eeh.`start` AS new_start,
				eeh.`end` AS new_end,
				eeh.`employee_contract_id`,
				eeh.`day`,
				eeh.`inside_type`,
				eeh.`created_on`,
				eeh.`modified_on`,
				IFNULL(preveeh.`start`, '') AS prev_start,
				IFNULL(preveeh.`end`, '') AS prev_end
			FROM `employee_extra_hours` eeh
			LEFT JOIN `employee_extra_hours` preveeh ON preveeh.`row_id` = eeh.`pre_row_id`
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r)
		{
			$this->extraHours[$r["employee_contract_id"]][$r["day"]][$r["inside_type"]][] =
			[
				"new_start"		=> $r["new_start"],
				"new_end"		=> $r["new_end"],
				"created_on"	=> $r["created_on"],
				"modified_on"	=> $r["modified_on"],
				"prev_start"	=> $r["prev_start"],
				"prev_end"		=> $r["prev_end"]
			];
		}
	}

	/**
	 * Visszaadja az összes extra hourst az adatbázisból bentlét és row_id / process id szerint, amelyek elfogadottak vagy elutasítottak
	 * @return void
	 */
	private function getAllEehDrafts()
	{
		$SQL = "
			SELECT
				`row_id`,
				`start`,
				`end`,
				`process_id`,
				`employee_contract_id`,
				`day`,
				`inside_type`
			FROM `employee_extra_hours`
			WHERE `status` IN ({$this->inv})
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r)
		{
			if (isset($r["inside_type_id"]) && $r["inside_type_id"] == "standby" && $this->useStandbyAndDaytypeChangeRequest)
			{
				$this->eehOtStandbyDutyDrafts[$r["inside_type"]][$r["process_id"]] =
				[
					"start"					=> $r["start"],
					"end"					=> $r["end"],
					"employee_contract_id"	=> $r["employee_contract_id"],
					"day"					=> $r["day"]
				];
			} else {
				$this->eehOtStandbyDutyDrafts[$r["inside_type"]][$r["row_id"]] =
				[
					"start"					=> $r["start"],
					"end"					=> $r["end"],
					"employee_contract_id"	=> $r["employee_contract_id"],
					"day"					=> $r["day"]
				];
			}
		}
	}

	/**
	 * Visszaadja az elfogadott vagy elutasított túlóra elrendeléseket process_id / row_id szerint
	 * @return void
	 */
	private function getAllWorkSchedOvertimes()
	{
		$SQL = "
			SELECT
				`row_id`,
				`process_id`,
				`employee_contract_id`,
				`day`,
				`type`,
				`daytype_id`,
				`time`
			FROM `work_schedule_overtime`
			WHERE `status` IN ({$this->inv})
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r)
		{
			if ($this->useApprovalWithEndorsement)
			{
				$this->workSchedOvertimes[$r["process_id"]] =
				[
					"time"					=> $r["time"],
					"type"					=> $r["type"],
					"employee_contract_id"	=> $r["employee_contract_id"],
					"day"					=> $r["day"],
					"daytype_id"			=> $r["daytype_id"]

				];
			} else {
				$this->workSchedOvertimes[$r["row_id"]] =
				[
					"time"					=> $r["time"],
					"type"					=> $r["type"],
					"employee_contract_id"	=> $r["employee_contract_id"],
					"day"					=> $r["day"],
					"daytype_id"			=> $r["daytype_id"]
				];
			}
		}
	}

	/**
	 * Visszaadja az elfogadott / elutasított / mentett / zárolt balanceokat row_id szerint
	 * @return void
	 */
	private function getAllBalanceDrafts()
	{
		$SQL = "
			SELECT
				`employee_contract_id`,
				`day`,
				`value`,
				`row_id`
			FROM `employee_calc`
			WHERE
					`inside_type_id` = 'balance'
				AND `status` IN ({$this->pub}, {$this->rej}, {$this->del}, {$this->lock})
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r) {
			$this->calcBalanceDrafts[$r["row_id"]] = ["employee_contract_id" => $r["employee_contract_id"], "value" => $r["value"], "day" => $r["day"]];
		}
	}

	/**
	 * Visszaadja az összes elfogadott vagy elutasított munkarend jóváhagyást
	 * @return void
	 */
	private function getAllWorkSchedApproves()
	{
		$SQL = "
			SELECT
				wsa.`row_id`,
				wsa.`group_id`,
				wsa.`begin_day`,
				wsa.`end_day`,
				wsa.`status`,
				IFNULL(u.`unit_name`, wsa.`group_id`) AS unit_name
			FROM `work_schedule_approve` wsa
			LEFT JOIN `unit` u ON
					u.`unit_id` = wsa.`group_id`
				AND u.`status` = {$this->pub}
				AND u.`valid_from` <= wsa.`end_day`
				AND wsa.`begin_day` <= IFNULL(u.`valid_to`, '{$this->end}')
			WHERE wsa.`status` IN ({$this->pub}, {$this->req})
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r) {
			$this->workSchedApproves[$r["row_id"]] = ["group_id" => $r["group_id"], "begin" => $r["begin_day"], "end" => $r["end_day"], "status" => $r["status"], "unit_name" => $r["unit_name"]];
		}
	}

	/**
	 * Visszaadja az elfogadott / elutasított egyéni munkarend szerkesztést process_idnként
	 * @return void
	 */
	private function getAllWorkSchedUsedDrafts()
	{
		if ($this->useStandbyAndDaytypeChangeRequest)
		{
			$SQL = "
				SELECT
					`day`,
					`work_start`,
					`work_end`,
					`employee_contract_id`,
					`process_id`,
					`status`,
					`daytype_name`
				FROM `work_schedule_used`
				WHERE `status` = {$this->inv}
			";
			$res = dbFetchAll($SQL);
			foreach ($res as $r) {
				$this->workSchedUsedDrafts[$r["process_id"]] = ["employee_contract_id" => $r["employee_contract_id"], "day" => $r["day"], "start" => $r["work_start"], "end" => $r["work_end"], "status" => $r["status"], "daytype" => $r["daytype_name"]];
			}
		}
	}

	/**
	 * Visszaadja az elfogadott és elutasított csoport váltásokat
	 * @return void
	 */
	private function getAllBulkGroupChangeReqDrafts()
	{
		$SQL = "
			SELECT
				main.`request_id`,
				main.`employee_contract_id`,
				main.`cog_id`,
				main.`status`,
				main.`valid_from`,
				IFNULL(cog2.`company_org_group_name`, main.`cog_id`) AS cog_name
			FROM `bulk_group_change_by_cog_request` main
			LEFT JOIN `company_org_group2` cog2 ON
					cog2.`company_org_group_id` = main.`cog_id`
				AND cog2.`status` = {$this->pub}
				AND main.`valid_from` BETWEEN cog2.`valid_from` AND IFNULL(cog2.`valid_to`, '{$this->end}')
			WHERE main.`status` IN ({$this->inv}, {$this->req})
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r) {
			$this->bulkGroupChangeByCogReqDrafts[$r["request_id"]] = ["employee_contract_id" => $r["employee_contract_id"], "cog_id" => $r["cog_id"], "status" => $r["status"], "cog_name" => $r["cog_name"], "valid_from" => $r["valid_from"]];
		}
	}

	/**
	 * Visszaadja az összes törölt v. törlésre jelölt távollétet a rendszerből
	 * @return void
	 */
	private function getDeletedAbsences()
	{
		$SQL = "
			SELECT
				`state_type_id`,
				`employee_contract_id`,
				`day`,
				`modified_on`
			FROM `employee_absence`
			WHERE
					`status` IN ({$this->delreq}, {$this->del})
				AND `modified_on` IS NOT NULL
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r) {
			$this->delAbsences[$r["employee_contract_id"]][$r["day"]][] = ["state_type" => $this->stateTypes[$r["state_type_id"]], "modified_on" => $r["modified_on"]];
		}
	}

	/**
	 * Visszaadja az összes visszavont elutasított és elfogadott távollétet
	 * @return void
	 */
	private function getAllAbsences()
	{
		$SQL = "
			SELECT
				`state_type_id`,
				MAX(`day`) AS end,
				MIN(`day`) AS start,
				`status`,
				`employee_contract_id`,
				`employee_absence_id`
			FROM `employee_absence`
			WHERE `status` IN ({$this->pub}, {$this->del}, {$this->rej})
			GROUP BY `employee_absence_id`
		";
		$res = dbFetchAll($SQL);
		foreach ($res as $r)
		{
			$this->allAbsences[$r["employee_absence_id"]] =
			[
				"state_type_id"			=> $this->stateTypes[$r["state_type_id"]],
				"start"					=> $r["start"],
				"end"					=> $r["end"],
				"status"				=> $r["status"],
				"employee_contract_id"	=> $r["employee_contract_id"]
			];
		}
	}

	/**
	 * Visszaadja a lekérdezett dropdown érték és label kombokat
	 * @param string $vFrom
	 * @param string $vTo
	 * @param string $user
	 * @param boolean $cronRun
	 * @return void
	 */
	private function getDropdowns($vFrom, $vTo, $user, $cronRun = false)
	{
		$SQL = "
			SELECT
				`controller_id`,
				`username`,
				`event_time`,
				`log_params`
			FROM `main_log`
			WHERE
					`event_time` BETWEEN DATE_SUB('{$vFrom}', INTERVAL 1 DAY) AND DATE_ADD('{$vTo}', INTERVAL 1 DAY)
				AND (`username` = '{$user}' OR '{$user}' = 'ALL')
				AND `username` IS NOT NULL
				AND `username` <> ''
				AND `log_message` = 'DROPDOWN'
			ORDER BY `username`, `controller_id`, `event_time` DESC
		";
		$res = dbFetchAll($SQL);
		if (!$cronRun) {
			foreach ($res as $r) {
				$allData		= json_decode($r["log_params"], true);
				$allDataResult	= isset($allData["data"]["data"]) ? $allData["data"]["data"] : null;
				$this->dropdowns[$r["username"]][$r["controller_id"]][$r["event_time"]][$allData["data"]["col"]] = $allDataResult;
			}
		} else {
			$this->writeDropdownFiles($res);
		}
	}

	/**
	 * Milyen esemény típus
	 * @param array $mainLog
	 * @return array
	 */
	private function getEvent($mainLog)
	{
		// Belépés & Kilépés & Felhasználóváltás
		if (
			($mainLog["controller_id"] == "login" || $mainLog["controller_id"] == "userSwitchMenu") &&
			($mainLog["action_id"] == "index" || $mainLog["action_id"] == "logout" || $mainLog["action_id"] == "switchUser" || $mainLog["action_id"] == "login")
		) {
			return $this->getUserLoggedInOutChanged($mainLog);
		}

		// Összesítőlap
		if ($mainLog["controller_id"] == "wfm/summarySheet") {
			return $this->getSumSheetEvents($mainLog);
		}

		// Keretegyenleg
		if (strpos($mainLog["controller_id"], "balanceManagement") !== false || strpos($mainLog["controller_id"], "frameManagement") !== false) {
			return $this->getFrameManagementEvents($mainLog);
		}

		// Munkarend + Túlóra kezelés
		if ($mainLog["controller_id"] == "wfm/workScheduleByUnit" || $mainLog["controller_id"] == "wfm/editWorkScheduleByEmployee" || $mainLog["controller_id"] == "wfm/workScheduleByGroup" || $mainLog["controller_id"] == "wfm/overtimeManagement") {
			return $this->getWorkScheduleAndOvertimeMgmtEvents($mainLog);
		}

		// Bérszámfejtés átadás
		if ($mainLog["controller_id"] == "payrollTransfer") {
			return $this->getPayrollTransferEvents($mainLog);
		}

		// Éves távollét + Távollét jóváhagyás
		if ($mainLog["controller_id"] == "ahp/absenceplanneryearview" || $mainLog["controller_id"] == "ahp/absenceapproval") {
			return $this->getAhpEvents($mainLog);
		}

		// Jóváhagyás
		if ($mainLog["controller_id"] == "approve") {
			return $this->getApproveEvents($mainLog);
		}

		// Origi munkavállaló kezelés fenti actionök + flex munkavállaló kezelés
		if ($mainLog["controller_id"] == "employee" || $mainLog["controller_id"] == "employeecontrol") {
			return $this->getEmployeeMgmtEvents($mainLog);
		}

		// Általános Grid2 CRUD
		if ($mainLog["action_id"] == "gridData" || $mainLog["action_id"] == "delete" || $mainLog["action_id"] == "exportXLS" || $mainLog["action_id"] == "save" || $mainLog["action_id"] == "gridEdit") {
			return $this->getBaseEvents($mainLog);
		}

		return [];
	}

	/**
	 * Grid2 Adatok: belépés, kilépés, felhasználóváltás
	 * @param array $mainLog
	 * @return array
	 */
	private function getUserLoggedInOutChanged($mainLog)
	{
		// belépés
		if ($mainLog["controller_id"] == "login" && $mainLog["action_id"] == "login") {
			$mainLog["event"] = $this->dicts["signedIn"];
		// felhasználó váltás
		} else if ($mainLog["controller_id"] == "login" && $mainLog["action_id"] == "switchUser") {
			$mainLog["event"] = $this->dicts["userChange"];
		// kijelentkezés
		} else if ($mainLog["controller_id"] == "login" && $mainLog["action_id"] == "logout") {
			$mainLog["event"] = $this->dicts["signedOut"];
		// lenyitja a felhasználó váltás ablakot
		} else if ($mainLog["controller_id"] == "userSwitchMenu" && $mainLog["action_id"] == "index") {
			$mainLog["event"] = $this->dicts["openedSwitchMenu"];
		} else {
			return [];
		}

		return $mainLog;
	}

	/**
	 * Összesítőlap tevékenységeit adja vissza
	 * @param array $mainLog
	 * @return array
	 */
	private function getSumSheetEvents($mainLog)
	{
		// Params
		$logParams = json_decode($mainLog["params"], true);

		// sima zárolás / feloldás / adat törlés
		if ($mainLog["action_id"] == "modifyCalcStatus" && ($logParams["POST.status"] == "LOCK" || $logParams["POST.status"] == "UNLOCK" || $logParams["POST.status"] == "DELSAVE") && isset($logParams["POST.sumsheetParams.startLockDate"]) && !isset($logParams["POST.sumsheetParams.endLockDate"]) && $logParams["POST.sumsheetParams.ecID"] != "ALL")
		{
			switch($logParams["POST.status"])
			{
				case "LOCK":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["singleLock"];
					break;
				case "UNLOCK":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["singleUnlock"];
					break;
				case "DELSAVE":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["singleDelData"];
					break;
			}
			$mainLog["changed_column"] = $logParams["POST.sumsheetParams.title"];
		// Regisztráció módosítás
		} else if ($mainLog["action_id"] == "gridEdit" && $logParams["GET.gridID"] == "regsGrid" && isset($logParams["POST.ids"]))
		{
			$exp = explode(" ", $logParams["POST." . $logParams["POST.ids"] . "_c0"]);
			$mainLog["changed_column"] = $this->getNameWithId($logParams["GET.sumsheetParams.ecID"], $logParams["POST." . $logParams["POST.ids"] . "_c0"]) . " - " . $this->formatDate($logParams["GET.sumsheetParams.startDate"], false, true) . $exp[1];

			// régi reg adatok
			$regs			= new Registration();
			$criteria		= new CDbCriteria();
			$criteria->condition = "`row_id` = {$logParams["POST.ids"]}";
			$reg			= $regs->find($criteria);
			$originalCost	= $this->getCostName($reg->cost_id, $reg->time);
			$newCost		= $this->getCostName($logParams["POST." . $logParams["POST.ids"] . "_c2"], $logParams["POST." . $logParams["POST.ids"] . "_c0"]);

			// idő v. esemény v. költséghely/viselő v. olvasó módosult
			if ($reg->time != $logParams["POST." . $logParams["POST.ids"] . "_c0"]) {
				$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["regTimeChanged"];
				$mainLog["changed_from"]	= $reg->time;
				$mainLog["changed_to"]		= $logParams["POST." . $logParams["POST.ids"] . "_c0"];
			} else if ($reg->event_type_id != $logParams["POST." . $logParams["POST.ids"] . "_c1"]) {
				$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["regTypeChanged"];
				$mainLog["changed_from"]	= $reg->event_type_id;
				$mainLog["changed_to"]		= $logParams["POST." . $logParams["POST.ids"] . "_c1"];
			} else if ($reg->cost_id != $logParams["POST." . $logParams["POST.ids"] . "_c2"] && $reg->cost_id != $newCost && $originalCost != $logParams["POST." . $logParams["POST.ids"] . "_c2"]) {
				$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["regCostChanged"];
				$mainLog["changed_from"]	= $originalCost;
				$mainLog["changed_to"]		= $newCost;
			} else if (($reg->terminal_id . $reg->reader_id) != $logParams["POST." . $logParams["POST.ids"] . "_c3"])
			{
				// régi és új terminál név
				$SQL = "
					SELECT
						t.`terminal_name` AS old_terminal_name,
						t2.`terminal_name` AS new_terminal_name
					FROM `terminal` t
					LEFT JOIN `terminal` t2 ON
							'{$logParams["POST." . $logParams["POST.ids"] . "_c0"]}' BETWEEN t2.`valid_from` AND IFNULL(t2.`valid_to`, '{$this->end}')
						AND t2.`status` = {$this->pub} " .
						($this->regGridEditable ? "" : " AND t2.`is_calc` = 1") .
						" AND CONCAT(t2.`terminal_id`, IFNULL(t2.`reader_id`, '')) = '" . $logParams["POST." . $logParams["POST.ids"] . "_c3"] . "'
					WHERE
							'{$reg->time}' BETWEEN t.`valid_from` AND IFNULL(t.`valid_to`, '{$this->end}')
						AND t.`status` = {$this->pub} " .
						($this->regGridEditable ? "" : " AND t.`is_calc` = 1") .
						" AND CONCAT(t.`terminal_id`, IFNULL(t.`reader_id`, '')) = '" . $reg->terminal_id . $reg->reader_id . "'
				";
				$res = dbFetchRow($SQL);
				$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["regReaderChanged"];
				$mainLog["changed_from"]	= $res["old_terminal_name"];
				$mainLog["changed_to"]		= $res["new_terminal_name"];
			} else {
				return [];
			}
		// Regisztráció felvétel
		} else if ($mainLog["action_id"] == "sumsheetAddRow" && $logParams["POST.grid_id"] == "regsGrid")
		{
			$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["regAdded"];
			$eventType					= (empty($logParams["POST.sumsheetParams.event_type_id"])) ? "" : " - " . $logParams["POST.sumsheetParams.event_type_id"];
			$mainLog["changed_column"]	= $logParams["POST.sumsheetParams.title"] . $eventType;
		// Regisztráció törlés
		} else if ($mainLog["action_id"] == "sumsheetDelRow" && isset($logParams["POST.ids"]) && $logParams["POST.modelName"] == "Registration")
		{
			$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["regDeleted"];
			$regs						= new Registration();
			$criteria					= new CDbCriteria();
			$criteria->condition		= "`row_id` = {$logParams["POST.ids"]}";
			$reg						= $regs->find($criteria);
			$exp						= explode(" ", $reg->time);
			$mainLog["changed_column"]	= $logParams["POST.sumsheetParams.title"] . " - " . $exp[1] . ' - ' . $logParams["POST.time"];
		// Jogcím módosítás
		} else if ($mainLog["action_id"] == "gridEdit" && $logParams["GET.gridID"] == "calcGrid" && isset($logParams["POST.ids"]))
		{
			// admin logból miről mire
			$res = $this->getFromAdminLog("EmployeeCalc", $logParams["POST.ids"], ["value", "inside_type_id"], $mainLog["changed_datetime"]);
			if (!empty($res))
			{
				$mainLog["event"] = $this->dicts["sumSheet"] . " - ";
				if (isset($res["inside_type_id"])) {
					$mainLog["event"]			.= $this->dicts["insideTypeChanged"];
					$mainLog["changed_from"]	= Dict::getValue($res["inside_type_id"]["old_value"]) . " - " . $logParams["POST." . $logParams["POST.ids"] . "_c1"];
					$mainLog["changed_to"]		= Dict::getValue($res["inside_type_id"]["new_value"]) . " - " . $logParams["POST." . $logParams["POST.ids"] . "_c1"];
				} else if (isset($res["value"])) {
					$mainLog["event"]			.= $this->dicts["calcValueChanged"];
					if (secToHis((int)$res["value"]["old_value"]) == "") { $oldVal = "00:00"; } else { $oldVal = secToHis((int)$res["value"]["old_valie"]); }
					$mainLog["changed_from"]	= Dict::getValue($logParams["POST." . $logParams["POST.ids"] . "_c0"]) . " - " . $oldVal;
					$mainLog["changed_to"]		= Dict::getValue($logParams["POST." . $logParams["POST.ids"] . "_c0"]) . " - " . secToHis((int)$res["value"]["new_value"]);
				}
				$mainLog["changed_column"] = $logParams["GET.sumsheetParams.title"];
			} else {
				// Lehet még költséghely / viselő módosítás is?
				return [];
			}
		// Jogcím felvétel
		} else if ($mainLog["action_id"] == "sumsheetAddRow" && $logParams["POST.grid_id"] == "calcGrid")
		{
			$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["calcAdded"];
			$mainLog["changed_column"]	= $this->getNameWithId($logParams["POST.sumsheetParams.ecID"], $logParams["POST.sumsheetParams.startDate"]) . " - " . $this->formatDate($logParams["POST.sumsheetParams.startDate"], true);
			$mainLog["changed_to"]		= Dict::getValue("wtde") . " - 00:00";
		// Jogcím törölve
		} else if ($mainLog["action_id"] == "sumsheetDelRow" && $logParams["POST.modelName"] == "EmployeeCalc" && isset($logParams["POST.ids"]))
		{
			// az inside type olyan nyelven lesz a paramsba ami a törlés pillanatába aktuális volt ezért a jelenlegi felhasználó nevéhez lekérjük a dict_id-t
			$SQL = "SELECT `dict_id` FROM `dictionary` WHERE `dict_value` = '{$logParams["POST.insideType"]}' AND `module` = 'ttwa-wfm' AND `valid` = 1";
			$res = dbFetchRow($SQL);
			$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["calcDeleted"];
			$mainLog["changed_column"]	= $this->getNameWithId($logParams["POST.sumsheetParams.ecID"], $logParams["POST.sumsheetParams.startDate"]) . " - " . $this->formatDate($logParams["POST.sumsheetParams.startDate"], false, true) . Dict::getValue($res["dict_id"]) . " - " . $logParams["POST.time"];
		// Csoportos feloldás / adattörlés / zárolás / túlóra elfogadás / túlóra jutalommá alakítás / túlóra váltás távolétté
		} else if ($mainLog["action_id"] == "modifyCalcStatus" && ($logParams["POST.status"] == "UNLOCK" || $logParams["POST.status"] == "DELSAVE" || $logParams["POST.status"] == "LOCK" || $logParams["POST.status"] == "SAVE" || $logParams["POST.status"] == "CHANGEPR" || $logParams["POST.status"] == "CHANGEOT_TO_ABS"))
		{
			switch ($logParams["POST.status"])
			{
				case "UNLOCK":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["groupUnlock"];
					break;
				case "DELSAVE":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["groupDelData"];
					break;
				case "LOCK":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["groupLock"];
					break;
				case "SAVE":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["overtimeApproved"];
					break;
				case "CHANGEPR":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["overtimeToPremium"];
					break;
				case "CHANGEOT_TO_ABS":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["overtimeToAbsredempt"];
					break;
			}

			// egy ember teljes sora esemény
			if ($logParams["POST.sumsheetParams.ecID"] != "ALL")
			{
				if ($logParams["POST.status"] == "SAVE" || $logParams["POST.status"] == "CHANGEPR" || $logParams["POST.status"] == "CHANGEOT_TO_ABS") {
					$mainLog["changed_column"] = $this->getNameWithId($logParams["POST.sumsheetParams.ecID"], $logParams["POST.sumsheetParams.startDate"]) . " - " . $this->formatDate($logParams["POST.sumsheetParams.startLockDate"], true);
				} else {
					$mainLog["changed_column"] = $this->getNameWithId($logParams["POST.sumsheetParams.ecID"], $logParams["POST.sumsheetParams.startDate"]) . " - " . $this->formatDate($logParams["POST.sumsheetParams.startLockDate"], false, true) . $this->formatDate($logParams["POST.sumsheetParams.endLockDate"], true);
				}
			// oszlop v. táblázat szintű esemény
			} else {
				if (!isset($logParams["POST.sumsheetParams.endLockDate"])) {
					$mainLog["changed_column"] = $this->formatDate($logParams["POST.sumsheetParams.startLockDate"], false, false, true);
				} else {
					$mainLog["changed_column"] = $this->formatDate($logParams["POST.sumsheetParams.startLockDate"], false, true) . $this->formatDate($logParams["POST.sumsheetParams.endLockDate"], false, false, true);
				}
				// kire van lekérve az összesítőlap --> rájuk fut a csoportos esemény
				$SQL = "SELECT params FROM `main_log` WHERE `event_time` < '{$mainLog["changed_datetime"]}' AND `controller_id` = 'wfm/summarySheet' AND `action_id` = 'gridData' AND `username` = '{$mainLog["modifier_username"]}' AND `params` LIKE '%mainGrid%' ORDER BY `event_time` DESC LIMIT 1";
				$res = dbFetchRow($SQL);
				$filters = json_decode($res["params"], true);
				$mainLog["changed_column"] .= $this->getFilterValues($filters, "", $filters["POST.searchInput.valid_from"], $filters["POST.searchInput.valid_to"]);
			}
		// Naptípus módosítás
		} else if ($mainLog["action_id"] == "daytypeModifySave")
		{
			$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["daytypeChanged"];
			$mainLog["changed_column"]	= $logParams["POST.sumsheetParams.title"];
			$mainLog["changed_from"]	= $logParams["POST.sumsheetParams.type_of_daytype"] . " - " . $logParams["POST.sumsheetParams.daytypeName"];
			$mainLog["changed_to"]		= $logParams["POST.sumsheetParams.dialog_type_of_daytype_select"] . " - " . $this->getDaytypeName($logParams["POST.sumsheetParams.dialog_daytype_select"], $logParams["POST.sumsheetParams.startDate"]);
		// Készenlét - ügyelet - túlóra
		} else if ($mainLog["action_id"] == "summarySheetSaveEmployeeExtraHours")
		{
			switch($logParams["POST.defaults.inside_type"])
			{
				case "overtime":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["overtimeChanged"];
					break;
				case "duty":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["dutyChanged"];
					break;
				case "standby":
					$mainLog["event"] = $this->dicts["sumSheet"] . " - " . $this->dicts["standbyChanged"];
					break;
			}
			$mainLog["changed_column"]	= $logParams["POST.sumsheetParams.title"];
			$extraHours = $this->getExtraHour($logParams["POST.hiddenDialogInput_modEmployeeExtraHoursDialog.ecID"], $logParams["POST.hiddenDialogInput_modEmployeeExtraHoursDialog.startDate"], $logParams["POST.defaults.inside_type"], $mainLog["changed_datetime"]);
			$mainLog["changed_from"]	= ($extraHours["prev_start"] == "") ? "" : $extraHours["prev_start"] . " - " . $extraHours["prev_end"];
			$mainLog["changed_to"]		= $extraHours["new_start"] . " - " . $extraHours["new_end"];
		// Távollét kiírás
		} else if ($mainLog["action_id"] == "summarySheetSaveAbsence" && $logParams["POST.generate_from"] == "absenceDialog")
		{
			$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["absenceAdded"];
			$mainLog["changed_column"]	= $logParams["POST.hiddenDialogInput_absenceDialog.title"] . " - " . $this->formatDate($logParams["POST.dialogInput_absenceDialog.valid_from"], false, true) . $this->formatDate($logParams["POST.dialogInput_absenceDialog.valid_to"], false, true) . $this->stateTypes[$logParams["POST.dialogInput_absenceDialog.absence_type"]];
		// Távollét törlés
		} else if ($mainLog["action_id"] == "summarySheetDeleteAbsence")
		{
			$mainLog["event"]			= $this->dicts["sumSheet"] . " - " . $this->dicts["absenceDeleted"];
			$mainLog["changed_column"]	= $logParams["POST.sumsheetParams.title"] . " - " . $this->getDeletedAbsence($logParams["POST.sumsheetParams.ecID"], $logParams["POST.sumsheetParams.startDate"], $mainLog["changed_datetime"]);
		} else {
			return $this->getBaseEvents($mainLog);
		}

		return $mainLog;
	}

	/**
	 * Dátum formázás és folytatás
	 * @param string $date
	 * @param boolean $withPointInEnd
	 * @param boolean $withMinusInEnd
	 * @param boolean $withBrInEnd
	 * @return string
	 */
	private function formatDate($date, $withPointInEnd = false, $withMinusInEnd = false, $withBrInEnd = false)
	{
		if ($withPointInEnd) {
			return str_replace("-", ".", $date) . ".";
		} else if ($withMinusInEnd) {
			return str_replace("-", ".", $date) . ". - ";
		} else if ($withBrInEnd) {
			return str_replace("-", ".", $date) . ". <br /> ";
		} else {
			return str_replace("-", ".", $date);
		}
	}

	/**
	 * Visszaadja a dolgozó nevét és törzsszámát napra
	 * @param string $employeeContractId
	 * @param string $time
	 * @param string $vFrom
	 * @param string $vTo
	 * @param string $employeeId
	 * @param string $empId
	 * @return string
	 */
	private function getNameWithId($employeeContractId = "", $time = "", $vFrom = "", $vTo = "", $employeeId = "", $empId = "")
	{
		// nincs ilyen id
		if (!isset($this->employees[$employeeContractId]) && $employeeId == "" && $empId == "") { return $employeeContractId; }
		if (!isset($this->employeeIds[$employeeId]) && $employeeContractId == "" && $empId == "") { return $employeeId; }
		if (!isset($this->empIds[$empId]) && $employeeContractId == "" && $employeeId == "") { return $empId; }

		// Employee id vagy contract id vagy emp id
		if ($employeeId != "" && $employeeContractId == "" && $empId == "") {
			$id		= $this->employeeIds[$employeeId];
			$ret	= $employeeId;
		} else if ($empId != "" && $employeeContractId == "" && $employeeId == "") {
			$id		= $this->empIds[$empId];
			$ret	= $empId;
		} else {
			$id		= $this->employees[$employeeContractId];
			$ret	= $employeeContractId;
		}

		// van-e bemeneti dátum
		$timeCheck	= false;
		$validCheck	= false;
		if ($time != "") {
			$date		= date('Y-m-d', strtotime($time));
			$timeCheck	= true;
		} else if ($vFrom != "" && $vTo != "") {
			$vFrom		= date('Y-m-d', strtotime($vFrom));
			$vTo		= date('Y-m-d', strtotime($vTo));
			$validCheck	= true;
		} else { return $ret; }

		// név lekérés
		foreach ($id as $validity => $name)
		{
			$exp = explode(":", $validity);
			if ($timeCheck) {
				$validityFrom	= date('Y-m-d', strtotime($exp[0]));
				$validityTo		= date('Y-m-d', strtotime($exp[1]));
				if (($date >= $validityFrom) && ($date <= $validityTo)) {
					return $name;
				}
			} else if ($validCheck) {
				$validityFrom	= date('Y-m-d', strtotime($exp[0]));
				$validityTo		= date('Y-m-d', strtotime($exp[1]));
				if ($vFrom <= $validityTo && $validityFrom <= $vTo) {
					return $name;
				}
			}
		}

		// nincs ilyen érvényessége
		return $ret;
	}

	/**
	 * Költséghely nevének lekérdezése id és időpont alapján
	 * @param string $costId
	 * @param string $time
	 * @return string
	 */
	private function getCostName($costId, $time)
	{
		if ($time != "") {
			$date = date('Y-m-d', strtotime($time));
		} else { return ""; }

		// név lekérés
		foreach ($this->costs as $keys => $validity)
		{
			$exp	= explode(":", $keys);
			$exp2	= is_string($validity) ? explode(":", $validity) : [];
			if ($exp[0] == $costId || $exp[1] == $costId) {
				$validityFrom	= date('Y-m-d', strtotime($exp2[0]));
				$validityTo		= date('Y-m-d', strtotime($exp2[1]));
				if (($date >= $validityFrom) && ($date <= $validityTo)) {
					return $exp[1];
				}
			}
		}

		return "";
	}

	/**
	 * Admin log rekordjait adja vissza
	 * @param string $model
	 * @param string $modelPK
	 * @param array $fields
	 * @param string $mainLogTime
	 * @param boolean $multiDimension
	 * @param array $absenceIds
	 * @param boolean $flexEmployeeMgmt
	 * @return array
	 */
	private function getFromAdminLog($model, $modelPK, $fields, $mainLogTime, $multiDimension = false, $absenceIds = [], $flexEmployeeMgmt = false, $userId = null)
	{
		if (!$this->workWithFiles)
		{
			$ret = [];
			$sec = ($flexEmployeeMgmt) ? "15" : $this->mainLogAdminLogInterval;
			foreach ($this->adminLog as $key => $value)
			{
				$exp = explode("|", $key, 4);
				if (($exp[0] == $model || $model == "ALL") && ($exp[1] == $modelPK || $modelPK == "ALL") && in_array($exp[2], $fields))
				{
					if (!empty($userId) && !empty($value["created_by"]) && $userId !== $value["created_by"]) {
						continue;
					}
					if (!empty($absenceIds))
					{
						if (!in_array($value["employee_absence_id"], $absenceIds) && !in_array($value["del_employee_absence_id"], $absenceIds))
						{
							continue;
						}
					}
					$aLogSec		= date("Y-m-d H:i:s", strtotime($exp[3]));
					$mainLogFrom	= date("Y-m-d H:i:s", strtotime($mainLogTime . "-" . $sec . " seconds"));
					$mainLogTo		= date("Y-m-d H:i:s", strtotime($mainLogTime . "+" . $sec . " seconds"));
					if ($aLogSec >= $mainLogFrom && $aLogSec <= $mainLogTo)
					{
						if (!$multiDimension) {
							$ret[$exp[2]] = $value;
						} else {
							$value["field"] = $exp[2];
							$value["max_row_id"] = $exp[1];
							$ret[] = $value;
						}
					}
				}
			}
			return $ret;
		} else {
			return $this->getFromAdminLogFromFile($model, $modelPK, $fields, $mainLogTime, $multiDimension = false, $absenceIds = [], $flexEmployeeMgmt = false);
		}
	}

	/**
	 * Visszaadja a kereső beállításokat
	 * @param array $filters
	 * @param string $time
	 * @param string $vFrom
	 * @param string $vTo
	 * @return string
	 */
	private function getFilterValues($filters, $time = "", $vFrom = "", $vTo = "")
	{
		$ret = "";

		$searchFields = ["company", "payroll", "workgroup", "unit", "employee_position", "company_org_group1", "company_org_group2", "company_org_group3", "employee_contract"];
		foreach ($searchFields as $sf)
		{
			$searches = [];
			if ($sf != "employee_contract")
			{
				$all = false;
				foreach ($filters as $key => $f)
				{
					if (strpos($key, $sf) !== false && strpos($key, "searchInput") !== false) {
						$searches[] = $f;
						if ($f == "ALL") {
							$all = true;
						}
					}
				}

				if (!empty($searches))
				{
					$ret .= $this->dicts[$sf] . ": ";
					if ($all) { $ret .= $this->dicts["all"]; }
					if ($time == "") {
						$ret .= $this->getNamesFromMainGroup($sf, $searches, "", $vFrom, $vTo);
					} else {
						$ret .= $this->getNamesFromMainGroup($sf, $searches, $time);
					}
				}
			} else {
				if (!empty($filters["POST.searchInput.employee_contract"])) {
					if ($time == "") {
						$ret .= $this->getNameWithId($filters["POST.searchInput.employee_contract"], "", $vFrom, $vTo);
					} else {
						$ret .= $this->getNameWithId($filters["POST.searchInput.employee_contract"], $time);
					}
				}
			}
		}

		return $ret;
	}

	/**
	 * Szűrt csoportosítások nevének visszaadása
	 * @param string $searchField
	 * @param array $searchValues
	 * @param string $time
	 * @param string $vFrom
	 * @param string $vTo
	 * @return string
	 */
	private function getNamesFromMainGroup($searchField, $searchValues, $time = "", $vFrom = "", $vTo = "")
	{
		// van-e bemeneti dátum
		$timeCheck	= false;
		$validCheck	= false;
		if ($time != "") {
			$date		= date('Y-m-d', strtotime($time));
			$timeCheck	= true;
		} else if ($vFrom != "" && $vTo != "") {
			$vFrom		= date('Y-m-d', strtotime($vFrom));
			$vTo		= date('Y-m-d', strtotime($vTo));
			$validCheck	= true;
		} else { return " <br /> "; }

		$names = [];
		// név lekérés
		foreach ($searchValues as $sv)
		{
			if (!isset($this->mainGroups[$searchField][$sv])) { continue; }
			foreach ($this->mainGroups[$searchField][$sv] as $validity => $name)
			{
				$exp = explode(":", $validity);
				if ($timeCheck) {
					$validityFrom	= date('Y-m-d', strtotime($exp[0]));
					$validityTo		= date('Y-m-d', strtotime($exp[1]));
					if (($date >= $validityFrom) && ($date <= $validityTo)) {
						$names[] = $name;
					}
				} else if ($validCheck) {
					$validityFrom	= date('Y-m-d', strtotime($exp[0]));
					$validityTo		= date('Y-m-d', strtotime($exp[1]));
					if ($vFrom <= $validityTo && $validityFrom <= $vTo) {
						$names[] = $name;
					}
				}
			}
		}

		// nincs ilyen érvényessége
		if (empty($names)) { return " <br /> "; }

		$ret = " ";
		$counter = 0;
		$count = count($names);
		foreach ($names as $n) {
			$counter++;
			$ret .= $n;
			if ($counter < $count) {
				$ret .= ", ";
			}
		}
		$ret .= " <br /> ";

		return $ret;
	}

	/**
	 * Visszaadja a naptípus nevét azonosító és dátum alapján
	 * @param string $daytypeId
	 * @param string $date
	 * @return string
	 */
	private function getDaytypeName($daytypeId, $time)
	{
		// nincs ilyen naptípus
		if (!isset($this->daytypes[$daytypeId])) { return ""; }

		if ($time != "") {
			$date = date('Y-m-d', strtotime($time));
		} else { return ""; }

		// név lekérés
		foreach ($this->daytypes[$daytypeId] as $validity => $name)
		{
			$exp			= explode(":", $validity);
			$validityFrom	= date('Y-m-d', strtotime($exp[0]));
			$validityTo		= date('Y-m-d', strtotime($exp[1]));
			if (($date >= $validityFrom) && ($date <= $validityTo)) {
				return $name;
			}
		}

		// nincs ilyen érvényessége
		return "";
	}

	/**
	 * Visszaadja az extra hourt (bementett és korábbi): készenlét / ügyelet / túlóra
	 * @param string $employeeContractId
	 * @param string $date
	 * @param string $insideType
	 * @param string $eventTime
	 * @return array
	 */
	private function getExtraHour($employeeContractId, $date, $insideType, $eventTime)
	{
		foreach ($this->extraHours[$employeeContractId][$date][$insideType] as $value)
		{
			$checkModified = false;
			if (!is_null($value["modified_on"])) {
				$checkModified	= true;
				$modifiedSec	= date("Y-m-d H:i:s", strtotime($value["modified_on"]));
			}
			$createdSec			= date("Y-m-d H:i:s", strtotime($value["created_on"]));
			$mainLogFrom		= date("Y-m-d H:i:s", strtotime($eventTime . "-" . $this->extraHoursMainLogInterval . " seconds"));
			$mainLogTo			= date("Y-m-d H:i:s", strtotime($eventTime . "+" . $this->extraHoursMainLogInterval . " seconds"));
			if ($createdSec >= $mainLogFrom && $createdSec <= $mainLogTo) {
				return ["new_start" => $value["new_start"], "new_end" => $value["new_end"], "prev_start" => $value["prev_start"], "prev_end" => $value["prev_end"]];
			} else if ($checkModified) {
				if ($modifiedSec >= $mainLogFrom && $modifiedSec <= $mainLogTo) {
					return ["new_start" => $value["new_start"], "new_end" => $value["new_end"], "prev_start" => $value["prev_start"], "prev_end" => $value["prev_end"]];
				}
			}
		}

		return ["new_start" => "", "new_end" => "", "prev_start" => "", "prev_end" => ""];
	}

	/**
	 * Visszaadja a törölt távollét nevét
	 * @param string $employeeContractId
	 * @param string $date
	 * @param string $eventTime
	 * @return string
	 */
	private function getDeletedAbsence($employeeContractId, $date, $eventTime)
	{
		foreach ($this->delAbsences[$employeeContractId][$date] as $value)
		{
			$modifiedSec	= date("Y-m-d H:i:s", strtotime($value["modified_on"]));
			$mainLogFrom	= date("Y-m-d H:i:s", strtotime($eventTime . "-" . $this->empAbsenceMainLogInterval . " seconds"));
			$mainLogTo		= date("Y-m-d H:i:s", strtotime($eventTime . "+" . $this->empAbsenceMainLogInterval . " seconds"));
			if ($modifiedSec >= $mainLogFrom && $modifiedSec <= $mainLogTo) {
				return $value["state_type"];
			}
		}

		return "";
	}

	/**
	 * Egyenlegkezelés tevékenységeit adja vissza
	 * @param array $mainLog
	 * @return array
	 */
	private function getFrameManagementEvents($mainLog)
	{
		$logParams = json_decode($mainLog["params"], true);
		$site = (strpos($mainLog["controller_id"], "balanceManagement") !== false) ? $this->dicts["balanceManagement"] : $this->dicts["frameManagement"];

		// érték mentés
		if ($mainLog["action_id"] == "saveCellValue")
		{
			$mainLog["event"]			= $site . " - " . $this->dicts["valueSaved"];
			$mainLog["changed_column"]	= $this->formatDate(mb_substr($logParams["POST.key"], 0, 10), false, false, true);
			$exp = explode("_", $logParams["POST.key"]);
			$mainLog["changed_column"]	.= $this->getNameWithId($exp[1], $exp[0]) . ". <br /> ";
			$mainLog["changed_column"]	.= ($logParams["POST.column"] == "next_balancetr") ? $this->dicts["balance_transmission"] : Dict::getValue($logParams["POST.column"]);
			$mainLog["changed_column"]	.= " - " . $logParams["POST.value"];
		// Mentés v. zárolás v. törlés
		} else if ($mainLog["action_id"] == "saveLockDelete")
		{
			if ($logParams["POST.all"])
			{
				switch ($logParams["POST.operation"])
				{
					case "SAVE":
						$mainLog["event"] = $site . " - " . $this->dicts["groupSave"];
						break;
					case "LOCK":
						$mainLog["event"] = $site . " - " . $this->dicts["groupLock"];
						break;
					case "DEL":
						$mainLog["event"] = $site . " - " . $this->dicts["groupDelData"];
						break;
				}

				$SQL		= "SELECT params FROM `main_log` WHERE `event_time` < '{$mainLog["changed_datetime"]}' AND `controller_id` = '{$mainLog["controller_id"]}' AND `action_id` = 'gridData' AND `username` = '{$mainLog["modifier_username"]}' ORDER BY `event_time` DESC LIMIT 1";
				$res		= dbFetchRow($SQL);
				$filters	= json_decode($res["params"], true);
				$mainLog["changed_column"] = $this->formatDate($filters["POST.searchInput.valid_date"], false, false, true);
				$mainLog["changed_column"] .= $this->getFilterValues($filters, $filters["POST.searchInput.valid_date"]);
			} else {
				switch ($logParams["POST.operation"])
				{
					case "SAVE":
						$mainLog["event"] = $site . " - " . $this->dicts["save"];
						break;
					case "LOCK":
						$mainLog["event"] = $site . " - " . $this->dicts["lock"];
						break;
					case "DEL":
						$mainLog["event"] = $site . " - " . $this->dicts["del"];
						break;
				}

				$mainLog["changed_column"] = $this->formatDate(mb_substr($logParams["POST.selected_ids"], 0, 10), false, false, true);
				$exp = explode(";", $logParams["POST.selected_ids"]);
				foreach ($exp as $rows) {
					$exp2						= explode("_", $rows);
					$employeeContractId			= isset($exp2[1]) ? $exp2[1] : null;
					$mainLog["changed_column"]	.= $this->getNameWithId($employeeContractId, $exp2[0]) . ". <br /> ";
				}
			}
		} else {
			return $this->getBaseEvents($mainLog);
		}

		return $mainLog;
	}

	/**
	 * Munkarend és túlóra kezelés eseményeit adja vissza
	 * @param array $mainLog
	 * @return array
	 */
	private function getWorkScheduleAndOvertimeMgmtEvents($mainLog)
	{
		$logParams = json_decode($mainLog["params"], true);

		// egységenkénti munkarend mentés
		if ($mainLog["controller_id"] == "wfm/workScheduleByUnit" && $mainLog["action_id"] == "index" && isset($logParams["POST.searchInput.unit_name"]))
		{
			$mainLog["event"] = $this->dicts["wsbu"] . " - " . $this->dicts["save"];

			$phAsRestday	= ($logParams["POST.searchInput.phAsRestday"]) ? $this->dicts["yes"] : $this->dicts["no"];
			$phRestDaytype	= $this->getDaytypeName($logParams["POST.searchInput.phRestdayType"], $logParams["POST.searchInput.valid_from"]);
			$mainLog["changed_column"]	= $this->dicts["workgroup"] . ": " . $logParams["POST.searchInput.workgroup_name"] . " - " . $this->dicts["unit"] . ": " . $logParams["POST.searchInput.unit_name"];
			$mainLog["changed_column"]	.= " <br /> " . $this->formatDate($logParams["POST.searchInput.valid_from"], false, true) . $this->formatDate($logParams["POST.searchInput.valid_to"], true);
			$mainLog["changed_column"]	.= " <br /> " . $this->dicts["number_of_repeated_days"] . ": " . $logParams["POST.searchInput.repeat"];
			$mainLog["changed_column"]	.= " <br /> " . $this->dicts["publicHolidayAsRestday"] . ": " . $phAsRestday;
			$mainLog["changed_column"]	.= " <br /> " . $this->dicts["wsuByUnitPhRestdayType"] . ": " . $phRestDaytype;
			$mainLog["changed_column"]	.= " <br /> " . $this->dicts["phCountry"] . ": " . Dict::getValue("lang_" . $logParams["POST.searchInput.phCountry"]);

			$mainLog["changed_to"]		= "";
			for ($i = 0; $i < (int)$logParams["POST.searchInput.repeat"]; $i++) {
				$dayToSave = date("Y-m-d", strtotime($logParams["POST.searchInput.valid_from"] . ' + ' . $i . 'day'));
				if (isset($logParams["POST.day." . $dayToSave])) {
					$mainLog["changed_to"] .= $this->formatDate($dayToSave, false, true) . $this->getDaytypeName($logParams["POST.day." . $dayToSave], $dayToSave) . " <br /> ";
				}
			}
		// munkavállalónkénti munkarend mentés
		} else if ($mainLog["controller_id"] == "wfm/editWorkScheduleByEmployee" && $mainLog["action_id"] == "index" && !isset($logParams["POST.yt0"]) && !empty($logParams))
		{
			$mainLog["event"]			= $this->dicts["wsbe"] . " - " . $this->dicts["save"];
			$mainLog["changed_column"]	= $this->getNameWithId($logParams["POST.searchInput.employee_contract"], $logParams["POST.searchInput.valid_month"] . "-01") . ". - " . $logParams["POST.searchInput.valid_month"];

			$mainLog["changed_to"]		= "";
			$start	= $logParams["POST.searchInput.valid_month"] . "-01";
			$exp	= explode("-", $logParams["POST.searchInput.valid_month"]);
			$days	= cal_days_in_month(CAL_GREGORIAN, ltrim($exp[1], '0'), $exp[0]);
			for ($i = 0; $i < $days; $i++) {
				$dayToSave = date("Y-m-d", strtotime($start . ' + ' . $i . 'day'));
				if (isset($logParams["POST.day." . $dayToSave])) {
					$mainLog["changed_to"] .= $this->formatDate($dayToSave, false, true) . $this->getDaytypeName($logParams["POST.day." . $dayToSave], $dayToSave) . " <br /> ";
				}
			}
		// egyéni munkarend szerkesztés
		} else if ($mainLog["controller_id"] == "wfm/workScheduleByGroup")
		{
			switch ($mainLog["action_id"])
			{
				// Naptípus v. kezdet v. vég módosítás
				case "saveWorkSchedule":
					if ($logParams["POST.inputType"] == "daytype_id") {
						$mainLog["event"]			= $this->dicts["wsu"] . " - " . $this->dicts["daytypeChanged"];
						$mainLog["changed_column"]	= $this->getNameWithId($logParams["POST.ecID"], $logParams["POST.currentDay"]) . " - " . $this->formatDate($logParams["POST.currentDay"], true);
						$mainLog["changed_from"]	= $this->getDaytypeName($logParams["POST.usedDaytype"], $logParams["POST.currentDay"]);
						$mainLog["changed_to"]		= $this->getDaytypeName($logParams["POST.inputValue"], $logParams["POST.currentDay"]);
					} else {
						$mainLog["event"]			= $this->dicts["wsu"] . " - " . $this->wsuFields[$logParams["POST.inputType"]];
						$mainLog["changed_column"]	= $this->getNameWithId($logParams["POST.ecID"], $logParams["POST.currentDay"]) . " - " . $this->formatDate($logParams["POST.currentDay"], true);
						$mainLog["changed_to"]		= $logParams["POST.inputValue"];
					}
					break;
                // Naptípus tömeges módosítása
				case "saveWorkScheduleToDB":
					if (empty($logParams)) {
						return [];
					}
					$mainLog["event"]			= $this->dicts["wsu"] . " - " . $this->dicts["daytype"] . ' ' . $this->dicts["save"];
					$mainLog["changed_to"]		= "";
					$lastName = "";
					foreach ($logParams as $key => $value) {
						$parts = explode('&', $value);
		
						$logParamsDetailed = [
							"POST.ecID"        => $parts[0],
							"POST.currentDay"  => $parts[1],
							"POST.inputType"   => $parts[2],
							"POST.inputValue"  => $parts[3],
							"POST.usedDaytype" => $parts[4],
						];
						
						if (empty($logParamsDetailed["POST.inputValue"])) {
							continue;
						}
						
						$newName = $this->getNameWithId($logParamsDetailed["POST.ecID"], $logParamsDetailed["POST.currentDay"]);
						if ($lastName !== $newName) {
							$mainLog["changed_to"] .= !empty($lastName) ? "<br>" : '';
							$mainLog["changed_to"] .= $newName . "<br>";
						}
						$lastName = $newName;
						$mainLog["changed_to"] .= $this->formatDate($logParamsDetailed["POST.currentDay"], true) . "<br>";
						$mainLog["changed_to"] .=
						$this->getDaytypeName(
							$logParamsDetailed["POST.inputValue"],
							$logParamsDetailed["POST.currentDay"]
						) . "<br>";
					}					
					break;
				// Munkarend másolás
				case "copyWS":
					$counter		= 0;
					$ecIdCounter	= 0;
					$dayCounter		= 0;
					$mainLog["event"]			= $this->dicts["wsu"] . " - " . $this->dicts["dayCopied"];
					$mainLog["changed_column"]	= $this->dicts["rua_dayCopied"];
					$mainLog["changed_to"]		= "";
					foreach ($logParams as $key => $val)
					{
						if (strpos($key, "allDatesInSelectedInterval") !== false) {
							$counter++;
							if ($counter >= 2) { $mainLog["event"] = $this->dicts["wsu"] . " - " . $this->dicts["wsFullCopied"]; }
							$mainLog["changed_column"] .= " <br /> " . $this->formatDate($val, true);
						} else if (strpos($key, "selectedEcIds") !== false) {
							if ($ecIdCounter === 0) { $mainLog["changed_column"] .=  " <br /> " . $this->dicts["selected_employees"]; }
							$ecIdCounter++;
							$mainLog["changed_column"] .= " <br /> " . $this->getNameWithId($val, $logParams["POST.allDatesInSelectedInterval.0"]) . ".";
						} else if (strpos($key, "selectedDaytypeData") !== false && strpos($key, ".date") !== false) {
							if ($dayCounter === 0) {
								$mainLog["changed_to"] .= $this->formatDate($val, true);
							} else {
								$mainLog["changed_to"] .= " <br /> " . $this->formatDate($val, true);
							}
							$dayCounter++;
						} else if (strpos($key, "selectedDaytypeData") !== false && strpos($key, "daytypeName") !== false) {
							$mainLog["changed_to"] .= " <br /> " . $this->dicts["daytype"] . ": " . $val;
						} else if (strpos($key, "paid") !== false) {
							if ($val == "1") { $mainLog["changed_to"] .= " <br /> " . $this->dicts["saveWorkSchedDiffAsPaid"] . ": " . $this->dicts["yes"]; }
						} else {
							if (strpos($key, "selectedDaytypeData") !== false && strpos($key, "Start") !== false)
							{
								$exp		= explode(".", $key);
								$count		= count($exp) - 1;
								$dictKey	= mb_substr($exp[$count], 0, -5);
								if ($dictKey == "work") { $dictKey = "worktime"; }
								$endKey		= mb_substr($key, 0, -5) . "End";
								if ($val != $logParams[$endKey] || ($this->standbyUpgradedVersion && strpos($key, "standby") !== false && strpos($val, ":") !== false)) {
									$mainLog["changed_to"] .= " <br /> " . Dict::getValue($dictKey) . ": " . $val . " - " . $logParams[$endKey];
								}
							}
						}
					}
					break;
				// Munkarend mentés
				case "saveWS":
					$mainLog["event"]			= $this->dicts["wsu"] . " - " . $this->dicts["wsSaved"];
					$exp						= explode(".", $logParams["POST.dialogInput_weekPickerDialog.week"]);
					$mainLog["changed_column"]	= $this->formatDate($exp[0], false, true) . $this->formatDate($exp[1], true);
					break;
				default:
					return [];
					break;
			}
		// Túlóra elrendelés
		} else if ($mainLog["controller_id"] == "wfm/overtimeManagement")
		{
			switch ($mainLog["action_id"])
			{
				// Elrendelés
				case "addOvertime":
					$SQL		= "SELECT params FROM `main_log` WHERE `event_time` < '{$mainLog["changed_datetime"]}' AND `controller_id` = '{$mainLog["controller_id"]}' AND `action_id` = 'addDateToTitle' AND `username` = '{$mainLog["modifier_username"]}' ORDER BY `event_time` DESC LIMIT 1";
					$res		= dbFetchRow($SQL);
					$filters	= json_decode($res["params"], true);
					$mainLog["event"] = $this->dicts["otMgmt"] . " - " . $this->dicts["otOrdered"];
					if ($logParams["POST.dialogInput_dhtmlxGrid.overtime_type"] == "RESTDAY")
					{
						$mainLog["changed_column"]		= $this->dicts["overtime"] . " " . $this->dicts["type"] . ": " . $this->dicts["daytype_type_of_day_restday"];
						if (!$this->overTimeRestdayUseInterval)
						{
							$mainLog["changed_column"]	.= " - " . $this->dicts["daytype"] . ": " . $this->getDaytypeName($logParams["POST.dialogInput_dhtmlxGrid.overtime_time_dialog"], $filters["POST.searchInput.valid_date"]);
							$mainLog["changed_column"]	.= " <br /> " . $this->formatDate($filters["POST.searchInput.valid_date"], false, true) . $this->dicts["selected_employees"];
							$exp = explode(";", $logParams["POST.ids"]);
							foreach ($exp as $ecId) { $mainLog["changed_column"] .= " <br /> " . $this->getNameWithId($ecId, $filters["POST.searchInput.valid_date"]) . "."; }
						} else {
							$mainLog["changed_column"]	.= " - " . $this->dicts["daytype"] . ": " . $this->getDaytypeName($logParams["POST.dialogInput_dhtmlxGrid.overtime_daytype"], $filters["POST.searchInput.valid_date"]);
							$mainLog["changed_column"]	.= " <br /> " . $this->formatDate($logParams["POST.dialogInput_dhtmlxGrid.restday_overtime_start"], false, true) . $this->formatDate($logParams["POST.dialogInput_dhtmlxGrid.restday_overtime_end"], false, true) . $this->dicts["selected_employees"];
							$exp = explode(";", $logParams["POST.ids"]);
							foreach ($exp as $ecId) { $mainLog["changed_column"] .= " <br /> " . $this->getNameWithId($ecId, $filters["POST.searchInput.valid_date"]) . "."; }
						}
					} else {
						if ($logParams["POST.dialogInput_dhtmlxGrid.overtime_type"] == "BEFOREWORK") {
							$mainLog["changed_column"] = $this->dicts["overtime"] . " " . $this->dicts["type"] . ": " . $this->dicts["before_worktime"];
						} else {
							$mainLog["changed_column"] = $this->dicts["overtime"] . " " . $this->dicts["type"] . ": " . $this->dicts["after_worktime"];
						}

						if (!$this->overTimeUpgradedVersion)
						{
							$mainLog["changed_column"]	.= " <br /> " . $this->dicts["time"] . ": " . secToHis($logParams["POST.dialogInput_dhtmlxGrid.overtime_time_dialog"]);
							$mainLog["changed_column"]	.= " <br /> " . $this->formatDate($filters["POST.searchInput.valid_date"], false, true) . $this->dicts["selected_employees"];
							$exp = explode(";", $logParams["POST.ids"]);
							foreach ($exp as $ecId) { $mainLog["changed_column"] .= " <br /> " . $this->getNameWithId($ecId, $filters["POST.searchInput.valid_date"]) . "."; }
						} else {
							$mainLog["changed_column"]	.= " <br /> " . $this->dicts["time"] . ": " . secToHis($logParams["POST.dialogInput_dhtmlxGrid.overtime_time_dialog"]);
							$mainLog["changed_column"]	.= " <br /> " . $this->dicts["break_between_wt_ot"] . ": " . secToHis($logParams["POST.dialogInput_dhtmlxGrid.break_between_wt_ot"]);
							$mainLog["changed_column"]	.= " <br /> " . $this->formatDate($filters["POST.searchInput.valid_date"], false, true) . $this->dicts["selected_employees"];
							$exp = explode(";", $logParams["POST.ids"]);
							foreach ($exp as $ecId) { $mainLog["changed_column"] .= " <br /> " . $this->getNameWithId($ecId, $filters["POST.searchInput.valid_date"]) . "."; }
						}
					}
					break;
				// Törlés v. jóváhagyás v. elutasítás
				case "delOvertime":
				case "acceptOvertime":
				case "rejectOvertime":
					$SQL		= "SELECT params FROM `main_log` WHERE `event_time` < '{$mainLog["changed_datetime"]}' AND `controller_id` = '{$mainLog["controller_id"]}' AND `action_id` = 'addDateToTitle' AND `username` = '{$mainLog["modifier_username"]}' ORDER BY `event_time` DESC LIMIT 1";
					$res		= dbFetchRow($SQL);
					$filters	= json_decode($res["params"], true);
					if ($mainLog["action_id"] == "delOvertime") {
						$title = $this->dicts["otDeleted"];
					} else if ($mainLog["action_id"] == "acceptOvertime") {
						$title = $this->dicts["otAccepted"];
					} else if ($mainLog["action_id"] == "rejectOvertime") {
						$title = $this->dicts["otRejected"];
					}
					$mainLog["event"]			= $this->dicts["otMgmt"] . " - " . $title;
					$mainLog["changed_column"]	= $this->formatDate($filters["POST.searchInput.valid_date"], false, true) . $this->dicts["selected_employees"];
					$exp						= explode(";", $logParams["POST.ids"]);
					foreach ($exp as $ecId) { $mainLog["changed_column"] .= " <br /> " . $this->getNameWithId($ecId, $filters["POST.searchInput.valid_date"]) . "."; }
					break;
				default:
					return [];
					break;
			}
		} else {
			return $this->getBaseEvents($mainLog);
		}

		return $mainLog;
	}

	/**
	 * Bérszámfejtés átadás események
	 * @param array $mainLog
	 * @return array
	 */
	private function getPayrollTransferEvents($mainLog)
	{
		$logParams = json_decode($mainLog["params"], true);

		// Bérátadás
		if ($mainLog["action_id"] == "payroll")
		{
			if ($logParams["POST.mode"] == "DEFAULT") { $mode = $this->ptrDefaultMode; } else { $mode = $logParams["POST.mode"]; }
			$mainLog["event"] = $this->dicts["ptr"] . " - " . Dict::getValue("payroll_transfer_type_" . strtolower($mode));
			if (isset($logParams["POST.searchInput.valid_date"])) {
				$vFrom	= mb_substr($logParams["POST.searchInput.valid_date"], 0, -2) . "01";
				$exp	= explode("-", $logParams["POST.searchInput.valid_date"]);
				$days	= cal_days_in_month(CAL_GREGORIAN, ltrim($exp[1], '0'), $exp[0]);
				$vTo	= mb_substr($logParams["POST.searchInput.valid_date"], 0, -2) . $days;
			} else if (isset($logParams["POST.searchInput.valid_month"])) {
				$vFrom	= $logParams["POST.searchInput.valid_month"] . "-01";
				$exp	= explode("-", $logParams["POST.searchInput.valid_month"]);
				$days	= cal_days_in_month(CAL_GREGORIAN, ltrim($exp[1], '0'), $exp[0]);
				$vTo	= $logParams["POST.searchInput.valid_month"] . "-" . $days;
			} else {
				$vFrom	= $logParams["POST.searchInput.valid_from"];
				$vTo	= $logParams["POST.searchInput.valid_to"];
			}

			$locked						= ($logParams["POST.locked"] == "1") ? $this->dicts["yes"] : $this->dicts["no"];
			$mainLog["changed_column"]	= $this->dicts["ptr_target"] . ": " . Dict::getValue("payroll_transfer_type_" . strtolower($mode)) . ". - " . $this->dicts["ptrLocked"] . ": " . $locked . ".";
			$mainLog["changed_column"]	.= " <br /> " . $this->formatDate($vFrom, false, true) . $this->formatDate($vTo, false, false, true);
			$mainLog["changed_column"]	.= $this->getFilterValues($logParams, "", $vFrom, $vTo);
		// Jóváhagyás v. elutasítás
		} else if ($mainLog["action_id"] == "payrollApproval")
		{
			if ($logParams["POST.approve"] == "true") { $mainLog["event"] = $this->dicts["ptr"] . " - " . $this->dicts["ptrApprove"]; } else { $mainLog["event"] = $this->dicts["ptr"] . " - " . $this->dicts["ptrDeny"]; }
			$mainLog["changed_column"]	= $this->dicts["message"] . ": " . $logParams["POST.msg"];
			$mainLog["changed_column"]	.= " - " . $this->dicts["created_by"] . ": " . $this->ptrIds[$logParams["POST.rowId"]]["creator"] . " - " . $this->dicts["created_on"] . ": " . $this->ptrIds[$logParams["POST.rowId"]]["created"];
		// Betöltésre került bérprogramba
		} else if ($mainLog["action_id"] == "payrollUpload")
		{
			$mainLog["event"]			= $this->dicts["ptr"] . " - " . $this->dicts["ptrUpload"];
			$mainLog["changed_column"]	= $this->dicts["created_by"] . ": " . $this->ptrIds[$logParams["POST.rowId"]]["creator"] . " - " . $this->dicts["created_on"] . ": " . $this->ptrIds[$logParams["POST.rowId"]]["created"];
		// Bérátadás megszakítás v. küldés jóváhagyásra.
		} else if ($mainLog["action_id"] == "payrollCancel" || $mainLog["action_id"] == "sendToApprove")
		{
			$title				= ($mainLog["action_id"] == "payrollCancel") ? $this->dicts["ptrCancel"] : $this->dicts["ptrSendToApprove"];
			$mainLog["event"]	= $this->dicts["ptr"] . " - " . $title;
			if (isset($logParams["POST.rowId"]) && $logParams["POST.rowId"] != "") {
				$mainLog["changed_column"] = $this->dicts["created_by"] . ": " . $this->ptrIds[$logParams["POST.rowId"]]["creator"] . " - " . $this->dicts["created_on"] . ": " . $this->ptrIds[$logParams["POST.rowId"]]["created"];
			} else {
				$mainLog["changed_column"] = $this->dicts["created_by"] . ": " . $this->ptrIds[$logParams["POST.ptrID"]]["creator"] . " - " . $this->dicts["created_on"] . ": " . $this->ptrIds[$logParams["POST.ptrID"]]["created"];
			}
		} else {
			return $this->getBaseEvents($mainLog);
		}

		return $mainLog;
	}

	/**
	 * Éves távollét és távollét jóváhagyás események
	 * @param array $mainLog
	 * @return array
	 */
	private function getAhpEvents($mainLog)
	{
		$logParams = json_decode($mainLog["params"], true);

		// Éves táv. kiírás v. törlés
		if ($mainLog["action_id"] == "saveAbsences" || $mainLog["action_id"] == "deleteAbsences")
		{
			$mainLog["event"]			= ($mainLog["action_id"] == "saveAbsences") ? $this->dicts["yearView"] . " - " . $this->dicts["absenceAdded"] : $this->dicts["yearView"] . " - " . $this->dicts["absenceDeleted"];
			$mainLog["changed_column"]	= $this->dicts["date"] . ": " . $this->formatDate($logParams["POST.absenceBegin"], false, true) . $this->formatDate($logParams["POST.absenceEnd"], true);
			$mainLog["changed_column"]	.= " - " . $this->dicts["absence_type_id"] . ": " . $this->stateTypes[$logParams["POST.absType"]];
			$res	= $this->getFromAdminLog("EmployeeAbsence", "ALL", ['status', 'full_day_absence', 'employee_contract_id', 'absence_hour'], $mainLog["changed_datetime"], true);
			$pks	= [];
			foreach ($res as $r)
			{
				if (!array_key_exists($r["field"], $pks))
				{
					if ($r["field"] == "status" && $r["new_value"] == $this->pub) {
						$mainLog["event"]			= $this->dicts["yearView"] . " - " . $this->dicts["absenceAdded"];
						$mainLog["changed_column"]	.= " <br /> " . $this->dicts["approved"] . ": " . $this->dicts["yes"];
						$pks[$r["field"]]			= true;
					} else if ($r["field"] == "status" && $r["new_value"] == $this->req) {
						$mainLog["event"]			= $this->dicts["yearView"] . " - " . $this->dicts["absenceAdded"];
						$mainLog["changed_column"]	.= " <br /> " . $this->dicts["approved"] . ": " . $this->dicts["no"];
						$pks[$r["field"]]			= true;
					} else if ($r["field"] == "status" && $r["new_value"] == $this->del) {
						$mainLog["event"]			= $this->dicts["yearView"] . " - " . $this->dicts["absenceDeleted"];
						$mainLog["changed_column"]	.= " <br /> " . $this->getNameWithId($r["employee_contract_id"], $logParams["POST.absenceEnd"]);
						$pks[$r["field"]]			= true;
					} else if ($r["field"] == "status" && $r["new_value"] == $this->delreq) {
						$mainLog["event"]			= $this->dicts["yearView"] . " - " . $this->dicts["absenceDeleteRequested"];
						$mainLog["changed_column"]	.= " <br /> " . $this->getNameWithId($r["employee_contract_id"], $logParams["POST.absenceEnd"]);
						$pks[$r["field"]]			= true;
					} else if ($r["field"] == "full_day_absence" && $r["new_value"] == "1") {
						$mainLog["changed_column"]	.= " <br /> " . $this->dicts["fullDayAbsence"] . ": " . $this->dicts["yes"];
						$pks[$r["field"]]			= true;
					} else if ($r["field"] == "absence_hour" && $r["new_value"] != "0") {
						$mainLog["changed_column"]	.= " <br /> " . $this->dicts["absence_hours"] . ": " . $r["new_value"];
						$pks[$r["field"]]			= true;
					} else if ($r["field"] == "employee_contract_id") {
						$mainLog["changed_column"]	.= " <br /> " . $this->getNameWithId($r["new_value"], $logParams["POST.absenceEnd"]);
						$pks[$r["field"]]			= true;
					}
				}
			}
		// Távollét jóváhagyás v. elutasítás
		} else if ($mainLog["action_id"] == "approveAbsence" || $mainLog["action_id"] == "rejectAbsence")
		{
			$ids		= explode(";", $logParams["POST.ids"]);
			$res		= $this->getFromAdminLog("EmployeeAbsence", "ALL", ['status'], $mainLog["changed_datetime"], true, $ids, false, $mainLog["user_id"]);
			$mainLog["changed_column"] = "";
			$counter	= count($res);
			$count		= 0;
			foreach ($res as $r)
			{
				// elfogadás 1->2 v. 4->5
				if ($r["old_value"] == $this->req && $r["new_value"] == $this->pub) {
					$mainLog["event"]			= $this->dicts["absenceApproval"] . " - " . $this->dicts["absence_accepted"];
					$mainLog["changed_column"]	.= $this->formatDate($r["day"], false, true) . $this->stateTypes[$r["state_type_id"]] . " - " . $this->getNameWithId($r["employee_contract_id"], $r["day"]);
				} else if ($r["old_value"] == $this->delreq && $r["new_value"] == $this->del) {
					$mainLog["event"]			= $this->dicts["absenceApproval"] . " - " . $this->dicts["absence_accepted"];
					$mainLog["changed_column"]	.= "(" . $this->dicts["absence_delete_required"] . ") " . $this->formatDate($r["day"], false, true) . $this->stateTypes[$r["state_type_id"]] . " - " . $this->getNameWithId($r["employee_contract_id"], $r["day"]);
				// elutasítás 1->3 v. 4->2
				} else if ($r["old_value"] == $this->req && $r["new_value"] == $this->rej) {
					$mainLog["event"]			= $this->dicts["absenceApproval"] . " - " . $this->dicts["absence_rejected"];
					$mainLog["changed_column"]	.= $this->formatDate($r["day"], false, true) . $this->stateTypes[$r["state_type_id"]] . " - " . $this->getNameWithId($r["employee_contract_id"], $r["day"]);
				} else if ($r["old_value"] == $this->delreq && $r["new_value"] == $this->pub) {
					$mainLog["event"]			= $this->dicts["absenceApproval"] . " - " . $this->dicts["absence_rejected"];
					$mainLog["changed_column"]	.= "(" . $this->dicts["absence_delete_required"] . ") " . $this->formatDate($r["day"], false, true) . $this->stateTypes[$r["state_type_id"]] . " - " . $this->getNameWithId($r["employee_contract_id"], $r["day"]);
				}
				$count++;
				if ($count < $counter) { $mainLog["changed_column"] .= " <br /> "; }
			}
		} else {
			return $this->getBaseEvents($mainLog);
		}

		return $mainLog;
	}

	/**
	 * Jóváhagyás felület eseményei
	 * @param array $mainLog
	 * @return array
	 */
	private function getApproveEvents($mainLog)
	{
		$logParams = json_decode($mainLog["params"], true);

		// Elfogadás
		if ($mainLog["action_id"] == "approve")
		{
			$mainLog["event"] = "";
			$ids = explode(";", $logParams["POST.selected_ids"]);
			foreach ($ids as $id)
			{
				$exp = explode("_", $id);
				switch ($exp[0])
				{
					case "absenceApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveAbsence"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveAbsence($exp[1], false);
						break;
					case "registrationApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveRegistration"] . " <br / > ";
						$regs						= new Registration();
						$criteria					= new CDbCriteria();
						$criteria->condition		= "`row_id` = {$exp[1]}";
						$reg						= $regs->find($criteria);
						if ($reg) {
							$mainLog["changed_column"] .= $this->dicts["cardNum"] . ": " . $reg->card . " - " . $this->dicts["time"] . ": " . $reg->time . " - " . $this->dicts["type"] . ": " . $reg->event_type_id . " <br /> ";
						}
						break;
					case "overtimeApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveOvertime"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveEeh($exp[1], "overtime");
						break;
					case "overtimeApproverOrderedWithEndorsement":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveOvertimeEndorse"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveWSOvertime($exp[1]);
						break;
					case "overtimeSeen":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveOvertimeSeen"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveWSOvertime($exp[1]);
						break;
					case "overtimeApproverOrdered":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveOvertime"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveWSOvertime($exp[1]);
						break;
					case "standbyApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveStandby"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveEeh($exp[1], "standby");
						break;
					case "dutyApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveDuty"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveEeh($exp[1], "duty");
						break;
					case "workScheduleApprove":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveWorkschedule"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveWS($exp[1]);
						break;
					case "daytypeChangeApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveDaytypeChange"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveDtChange($exp[1]);
						break;
					case "cogChange":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["approveCogChange"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveCogChange($exp[1]);
						break;
					default:
						return [];
						break;
				}
			}
			$mainLog["event"]			= mb_substr($mainLog["event"], 0, -8);
			$mainLog["changed_column"]	= mb_substr($mainLog["changed_column"], 0, -8);
		// Elutasítás
		} else if ($mainLog["action_id"] == "reject")
		{
			$mainLog["event"] = "";
			$ids = explode(";", $logParams["POST.selected_ids"]);
			foreach ($ids as $id)
			{
				$exp = explode("_", $id);
				switch ($exp[0])
				{
					case "absenceApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectAbsence"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveAbsence($exp[1], true);
						break;
					case "registrationApprover":
						$mainLog["event"]		.= $this->dicts["approveController"] . " - " . $this->dicts["rejectRegistration"] . " <br / > ";
						$regs					= new Registration();
						$criteria				= new CDbCriteria();
						$criteria->condition	= "`row_id` = {$exp[1]}";
						$reg					= $regs->find($criteria);
						if ($reg) {
							$mainLog["changed_column"] .= $this->dicts["cardNum"] . ": " . $reg->card . " - " . $this->dicts["time"] . ": " . $reg->time . " - " . $this->dicts["type"] . ": " . $reg->event_type_id . " <br /> ";
						}
						break;
					case "overtimeApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectOvertime"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveEeh($exp[1], "overtime");
						break;
					case "overtimeApproverOrderedWithEndorsement":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectOvertimeEndorse"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveWSOvertime($exp[1]);
						break;
					case "overtimeSeen":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectOvertimeSeen"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveWSOvertime($exp[1]);
						break;
					case "overtimeApproverOrdered":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectOvertime"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveWSOvertime($exp[1]);
						break;
					case "standbyApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectStandby"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveEeh($exp[1], "standby");
						break;
					case "dutyApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectDuty"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveEeh($exp[1], "duty");
						break;
					case "workScheduleApprove":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectWorkschedule"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveWS($exp[1]);
						break;
					case "daytypeChangeApprover":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectDaytypeChange"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveDtChange($exp[1]);
						break;
					case "cogChange":
						$mainLog["event"]			.= $this->dicts["approveController"] . " - " . $this->dicts["rejectCogChange"] . " <br / > ";
						$mainLog["changed_column"]	.= $this->getApproveCogChange($exp[1]);
						break;
					default:
						return [];
						break;
				}
			}
			$mainLog["event"]			= mb_substr($mainLog["event"], 0, -8);
			$mainLog["changed_column"]	= mb_substr($mainLog["changed_column"], 0, -8);
		} else {
			return $this->getBaseEvents($mainLog);
		}

		return $mainLog;
	}

	/**
	 * Visszaadja a jóváhagyás oldalon elfogadott v. elutasított távollétet
	 * @param string $employeeAbsenceId
	 * @param boolean $rejected
	 * @return string
	 */
	private function getApproveAbsence($employeeAbsenceId, $rejected)
	{
		if (!isset($this->allAbsences[$employeeAbsenceId])) { return " <br /> "; }
		$res = $this->allAbsences[$employeeAbsenceId];
		if ($res["status"] == $this->pub && $rejected) { $ret = $this->dicts["requestWithdrawn"] . " - "; } else { $ret = ""; }
		return $ret . $this->getNameWithId($res["employee_contract_id"], $res["start"]) . ". - " . $res["state_type_id"] . " - " . $this->formatDate($res["start"], false, true) . $this->formatDate($res["end"], false, false, true);
	}

	/**
	 * Visszaadja a jóváhagyás oldalon elfogadott v. elutasított túlórát / készenlétet / ügyeletet
	 * @param string $id
	 * @param string $insideType
	 * @return string
	 */
	private function getApproveEeh($id, $insideType)
	{
		if (!isset($this->eehOtStandbyDutyDrafts[$insideType][$id])) { return " <br /> "; }
		$res = $this->eehOtStandbyDutyDrafts[$insideType][$id];
		return $this->getNameWithId($res["employee_contract_id"], $res["day"]) . ". - " . $this->formatDate($res["day"], false, true) . $res["start"] . " - " . $res["end"] . " <br /> ";
	}

	/**
	 * Visszaadja a jóváhagyás oldalon elfogadott v. elutasított túlóra elrendelést
	 * @param string $id
	 * @return string
	 */
	private function getApproveWSOvertime($id)
	{
		if (!isset($this->workSchedOvertimes[$id])) { return " <br /> "; }
		$res = $this->workSchedOvertimes[$id];
		if ($res["type"] == "AFTERWORK") { $type = $this->dicts["afterWorktime"]; $daytype = " <br /> "; }
		if ($res["type"] == "BEFOREWORK") { $type = $this->dicts["beforeWorktime"]; $daytype = " <br /> "; }
		if ($res["type"] == "RESTDAY") { $type = $this->dicts["restday"]; $daytype = " - " . $this->getDaytypeName($res["daytype_id"], $res["day"]) . " <br /> "; }
		return $this->getNameWithId($res["employee_contract_id"], $res["day"]) . ". - " . $this->dicts["type"] . ": " . $type . " - " . $this->dicts["time"] . ": " . $this->formatDate($res["day"], false, true) . secToHis($res["time"]) . $daytype;
	}

	/**
	 * Visszaadja a jóváhagyás oldalon elfogadott v. elutasított munkarendet
	 * @param string $id
	 * @return string
	 */
	private function getApproveWS($id)
	{
		if (!isset($this->workSchedApproves[$id])) { return " <br /> "; }
		$res = $this->workSchedApproves[$id];
		return $this->formatDate($res["begin"], false, true) . $this->formatDate($res["end"], false, true) . $this->dicts["unit"] . ": " . $res["unit_name"] . " <br /> ";
	}

	/**
	 * Visszaadja a jóváhagyás oldalon elfogadott v. elutasított egyéni munkarendet
	 * @param string $id
	 * @return string
	 */
	private function getApproveDtChange($id)
	{
		if (!isset($this->workSchedUsedDrafts[$id])) { return " <br /> "; }
		$res = $this->workSchedUsedDrafts[$id];
		return $this->getNameWithId($res["employee_contract_id"], $res["day"]) . ". - " . $this->formatDate($res["day"], false, true) . $res["start"] . " - " . $res["end"] . ". - " . $this->dicts["daytype"] . ": " . $res["daytype"] . " <br /> ";
	}

	/**
	 * Visszaadja a jóváhagyás oldalon elfogadott v. elutasított csoport 2 váltást
	 * @param string $id
	 * @return string
	 */
	private function getApproveCogChange($id)
	{
		if (!isset($this->bulkGroupChangeByCogReqDrafts[$id])) { return " <br /> "; }
		$res = $this->bulkGroupChangeByCogReqDrafts[$id];
		return $this->getNameWithId($res["employee_contract_id"], $res["valid_from"]) . ". - " . $this->formatDate($res["valid_from"], false, true) . $this->dicts["company_org_group2"] . ": " . $res["cog_name"] . " <br /> ";
	}

	/**
	 * Alap munkavállaló kezelés gomb események (szabi keret számolás / lezárolás / törlés)
	 * Többi eseményre lehet a Munkavállalói adatváltozás kimutatást használni
	 * Flexes munkavállaló kezelés események
	 * @param array $mainLog
	 * @return array
	 */
	private function getEmployeeMgmtEvents($mainLog)
	{
		$logParams = json_decode($mainLog["params"], true);

		// Szabadság keret számolás
		if ($mainLog["action_id"] == "absenceCalc" && $mainLog["controller_id"] == "employee")
		{
			$mainLog["event"]			= $this->dicts["employeeMgmt"] . " - " . $this->dicts["absenceCalc"];
			$mainLog["changed_column"]	= "";
			// kire van lekérve a munkavállaló kezelés --> rájuk fut a csoportos esemény
			$SQL		= "SELECT params FROM `main_log` WHERE `event_time` < '{$mainLog["changed_datetime"]}' AND `controller_id` = 'employee' AND `action_id` = 'gridData' AND `username` = '{$mainLog["modifier_username"]}' ORDER BY `event_time` DESC LIMIT 1";
			$res		= dbFetchRow($SQL);
			$filters	= json_decode($res["params"], true);
			$mainLog["changed_column"] .= $this->getFilterValues($filters, $filters["POST.searchInput.valid_date"]);
		// Lezárolás
		} else if ($mainLog["action_id"] == "save" && $logParams["POST.generateFrom"] == "employeeLockDialog" && $mainLog["controller_id"] == "employee")
		{
			$mainLog["event"]			= $this->dicts["employeeMgmt"] . " - " . $this->dicts["employeeLocked"];
			$exp						= explode("_", $logParams["POST.editPK"]);
			$mainLog["changed_column"]	= $this->getNameWithId("", $exp[1], "", "", $exp[0]);
			$mainLog["changed_column"]	.= " - " . $this->formatDate($logParams["POST.dialogInput_employeeLockDialog.lock_date"], true);
		// Törlés
		} else if ($mainLog["action_id"] == "delete" && $mainLog["controller_id"] == "employee")
		{
			$mainLog["event"]			= $this->dicts["employeeMgmt"] . " - " . $this->dicts["employeeDeleted"];
			// Lehetne több és akkor foreachelni kéne, de dúúúrva lenne ha dolgozó kezelésbe aktív lenne a multiselect
			$exp						= explode("_", $logParams["POST.ids"]);
			$mainLog["changed_column"]	= $this->getNameWithId($exp[0], $exp[1]);
			$mainLog["changed_column"]	.= " - " . $this->formatDate($exp[1], true);
		// Flexes munkavállaló kezelés utolsó történetiség törlés
		} else if ($mainLog["controller_id"] == "employeecontrol" && $mainLog["action_id"] == "deleteLastHistory")
		{
			$mainLog["event"]			= $this->dicts["employeeMgmt"] . " - " . $this->dicts["employeeDeleteLastHistory"];
			$exp						= explode("_", $logParams["POST.editPK"]);
			$mainLog["changed_column"]	= $this->getNameWithId("", $logParams["POST.nextInterval.0"], "", "", "", $exp[0]) . ". <br /> " . Dict::getValue($logParams["POST.activeTab"] . "_dict") . " - " . $this->formatDate($logParams["POST.interval.0"], false, true) . $this->formatDate($logParams["POST.interval.1"], false, false, true);
			$mainLog["changed_column"]	.= $this->formatDate($logParams["POST.nextInterval.0"], false, true) . $this->formatDate($logParams["POST.nextInterval.1"], true) . " " . $this->dicts["employeeValidityExtended"];
		// Flexes munkavállaló kezelés új történetiség
		} else if ($mainLog["controller_id"] == "employeecontrol" && $mainLog["action_id"] == "save" && $logParams["POST.newItem"] == "true" && $mainLog["log_message"] != "GRID_DATA_CHANGE")
		{
			$mainLog["event"]		= $this->dicts["employeeMgmt"] . " - " . $this->dicts["employeeDataAdded"];
			$exp					= explode("_", $logParams["POST.editPK"]);
			$mainLog["changed_to"]	= "";
			foreach ($logParams as $key => $val)
			{
				if (strpos($key, "dialogInput") !== false && !empty($val))
				{
					$exp2							= explode(".", $key);
					$mainLog["changed_to"]			.= Dict::getValue($exp2[2]) . ": " . $val . " <br /> ";
					if (strpos($key, "valid_from") !== false) {
						$mainLog["changed_column"]	= $this->getNameWithId("", $val, "", "", "", $exp[0]) . ". - " . Dict::getValue($logParams["POST.generateFrom"] . "_dict");
					}
				}
			}
			$mainLog["changed_to"]	= mb_substr($mainLog["changed_to"], 0, -8);
		// Flexes munkavállaló kezelés szerkesztés
		} else if ($mainLog["controller_id"] == "employeecontrol" && $mainLog["action_id"] == "save" && $logParams["POST.newItem"] == "false" && $mainLog["log_message"] != "GRID_DATA_CHANGE")
		{
			$mainLog["event"]			= $this->dicts["employeeMgmt"] . " - " . $this->dicts["employeeDataChanged"];
			$exp						= explode("_", $logParams["POST.editPK"]);
			$exp2						= explode(" - ", $logParams["POST.historyInterval"]);
			$mainLog["changed_column"]	= $this->getNameWithId("", $exp2[0], "", "", "", $exp[0]) . ". <br /> " . Dict::getValue($logParams["POST.generateFrom"] . "_dict") . " - " . $this->formatDate($exp2[0], false, true) . $this->formatDate($exp2[1], true);
			$mainLog["changed_from"]	= "";
			$mainLog["changed_to"]		= "";
			foreach ($logParams as $key => $val)
			{
				if (strpos($key, "dialogInput") !== false && !empty($val) && strpos($key, "valid_from") === false && strpos($key, "valid_to") === false)
				{
					$exp	= explode(".", $key);
					$res	= $this->getFromAdminLog("ALL", "ALL", [$exp[2]], $mainLog["changed_datetime"], false, [], true);
					if (!empty($res)) {
						$mainLog["changed_from"]	.= Dict::getValue($exp[2]) . ": " . $res[$exp[2]]["old_value"] . " <br /> ";
						$mainLog["changed_to"]		.= Dict::getValue($exp[2]) . ": " . $res[$exp[2]]["new_value"] . " <br /> ";
					}
				}
			}
			$mainLog["changed_from"]	= mb_substr($mainLog["changed_from"], 0, -8);
			$mainLog["changed_to"]		= mb_substr($mainLog["changed_to"], 0, -8);
		} else {
			return $this->getBaseEvents($mainLog);
		}

		return $mainLog;
	}

	/**
	 * Általános események
	 * @param array $mainLog
	 * @return array
	 */
	private function getBaseEvents($mainLog)
	{
		$logParams				= json_decode($mainLog["params"], true);
		$mainLog["log_params"]	= json_decode($mainLog["log_params"], true);
		$actLogParams			= $mainLog["log_params"]["data"] ?? [];

		// Adat lekérés
		if ($mainLog["action_id"] == "gridData")
		{
			$mainLog["event"] = $mainLog["page_title"] . " - " . $this->dicts["gridData"];
			$useSearchInput = false;
			if (isset($logParams["POST.searchInput.valid_date"])) {
				$mainLog["changed_column"]	= $this->formatDate($logParams["POST.searchInput.valid_date"], false, false, true);
				$date						= $logParams["POST.searchInput.valid_date"];
				$useSearchInput				= true;
			} else if (isset($logParams["POST.searchInput.date"])) {
				$mainLog["changed_column"]	= $this->formatDate($logParams["POST.searchInput.date"], false, false, true);
				$date						= $logParams["POST.searchInput.date"];
				$useSearchInput				= true;
			} else if (isset($logParams["POST.searchInput.day"])) {
				$mainLog["changed_column"]	= $this->formatDate($logParams["POST.searchInput.day"], false, false, true);
				$date						= $logParams["POST.searchInput.day"];
				$useSearchInput				= true;
			} else if (isset($logParams["POST.searchInput.valid_from"]) && isset($logParams["POST.searchInput.valid_to"])) {
				$mainLog["changed_column"]	= $this->formatDate($logParams["POST.searchInput.valid_from"], false, true) . $this->formatDate($logParams["POST.searchInput.valid_to"], false, false, true);
				$date						= null;
				$useSearchInput				= true;
			}

			if ($useSearchInput)
			{
				if (is_null($date)) {
					$mainLog["changed_column"] .= $this->getFilterValues($logParams, "", $logParams["POST.searchInput.valid_from"], $logParams["POST.searchInput.valid_to"]);
				} else {
					$mainLog["changed_column"] .= $this->getFilterValues($logParams, $date);
				}
			}
		// XLS export
		} else if ($mainLog["action_id"] == "exportXLS")
		{
			$mainLog["event"] = $mainLog["page_title"] . " - " . $this->dicts["exportXLS"];
		// Új felvétel nem történetiséggel
		} else if ($mainLog["action_id"] == "save" && $mainLog["log_message"] == "GRID_DATA_CHANGE" && isset($logParams["POST.dialogInput_dhtmlxGrid.row_id"]) && $logParams["POST.dialogInput_dhtmlxGrid.row_id"] == "" && !isset($logParams["POST.newItem"]))
		{
			if (!isset($actLogParams[0]["model"])) { return []; }
			$model					= new $actLogParams[0]["model"];
			$labels					= $model->attributeLabels();
			$mainLog["event"]		= $mainLog["page_title"] . " - " . $this->dicts["newAdded"];
			$mainLog["changed_to"]	= "";
			foreach ($actLogParams as $param)
			{
				if (isset($labels[$param["changedColumn"]]))
				{
					if (isset($model->identifyColumn)) {
						if ($param["changedColumn"] != $model->identifyColumn) {
							$mainLog["changed_to"] .= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["newValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
						}
					} else {
						$mainLog["changed_to"] .= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["newValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
					}
				}
			}
			$mainLog["changed_to"] = mb_substr($mainLog["changed_to"], 0, -8);
		// Szerkesztés nem történetiséget
		} else if ($mainLog["action_id"] == "save" && $mainLog["log_message"] == "GRID_DATA_CHANGE" && isset($logParams["POST.dialogInput_dhtmlxGrid.row_id"]) && $logParams["POST.dialogInput_dhtmlxGrid.row_id"] != "" && !isset($logParams["POST.newItem"]))
		{
			if (!isset($actLogParams[0]["model"])) { return []; }
			$mainLog["event"]			= $mainLog["page_title"] . " - " . $this->dicts["edited"];
			$mainLog["changed_column"]	= "";
			$mainLog["changed_from"]	= "";
			$mainLog["changed_to"]		= "";
			$ignoreCols					= ["modified_on", "modified_by", "row_id", "pre_row_id", "status", "created_on", "created_by"];
			$model						= $actLogParams[0]["model"]::model()->findbyPk($logParams["POST.dialogInput_dhtmlxGrid.row_id"]);
			if (is_null($model)) {
				$model						= new $actLogParams[0]["model"];
				$labels						= $model->attributeLabels();
				$mainLog["changed_column"]	.= $this->dicts["stDeleted"];
			} else {
				$labels = $model->attributeLabels();
				if (method_exists($model, "getAttributes"))
				{
					$attrs = $model->getAttributes();
					foreach ($attrs as $key => $data)
					{
						if (!in_array($key, $ignoreCols) && $data != "")
						{
							if (isset($model->identifyColumn)) {
								if ($key != $model->identifyColumn) {
									$mainLog["changed_column"] .= $labels[$key] . ": " . $this->getDropdownLabel($data, $key, $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
								}
							} else {
								$mainLog["changed_column"] .= $labels[$key] . ": " . $this->getDropdownLabel($data, $key, $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
							}
						}
					}
				}
				$mainLog["changed_column"] = mb_substr($mainLog["changed_column"], 0, -8);
			}

			foreach ($actLogParams as $param)
			{
				if (isset($labels[$param["changedColumn"]]))
				{
					if (isset($model->identifyColumn)) {
						if ($param["changedColumn"] != $model->identifyColumn) {
							$mainLog["changed_from"]	.= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["prevValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
							$mainLog["changed_to"]		.= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["newValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
						}
					} else {
						$mainLog["changed_from"]	.= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["prevValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
						$mainLog["changed_to"]		.= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["newValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
					}
				}
			}

			$mainLog["changed_from"]	= mb_substr($mainLog["changed_from"], 0, -8);
			$mainLog["changed_to"]		= mb_substr($mainLog["changed_to"], 0, -8);
		// Törlés nem történetiséget
		} else if ($mainLog["action_id"] == "delete" && $mainLog["log_message"] == "GRID_DATA_DELETE" && !isset($logParams["POST.editPK"]))
		{
			if (!isset($actLogParams["model"]) || class_exists($actLogParams["model"]) != true) { return []; }
			$mainLog["event"]			= $mainLog["page_title"] . " - " . $this->dicts["deleted"];
			$ignoreCols					= ["modified_on", "modified_by", "row_id", "pre_row_id", "status", "created_on", "created_by"];
			$model						= $actLogParams["model"]::model()->findbyPk($actLogParams["id"]);
			$mainLog["changed_column"]	= "";
			if (is_null($model)) {
				$model						= new $actLogParams["model"];
				$labels						= $model->attributeLabels();
				$mainLog["changed_column"]	.= $this->dicts["stDeleted"];
			} else {
				$labels = $model->attributeLabels();
				if (method_exists($model, "getAttributes"))
				{
					$attrs = $model->getAttributes();
					foreach ($attrs as $key => $data)
					{
						if (!in_array($key, $ignoreCols) && $data != "")
						{
							if (isset($model->identifyColumn)) {
								if ($key != $model->identifyColumn) {
									$mainLog["changed_column"] .= $labels[$key] . ": " . $this->getDropdownLabel($data, $key, $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
								}
							} else {
								$labels[$key] = $labels[$key] ?? null;
								$mainLog["changed_column"] .= $labels[$key] . ": " . $this->getDropdownLabel($data, $key, $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
							}
						}
					}
				}
				$mainLog["changed_column"] = mb_substr($mainLog["changed_column"], 0, -8);
			}

		// Új történetiség felvétel
		} else if (
			isset($mainLog["action_id"])
			&& isset($mainLog["log_message"])
			&& isset($logParams["POST.dialogInput_dhtmlxGrid.row_id"])
			&& isset($logParams["POST.newItem"])
			&& $mainLog["action_id"] == "save"
			&& $mainLog["log_message"] != "GRID_DATA_CHANGE"
			&& $logParams["POST.dialogInput_dhtmlxGrid.row_id"] != ""
			&& $logParams["POST.newItem"] == "true"
		)
		{
			$mainLog["event"]		= $mainLog["page_title"] . " - " . $this->dicts["validityAdded"];
			$mainLog["changed_to"]	= "";
			foreach ($logParams as $key => $val)
			{
				if (strpos($key, "dialogInput") !== false && !empty($val))
				{
					$exp	= explode(".", $key);
					$dict	= Dict::getValue($exp[2]);
					if ($dict != "") { $mainLog["changed_to"] .= $dict . ": " . $this->getDropdownLabel($val, $exp[2], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> "; }
				}
			}
			$mainLog["changed_column"] = isset($mainLog["changed_column"]) ? mb_substr($mainLog["changed_column"], 0, -8) : null;
		// Történetiség szerkesztés
		} else if ($mainLog["action_id"] == "save" && $mainLog["log_message"] == "GRID_DATA_CHANGE" && isset($logParams["POST.dialogInput_dhtmlxGrid.row_id"]) && $logParams["POST.dialogInput_dhtmlxGrid.row_id"] != "" && $logParams["POST.newItem"] == "false")
		{
			if (!isset($actLogParams[0]["model"])) { return []; }
			$mainLog["event"]			= $mainLog["page_title"] . " - " . $this->dicts["validityEdited"];
			$mainLog["changed_column"]	= "";
			$mainLog["changed_from"]	= "";
			$mainLog["changed_to"]		= "";
			$ignoreCols					= ["modified_on", "modified_by", "row_id", "pre_row_id", "status", "created_on", "created_by"];
			$model						= $actLogParams[0]["model"]::model()->findbyPk($logParams["POST.dialogInput_dhtmlxGrid.row_id"]);
			if (is_null($model)) {
				$model						= new $actLogParams[0]["model"];
				$labels						= $model->attributeLabels();
				$mainLog["changed_column"]	.= $this->dicts["stDeleted"];
			} else {
				$labels = $model->attributeLabels();
				if (method_exists($model, "getAttributes"))
				{
					$attrs = $model->getAttributes();
					foreach ($attrs as $key => $data)
					{
						if (!in_array($key, $ignoreCols) && $data != "")
						{
							if (isset($model->identifyColumn)) {
								if ($key != $model->identifyColumn) {
									$mainLog["changed_column"] .= $labels[$key] . ": " . $this->getDropdownLabel($data, $key, $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
								}
							} else {
								$mainLog["changed_column"] .= $labels[$key] . ": " . $this->getDropdownLabel($data, $key, $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
							}
						}
					}
				}
				$mainLog["changed_column"] = mb_substr($mainLog["changed_column"], 0, -8);
			}

			foreach ($actLogParams as $param)
			{
				if (isset($labels[$param["changedColumn"]]))
				{
					if (isset($model->identifyColumn)) {
						if ($param["changedColumn"] != $model->identifyColumn) {
							$mainLog["changed_from"]	.= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["prevValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
							$mainLog["changed_to"]		.= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["newValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
						}
					} else {
						$mainLog["changed_from"]	.= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["prevValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
						$mainLog["changed_to"]		.= $labels[$param["changedColumn"]] . ": " . $this->getDropdownLabel($param["newValue"], $param["changedColumn"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
					}
				}
			}

			$mainLog["changed_from"]	= mb_substr($mainLog["changed_from"], 0, -8);
			$mainLog["changed_to"]		= mb_substr($mainLog["changed_to"], 0, -8);
		// Történetiség törlés
		} else if ($mainLog["action_id"] == "delete" && $mainLog["log_message"] == "GRID_DATA_DELETE" && isset($logParams["POST.editPK"]))
		{
			if (!isset($actLogParams["model"]) || class_exists($actLogParams["model"]) != true) { return []; }
			$mainLog["event"]			= $mainLog["page_title"] . " - " . $this->dicts["validityDeleted"];
			$ignoreCols					= ["modified_on", "modified_by", "row_id", "pre_row_id", "status", "created_on", "created_by"];
			$model						= $actLogParams["model"]::model()->findbyPk($logParams["POST.dialogInput_dhtmlxGrid.row_id"]);
			$mainLog["changed_column"]	= "";
			if (is_null($model)) {
				$model						= new $actLogParams["model"];
				$labels						= $model->attributeLabels();
				$mainLog["changed_column"]	.= $this->dicts["stDeleted"];
			} else {
				$labels = $model->attributeLabels();
				if (method_exists($model, "getAttributes"))
				{
					$attrs = $model->getAttributes();
					foreach ($attrs as $key => $data)
					{
						if (!in_array($key, $ignoreCols) && $data != "")
						{
							if (isset($model->identifyColumn)) {
								if ($key != $model->identifyColumn) {
									$mainLog["changed_column"] .= $labels[$key] . ": " . $this->getDropdownLabel($data, $key, $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
								}
							} else {
								$mainLog["changed_column"] .= $labels[$key] . ": " . $this->getDropdownLabel($data, $key, $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
							}
						}
					}
				}
				$mainLog["changed_column"] = mb_substr($mainLog["changed_column"], 0, -8);
			}
		// Inline szerkesztés - STATE_TYPE
		} else if ($mainLog["action_id"] == "gridEdit" && $mainLog["log_message"] == "STATE_TYPE_DATA_CHANGE")
		{
			$mainLog["event"]			= $this->dicts["pageTitleAbsenceType"] . " - " . $this->dicts["inlinEdited"];
			$mainLog["changed_column"]	= "";
			$mainLog["changed_from"]	= "";
			$mainLog["changed_to"]		= "";
			$ignoreCols					= ["modified_on", "modified_by", "row_id", "pre_row_id", "status", "created_on", "created_by"];
			$model						= StateType::model()->findbyPk($actLogParams[0]["row_id"]);
			if (is_null($model)) {
				$model						= new StateType;
				$labels						= $model->attributeLabels();
				$mainLog["changed_column"]	.= $this->dicts["stDeleted"];
			} else {
				$labels						= $model->attributeLabels();
				$mainLog["changed_column"]	.= $this->dicts["absenceType"] . ": " . Dict::getValue($model->name_dict_id);
			}

			foreach ($actLogParams as $param)
			{
				if (isset($labels[$param["field"]]) && $param["field"] != "payroll_calc_id")
				{
					if (isset($model->identifyColumn)) {
						if ($param["field"] != $model->identifyColumn) {
							$mainLog["changed_from"]	.= $labels[$param["field"]] . ": " . $this->getDropdownLabel($param["prevValue"], $param["field"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
							$mainLog["changed_to"]		.= $labels[$param["field"]] . ": " . $this->getDropdownLabel($param["newValue"], $param["field"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
						}
					} else {
						$mainLog["changed_from"]	.= $labels[$param["field"]] . ": " . $this->getDropdownLabel($param["prevValue"], $param["field"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
						$mainLog["changed_to"]		.= $labels[$param["field"]] . ": " . $this->getDropdownLabel($param["newValue"], $param["field"], $mainLog["controller_id"], $mainLog["modifier_username"], $mainLog["changed_datetime"]) . " <br /> ";
					}
				}
		}

			$mainLog["changed_from"]	= mb_substr($mainLog["changed_from"], 0, -8);
			$mainLog["changed_to"]		= mb_substr($mainLog["changed_to"], 0, -8);
		// Inline szerkesztés - egyéb - nem szép - nincs elég jó logolás
		} else if ($mainLog["action_id"] == "gridEdit" && $mainLog["log_message"] != "STATE_TYPE_DATA_CHANGE")
		{
			$SQL	= "SELECT `page_title` FROM `main_log` WHERE `event_time` < '{$mainLog["changed_datetime"]}' AND `controller_id` = '{$mainLog["controller_id"]}' AND `action_id` = 'gridData' AND `username` = '{$mainLog["modifier_username"]}' AND `page_title` <> 'undefined' ORDER BY `event_time` DESC LIMIT 1";
			$res	= dbFetchRow($SQL);
			$mainLog["event"]			= Dict::getValue($res["page_title"]) . " - " . $this->dicts["inlinEdited"];
			$mainLog["changed_column"]	= $this->dicts["modifiedEaseIdentifier"] . ": " . $logParams["POST.ids"];
			$mainLog["changed_to"]		= "";
			foreach ($logParams as $key => $val)
			{
				$exp	= explode("_", $key);
				$c		= count($exp);
				if (strpos($exp[$c-1], "c") !== false && $val != "") {
					$mainLog["changed_to"] .= $val . " <br /> ";
				}
			}
			$mainLog["changed_to"] = mb_substr($mainLog["changed_to"], 0, -8);
		} else {
			return [];
		}

		return $mainLog;
	}

	/**
	 * Visszaadja a kiválasztott dropdown értékét (valuet nem idt)
	 * @param string $id
	 * @param string $col
	 * @param string $controller
	 * @param string $user
	 * @param string $time
	 * @return string
	 */
	private function getDropdownLabel($id, $col, $controller, $user, $time)
	{
		$ret = $id;
		if (!$this->workWithFiles)
		{
			if (isset($this->dropdowns[$user][$controller]))
			{
				foreach ($this->dropdowns[$user][$controller] as $ddTime => $rows)
				{
					$eventTime		= strtotime($time);
					$eventDdMin		= strtotime($time . "-" . $this->maxInterval . " seconds");
					$dropdownTime	= strtotime($ddTime);
					if ($dropdownTime <= $eventTime && $dropdownTime >= $eventDdMin)
					{
						if (isset($rows[$col]))
						{
							foreach ($rows[$col] as $ddId => $ddData) {
								if ($ddId == $id) {
									return $ddData["data"];
								}
							}
						}
					}
				}
			}
			return $ret;
		} else {
			return $this->getDropdownLabelFromFile($id, $col, $controller, $user, $time);
		}

	}

	/**
	 * Visszaadja a main log adatokat adott időszakra, vagy SQL-ből vagy fileokból
	 * Megfelelő cronMode esetén kiírja a main log adatokat fájlokba
	 * @param string $validFrom
	 * @param string $validTo
	 * @param string $username
	 * @param string $whereSQL
	 * @param int $cronMode
	 * @param mixed $periods
	 * @param string $rowId
	 * @return array
	 */
	private function getMainLogs($validFrom, $validTo, $username, $whereSQL, $cronMode = 0 /* 0 = Nem cron fut, 1 = Első csak lekérés, 2 = Visszanyerés intervallumra */, $periods = ["oneDay"], $rowId = "")
	{
		if ($cronMode != 2)
		{
			if ($cronMode == 1) { $userSearch = ""; } else { $userSearch = "AND (log.`username` = '{$username}' OR 'ALL' = '{$username}')"; }
			if ($cronMode == 1) { $logRowIdSQL = "log.`row_id`,"; } else {$logRowIdSQL = ""; }
			$mainLogSQL = "
				SELECT
					{$logRowIdSQL}
					log.`username` AS modifier_username,
					" . Employee::getParam("fullname_with_emp_id", "e", Dict::getLang()) . " AS fullname,
					r.`rolegroup_name` AS rolegroup,
					CASE
						WHEN d.`dict_id` = 'page_title_ahp_year' THEN '" . Dict::getValue("menu_item_ahp_year") . "'
						WHEN d.`dict_id` = 'page_title_ahp_month' THEN '" . Dict::getValue("menu_item_ahp_month") . "'
						WHEN d.`dict_id` = 'page_title_frame_management_with_limit' THEN '" . Dict::getValue("page_title_frame_management") . "'
						ELSE d.`dict_value`
					END AS page_title,
					log.`log_message`,
					log.`controller_id`,
					log.`action_id`,
					'' AS changed_from,
					'' AS changed_to,
					log.`event_time` AS changed_datetime,
					log.`log_params`,
					log.`params`,
					log.`user_id`
				FROM `main_log` log
				LEFT JOIN `user` u ON
						u.`username` = log.`username`
					AND u.`status` = {$this->pub}
					AND log.`event_time` BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->end}')
				LEFT JOIN `employee` e ON
						e.`employee_id` = u.`employee_id`
					AND e.`status` = {$this->pub}
					AND log.`event_time` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->end}')
				LEFT JOIN `auth_rolegroup` r ON
						r.`rolegroup_id` = u.`rolegroup_id`
				LEFT JOIN `dictionary` d ON
						d.`dict_id` = log.`page_title`
					AND d.`lang` = '{$this->lang}'
					AND d.`valid` = 1
				WHERE
						log.`event_time` BETWEEN '{$validFrom}' AND DATE_ADD('{$validTo}', INTERVAL 1 DAY)
					{$userSearch}
					AND log.`username` <> ''
					AND log.`username` IS NOT NULL
					AND log.`log_message` <> 'DROPDOWN'
					{$whereSQL}
			";

			if ($rowId != "") {
				$mainLogSQL .= " AND log.`row_id` > {$rowId}";
			}
			if (!$this->processGridDataRows) {
				$mainLogSQL .= " AND log.`action_id` <> 'gridData'";
			}
			if (!$this->processLogins) {
				$mainLogSQL .= " AND log.`controller_id` NOT IN ('login', 'userSwitchMenu')";
			}

			if ($cronMode == 0)
			{
				if (!$this->showGridDataRows) {
					$mainLogSQL .= " AND log.`action_id` <> 'gridData' ORDER BY log.`event_time` DESC";
				} else {
					$mainLogSQL .= " ORDER BY log.`event_time` DESC";
				}
				$retArr = dbFetchAll($mainLogSQL);
				return $retArr;
			}
			if ($cronMode == 1) {
				$retArr = dbFetchAll($mainLogSQL . " ORDER BY log.`row_id` ASC");
				$this->writeMainLogToFiles($retArr, $validFrom, $validTo, $periods);
				return [];
			}
		} else {
			$retArr = $this->readMainLogFiles($validFrom, $validTo);
			return $retArr;
		}
	}

	/**
	 * Kiírja a main log adatokat fájlokba
	 * @param array $data
	 * @param string $vFrom
	 * @param string $vTo
	 * @param mixed $periods
	 * @return void
	 */
	private function writeMainLogToFiles($data, $vFrom, $vTo, $periods)
	{
		// Mappa létrehozás
		if (!file_exists($this->fullPath)) {
			mkdir($this->fullPath, 0750, true);
		}

		if (is_array($periods) && $periods[0] == "oneDay")
		{
			// egy nap
			$file		= new SplFileInfo($this->fullPath . '/' . $vFrom . "_" . $vTo . "_mainLog.txt");
    		$fileobj	= $file->openFile('a');
			foreach ($data as $d) {
				writeLineToFile($fileobj, json_encode($d, JSON_UNESCAPED_UNICODE));
			}
			// Nyitott fájl stream MEGSEMMÍSÍTÉSE
			$fileobj	= null;
		} else {
			// több nap
			// Fájl objektek létrehozása
			$objects = [];
			foreach ($periods as $period)
			{
				$from	= date("Y-m-d", strtotime(($this->ruaSpeedUpRunWeekly) ? $period["start"] : $period->format("Y-m-d")));
				$to		= date("Y-m-d", strtotime(($this->ruaSpeedUpRunWeekly) ? $period["end"] : $period->format("Y-m-d")));
				$objects[$from . "_" . $to]["info"]		= new SplFileInfo($this->fullPath . '/' . $from . "_" . $to . "_mainLog.txt");
				$objects[$from . "_" . $to]["object"]	= $objects[$from . "_" . $to]["info"]->openFile('a');
			}
			foreach ($data as $d)
			{
				$datetime = date("Y-m-d", strtotime($d["changed_datetime"]));
				foreach ($periods as $period)
				{
					$from	= date("Y-m-d", strtotime(($this->ruaSpeedUpRunWeekly) ? $period["start"] : $period->format("Y-m-d")));
					$to		= date("Y-m-d", strtotime(($this->ruaSpeedUpRunWeekly) ? $period["end"] : $period->format("Y-m-d")));
					if ($datetime >= $from && $datetime <= $to) {
						writeLineToFile($objects[$from . "_" . $to]["object"], json_encode($d, JSON_UNESCAPED_UNICODE));
					}
				}
			}
			// Nyitott fájl stream MEGSEMMÍSÍTÉSE
			foreach ($objects as $validity => $spl) {
				if (isset($spl["object"])) { $spl["object"] = null; }
			}
		}
	}

	/**
	 * Visszaadja a main log adatokat fájlokból intervallumra
	 * @param string $validFrom
	 * @param string $validTo
	 * @return array
	 */
	private function readMainLogFiles($validFrom, $validTo) {
		return readFromFile($this->fullPath, $validFrom . "_" . $validTo . "_mainLog.txt", true);
	}

	/**
	 * Kiírja az admin log adatokat fájlokba
	 * @param array $data
	 * @return void
	 */
	private function writeAdminLogFiles($data)
	{
		// Mappa létrehozás
		if (!file_exists($this->fullPath . '/adminLogs')) {
			mkdir($this->fullPath . '/adminLogs', 0750, true);
		}

		// Adatok írása
		$models = [];
		foreach ($data as $d)
		{
			if (empty($d["model_pk"])) { $modelPK = "empty"; } else { $modelPK = $d["model_pk"]; }
			if (isset($d["model"]) && !in_array($d["model"], $models)) { $models[] = $d["model"]; }
			if (!isset(${$d["model"]})) {
				${$d["model"]}				= [];
				${$d["model"]}["info"]		= new SplFileInfo($this->fullPath . '/adminLogs/' . $d["model"] . ".txt");
				${$d["model"]}["object"]	= ${$d["model"]}["info"]->openFile('a');
			}

			$record =
			[
				"old_value"					=> $d["old_value"],
				"new_value"					=> $d["new_value"],
				"employee_contract_id"		=> $d["employee_contract_id"],
				"day"						=> $d["day"],
				"state_type_id"				=> $d["state_type_id"],
				"employee_absence_id"		=> $d["employee_absence_id"],
				"del_employee_absence_id"	=> $d["del_employee_absence_id"],
				"model_pk"					=> $modelPK,
				"field"						=> $d["field"],
				"created_on"				=> $d["created_on"]
			];

			writeLineToFile(${$d["model"]}["object"], json_encode($record, JSON_UNESCAPED_UNICODE));
		}
		// Nyitott fájl stream MEGSEMMÍSÍTÉSE
		foreach ($models as $m) {
			if (isset(${$m}["object"])) { ${$m}["object"] = null; }
		}
	}

	/**
	 * Kiírja a dropdown adatokat fájlokat
	 * @param array $data
	 * @return void
	 */
	private function writeDropdownFiles($data)
	{
		// Mappa létrehozás
		if (!file_exists($this->fullPath . '/dropDowns')) {
			mkdir($this->fullPath . '/dropDowns', 0750, true);
		}

		// Adatok írása
		foreach ($data as $d)
		{
			$allData		= json_decode($d["log_params"], true);
			$controllerId	= preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $d["controller_id"]));
			$username		= preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $d["username"]));

			// Nyitott fájl stream MEGSEMMÍSÍTÉSE
			if (isset($prevUsername) && isset($prevControllerId) && isset(${$prevUsername . $prevControllerId}) && ($prevUsername != $username || $prevControllerId != $controllerId)) {
				if (isset(${$prevUsername . $prevControllerId}["object"])) { ${$prevUsername . $prevControllerId}["object"] = null; }
			}

			// Mappa létrehozás
			if (!file_exists($this->fullPath . '/dropDowns/' . $username)) {
				mkdir($this->fullPath . '/dropDowns/' . $username, 0750, true);
			}

			if (!isset(${$username . $controllerId})) {
				${$username . $controllerId}			= [];
				${$username . $controllerId}["info"]	= new SplFileInfo($this->fullPath . '/dropDowns/' . $username . '/' . $controllerId . ".txt");
				${$username . $controllerId}["object"]	= ${$username . $controllerId}["info"]->openFile('a');
			}

			$record = [
				"event_time"	=> $d["event_time"],
				"col"			=> $allData["data"]["col"],
				"data"			=> $allData["data"]["data"]
			];

			writeLineToFile(${$username . $controllerId}["object"], json_encode($record, JSON_UNESCAPED_UNICODE));

			$prevUsername		= $username;
			$prevControllerId	= $controllerId;
		}
	}

	/**
	 * Visszaadja a dropdown labelt fájlokból
	 * @param string $id
	 * @param string $col
	 * @param string $controller
	 * @param string $user
	 * @param string $time
	 * @return string
	 */
	private function getDropdownLabelFromFile($id, $col, $controller, $user, $time)
	{
		$ret			= $id;
		$controllerId	= preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $controller));
		$username		= preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $user));
		$data			= readFromFile($this->fullPath . '/dropDowns/' . $username, $controllerId . ".txt", true);
		foreach ($data as $d)
		{
			$eventTime		= strtotime($time);
			$eventDdMin		= strtotime($time . "-" . $this->maxInterval . " seconds");
			$dropdownTime	= strtotime($d["event_time"]);
			if ($dropdownTime <= $eventTime && $dropdownTime >= $eventDdMin && $col == $d["col"]) {
				foreach ($d["data"] as $key => $dd) {
					if ($key == $id) { return $dd["data"]; }
				}
			}
		}
		return $ret;
	}

	/**
	 * Visszaadja az adatot az admin log fájlokból
	 * @param string $model
	 * @param string $modelPK
	 * @param array $fields
	 * @param string $mainLogTime
	 * @param boolean $multiDimension
	 * @param array $absenceIds
	 * @param boolean $flexEmployeeMgmt
	 * @return array
	 */
	private function getFromAdminLogFromFile($model, $modelPK, $fields, $mainLogTime, $multiDimension = false, $absenceIds = [], $flexEmployeeMgmt = false)
	{
		$ret	= [];
		$sec	= ($flexEmployeeMgmt) ? "15" : $this->mainLogAdminLogInterval;

		$data	= [];
		if ($model == "ALL") {
			foreach (glob($this->fullPath . "/adminLogs/*.txt") as $filename) {
				$data = array_merge($data, readFromFile($this->fullPath . '/adminLogs', $filename, true));
			}
		} else {
			$data = readFromFile($this->fullPath . '/adminLogs', $model . ".txt", true);
		}

		foreach ($data as $d)
		{
			if (($modelPK == "ALL" || $modelPK == $d["model_pk"]) && in_array($d["field"], $fields))
			{
				if (!empty($absenceIds))
				{
					if (!in_array($d["employee_absence_id"], $absenceIds) && !in_array($d["del_employee_absence_id"], $absenceIds)) {
						continue;
					}
				}

				$aLogSec		= date("Y-m-d H:i:s", strtotime($d["created_on"]));
				$mainLogFrom	= date("Y-m-d H:i:s", strtotime($mainLogTime . "-" . $sec . " seconds"));
				$mainLogTo		= date("Y-m-d H:i:s", strtotime($mainLogTime . "+" . $sec . " seconds"));
				if ($aLogSec >= $mainLogFrom && $aLogSec <= $mainLogTo)
				{
					if (!$multiDimension) {
						$ret[$d["field"]] = $d;
					} else {
						$d["max_row_id"] = $d["model_pk"];
						$ret[] = $d;
					}
				}
			}
		}

		return $ret;
	}

	/**
	 * Kitörli a cron által keletkezett mappákat és fájlokat
	 * @return void
	 */
	private function deleteWrittenFiles() {
		destroy_dir($this->fullPath . '/');
	}
}

?>
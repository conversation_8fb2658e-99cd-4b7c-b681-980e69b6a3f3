<?php

Yang::import('application.components.EmployeeImport.*');

define('DS', DIRECTORY_SEPARATOR);

class EckerleKKImportController extends Controller
{
	private $tableName = 'temp_employee_import';
	private $employeeRootTabs = [];
	private $publishedStatus = Status::PUBLISHED;
	private $defaultStart;
	private $defaultEnd;
	private $companyIds = [
		'Törzsadatokind.xls' => '1',
		'Törzsadatokinv.xls' => '2',
		'TörzsadatokAUT.xls' => '3',
		'Törzsadatokae.xls' => '4'
	];
	
	public function actionIndex()
	{
		EmployeeImport::setClassVar('createAdditionalTablesIfNotExists', 
										[
											'employee_position',
											'company_org_group2', 
											'unit', 
											'cost', 
											'cost_center',
											'workgroup',
											'payroll'
										]
									);
		EmployeeImport::setClassVar('increaseContractNumber', true);
		$this->defaultStart = App::getSetting('defaultStart');
		$this->defaultEnd = App::getSetting('defaultEnd');
		$customerFtpFolderName = App::getSetting('customerFtpFolderName');
		
		Yang::log("START data import " . date('Y-m-d H:i:s'), 'log', 'system.Import');
		
		$path = Yang::getBasePath() . DS . '..' . DS . 'ftp' . DS . $customerFtpFolderName;
		
		$files = glob( $path . DS . '*.xls' );
		
		if (!empty($files))
		{
			$_SESSION["tiptime"]["rootSession"] = true;
			
			for ($i = 0; $i < count($files); $i++)
			{
				$fileDate = date("Y-m-d", filectime($files[$i]));
				EmployeeImport::setClassVar('fileDate', $fileDate);

				$dataArray = XlsxToArray::getExcelFileToArray($files[$i]);

				$fileName = end(explode(DS, $files[$i]));
				$companyId = $this->companyIds[$fileName];

				unset($dataArray[1]);

				if ($companyId)
				{
					$this->processEmployee($dataArray, $companyId);
				}

				$empIds = array_unique(array_column($dataArray, "30"));

				if (!empty($empIds)) {
					EmployeeImport::lockMissingEmployees($empIds, "company_id = '{$companyId}'");
				}

				$this->archiveFiles($path . DS . basename($files[$i]), basename($files[$i], ".txt"), $customerFtpFolderName);
			}
		} else {
			Yang::log('NO FILES', 'log', 'system');
		}
		
		Yang::log("STOP data import " . date('Y-m-d H:i:s'), 'log', 'system.Import');
	}
	
	 /**
     * Dolgozói adatok feldolgozása
     *
     * @param array $data
     */
    public function processEmployee($contentArray, $companyId)
	{
        if (count($contentArray))
		{
			$datas = [];
			$row_count = 0;
			$columnsSql = "
							SELECT
								IF(`db_table_name` IS NOT NULL, CONCAT(`db_table_name`, 'Tab__', `db_column_name`), `row_id`) AS database_column_name
							FROM
								`import_columns`
							";
			$columns = dbFetchColumn($columnsSql);
			$columnsForImport = [];
			foreach ($columns as $column)
			{
				$columnsForImport[] = ['database_column_name' => $column];
			}
			EmployeeImport::setColumns($columnsForImport);
			$columns = array_merge(['errors'], $columns);

			$tabSql = "SELECT DISTINCT CONCAT(`db_table_name`, 'Tab') FROM `import_columns` WHERE `db_table_name` IS NOT NULL";
			$this->employeeRootTabs = dbFetchColumn($tabSql);
		
            foreach ($contentArray as $key => $row)
			{
                if (is_array($row) && count($row))
				{
					$insertable = true;

					$actualRow = [];
					$empId = "";
					$dateColumns = ['valid_from', 'date_of_birth'];
					for ($i = 0; $i < count($row); $i++)
					{
						if (!is_int($columns[$i+1]))
						{
							if ($columns[$i+1] == 'employeeTab__emp_id') {
								$empId = iconv('UTF-8', 'UTF-8//TRANSLIT', trim($row[$i]));
								$actualRow[] = $empId;
							} else if ($columns[$i+1] == 'employeeContractTab__daily_worktime' && empty($row[$i])) {
								$actualRow[] = 8;
							} else if ($columns[$i+1] == 'employeeContractTab__wage_type') {
								if ($row[$i] == 'Havi béres') {
									$actualRow[] = 'MoLo';
								} else {
									$actualRow[] = iconv('UTF-8', 'UTF-8//TRANSLIT', trim($row[$i]));
								}
							}
							else if (!empty(array_filter($dateColumns, fn($term) => strpos($columns[$i+1], $term) !== false)))
							{
								$validFrom = $this->validateDate($row[$i]);
								if ($validFrom) {
									$actualRow[] = $validFrom;
								} else {
									$actualRow[] = $this->defaultStart;
								}
							} 
							else if (strpos($columns[$i+1], 'valid_to') !== false)
							{
								$validTo = $this->validateDate($row[$i]);
								if ($validTo) {
									$actualRow[] = $validTo;
								} else {
									$actualRow[] = $this->defaultEnd;
								}
							}
							else if ($columns[$i+1] == 'employeeCardTab__card' && empty($row[$i]))
							{
								$actualRow[] = $empId;
							}
							else {
								$actualRow[] = iconv('UTF-8', 'UTF-8//TRANSLIT', trim($row[$i]));
							}
						}
					}

					if ($insertable) {
						$datas[] = $actualRow;
					}
					$row_count ++;
                }
                
            }
			
			$columnsToProcess = array_combine($columns, $columns);

			EmployeeImport::setClassVar('lockEmployeeIfGetECValidTo', true);
			EmployeeImport::setClassVar('lockableTables', 
												[
													"employee"				=> "employee_id",
													"employee_address"		=> "employee_id",
													"employee_base_absence"	=> "employee_contract_id",
													"employee_card"			=> "employee_contract_id",
													"employee_contract"		=> "employee_contract_id",
													"employee_cost"			=> "employee_contract_id",
													"employee_ext"			=> "employee_id",
													"employee_salary"		=> "employee_contract_id",
												]
										);
			EmployeeImport::setClassVar('writeLog', true);

			$jsonResult = EmployeeImport::processData($datas, $columnsToProcess, $this->tableName, 'received_datas', $this->employeeRootTabs);
			
			$result = json_decode($jsonResult);
			if ($result->success_status == 1)
			{
				$insertabelColumns = [];
				
				foreach ($columns as $column)
				{
					$tableCol = explode('__', $column);
					$table = 'noTable';
					$col = $column;
					if (isset($tableCol[1])) {
						$col = $tableCol[1];
						$table = $tableCol[0];
					}
					
					$insertabelColumns[] =	[
												'database_column_name' => $column,
												'table_name' => $table,
												'column_name' => $col
											];
				}



				$this->save($this->tableName, $insertabelColumns, $this->employeeRootTabs, $companyId);
			}
			else
			{
				echo $result->error;
			}
        } else {
            Yang::log('NO DATA IN FILE', 'log', 'system.Import');

        }
    }

    /**
     * @param $date
     * @return false|string
     */
    public function validateDate($date): false|string
    {
        if (empty($date)) {
            return false;
        }

        try {
            $dateObj = new DateTime($date);
            return $dateObj->format('Y-m-d');
        } catch (Exception $e) {
            return false;
        }
    }

	/**
	 * Check if data file exists
	 * @param string $file
	 * @return boolean
	 */
	public function checkFile($file)
	{
		if (file_exists($file)) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * Given the full path and name of the file archive it to a different $path directory and if more than 5 files in archive folder delete oldest
	 * @param string $fileToArchive
	 * @param string $fileName
	 * @return void
	 */
	public function archiveFiles($fileToArchive, $fileName, $customerFtpFolderName)
	{
		// Create folder for archiving
		$path = Yang::getBasePath() . DS . '..' . DS . 'webroot' . DS . 'file_storage' . DS . 'autoImport' . DS . $customerFtpFolderName . DS . 'employee';
		$extension = substr($fileToArchive, -3);

		if (!file_exists($path) && !is_dir($path)) {
			mkdir($path, 0755, TRUE);       
		}

		if ($this->checkFile($fileToArchive) === true) {
			copy($fileToArchive, $path . DS . $fileName . "__" . date('Y-m-d') . "." . $extension);
			unlink($fileToArchive);
		}
		
		// If more than 5 files delete latest ones
		$files = glob( $path . DS . '*.*' );
		$latest = date("Y-m-d");
		foreach ($files as $f)
		{
			$string = basename($f);
			$string = explode("__", $string);
			$date = $string[1];
			if ($date < $latest) {
				$latest = $date;
			}
		}
		if (count($files) > 10) {
			if ($this->checkFile($path . DS . $fileName . "__" . $latest . "." . $extension) === true) {
				unlink($path . DS . $fileName . "__" . $latest . "." . $extension);
			}
		}
	}

	/**
	 * Elmenti a temp táblában lévő nem hibás sorokat
	 * @param string $tableName - temp tábla neve
	 * @param array $columns - beszúrandó oszlop adatai
	 * @param array $employeeRootTabs - érintett táblák
	 */
	public function save($tableName, $columns = null, $employeeRootTabs = null, $companyId)
	{
		EmployeeImport::setColumns($columns);
		
		$uploadedEmpSql = "
			SELECT
				*
			FROM
				`{$tableName}` main
			WHERE
					main.`errors` IS NULL OR main.`errors` = ''
			;";
		$savedData = dbFetchAll($uploadedEmpSql);

		if (!empty($savedData))
		{
			$tables = [];
			$tableTabs = array_column($columns, "table_name");
			foreach ($tableTabs as $table)
			{
				if ($table !== "noTable")
				{
					$modelName = strpos($table, 'Tab') ? ucfirst(substr($table, 0, -3)) : ucfirst($table);
					$model = new $modelName;
					$dbTable = $model->tableName();
					$tables[$dbTable] = $dbTable;
				}
			}

			foreach ($tables as $table)
			{
				$dropTempTableSql = "
					DROP TABLE IF EXISTS `temp_{$table}_before_upload_bck`
				";

				dbExecute($dropTempTableSql);

				$createTempTableSql = "
						CREATE TABLE `temp_{$table}_before_upload_bck`
						SELECT *
						FROM `{$table}`;
					";
				dbExecute($createTempTableSql);
			}
		}
		
		foreach ($savedData as $key => $value)
		{
			$i = 0;
			$row = [];
			// az index formátuma: táblanévTab__oszlopnév
			foreach ($columns as $attrs)
			{
				$index = $attrs['database_column_name'];
				$table = $attrs['table_name'];
				$col = $attrs['column_name'];
				if ($table !== 'no_table' && $table !== 'userTab')
				{
					if(isset($value[$index])) {
						$row[$table][$col] = $value[$index];
					} else {
						$row[$table][$col] = "";
					}
				}
				$i++;
			}

			$row['employeeTab']['company_id'] = $companyId;
			
			$errors = EmployeeImport::validateAndSaveDataRow($row, true, $employeeRootTabs, false, true);
		}
	}
}

<?php
Yang::loadComponentNamespaces('Vue');

use Components\Vue\Provider\CompetencyProvider;
use Components\Vue\Provider\ShiftTaskScheduleProvider;
Yang::import('application.controllers.customers.eisberg.AttendanceSheetAndPayrollCalculation');
/**
 * Eisberg számára készült reggeli beosztás kimutatás controllere
 */
class ReportMorningShiftController extends Grid2Controller
{
    const COLOR_WHITE = '#fff';
    const COLOR_GREEN = '#20b020';
    const COLOR_RED = '#bf1111';
    const COLOR_GREY = '#D3D3D3';
    private $publishedStatus = Status::PUBLISHED;
    private $defaultEnd;
    private $training;
	protected $view = 'application.views.customers.eisberg.reportMorningShift.reportGridContent';

    protected $xlsxOptions = [
        'printOptions' => '<printOptions headings="false" gridLines="true" gridLinesSet="true" horizontalCentered="true" verticalCentered="true"/>',
        'margins' => '<pageMargins left="0.2" right="0.2" top="0.2" bottom="0.2" header="0" footer="0"/>',
        'pageSetup' => '<pageSetup blackAndWhite="false" cellComments="none" copies="1" draft="false" firstPageNumber="1" fitToHeight="1" fitToWidth="1" horizontalDpi="300" orientation="landscape" pageOrder="downThenOver" paperSize="1" scale="100"  useFirstPageNumber="false" usePrinterDefaults="false" verticalDpi="300"/>',

    ];

	public function __construct()
	{
		parent::__construct("customers/eisberg/reportMorningShift");
		$this->defaultEnd = App::getSetting('defaultEnd');
        $this->training = Dict::getModuleValue('ttwa-ahp-core','absence_type_training');
	}

	/**
	 * beállítja az oldal címét (a 'page_title_timesheet' dictionary értékét kell megváltoztatni ügyfelenként, ha nem jó nekik a mostani cím)
	 */
	protected function G2BInit()
	{
		parent::G2BInit();
		parent::setControllerPageTitleId('page_title_reportMorningShift_eisberg','ttwa-wfm');
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("modify",			false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);

		parent::enableReportMode();

		parent::setExportFileName(Dict::getValue("page_title_reportMorningShift_eisberg"));
		$this->setReportOrientation("portrait");
	}

		/**
	 *
	 * @return array
	 */
	protected function search()
	{
		$submit = ['submit'		=> ['col_type' => 'searchBarReloadReportGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => ''], ];
		$searchFromDb = $this->getPreDefinedSearch(Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_DATE, Grid2Controller::G2BC_SEARCH_WITH_DEFAULT_GROUP_FILTER, "workSchedule");
        $searchFromDb['company_org_group1']['default_value'] = '1'; 
        unset($searchFromDb['employee_contract']);
        unset($searchFromDb['submit']);
        return Yang::arrayMerge($searchFromDb, $submit);
	}

	/**
	 * Az az adatbázis lekérdezés, amelyből a szükséges adatokat megkapjuk
	 * @return array
	 */
	protected function getData(array $gargSQL, string $SQLfilter, array $filter) : array
	{
		$date = $filter['valid_date'];

		$SQL = "
			SELECT
				employee_contract.`employee_contract_id`,
				" . Employee::getParam('fullname_with_emp_id', 'employee') . " AS fullname,
				 IF(white_cap.row_id IS NOT NULL, 1, 0) AS white_cap,
				 IF(sts.row_id IS NOT NULL, comp.competency_name, IF(ecomp.row_id IS NOT NULL, comp2.competency_name, '')) AS area,
				 IF(sts.row_id IS NOT NULL, 0, IF(ecomp.row_id IS NOT NULL, 0, 1)) AS area_order
			FROM `employee`
			LEFT JOIN `company` ON
					company.`company_id` = employee.`company_id`
					AND company.valid_from <= '{$date}'
					AND IFNULL(company.valid_to, '{$this->defaultEnd}') >= '{$date}'
					AND company.`status` = {$this->publishedStatus}
			LEFT JOIN `payroll` ON
					`payroll`.`payroll_id` = `employee`.`payroll_id`
					AND `payroll`.`valid_from` <= '{$date}'
					AND IFNULL(`payroll`.`valid_to`, '{$this->defaultEnd}') >= '{$date}'
					AND `payroll`.`status` = {$this->publishedStatus}
			LEFT JOIN `employee_contract` ON
					employee_contract.`employee_id` = employee.`employee_id`
				AND	employee_contract.`status` = {$this->publishedStatus}
				AND employee_contract.valid_from <= '{$date}'
				AND IFNULL(employee_contract.valid_to, '{$this->defaultEnd}') >= '{$date}'
				AND employee_contract.ec_valid_from <= '{$date}'
				AND IFNULL(employee_contract.ec_valid_to, '{$this->defaultEnd}') >= '{$date}'
				AND `employee_contract`.`valid_from` <= IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
				AND `employee_contract`.`ec_valid_from` <= IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
			";

			if(EmployeeGroupConfig::isActiveGroup('unit_id')) {
				$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCal("unit_id","employee_contract","","","employee","'$date'","'$date'");
			}
			$SQL .= "LEFT JOIN `unit` ON
							unit.`status` = {$this->publishedStatus}
						AND unit.`unit_id`=".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
						AND unit.valid_from <= '{$date}'
						AND IFNULL(unit.valid_to, '{$this->defaultEnd}') >= '{$date}'
					";

			if(EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
				$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCal("workgroup_id","employee_contract","","","employee","'$date'","'$date'");
			}
			$SQL .= "LEFT JOIN `workgroup` ON
						workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
					AND workgroup.`status` = {$this->publishedStatus}
					AND workgroup.valid_from <= '{$date}'
					AND IFNULL(workgroup.valid_to, '{$this->defaultEnd}') >= '{$date}'
				";

			if(EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
				$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCal("company_org_group1_id","employee_contract","","","employee","'$date'","'$date'");
			}
			$SQL .= "LEFT JOIN `company_org_group1` ON
						company_org_group1.`status` = {$this->publishedStatus}
					AND company_org_group1.`company_org_group_id`=".EmployeeGroup::getActiveGroupSQL("company_org_group1_id","employee")."
					AND company_org_group1.valid_from <= '{$date}'
					AND IFNULL(company_org_group1.valid_to, '{$this->defaultEnd}') >= '{$date}'
				";
            
            $SQL .= "
                    LEFT JOIN shift_task_schedule white_cap ON
						white_cap.employee_contract_id = employee_contract.employee_contract_id
					AND white_cap.`status` = {$this->publishedStatus}
					AND white_cap.identifier = '10'
					AND '{$date}' = white_cap.`date`
					LEFT JOIN shift_task_schedule sts ON
						sts.employee_contract_id = employee_contract.employee_contract_id
					AND sts.`status` = {$this->publishedStatus}
					AND sts.`type` = 'PRIMARY'
					AND '{$date}' = sts.`date`
				    LEFT JOIN competency comp ON
						comp.competency_id = sts.identifier
					AND comp.`status` = {$this->publishedStatus}
				    LEFT JOIN employee_competency ecomp ON
						ecomp.employee_contract_id = employee_contract.employee_contract_id
					AND ecomp.`status` = {$this->publishedStatus}
					AND ecomp.`order` = 1
					AND '{$date}' BETWEEN ecomp.`valid_from` AND ecomp.`valid_to`
				    LEFT JOIN competency comp2 ON
						comp2.competency_id = ecomp. competency_id
					AND comp2.`status` = {$this->publishedStatus}
					LEFT JOIN employee_absence ON
						employee_absence.employee_contract_id = employee_contract.employee_contract_id
					AND employee_absence.`status` = {$this->publishedStatus}
					AND employee_absence.`day` = '{$date}'
            ";
            
			$SQL .= "
				WHERE
					$SQLfilter
					AND `employee`.`status` = {$this->publishedStatus}
					AND `employee_absence`.`row_id` IS NULL
					{$gargSQL['where']}
			GROUP BY
				employee_contract.`employee_contract_id`
			ORDER BY
				area_order, area, fullname ASC
		";

		return dbFetchAll($SQL, 'employee_contract_id');
	}

	protected function generatePDFFile($fileID, $results)
	{
		$_POST['host'] = 'http://127.0.0.1:8000/htmlToPDF/';
		$_POST['callback'] = 'c_' . rand(1000, 99999);
		$result = $this->actionSetReport();

		$json_start = strpos($result, '{');
		$json_end = strrpos($result, '}');
		$json = substr($result, $json_start, $json_end - $json_start + 1);
		$data = json_decode($json);

		$_POST['gridID'] = 'dthtmlxGrid';
		$_POST['reportId'] = $data->report_id;
		$_POST['reportMode'] = 'NODE';
		$_POST['fileID'] = $fileID;
		$_POST['fileName'] = 'auto_generated_attendanceSheet_report';
		$_POST['fileType'] = 'pdf';
		$_POST['orientation'] = 'portrait';
		$this->actionInitJReport();

		$_POST['report_id'] = $data->report_id;
		$_POST['report_ids'][] = $data->report_id;
		$this->actionRunReport();

		$this->actionGetReport();
	}

	/**
	 * A kereső mezők alapján lekéri a szükséges adatokat és átadja a view-nak
	 * @return boolean
	 */
	public function actionReportGridContent()
	{
		ini_set('max_execution_time', 1800);
		ini_set('memory_limit', '3096M');
		$this->G2BInit();
		$this->layout = "//layouts/ajax";

		$forceLoad = requestParam('forceLoad');
		if (empty($forceLoad)) {
			return false;
		}

		$filter = requestParam('searchInput');
        $_SESSION["tiptime"]["reportMorningShiftDate"] = $filter["valid_date"];
		$filter['interval'] = [
				'valid_from' => $filter["valid_date"],
				'valid_to' => $filter["valid_date"]
		];

		$gpf		= new GetPreDefinedFilter($this->getControllerID(), ["employee.`valid_from`", "employee.`valid_to`"], ['company' => "employee", 'payroll' => "employee"], false, $filter);
		$SQLfilter	= $gpf->getFilter();
		$SQLfilter = App::replaceSQLFilter($SQLfilter, $filter);
		$art		= new ApproverRelatedGroup;
		$gargSQL	= $art->getApproverReleatedGroupSQL("Employee", "workSchedule", false, $filter['valid_date'], "AND", "CurrentDate", $this->getControllerID());

		$employeeData = $this->getData($gargSQL, $SQLfilter, $filter);
		$employeeContractIds = array_map('strval', array_keys($employeeData));
        $workSchedule = $this->getWorkScheduleData($employeeData, $filter);
		$shiftSecondaryTasks = (new ShiftTaskScheduleProvider($employeeContractIds, $filter['valid_date'], $filter['valid_date']))->getDailySecondaryCompetencies();
        $competencies = (new CompetencyProvider(null, true))->getProviderData();
		$this->filterNotMorningWorkers($employeeData, $workSchedule, $filter);
        $this->addWorkScheduleData($employeeData, $workSchedule, $filter);
        $this->addPayRollData($employeeData,$this->getPreviousDayPayrollData($employeeData, $filter));
        $this->setTrainingData($employeeData, $workSchedule);
		$this->addSecondaryTasksData($employeeData, $shiftSecondaryTasks, $competencies, $filter['valid_date']);
        
        $this->render($this->view, ["results" => $employeeData, "header" => $this->getHeaderValues($filter["valid_date"]), "filter" => $filter]);
	}
    
    protected function getWorkScheduleData($employeeData, $filter): array 
    {
        $contractIds = array_map('strval', array_keys($employeeData));
        $gews = new GetEmployeeWorkSchedule($filter['valid_date'], $filter['valid_date'], $contractIds, true, "workSchedule", "2,6");
        return $gews->get();
    }
    
    protected function filterNotMorningWorkers(&$employeeData, $workSchedule, $filter): void 
    {
        foreach ($employeeData as $contractId => $employee) {
            $usedWorkStart = $workSchedule[$contractId][$filter['valid_date']]['used_work_start'];
            if (is_null($usedWorkStart) ||
                ($usedWorkStart > "12:00") ||
                ($workSchedule[$contractId][$filter['valid_date']]['used_type_of_daytype'] == 'RESTDAY')) {
                unset($employeeData[$contractId]);
            }
        } 
    }
    
    protected function addWorkScheduleData(&$employeeData, $workSchedule, $filter): void
    {
        foreach ($employeeData as $contractId => $employee) {
            $employeeData[$contractId]['workTimeStart'] = $workSchedule[$contractId][$filter['valid_date']]['used_work_start'] ?? '';
            $employeeData[$contractId]['workTimeEnd'] = $workSchedule[$contractId][$filter['valid_date']]['used_work_end'] ?? '';
            $employeeData[$contractId]['dayTypeId'] = $workSchedule[$contractId][$filter['valid_date']]['used_daytype_id'] ?? '';
        }
    }
    
    protected function getPreviousDayPayrollData($employeeData, $searchFilter): array
    {
        $filter = [];
        $filter['valid_date'] = date("Y-m-d", strtotime($searchFilter['valid_date'] . " -1 day"));
        
        $filter['interval'] = [
            'valid_from' =>  $filter['valid_date'],
            'valid_to' =>  $filter['valid_date']
        ];

        $gpf		= new GetPreDefinedFilter($this->getControllerID(), ["employee.`valid_from`", "employee.`valid_to`"], ['company' => "employee", 'payroll' => "employee"], false, $filter);
        $SQLfilter	= $gpf->getFilter();
        $SQLfilter = App::replaceSQLFilter($SQLfilter, $filter);
        $art		= new ApproverRelatedGroup;
        $gargSQL	= $art->getApproverReleatedGroupSQL("Employee", "workSchedule", false,  $filter['valid_date'], "AND", "CurrentDate", $this->getControllerID());

        $contractIds = array_map('strval', array_keys($employeeData));
        $payRollSQLFilter = $SQLfilter .
            "   
             AND `employee_contract`.`employee_contract_id` IN ('" . join("','", $contractIds) ."')"; 
        
        return AttendanceSheetAndPayrollCalculation::getCalculationResult($gargSQL, $payRollSQLFilter, $this->getControllerID(), "", $filter['valid_date'], true);
    }
    
    protected function addPayRollData(&$employeeData, $payrolls): void
    {
        foreach ($payrolls as $employeePayroll) {
            $contractId = $employeePayroll['contract_id'];
            $balance = $employeePayroll['balance_downtime_ot_plan'] ?? '';
            $employeeData[$contractId]['balanceDowntimeOtPlan'] = $balance;

            if ($balance !== '') {
                $balanceFloat = floatval($balance);
                $color = self::COLOR_GREY;
                if ($balanceFloat > 0) {
                    $color = self::COLOR_GREEN;
                } elseif ($balanceFloat < 0) {
                    $color = self::COLOR_RED;
                }
                $employeeData[$contractId]['background_color'] = $color;
            }
        } 
    }

    protected function setTrainingData(&$employeeData, $workSchedule): void
    {
        $internalTrainingDayIds = ['209', '359', '351', '362', '376', '390'];
        $lastEmployees = [];
        foreach ($employeeData as $contractId => $employee) {
            if(in_array($employee['dayTypeId'], $internalTrainingDayIds)){
                $employeeData[$contractId]['note'] = $this->training;
            }
        }
        $employeeData = array_merge($employeeData, $lastEmployees);
    }

	protected function addSecondaryTasksData(&$employeeData, $shiftSecondaryTasks, $competencies, $date): void
	{
		foreach ($employeeData as $index => $employee) {
			$contractId = $employee['employee_contract_id'];
			$employeeData[$index]['secondaryTasks'] = array_map(function($task) use ($competencies) {
				return $competencies[$task]['competency_name'];
			}, $shiftSecondaryTasks[$date][$contractId] ?? []);
		}
	}

    /**
	 * A jelenléti ív fő táblázatának fejléce szövegei
	 * @return array
	 */
	protected function getHeaderValues($filteredDate)
	{
        
        $calendarInfo = Calendar::model()->findAllByAttributes(['date' => $filteredDate]);
        $week = ltrim($calendarInfo[0]->week_starting_monday, '0');
        
        $dayNames = [
            '1' => Dict::getValue('monday'),
            '2' => Dict::getValue('tuesday'),
            '3' => Dict::getValue('wednesday'),
            '4' => Dict::getValue('thursday'),
            '5' => Dict::getValue('friday'),
            '6' => Dict::getValue('saturday'),
            '7' => Dict::getValue('sunday')
        ];
            
        $timeDate = strtotime($filteredDate);
        
		return [
                'dateWithTime' => date('Y-m-d G:i'),
                'date' => date('Y-m-d', $timeDate),
                'dateWithWeek' => date('Y', $timeDate) . " {$week}. " . Dict::getValue('week'),
                'dayName' => ucfirst($dayNames[$calendarInfo[0]->day_of_week]),
                'workTimeStart' => Dict::getModuleValue('ttwa-wfm','start_of_work'),
                'workTimeEnd' => Dict::getModuleValue('ttwa-wfm','end_of_work'),
                'dailyCompetency' => Dict::getModuleValue('ttwa-wfm','area_of_work'),
				'secondaryTasks' => Dict::getModuleValue('ttwa-wfm','additional_area_of_work'),
                'note' => Dict::getModuleValue('ttwa-base','note'),
                'balanceDowntimeOtPlan' => Dict::getModuleValue('ttwa-wfm','downTimePerOT')
		];
	}

    protected function generateExcelXlsWriter($type, $filename = null, $hasMultiGridMode = false)
    {
        $results = $_SESSION["tiptime"]["reportMorningShift"];

        $title = "TTWA Report";
        require_once Yang::getBasePath().'/extensions/XLSXWriter/xlsxwriter.class.php';
        $objphpwriter = new XLSXWriter();
        $objphpwriter->setAuthor($title);

		$filteredDate = Yang::getRequestParam('searchInput')['valid_date'];

		$headers = $this->getHeaderValues($filteredDate);
		$headerWidths = $this->getHeaderWidthsForXlsxExport();
        $this->setXlsxOptions($objphpwriter);

        $filename = $this->getExportFileName() . date("_Y.m.d_H.i.s") . '.xlsx';

        $values_widths = [];

		$i = 0;
		foreach ($headers as $item) {
			$values_widths[$i] =  isset($headerWidths[$i]) ? $headerWidths[$i] : $headerWidths['other'];
			$i++;
		}

        $styles_header = ['wrap_text'=>true,'font'=>'Calibri Light','font-size'=>8, 'font-style'=>'bold', 'fill'=>'#c0c0c0', 'halign'=>'center', 'valign'=>'center', 'widths'=>$values_widths, 'border' => 'left,right,top,bottom', 'border-style'=>'thin'];
        $styles1 = [
			['font'=>'Calibri Light','font-size'=>8, 'widths'=>$values_widths, 'border'=>'left,right,top,bottom', 'border-style'=>'thin',  'halign'=>'left'],
			['font'=>'Calibri Light','font-size'=>8, 'widths'=>$values_widths, 'border'=>'left,right,top,bottom', 'border-style'=>'thin',  'halign'=>'center'],
			['font'=>'Calibri Light','font-size'=>8, 'widths'=>$values_widths, 'border'=>'left,right,top,bottom', 'border-style'=>'thin',  'halign'=>'center'],
			['font'=>'Calibri Light','font-size'=>8, 'widths'=>$values_widths, 'border'=>'left,right,top,bottom', 'border-style'=>'thin',  'halign'=>'center'],
			['font'=>'Calibri Light','font-size'=>8, 'widths'=>$values_widths, 'border'=>'left,right,top,bottom', 'border-style'=>'thin',  'halign'=>'center'],
			['font'=>'Calibri Light','font-size'=>8, 'widths'=>$values_widths, 'border'=>'left,right,top,bottom', 'border-style'=>'thin',  'halign'=>'center'],
			['font'=>'Calibri Light','font-size'=>8, 'widths'=>$values_widths, 'border'=>'left,right,top,bottom', 'border-style'=>'thin',  'halign'=>'center'],
			['font'=>'Calibri Light','font-size'=>8, 'widths'=>$values_widths, 'border'=>'left,right,top,bottom', 'border-style'=>'thin',  'halign'=>'center']
		];
        $noBorderStyle = ['font'=>'Calibri Light','font-size'=>8, 'widths'=>$values_widths];

        $compressRows = useExperimental("use compressed grid data array") ? 1 : 0;
        if($compressRows == 1)
        {
            foreach ($results as $data)
            {
                $data = $this->decodeRowData($data);
                $resultsData[] = $data;
            }
        }
        else
        {
            $resultsData = $results;
        }

        foreach ($resultsData as $ecId => $data)
        {
            if (!empty($data['table_start'])) {
                foreach ($data['table_start'] as $row) {
                    $objphpwriter->writeSheetRow($title, $row, $styles_header);
                }
            }

            if (isset($data['data']))
            {
                $this->writeRow($objphpwriter, $data['data'], $styles1);
            }

            foreach ($data['table_end'] as $row) {
                $objphpwriter->writeSheetRow($title, $row, $noBorderStyle);
            }
        }
		$objphpwriter->markMergedCell($title, '0', '0', '0', '7');
        $objphpwriter->markMergedCell($title, '1', '1', '1', '7');

        $objphpwriter->writeToFile(Yang::getBasePath().'/../webroot/reports/'.$filename);
        $url = baseURL().'/reports/'.$filename;
        return $url;
    }

    protected function setXlsxOptions(&$objphpwriter)
    {
        foreach ($this->xlsxOptions as $key => $option)
        {
            $objphpwriter->setVar($key, $option);
        }
    }

    protected function writeRow($objphpwriter, $data, $styles1, $dict = false)
    {
        $title = "TTWA Report";
        foreach ($data as $line => $attrs)
        {
            $colInd = 0;

            $values_rows = [];
            foreach ($attrs as $column => $value)
            {
                if ($dict)
                {
                    $value = Dict::getValue($value);
                }
                $values_rows[] = $value;
            }

            $objphpwriter->writeSheetRow($title, $values_rows, $styles1);
        }
    }

	protected function getHeaderWidthsForXlsxExport()
	{
		$widths = [
			0 => 25,
			'other' => 9
		];

		return $widths;
	}
}
<?php

use Cassandra\Date;

trait Grid2SummarySheetActionsLocalMenu {

	public function actionLocalMenu()
	{
		$this->layout = "//layouts/ajax";
		$publishedStatus = Status::PUBLISHED;
		$showAlwaysAccepOT = App::getSetting("showAlwaysAccepOT");
        $attendanceApproval = (int)App::getSetting("attendanceApproval");
		$summarysheetLocalMenuLimit = App::hasRight($this->getControllerID(), "summarysheet_localmenulimit");

		$sumsheetParams = requestParam('sumsheetParams');

		$currECID = "ALL";

		$currentDate = date("Y-m-d");

		if (isset($sumsheetParams["ecID"])) {
			$currECID = $sumsheetParams["ecID"];
		}

		$employeeId = null;
		if($sumsheetParams['ecID'] != 'ALL' && (App::hasRight('employee','modify') || App::getRight("wfm/summarySheet","openEmployeeManagement")) )
		{
			if (isset($sumsheetParams["employee_id"]) && $sumsheetParams["employee_id"] != '') {
				$employeeId = $sumsheetParams["employee_id"];
			} else {
				$employeeContract = new EmployeeContract();
				$criteria = new CDbCriteria();
				$criteria->condition = "`employee_contract_id` = '".$sumsheetParams['ecID']."' 
									AND `status` = " . $publishedStatus . " 
									AND '{$sumsheetParams["startDate"]}' >= `valid_from` AND '{$sumsheetParams["endDate"]}' <= IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "')";

				$employeeContractResults = $employeeContract->find($criteria);
				$employeeId = $employeeContractResults->employee_id;
			}
		}


		$localmenuRights = $this->getSumSheetRights($sumsheetParams);

		$params = '';

		$dates['startDate'] = $sumsheetParams["startDate"];
		if(isset($sumsheetParams["endDate"])){
			$dates['endDate'] = $sumsheetParams["endDate"];
		}

		$summarySheetLimit = SummarySheetLimitation::dateInInterval($dates, App::getRolegroup(), $sumsheetParams['ecID']);

		$lockFunctionAfterClick = ((bool) App::getSetting("legalControlOnSummarySheetBeforeLock")) ? "legalControlBeforeLock" : "summarySheetLock";

		$sumsheetParams['startLockDate'] = max($sumsheetParams['startDate'], $summarySheetLimit['startDate']);
		if(isset($sumsheetParams['endDate'])){
			$sumsheetParams['endLockDate'] = min($summarySheetLimit['endDate'], $sumsheetParams['endDate']);
		}

		foreach ($sumsheetParams as $key => $value) {
			if (!empty($params)) {
				$params .= "&";
			}
			$params .= "sumsheetParams[$key]=$value";
		}
		$params = addslashes($params);

		$title = '';

		if (isset($sumsheetParams["title"]) && !empty($sumsheetParams["title"])) {
			$title = $sumsheetParams["title"];
		}
		$title = addslashes($title);

		$resp = '';

		$resp .= '<div class="line"></div>';

		if (isset($sumsheetParams["locked"]) && (int)$sumsheetParams["locked"] === 1 && (int)App::getSetting("summarySheet_lockedNotLockedSearch") && (empty($sumsheetParams["title"]) || $sumsheetParams["title"] == "undefined")) {
			$results = ['data' => 'lockedDaysDisabled'];
			echo json_encode($results);
			exit;
		}

		$absenceLock = App::getSetting('summarySheetLockAbsences');

		$hasNoErrorWorkedaLot = (isset($sumsheetParams["hasErrorWorkedaLot"]) && (int) $sumsheetParams["hasErrorWorkedaLot"] === 0) || (!isset($sumsheetParams["hasErrorWorkedaLot"]));

		$doNotLockDaysPurpleCells = true;
		if (isset($sumsheetParams["hasRequiredValues"]) && (int)$sumsheetParams["hasRequiredValues"] === 1) {
			$doNotLockDaysPurpleCells = !App::hasRight($this->getControllerID(), "sumsheet_do_not_lock_days_purple_cells");
		}

		// ha mentve van vagy nincs hiba a számolásban
		if (   (isset($sumsheetParams["saved"]) && (int) $sumsheetParams["saved"] === 1)
			|| (!isset($sumsheetParams["saved"]))
			|| (isset($sumsheetParams["calcNoError"]) && (int) $sumsheetParams["calcNoError"] === 1)
			|| (!isset($sumsheetParams["calcNoError"]))
			|| ($absenceLock == 1 && isset($sumsheetParams['hasAbsence']) && (int) $sumsheetParams['hasAbsence'] === 1)
			|| ($absenceLock == 1 && !isset($sumsheetParams['hasAbsence']))
			|| (
					isset($sumsheetParams["isItWeekend"]) && (int) $sumsheetParams["isItWeekend"] === 1
				|| (isset($sumsheetParams["endDate"]) && $sumsheetParams["endDate"] < date('Y-m-d'))
				|| (
						isset($sumsheetParams["isItHoliday"]) && (int) $sumsheetParams["isItHoliday"] === 1
					|| (isset($sumsheetParams["endDate"]) && $sumsheetParams["endDate"] < date('Y-m-d'))
				   )
			   )
			)
		{
			if (((isset($sumsheetParams["locked"]) && (int) $sumsheetParams["locked"] === 0) || (!isset($sumsheetParams["locked"]))) && $summarySheetLimit['valid'] && $hasNoErrorWorkedaLot && $doNotLockDaysPurpleCells) {
				if ($localmenuRights["localmenu_lock"]) {
					$resp .= '<div class="item" id="summarySheetLock" onclick="' . $lockFunctionAfterClick . '(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_lock") . '</div>';
				}
				if ($localmenuRights["localmenu_changeottoabsence"] && ((isset($sumsheetParams["hasApprovedOvertime"]) && (int) $sumsheetParams["hasApprovedOvertime"] === 1))) {
					$resp .= '<div class="item" id="summarySheetChangeOtToAbsence" onclick="summarySheetChangeOtToAbsence(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_change_ot_to_absence") . '</div>';
				}
			}
		}

		

		$confirm = Dict::getModuleValue("ttwa-wfm", "sumsheet_regAccept_confirm");
		$errorMessage = Dict::getValue('an_error_occured');

		if ($currECID !== "ALL" && $currECID !== Yang::getUserECID() && $localmenuRights["localmenu_reg_accept"] && ((isset($sumsheetParams["hasRequiredValues"]) && (int) $sumsheetParams["hasRequiredValues"] === 1))) {
			$resp .= '<div class="item" id="summarySheetRegAccept" onclick="summarySheetRegAccept(
				\'' . $sumsheetParams["startDate"] . '\',
				\'' . $confirm . '\',
				\'' . $errorMessage . '\',
				\'' . $params . '\'
			);">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_regAccept") . '</div>';
		}
		$confirm = Dict::getModuleValue("ttwa-wfm", "sumsheet_regNotAccept_confirm");

		if ($currECID !== "ALL" && $currECID !== Yang::getUserECID() && $localmenuRights["localmenu_reg_not_accept"] && ((isset($sumsheetParams["hasRequiredValues"]) && (int) $sumsheetParams["hasRequiredValues"] === 1))) {
			$resp .= '<div class="item" id="summarySheetRegNotAccept" onclick="summarySheetRegNotAccept(
				\'' . $sumsheetParams["startDate"] . '\',
				\'' . $confirm . '\',
				\'' . $errorMessage . '\',
				\'' . $params . '\'
			);">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_regNotAccept") . '</div>';
		}

		if ((isset($sumsheetParams["calcNoError"]) && (int) $sumsheetParams["calcNoError"] === 1) || (!isset($sumsheetParams["calcNoError"])) && $summarySheetLimit['valid']) {
			// nincs mentve és lockolva
			if (( (isset($sumsheetParams["saved"]) && (int) $sumsheetParams["saved"] === 0) || (!isset($sumsheetParams["saved"])) ) && ( (isset($sumsheetParams["locked"]) && (int) $sumsheetParams["locked"] === 0) || (!isset($sumsheetParams["locked"])) )) {

				$confirmOutOfTimeChange = Dict::getModuleValue("ttwa-wfm", "sumsheet_OutOfTimeChange_confirm");
				if ($currECID !== "ALL" && $currECID !== Yang::getUserECID() && $localmenuRights["localmenu_OutOfTimeChangeDelete"] && ((isset($sumsheetParams["hasOutOfTimeChangeValues"]) && (int) $sumsheetParams["hasOutOfTimeChangeValues"] === 1))) {
					$resp .= '<div class="item" id="summarySheetOutOfTimeChange" onclick="summarySheetOutOfTimeChange(
						\'' . $sumsheetParams["startDate"] . '\',
						\'' . $currECID . '\',
						\'' . $confirmOutOfTimeChange . '\',
						\'' . $errorMessage . '\',
						\'' . $params . '\'
					);">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_outOfTimeChange") . '</div>';
				}

				if (!$summarysheetLocalMenuLimit && $currECID !== "ALL" && $localmenuRights["localmenu_changepr"] && ((isset($sumsheetParams["hasOvertimeRequest"]) && (int) $sumsheetParams["hasOvertimeRequest"] === 1))) {
					$resp .= '<div class="item" id="summarySheetChangePR" onclick="summarySheetChangePR(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_change_pr") . '</div>';
				}

				if (!$summarysheetLocalMenuLimit && $currECID !== "ALL" && $localmenuRights["localmenu_changeszaot"] && ((isset($sumsheetParams["isItWeekend"]) && (int) $sumsheetParams["isItWeekend"] === 1))) {
					$resp .= '<div class="item" id="summarySheetChangeSZAOT" onclick="summarySheetChangeSZAOT(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_changeszaot") . '</div>';
				}

				/*
				 * A következő feltétel a túlóra-elfogadás menüpont megjelenésének kritériumait tartalmazza:
				 * 1. A felhasználó szerepkör-csoportja rendelkezik az összesítő lapon túlóra-jóváhagyási joggal, az érintett munkavállaló
				 *		nem balanszos, túlóra-igénylése van, és egyetlen munkavállaló egyetlen napjáról van szó. (egyetlen cella)
				 * 2. A felhasználó szerepkör-csoportja rendelkezik a "nap szintű" túlóra-jóváhagyási joggal az összesítő lapon,
				 *		egyetlen naptári napot érintően, az összesítő lapon szereplő összes munkavállaló esetében elfogadhatja a túlórákat.
				 * 3. A felhasználó szerepkör-csoportja rendelkezik a "munkavállalói szintű" túlóra-jóváhagyási joggal az összesítő lapon,
				 *		egyetlen munkavállalót érintően, az összesítő lapon megadott intervallumban fogadhatja el a túlórákat.
				 * 4. A felhasználó szerepkör-csoportja rendelkezik a "táblázat szintű" túlóra-jóváhagyási joggal az összesítő lapon,
				 *		az összesítő lapon szereplő összes munkavállalót érintően, a megadott intervallumban fogadhatja el a túlórákat.
				 */

				if ($summarySheetLimit['valid'] &&
						(
							$showAlwaysAccepOT
						||
						(
								$localmenuRights["localmenu_acceptot"]
							&&
							(
									(isset($sumsheetParams["hasOvertimeRequest"]) && (int) $sumsheetParams["hasOvertimeRequest"] === 1)
								||	!isset($sumsheetParams["hasOvertimeRequest"])
							)
							&&
							(
								isset($sumsheetParams["ecID"]) && $sumsheetParams["ecID"] !== 'ALL'
							)
							&&
								!isset($sumsheetParams["endDate"])
						)
						||
						(
								$localmenuRights["localmenu_acceptot_day"]
							&&
								(isset($sumsheetParams["ecID"]) && $sumsheetParams["ecID"] === 'ALL')
							&&
								!isset($sumsheetParams["endDate"])
						)
						||
						(
								$localmenuRights["localmenu_acceptot_employee"]
							&&
								(isset($sumsheetParams["ecID"]) && $sumsheetParams["ecID"] !== 'ALL')
							&&
								($currECID !== Yang::getUserECID() || App::hasRight($this->getControllerID(), "edit_himself"))
							&&
								(isset($sumsheetParams["startDate"]) && isset($sumsheetParams["endDate"]) && $sumsheetParams["startDate"] !== $sumsheetParams["endDate"])
						)
						||
						(
								$localmenuRights["localmenu_acceptot_table"]
							&&
								(isset($sumsheetParams["ecID"]) && $sumsheetParams["ecID"] === 'ALL')
							&&
								(isset($sumsheetParams["startDate"]) && isset($sumsheetParams["endDate"]) && $sumsheetParams["startDate"] !== $sumsheetParams["endDate"])
						)
					))
				{
					if ($summarysheetLocalMenuLimit && $localmenuRights["localmenu_changepr"]) {
						$resp .= '<div class="item" id="summarySheetChangePR" onclick="summarySheetChangePR(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_change_pr") . '</div>';
					}
					if ($summarysheetLocalMenuLimit && $localmenuRights["localmenu_changeszaot"] && ((isset($sumsheetParams["isItWeekend"]) && (int) $sumsheetParams["isItWeekend"] === 1))) {
						$resp .= '<div class="item" id="summarySheetChangeSZAOT" onclick="summarySheetChangeSZAOT(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_changeszaot") . '</div>';
					}
					$resp .= '<div class="item" id="summarySheetAcceptOT" onclick="summarySheetAcceptOT(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_accept_ot") . '</div>';
					
				}else{
					if(
						$localmenuRights["localmenu_acceptbot"] //TODO: Bele lehetne tenni, hogy csak akkor ha vam bot...
						&&
						($currECID !== Yang::getUserECID() || App::hasRight($this->getControllerID(), "edit_himself"))
						&& $summarySheetLimit['valid']
					)
					{
						$resp .= '<div class="item" id="summarySheetAcceptOT" onclick="summarySheetAcceptOT(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_accept_bot") . '</div>';
					}
				}
			}
		}
		// lockolva
		if ((isset($sumsheetParams["locked"]) && (int) $sumsheetParams["locked"] === 1) || (!isset($sumsheetParams["locked"])) && $summarySheetLimit['valid']) {
			if ($localmenuRights["localmenu_unlock"] && $summarySheetLimit['valid']) {
				$resp .= '<div class="item" id="summarySheetUnLock" onclick="summarySheetUnLock(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_unlock") . '</div>';
			}
		}
		// ha mentve van
		if ((((isset($sumsheetParams["saved"]) && (int) $sumsheetParams["saved"] === 1) || (!isset($sumsheetParams["saved"]))) || ((isset($sumsheetParams["locked"]) && (int) $sumsheetParams["locked"] === 1) || (!isset($sumsheetParams["locked"])))) && $summarySheetLimit['valid']) {
			if ($localmenuRights["localmenu_delete"] && (
					((isset($sumsheetParams["locked"]) && (int) $sumsheetParams["locked"] === 1) AND $localmenuRights["localmenu_unlock"]) || (!isset($sumsheetParams["locked"]) || (int) $sumsheetParams["locked"] != 1)
					)
			) {
				$resp .= '<div class="item" id="summarySheetDelSaved" onclick="summarySheetDelSaved(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_del_saved") . '</div>';
			}
		}

		if (isset($sumsheetParams["cellClick"]) && $summarySheetLimit['valid']) {
			if (
					((isset($sumsheetParams["locked"]) && (int) $sumsheetParams["locked"] === 0) || !isset($sumsheetParams["locked"]))
				&&	!$sumsheetParams["missingEmployee"]
				) {
				if ($localmenuRights["localmenu_modify"]) {
					$resp .= '<div class="item" id="summarySheetModify" onclick="summarySheetModify(\'' . $title . " - " . Dict::getModuleValue("ttwa-wfm", "sumsheet_modify") . '\',\'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_modify") . '</div>';
				}
			}
		}

		if ($currECID !== "ALL" && isset($sumsheetParams["startDate"]) && isset($sumsheetParams["endDate"])) {
			if (isset($localmenuRights["localmenu_add_absence"]) && $localmenuRights["localmenu_add_absence"]) {
				$resp .= '<div class="line"></div>';
				$resp .= '<div class="item" id="summarySheetAbsence" onclick="summarySheetAddAbsence(\'' . $title . " - " . Dict::getModuleValue("ttwa-wfm", "sumsheet_add_absence") . '\', \'' . $params . '\');">' . Dict::getModuleValue("ttwa-wfm", "sumsheet_add_absence") . '</div>';
			}
		}

		if (isset($sumsheetParams["hasAbsence"]) && (int) $sumsheetParams["hasAbsence"] === 1) {
			$absenceStatus = $_SESSION['employeeAbsences'][$sumsheetParams["ecID"]][$sumsheetParams['startLockDate']]['status'];
			if ($absenceStatus == '1' && $localmenuRights["localmenu_approve_absence"] && ($sumsheetParams["startDate"] >= $currentDate || $localmenuRights["absence_all_days_editable"]))
			{
				$resp .= '<div class="line"></div>';
				$resp .= '<div class="item" id="summarySheetDeleteAbsence" onclick="summarySheetApproveAbsence(\'' . $params . '\');">' . Dict::getValue("sumsheet_accept_absence") . '</div>';
				$resp .= '<div class="item" id="summarySheetDeleteAbsence" onclick="summarySheetRejectAbsence(\'' . $params . '\');">' . Dict::getModuleValue("ttwa-ahp", "msg_th_ahp_absenceapproval_rej_add") . '</div>';
				$resp .= '<div class="line"></div>';
			}
			else if ($localmenuRights["localmenu_del_absence"] && ($sumsheetParams["startDate"] >= $currentDate || $localmenuRights["absence_all_days_editable"])) {
				$resp .= '<div class="line"></div>';
				$resp .= '<div class="item" id="summarySheetDeleteAbsence" onclick="summarySheetDeleteAbsence(\'' . $params . '\');">' . Dict::getValue("sumsheet_delete_absence") . '</div>';
			}
		}

		if(!is_null($employeeId) && ((App::hasRight('employee','modify') || App::getRight("wfm/summarySheet","openEmployeeManagement")) && App::getSetting('enableSummarySheetEmployeeButton') == 1 )){
			//Employee Management Button
			$pageTitle = Dict::getValue('page_title_employee_management');
			$pageTitleModify = $pageTitle.' - '.Dict::getValue('modify');
			$resp .= '<div class="item" id="employeeManagementButton" onclick="openEmployeeManagementDialog(\''.$employeeId.'\',\''.date('Y-m-d').'\', \''.$pageTitle.'\', \''.$pageTitleModify.'\')">' . Dict::getValue('employee_management') . '</div>';
		}

        if($attendanceApproval === 1 && $sumsheetParams["cellClick"] !== "true" && isset($sumsheetParams["endDate"]))
        {
            $attendanceApprovalMenuDict = $currECID === Yang::getUserECID() ? Dict::getValue('self_approval') : Dict::getValue('managerial_approval');
            $resp .= '<div class="item" id="attendanceApproval" onclick="attendanceApproval(\''.$sumsheetParams['ecID'].'\', \''.Dict::getValue('are_you_sure_to_approve').'\', \''.Dict::getValue('failed').'\')">'.$attendanceApprovalMenuDict.'</div>';
        }

		$grouping = App::getSetting('specialFrameDaysGroup');

		if ($currECID === 'ALL' && !isset($sumsheetParams["endDate"]) && $summarySheetLimit['valid']					// ha oszlopra kattintottunk
			&& ($localmenuRights["localmenu_activate_frame_day"] || $localmenuRights["localmenu_deactivate_frame_day"])	// és van joga
		)
		{
			if (!empty($grouping))
			{
				$groupTableData = linkGroup($grouping);

				$groupFiltered = ($_SESSION['summarySheet_filters'][$grouping] !== 'ALL');

				$specialFrameDayOptionSql = " SELECT special_frameday_option
												FROM {$grouping}
												WHERE " . $groupTableData['id_in_related_table'] . " = '" . $_SESSION['summarySheet_filters'][$grouping] . "'
												AND status = " . $publishedStatus . "
												AND '" . $sumsheetParams["startDate"] . "' BETWEEN valid_from AND valid_to";
				$isSpecialFrameDayOption = dbFetchValue($specialFrameDayOptionSql);

				if(!empty($isSpecialFrameDayOption))
				{
					$isSpecialFrameDaySql = "
						SELECT
							*
						FROM
							special_frame_days_by_group
						WHERE
								group_id = '" . $groupTableData['related_id'] . "'
							AND	group_value = '" . $_SESSION['summarySheet_filters'][$grouping] . "'
							AND date = '" . $sumsheetParams["startDate"] . "'
							AND status = " . $publishedStatus;
					$isSpecialFrameDay = dbFetchAll($isSpecialFrameDaySql);

					$errorMessage = Dict::getValue('an_error_occured');

					$groupNameSql = "
									SELECT 
										" . $groupTableData['group_name_column_in_related_table'] . "
									FROM 
										$grouping 
									WHERE 
											" . $groupTableData['id_in_related_table'] . "  = '" . $_SESSION['summarySheet_filters'][$grouping] . "'
										AND status = " . $publishedStatus . "
										AND '" . $sumsheetParams["startDate"] . "' BETWEEN valid_from AND valid_to
									";
					$groupName = dbFetch($groupNameSql, 'queryScalar');


					if (empty($isSpecialFrameDay))
					{
						$confirm = Dict::getValue('activate_frame_day_confirm', ['group' => Dict::getValue($grouping), 'groupName' => $groupName, 'date' => $sumsheetParams["startDate"]]);

						$resp .= '<div class="item" id="summarySheetActivateFrameDay" 
										style="' . ($groupFiltered ? "" : "pointer-events: none; color: lightgrey;" ) . '"
										onclick="summarySheetActivateFrameDay(
													\'' . $_SESSION['summarySheet_filters'][$grouping] . '\', 
													\'' . $sumsheetParams["startDate"] . '\',
													\'' . $confirm . '\',
													\'' . $errorMessage . '\',
												);"
								>'
										. Dict::getModuleValue("ttwa-wfm", "activate_frame_day")
								. '</div>';
					}
					else
					{
						$resp .= '<div class="item" id="summarySheetDeactivateFrameDay" 
										style="' . ($groupFiltered ? "" : "pointer-events: none; color: lightgrey;" ) . '"
										onclick="summarySheetDeactivateFrameDay(
											\'' . $_SESSION['summarySheet_filters'][$grouping] . '\', 
											\'' . $sumsheetParams["startDate"] . '\',
											\'' . Dict::getValue('deactivate_frame_day_confirm',
																	['group' => Dict::getValue($grouping), 'groupName' => $groupName, 'date' => $sumsheetParams["startDate"]]) . '\',
											\'' . $errorMessage . '\',
										);"
									>'
										. Dict::getModuleValue("ttwa-wfm", "deactivate_frame_day")
								. '</div>';
					}
				}
			}
		}
	
		$results = [
			'data' => $resp,
        ];

		echo json_encode($results);
	}

	public function actionRunLegalControl()
	{
		$this->layout = "//layouts/ajax";

		if (is_null(Yang::session('summarySheet_activeEmployees'))) {
			Yang::log("SESSION timed out...", 'log', 'system.SummarySheet.LocalMenu');
			return false;
		}

		$this->G2BInit();

		$employeeContracts = [];
		$response = [];

		$sumsheetParams = requestParam('sumsheetParams');

		if (isset($sumsheetParams["startDate"]) && isset($sumsheetParams["startLockDate"])) {
				$sumsheetParams["startDate"] = $sumsheetParams["startLockDate"];
		}
		if (isset($sumsheetParams["endDate"]) && isset($sumsheetParams["endLockDate"])) {
			$sumsheetParams["endDate"] = $sumsheetParams["endLockDate"];
		}

		if (isset($sumsheetParams["startDate"])) {
			$startDate = $sumsheetParams["startDate"];
		}

        $endDate = $sumsheetParams["endDate"] ?? $startDate;

		if (isset($sumsheetParams["ecID"]) && $sumsheetParams["ecID"] !== "ALL") {
			$employeeContracts = [$sumsheetParams["ecID"]];
		} else {
			$activeEmployees = $this->activeEmployees;

			foreach ($activeEmployees as $employee) {
				$employeeContracts[] = $employee["employee_contract_id"];
			}
		}

		$legalControlOnLock = new GetLegalControlViolation($sumsheetParams["startDate"],$sumsheetParams["endDate"],$employeeContracts);
		$violations = $legalControlOnLock->getLegalControlViolation();

		if (count($violations) > 0)
		{
			$violationsHtml = $this->getLegalControlViolationsHtml($violations);

			$response = [
				"status"		=> 1,
				"violations"	=> $violations,
				"html"			=> $violationsHtml
			];
		} else {
			$response = ["status" => 0];
		}

		echo json_encode($response);
        return true;
	}

	private function getLegalControlViolationsHtml($violations)
	{
		$html = "<table>
					<thead>
						<tr>
							<th>" . Dict::getValue("employee_name") . "</th>
							<th>" . Dict::getValue("emp_id") . "</th>
							<th>" . Dict::getValue("type_of_violation") . "</th>
							<th>" . Dict::getValue("time_of_violation") . "</th>
							<th>" . Dict::getValue("note") . "</th>
						</tr>
					</thead>
					<tbody>
				";

		for ($i = 0; $i < count($violations); $i++) {
			$html .= "
						<tr id = 'violation_$i' class = 'violationRow'>
							<td class = 'employeeName'>" . $violations[$i]["employee_name"] . "</td>
							<td class = 'empId'>" . $violations[$i]["emp_id"] . "</td>
							<td class = 'typeOfViolation'>" . $violations[$i]["type_of_violation"] . "</td>
							<td class = 'timeOfViolation'>" . $violations[$i]["time_of_violation"] . "</td>
							<td class = 'noteToViolation'>
								<textarea id='note' name='note' rows='1'></textarea>
							</td>
						</tr>	
					";
		}

		$html .= "	</tbody>
				</table>";

		return $html;
	}

	public function actionLogAfterLegalControl()
	{
		$logParams = requestParam('logParams');
		$createdBy = userID();
		$createdOn = date("Y-m-d H:i:s");

        $sqlValues = '';
		for ($i = 0; $i < count($logParams); $i++) {
            $sqlValues .= "('" .
						$logParams[$i]["emp_id"] . "','" .
						$logParams[$i]["employee_name"] . "','" .
						$logParams[$i]["type_of_violation"] . "','" .
						$logParams[$i]["time_of_violation"] . "','" .
						htmlspecialchars(stripcslashes(trim($logParams[$i]["note"]))) . "','" .
						$logParams[$i]["action"] . "','" .
						$createdBy . "','" .
						$createdOn . "'" .
					")";

            $sqlValues .= ($i < count($logParams) - 1) ? "," : ";";
		}
        $SQL = "INSERT INTO `legal_control_sumsheet_log`
					(`emp_id`, `employee_name`, `type_of_violation`, `time_of_violation`, `note`, `action`, `created_by`, `created_on`) 
                VALUES {$sqlValues}";

		try {
			dbExecute($SQL);

			$response = [
				'status' => 1
			];

		} catch (Exception $e) {
			Yang::log($e, 'error', 'logAfterLegalControl');

			$response = [
				'status'		=> 0,
				'errorMessage'	=> Dict::getValue("legalControlSumsheetLogSaveError")
			];
		}

		echo json_encode($response);
	}

	public function actionModifyCalcStatus()
	{
		$this->layout = "//layouts/ajax";

		if (is_null(Yang::session('summarySheet_activeEmployees'))) {
			Yang::log("SESSION timed out...", 'log', 'system.SummarySheet.LocalMenu');
			return false;
		}

		$searchFilter = Yang::session('summarySheet_filters');
		$SettingDoNotLockOrangeCells = App::getSetting("doNotLockDaysMarkedOrange");
		$summarysheetAutomatiLockStatus = App::hasRight($this->getControllerID(), "summarysheet_automaticlockstatus");
		$summarysheetLocalMenuLimit = App::hasRight($this->getControllerID(), "summarysheet_localmenulimit");
        $summarysheetDoNotLockDaysPurpleCells = App::hasRight($this->getControllerID(), "sumsheet_do_not_lock_days_purple_cells");
        $maxWork12hError = (int)App::getSetting('wfmSumSheetError_MaxTotalWorktime12');
		$draftStatus = Status::DRAFT;
		$publishedStatus = Status::PUBLISHED;
		$savedStatus = Status::SAVED;
		$lockedStatus = Status::LOCKED;

		$this->G2BInit();
        $modifiedSet = "`modified_on` = '" . date('Y-m-d H:i:s') . "', `modified_by` = '" . $this->userIdToSwitch  . "'";

		$status = requestParam('status');

		$sumsheetParams = requestParam('sumsheetParams');
        
		$userECID = Yang::getUserECID();

		$localmenuRight = $this->getSumSheetRights($sumsheetParams);

		if ($status === "LOCK" && !$localmenuRight["localmenu_lock"]) {
			return false;
		}
		if ($status === "UNLOCK" && !$localmenuRight["localmenu_unlock"]) {
			return false;
		}
		if ($status === "DELSAVE" && !$localmenuRight["localmenu_delete"]) {
			return false;
		}
		if ($status === "SAVE" && !$localmenuRight["localmenu_acceptot"] && !$localmenuRight["localmenu_acceptbot"]) {
			return false;
		}
		if ($status === "CHANGEPR" && !$localmenuRight["localmenu_changepr"]) {
			return false;
		}
		if ($status === "CHANGESZAOT" && !$localmenuRight["localmenu_changeszaot"]) {
			return false;
		}
		$statusChangepr = false;
		$statusOvertimeBalancePr = false;
		$statusChangeSzaot = false;
		if ($summarysheetAutomatiLockStatus && $status === "SAVE" )
		{
			$status = "LOCK";
			$statusOvertimeBalancePr = true;
		}
		elseif($summarysheetAutomatiLockStatus && $status === "CHANGEPR")
		{
			$status = "LOCK";
			$statusChangepr = true;
		}
		elseif($summarysheetAutomatiLockStatus && $status === "CHANGESZAOT")
		{
			$status = "LOCK";
			$statusChangeSzaot = true;
		}

		if (isset($sumsheetParams["startDate"]) && isset($sumsheetParams["startLockDate"])) {
			$sumsheetParams["startDate"] = $sumsheetParams["startLockDate"];
		}
		if (isset($sumsheetParams["endDate"]) && isset($sumsheetParams["endLockDate"])) {
			$sumsheetParams["endDate"] = $sumsheetParams["endLockDate"];
		}

		$startDate = $sumsheetParams["startDate"] ?? date("Y-m-d");
        $endDate = $sumsheetParams["endDate"] ?? $startDate;

        $employeeContracts = [];

		if (isset($sumsheetParams["ecID"]) && $sumsheetParams["ecID"] !== "ALL") {
			$employeeContracts = [$sumsheetParams["ecID"]];
		} else {
			if ($summarysheetLocalMenuLimit) {
				$linesPerPage	= isset($linesOverride) ?: Yang::session('summarySheet_linesPerPage') ?: 50;
				$pageStart = $this->getPageStartLocalMenu();
				$this->setActiveEmployeesPage($pageStart,$linesPerPage);
				$activeEmployees = $this->activeEmployeesPage;
			}
			else {
				$activeEmployees = $this->activeEmployees;
			}
			
			$edit_himselfRight = App::hasRight($this->getControllerID(), "edit_himself");

			foreach ($activeEmployees as $employee) {
				if ($userECID === $employee["employee_contract_id"] && !$edit_himselfRight) { // only can req
				} else {
					$employeeContracts[] = $employee["employee_contract_id"];
				}
			}
		}
		$employeeContractsChunks = array_chunk($employeeContracts, 100);
		$otherErrorsSqlPart = "";
        $otv_valid['valid_from'] = $startDate;
        $otv_valid['valid_to'] = $endDate;

		foreach($employeeContractsChunks AS $employeeContracts)
		{
			$filter = "1";
			if (count($employeeContracts) > 0) {
                $absenceDays = EmployeeAbsence::getEmployeeAbsencesByInterval($employeeContracts, $startDate, $endDate);
				$filter .= " AND `employee_contract_id` IN ('" . implode("','", $employeeContracts) . "')";
			} else {
				$filter .= " AND 0";
			}


			if ($startDate === $endDate) {
				$filter .= " AND `day` = '" . $startDate . "'";
			} else {
                $filter .= " AND (`day` BETWEEN '" . $startDate . "' AND '" . $endDate . "')";
			}

			$delCalc = $filter;
			$originalFilter = $filter;
			$filterCalc = "";
			$newStatus = 0;
			$lockTypeException = (new GetEmployeeCalc())->getLockTypeException();

			if ($SettingDoNotLockOrangeCells || $summarysheetDoNotLockDaysPurpleCells) {
				$emMess = new GetEmployeeCalcMessage();
				$employeeCalcMessage = $emMess->get($startDate, $endDate, $employeeContracts);
				$gews = new GetEmployeeWorkSchedule($startDate, $endDate, $employeeContracts, true/*$skipCheckApprovers*/, "workForce", "1,2,4");
				$workSchedule = $gews->get();
			}

			if ($status === "LOCK" || $status === "SAVE" || $status === "CHANGEPR" || $status === "CHANGESZAOT" || $status === "CHANGEOT_TO_ABS") {
				$newStatus = $status === "LOCK" ? $lockedStatus : $savedStatus;

				try {
                    $timeCardCalculation = new TimeCardCalculation($employeeContracts, $startDate, $endDate, false);
                    $timeCardCalculation->payrollcalculation();
				} catch (Exception $e) {
                    Yang::log($e->getMessage(), 'error', 'system.SummarySheet.LocalMenu.modifyCalcStatus');
				}

				$gec = new GetEmployeeCalc();
				$employeeCalc = $gec->get($startDate, $endDate, $employeeContracts, "1,2,5,6");

                $GetLegalControlOvertime = new GetLegalControlOvertime($this->getControllerID(), $otv_valid, FALSE, "workForce", FALSE, TRUE, $employeeContracts);
                $getLegalControlMinDailyWorktime = new GetLegalControlMinDailyWorktime($this->getControllerID(), $otv_valid, FALSE, "workForce", TRUE, $employeeContracts);

                $dailyRegCounts = $this->getRegistrationsCount($startDate, $endDate, $employeeContracts);
				$isItTheSameDay = $startDate === $endDate;

				foreach ($employeeCalc as $ecID => $calcArr) {
					foreach ($calcArr as $date => $calcRows) {
						$worktime = 0;
                        $overtime = 0;
                        $draftOvertime = 0;
                        $balance = 0;
                        $frameworkBalance = 0;
                        $draftFrameworkBalance = 0;
						$currentDailyRegCount = 0;
						if (isset($dailyRegCounts[$ecID][$date]) && $dailyRegCounts[$ecID][$date]) {
							$currentDailyRegCount = $dailyRegCounts[$ecID][$date];
						}

						$haveOvertimeRequest = WorkScheduleOvertime::haveOvertimeRequest($ecID, $date);
						$overtime_violation = $GetLegalControlOvertime->getViolationByEcId($ecID);
						$daily_min_worktime_violation = $getLegalControlMinDailyWorktime->getViolationByEcId($ecID);

						$otherErrorsSqlPart .=
							($SettingDoNotLockOrangeCells && $isItTheSameDay === false
								?
								$this->getSqlWhereCondtionsForCellsNotToBeLockedIfOrangeExclamationMarkPresents($employeeCalcMessage, $workSchedule, $ecID, $calcRows, $date, $worktime, $overtime, $draftOvertime, $balance, $frameworkBalance, $draftFrameworkBalance, $workedTime)
								:
								"");

						$totalworktime = 0;
						foreach ($calcRows as $calcRow) {
							$hasAbsence = isset($absenceDays[$calcRow['employee_contract_id']][$calcRow["day"]]);
							$hasCalcError = !empty($calcRow["employee_calc_error_type_id"]) && (int) $calcRow["employee_calc_error_type_id"] != 0;
							if (
								($hasCalcError)
								|| $currentDailyRegCount > 0 || $haveOvertimeRequest || $hasAbsence
							) {
								//calculation Error Found !
								if (
									$hasAbsence &&
									($calcRow['status'] == $savedStatus ||
										$calcRow['status'] == $publishedStatus ||
										$calcRow['status'] == $draftStatus)
								) {
									if ($hasCalcError) {
										$absenceDays[$calcRow['employee_contract_id']][$calcRow["day"]]['employee_calc_error_type_id'] = (int) $calcRow["employee_calc_error_type_id"];
									}
								} else { // ez azért kell, hogy a hibás napokat ne zárolja
									// `status` != 5: mentett napot engedjen zárolni, hiába van employee_calc_error_type_id
									if ($calcRow['status'] != $lockedStatus) {
										$filter .= " AND IF(`employee_contract_id` = '" . $calcRow["employee_contract_id"] . "' ";
										if ($newStatus === $lockedStatus) {
											$filter .= "AND `status` != $savedStatus";
										}
										$filter .= ", `day` != '" . $calcRow["day"] . "', 1)";
									}
								}
							} elseif (
								$newStatus === $lockedStatus
								&& isset($overtime_violation[$calcRow["day"]])
								&& $overtime_violation[$calcRow["day"]]['display_type'] === LegalControl::DISPLAY_TYPE_ERROR
							) {
								$filter .= " AND IF(`employee_contract_id` = '" . $calcRow["employee_contract_id"] . "', `day` != '" . $calcRow["day"] . "', 1)";
							} elseif (
								$newStatus === $lockedStatus
								&& isset($daily_min_worktime_violation[$calcRow["day"]])
								&& $daily_min_worktime_violation[$calcRow["day"]]["display_type"] === LegalControl::DISPLAY_TYPE_ERROR
							) {
								$filter .= " AND IF(`employee_contract_id` = '" . $calcRow["employee_contract_id"] . "', `day` != '" . $calcRow["day"] . "', 1)";
							}
							$totalworktime += ($calcRow['status'] == $publishedStatus || $calcRow['status'] == $draftStatus) ? $calcRow['value'] : 0;
						}
						if (
							$maxWork12hError
							&& ($newStatus === $lockedStatus
								|| ($newStatus === $savedStatus
									&& $status !== "CHANGEPR"
									&& $status !== "CHANGESZAOT"))
							&& $totalworktime > (12 * 3600)
						) {
							$filter .= " AND IF(`employee_contract_id` = '" . $ecID . "', `day` != '" . $date . "', 1)";
						}
					}
					if (!empty($absenceDays[$ecID])) {
						foreach ($absenceDays[$ecID] as $day => $absence) {
							if (
								App::getSetting('summarySheetLockAbsences') == 1
								&& isset($absence['status'])
								&& $absence['status'] == $publishedStatus
								&& !in_array($absence['employee_calc_error_type_id'], [2, 60])
							) {
								continue;
							}

							$filter .= " AND IF(`employee_contract_id` = '" . $ecID . "' ";
							if ($newStatus === $lockedStatus) {
								$filter .= "AND `status` != $savedStatus";
							}
							$filter .= ", `day` NOT IN ('" . $day . "'), 1)";
						}
					}
				}

                if ($summarysheetDoNotLockDaysPurpleCells && $newStatus === $lockedStatus)
                {
                    $period = new DatePeriod(
                                    new DateTime($startDate),
                                    new DateInterval('P1D'),
                                    (new DateTime($endDate))->modify("+ 1 day")
                    );
                    foreach($employeeContracts as $employeeContract)
                    {
                        foreach($period as $periodDay)
                        {
                            $currentDay = $periodDay->format('Y-m-d');
                            $overtimeStatus = $workSchedule[$employeeContract][$currentDay]['overtime_status'] ?? null;
                            $standbyStatus = $workSchedule[$employeeContract][$currentDay]['standby_status'] ?? null;
                            $dutyStatus = $workSchedule[$employeeContract][$currentDay]['duty_status'] ?? null;
                            if ($overtimeStatus === 1 || $standbyStatus === 1 || $dutyStatus === 1)
                            {
                                $filter .= " AND IF(`employee_contract_id` = '" . $employeeContract . "', `day` != '" . $currentDay . "', 1)";
                            }
                        }
                    }
				}

				if ($newStatus === $savedStatus) {
					$filter .= " AND `status` IN ($draftStatus, $publishedStatus)";
				}

				$filterCalc .= $filter;

				if ($newStatus === $lockedStatus) {
					$filterCalc .= " AND IF(`inside_type_id` LIKE 'wt%%', `status` IN ($publishedStatus, $savedStatus), 1)";
					if (App::hasRight($this->getControllerID(), "sumsheet_autolock_balance")) {
						$filterCalc .= " AND IF(`inside_type_id` LIKE 'balance', `status` IN ($publishedStatus, $savedStatus), 1)";
					} else {
						$filterCalc .= " AND IF(`inside_type_id` LIKE 'balance', `status` IN ($savedStatus), 1)";
					}

					if (App::hasRight($this->getControllerID(), "sumsheet_autolock_overtime") || ($statusOvertimeBalancePr || $statusChangepr || $statusChangeSzaot)) {
						$filterCalc .= " AND IF(`inside_type_id` LIKE 'ot%%', `status` IN ($draftStatus, $publishedStatus, $savedStatus), 1)";
					} else {
						$filterCalc .= " AND IF(`inside_type_id` LIKE 'ot%%', `status` IN ($publishedStatus, $savedStatus), 1)";
					}
					if (App::hasRight($this->getControllerID(), "sumsheet_autolock_bot") || ($statusOvertimeBalancePr || $statusChangepr || $statusChangeSzaot)) {
						$filterCalc .= " AND IF(`inside_type_id` LIKE 'bot%%', `status` IN (1,$publishedStatus, $savedStatus), 1)";
					} else {
						$filterCalc .= " AND IF(`inside_type_id` LIKE 'bot%%', `status` IN ($publishedStatus, $savedStatus), 1)";
					}
				}

				$filter .= " AND `status` IN ($publishedStatus, $savedStatus)"; // lock only saved and unlocked statuses
			} elseif ($status === "UNLOCK") {
				$newStatus = 5;

				$filter .= " AND `status` IN (6)"; // unlock only locked!
				$filterCalc .= $filter;
			} elseif ($status === "DELSAVE") {
				/**
				 * Törli a employee_calc_absredempt az esetleges keretet a tárgynap(ok)ra.
				 */
				$sql = "
					DELETE FROM `employee_calc_absredempt` WHERE $filter
				";

				dbExecute($sql);

				$newStatus = 2;

				// Ha nincs zárolás feloldás jog adat törlésnél ne oldja fel a zárolt napot
				if (!$localmenuRight["localmenu_unlock"]) {
					$filter .= " AND `status` = 5 ";
				} else {
					$filter .= " AND `status` IN (5, 6) ";
				}

				if ((int)App::getSetting("summarySheet_lockedNotLockedSearch") && $searchFilter["lockedSearch"] == "no")
				{
					$end = (isset($sumsheetParams["endDate"])) ? $sumsheetParams["endDate"] : $sumsheetParams["startDate"];
					$usedDaytype = GetEmployeeCalcUsedDaytype::get($sumsheetParams["startDate"], $end, $employeeContracts);
					foreach($employeeContracts as $ecID)
					{
						foreach ($usedDaytype[$ecID] as $ecud)
						{
							if ($ecud[0]["status"] == Status::LOCKED) {
								$filter .= " AND (IF(`employee_contract_id` = '{$ecID}' AND `day` = '{$ecud[0]["day"]}', 0, 1) = 1) ";
							}
						}
					}
				}

				$filterCalc .= $filter;
			}

			if ($status === "CHANGEPR" || $statusChangepr) {
				$statusChangepr ? $premiumotStatus = ", status = $savedStatus" : $premiumotStatus ="";
				$sql = "
					UPDATE `employee_calc` SET `inside_type_id` = CONCAT('premium_', `inside_type_id`)$premiumotStatus WHERE $filterCalc AND `inside_type_id` LIKE 'ot%%'
				";

				dbExecute($sql);
			} elseif ($status === "CHANGESZAOT" || $statusChangeSzaot) {
				$statusChangeSzaot ? $szaotStatus = ", status = $savedStatus" : $szaotStatus ="";
				$sql = "
					UPDATE `employee_calc` SET `inside_type_id` = CONCAT(`inside_type_id`, 'sza')$szaotStatus WHERE $filterCalc AND `inside_type_id` LIKE 'ot%%'
				";

				dbExecute($sql);
			}

			$filterCalc .= "  AND `inside_type_id` NOT IN('" . implode("','", $lockTypeException) . "')  
			                  AND `status` NOT IN (". Status::DELETE_REQUEST .", ". Status::INVALID .")";

			#DEV-9330-4B
			if ($newStatus) {
                $updateSet = "`status` = {$newStatus}, {$modifiedSet}";
				dbExecute("UPDATE `employee_calc` SET {$updateSet} WHERE {$filterCalc} {$otherErrorsSqlPart}");
				dbExecute("UPDATE `employee_calc_used_daytype` SET {$updateSet} WHERE {$filter} {$otherErrorsSqlPart}");
				dbExecute("UPDATE `employee_calc_message` SET {$updateSet} WHERE {$filter} {$otherErrorsSqlPart}");
				dbExecute("UPDATE `employee_calc_other_states` SET {$updateSet} WHERE {$filter} {$otherErrorsSqlPart}");
				#DEV-9330-4E

				$newStatus = $newStatus === $savedStatus ? $publishedStatus : $newStatus;
				$filter.=" AND `status`!=" . Status::DELETED;
                $updateAllSet = [
                    'status' => $newStatus,
                    'modified_on' => date('Y-m-d H:i:s'),
                    'modified_by' => $this->userIdToSwitch
                ];
				EmployeeExtraHours::model()->updateAll($updateAllSet, $filter);
				WorkScheduleOvertime::model()->updateAll($updateAllSet, $filter);

				if ($status === "LOCK") {
					$selDel = "
                        DELETE FROM
                                `employee_calc`
                        WHERE
                                $delCalc 
                            AND (`inside_type_id` NOT IN ('" . implode("','", $lockTypeException) . "') 
                                OR (`inside_type_id` IS NULL)
                            ) 
                            AND `status` >= ".Status::DRAFT." 
                            AND `status` < ".Status::DELETED."
					";
					dbExecute($selDel);
				}

				if (App::getSetting('showAttendanceSheetNotifictionAfterLogin') == '1') {
					$aas = new AttendanceSheetStatusAction($this->filters);
					if ($newStatus === $lockedStatus) {
						$aas->attendanceSheetStatusLock();
					} elseif ($newStatus === $publishedStatus) {
						$aas->attendanceSheetStatusUnlock();
					}
				}

				$message = "Log: Update EmployeeCalc user: " . userID() .
						" startDay: " . $sumsheetParams["startDate"];
				$message .= isset($sumsheetParams["endDate"]) ? " endDay: " . $sumsheetParams["endDate"] : "";
				$message .= " employeeContracts: ";
				$message .= (count($employeeContracts) > 0) ? implode("','", $employeeContracts) : "ALL";
				$message .= " status: " . $status;
				echo Yang::log($message, 'log', 'system.SummarySheet.LocalMenu');
			}

			/**
			 * A szabadság megváltási keretet tartalmazó employee_calc_absredempt táblába keszúra az esetlegesen képződő
			 * keretet, majd módosítja az employee_calc táblát a megfelelő inside_type_id-val.
			 */
			$absRedemptSecs = "SUM(`value`)";
			if (App::getSetting('absredemptAddHalfHour') == '1') {
				$absRedemptSecs = "IF(SUM(`value`) >= 5400,
										SUM(`value`) + 1800,
										SUM(`value`))
						";
			}

			if ($status === "CHANGEOT_TO_ABS") {
				$sql = "
					INSERT INTO `employee_calc_absredempt` (
                        SELECT NULL, `employee_contract_id`, `day`, " .
                            $absRedemptSecs . ", 2, '" . userID() . "', '" . date("Y-m-d H:i:s") . "', NULL, NULL, NULL 
                        FROM `employee_calc`
                        WHERE $originalFilter 
                            AND `status` = 5 
                            AND LEFT(`inside_type_id`, 2) LIKE 'ot' 
                        GROUP BY `employee_contract_id`, `day`
                    )
					ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), `modified_by` = '" . $this->userIdToSwitch . "', `modified_on` = '" . date("Y-m-d H:i:s") . "'
				";
				dbExecute($sql);

				$sql = "
					UPDATE `employee_calc` 
					    SET `inside_type_id` = CONCAT('absredempt_wt', SUBSTRING(`inside_type_id`, 3)), 
					        {$modifiedSet}
						WHERE $originalFilter 
						  AND `status` = 5 
						  AND LEFT(`inside_type_id`, 2) LIKE 'ot' 
						  AND LEFT(`inside_type_id`, 3) NOT LIKE 'otw' 
						  AND LEFT(`inside_type_id`, 5) NOT LIKE 'otstw'
				";
				dbExecute($sql);

				$sql = "
					UPDATE `employee_calc` 
					    SET `inside_type_id` = CONCAT('absredempt_ot', 
					                            IF(
					                                LEFT(`inside_type_id`, 3) = 'otw', 
					                                SUBSTRING(`inside_type_id`, 4), 
					                                SUBSTRING(`inside_type_id`, 3)
					                            )
					                        ),
					        {$modifiedSet}
						WHERE $originalFilter 
						  AND `status` = 5
						  AND (LEFT(`inside_type_id`, 3) LIKE 'otw' OR LEFT(`inside_type_id`, 5) LIKE 'otstw')
				";
				dbExecute($sql);
			}

			if (   $status === "DELSAVE"
                || $status === "UNLOCK"
                || (
                        $status === "LOCK"
                    && (
                        isset($sumsheetParams["saved"])
                        && (int) $sumsheetParams["saved"] === 1
                    )
                )
            ) {
				if ($status === "UNLOCK") {
					$newStatus = Status::SAVED;
				} elseif ($status === "DELSAVE") {
					$newStatus = Status::DRAFT_DELETE;
				} elseif ($status === "LOCK") {
					$newStatus = Status::LOCKED;
				}
				/**
				 * Napi keretegyenleg törlésre állítása.
				 */
				$dbsvSQL = "
					UPDATE
						`daily_balance_saved_values`
					SET
						`status` = {$newStatus},
						`modified_by` = '" . $this->userIdToSwitch . "',
						`modified_on` = NOW()
					WHERE
							$originalFilter
						AND `status` IN ('6', '5')
				";

				dbExecute($dbsvSQL);
			}
			if(Yang::getParam('customerDbPatchName') == "sealing")
			{
				$this->lockLeadingCell(
                    $sumsheetParams["startDate"],
                    $sumsheetParams["endDate"],
                    $employeeContracts,
                    $status
                );
			}
		}
        return true;
	}

    private function getRegistrationsCount($startDate, $endDate, $employeeContracts)
    {
        $publishedStatus = Status::PUBLISHED;
        $draftStatus = Status::DRAFT;
        $deleteRequestStatus = Status::DELETE_REQUEST;
        $waitModifyStatus = Status::WAIT_MODIFY;
        $defaultEnd = App::getSetting("deafultEnd");
        $SQL = "SELECT
            ec.`employee_contract_id`,
            cal.date,
            COUNT(reg.row_id) AS reg_count
        FROM `calendar` cal
        LEFT JOIN `employee` e ON
            e.`status` = $publishedStatus
            AND (cal.date BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '$defaultEnd'))
        LEFT JOIN `employee_contract` ec ON
                ec.`employee_id`=e.`employee_id`
            AND ec.`status`= $publishedStatus
            AND cal.date BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '$defaultEnd')
            AND cal.date BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '$defaultEnd')
        LEFT JOIN `employee_card` ecard ON
                ecard.`employee_contract_id`=ec.`employee_contract_id`
            AND ecard.`status`= $publishedStatus
            AND cal.date BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '$defaultEnd')
        LEFT JOIN `employee_calc_used_daytype` ecud ON
                ecud.`employee_contract_id`=ec.`employee_contract_id`
            AND ecud.`day`=cal.date
            AND ecud.`status`= $publishedStatus
        LEFT JOIN `registration` reg ON
                reg.`card`=ecard.`card`
            AND reg.`status` IN ($draftStatus,$deleteRequestStatus,$waitModifyStatus)
            AND reg.`time` BETWEEN ecud.`reg_filter_from` AND ecud.`reg_filter_to`
        WHERE
            cal.date BETWEEN '$startDate' AND '$endDate'
            AND ec.`employee_contract_id` IN ('" . implode("','", $employeeContracts) . "')
            AND ec.`employee_contract_id` != ''
            AND reg.`row_id` IS NOT NULL
        GROUP BY
            ec.`employee_contract_id`,
                cal.date
        ";

        $results = dbFetchAll($SQL);

        $dailyRegCounts = [];
        foreach ($results as $reg) {
            $dailyRegCounts[$reg["employee_contract_id"]][$reg["date"]] = $reg["reg_count"];
        }
        return $dailyRegCounts;
    }

	private function lockLeadingCell($fromDate,$toDate,$employeeContracts,$status) 
	{
		if($status == "LOCK" || $status == "UNLOCK" || $status == "DELSAVE")
		{
			$sql = "
			SELECT employee_contract_id, day FROM employee_calc
			WHERE
			`day` BETWEEN '" . $fromDate . "' AND '" . $fromDate . "'
			AND `employee_contract_id` IN ('" . implode("','", $employeeContracts) . "')
			";
		
			$ecDatas = dbFetchAll($sql);
			foreach($ecDatas as $ecData ) 
			{
				$lcr = LeadingCellRecording::model()->findByAttributes([
					"employee_contract_id" => $ecData['employee_contract_id'],
					"day" => $ecData['day']
				]);
				if ($lcr)
				{
					$lcr->status = ($status=="LOCK") ? Status::LOCKED : Status::PUBLISHED;
					$lcr->save();
				}
			}
		}
			
	}

	/**
	 * Azoknál a celláknál, ahol naracssárga felkiáltójel van, nem engedi a tömeges zárást, hogy azokat manuálisan kelljen lezárni. Stricker kérte.
	 * A metódus az ezt megvalósító where-be ágyazandó SQL részletet állítja össze, azaz mely szerződésszám-nap párok azok, ahol van ranacssárga felkiáltó jel.
	 *
	 * Csak akkor hívódik meg, ha App::getSetting("doNotLockDaysMarkedOrange") == 1
	 * #DEV-9330
	 */
	private function getSqlWhereCondtionsForCellsNotToBeLockedIfOrangeExclamationMarkPresents($employeeCalcMessage, $workSchedule, $ecID, $calcRows, $date, &$worktime, &$overtime, &$draftOvertime, &$balance, &$frameworkBalance, &$draftFrameworkBalance, &$workedTime)
	{
		$scheduleForDay = valueOrNull($workSchedule[$ecID][$date]) ?: [];
		$needToWork = $scheduleForDay["used_full_work_time"];
		foreach ($calcRows as $calc) {
			$calcValue = $calc["value"] == "" ? 0 : $calc["value"];
			$calcStatus = $calc["status"];
			$x = $calc["inside_type_id"];
			$presencePrefix = substr($x, 0, 2);                                                  //	ot
			$presencePrefix = $presencePrefix == "bo" ? substr($x, 0, 3) : $presencePrefix;     //	bot
			$presencePrefix = $presencePrefix == "ba" ? substr($x, 0, 7) : $presencePrefix;     //	balance*
			$presencePrefix = $presencePrefix == "ab" ? substr($x, 0, 13) : $presencePrefix;    //	absredempt_wt*
			$presencePrefix = $presencePrefix == "le" ? $x : $presencePrefix;                   //	leavingWorktime
			$presencePrefix = $presencePrefix == "pr" ? "premium" : $presencePrefix;            //	premiumHours

			switch ($presencePrefix) {
				case 'bot':
					$frameworkBalance += $calcValue;
					break;
				case 'balance':
					if ($calcStatus <> 1) {
						$balance += $calcValue;
					}
					break;
				case 'wt':
				case 'leavingWorktime':
				case 'absredempt_wt':
					$worktime += $calcValue;
					break;
				case 'ot':
				case 'absredempt_ot':
					if (!oneof($calcStatus, [
						Status::DRAFT,
						Status::PUBLISHED,
						Status::SAVED,
						Status::LOCKED,
					])) {
						break;
					}
					$overtime += $calcValue;
					break;
			}
		}
		$skippable = false;
		foreach ($employeeCalcMessage[$ecID] as $currentDay => $subArray) {
			if ($date == $currentDay) {
				foreach ($subArray as $subSubArray) {
					if ($subSubArray["employee_contract_id"] == $ecID) {
						$messageId = $subSubArray["message_id"];
						$skippable = $messageId == "ordinaryTimeViolation";             // && strpos($messageId, 'Flexible') !== false
						if ($skippable) {
							break(2);
						}
					}
				}
			}
		}

		$workedTime = $worktime + $overtime + $draftOvertime + $balance + $frameworkBalance + $draftFrameworkBalance;
		$skippable = !($skippable === false) || $needToWork > $workedTime;

		if ($skippable) {
			$otherErrorsSqlPart = " AND not(`day` = '" . $date . "' AND employee_contract_id = '$ecID')";
		}

		return $otherErrorsSqlPart ?? "";
	}


}
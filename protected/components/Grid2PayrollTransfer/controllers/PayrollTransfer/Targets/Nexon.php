<?php

trait Nexon
{
	private function createNexonPayroll($conn, $report, $valid_from, $valid_to, $lastMonthPtr, $valid, $thisMonthPtr, $lastMonthIdoadat, $originalValidFrom) {
		Yang::log("createNexonPayroll", 'log', 'system.PT');

		$cust = App::getSetting("ptr_customer");
        $csgFileCreate = App::getSetting("nexon_csg_file_create");
		$ptrCreateDate = new DateTime($valid);

		$forceOriginalSchedule = $cust==="ROSENBERGER" && (int)requestParam('forceOriginalSchedule');

		$db=Yang::getParam('dbName');
        $fileDateFormat = App::getSetting("payrollFileDateFormat");
        $fileDate = !empty($fileDateFormat)  ? date($fileDateFormat) : "";
		$folderDate = date('YmdHis');
		$month = strtolower(date("F", strtotime($valid_from)));
		$monthName = ucfirst(Dict::getValue($month));
		$year = date("Y", strtotime($valid_from));
		$prtID = $this->prtID;
		$path = Yang::getAlias('webroot').DIRECTORY_SEPARATOR."upload".DIRECTORY_SEPARATOR."PayrollTransfer".
				DIRECTORY_SEPARATOR.$db.DIRECTORY_SEPARATOR.$folderDate;
		$pointerFilePath = Yang::getAlias('webroot').DIRECTORY_SEPARATOR."upload".DIRECTORY_SEPARATOR."PayrollTransfer".
				DIRECTORY_SEPARATOR.$db;

		$writeFiles = false;
		if (is_writable(Yang::getAlias('webroot').DIRECTORY_SEPARATOR."upload".DIRECTORY_SEPARATOR."PayrollTransfer")) {
			$writeFiles = true;
		}

		if ($writeFiles && !is_dir($path)){
			mkdir($path, 0777, true);
		}

		if ($thisMonthPtr["ptrCorrection"]) {
			$currentLockedData = [];
		}

		$fgID = $prtID;

		if ($cust === "KATLAS") {
			// Check for quiting employee
			if ($this->quitingEmployee == "yes") {
				$eccheck = "AND `ec_valid_to` = '{$valid}' AND employee_calc_payroll.`date` <= `ec_valid_to`";
			} else {
				$eccheck = "AND employee_calc_payroll.`date` <= `ec_valid_to` AND `ec_valid_to` >= `search_filter_to`";
			}


			// Get overtimes
			$dailyOT = "
				SELECT
					`payroll_emp_id` AS `id`,
					`date`,
					value_sum_payroll AS value
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` = 'TA'
						AND `payroll_emp_id` IS NOT NULL
						AND `value_sum_payroll` > 0
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					`transfer_id_2`,
					`payroll_emp_id`,
					IF(`total_sum_2`, '', `value_sum_payroll`)
			";

			$katlasDailyOT = $conn->createCommand($dailyOT)->queryAll();
			$employeeOTs = [];
			$employeeOTSums = [];
			foreach ($katlasDailyOT as $dataRows) {
				$employeeOTs[$dataRows["id"]][$dataRows["date"]] = $dataRows["value"];
				if (!isset($employeeOTSums[$dataRows["id"]])) {
					$employeeOTSums[$dataRows["id"]] = $dataRows["value"];
				} else {
					$employeeOTSums[$dataRows["id"]] += $dataRows["value"];
				}
			}

			// Get Daily DT time vs worktime difference
			$dailyDTWTDiff = "
				SELECT
					`payroll_emp_id` AS `id`,
					`date`,
					IF(`is_public_holiday` = '1' AND `wsu_type_of_daytype` <> 'RESTDAY',  '0', IF(`dt_daily_worktime` > `dt__worktime_sum` AND `dt__worktime_sum` > 0, IF(`state_type_id` IS NOT NULL,
						'0',
						`dt_daily_worktime` - `dt__base_worktime_sum`
					), '0' ) ) AS value
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";

			$katlasDailyDTWTDiff = $conn->createCommand($dailyDTWTDiff)->queryAll();
			$employeeDTWTDiffs = [];
			$employeeDTWTDiffSums = [];
			foreach ($katlasDailyDTWTDiff as $dataRows) {
				$employeeDTWTDiffs[$dataRows["id"]][$dataRows["date"]] = $dataRows["value"];
				if (!isset($employeeDTWTDiffSums[$dataRows["id"]])) {
					$employeeDTWTDiffSums[$dataRows["id"]] = $dataRows["value"];
				} else {
					$employeeDTWTDiffSums[$dataRows["id"]] += $dataRows["value"];
				}
			}

			$tableValues = [];
			// Do the MAGIC
			foreach ($employeeDTWTDiffs as $empId => $data)
			{
				foreach ($data as $day => $dtWTVal)
				{
					if (isset($employeeOTSums[$empId]) && $employeeOTSums[$empId] > 0 && $dtWTVal > 0)
					{
						if ($dtWTVal >= $employeeOTSums[$empId])
						{
							$tableValues[$empId][$day]["worktime_ot_add"] = $employeeOTSums[$empId];
							$tableValues[$empId][$day]["ot_used_for_dt_wt"] = "0";
							$employeeOTSums[$empId] = 0;
						} else {
							$tableValues[$empId][$day]["worktime_ot_add"] = $dtWTVal;
							$tableValues[$empId][$day]["ot_used_for_dt_wt"] = "0";
							$employeeOTSums[$empId] = $employeeOTSums[$empId] - $dtWTVal;
						}
					} else {
						$tableValues[$empId][$day]["ot_used_for_dt_wt"] = "0";
						$tableValues[$empId][$day]["worktime_ot_add"] = "0";
					}
				}
			}

			// Reset OT Sums
			$employeeOTSums = [];
			foreach ($katlasDailyOT as $dataRows) {
				if (!isset($employeeOTSums[$dataRows["id"]])) {
					$employeeOTSums[$dataRows["id"]] = $dataRows["value"];
				} else {
					$employeeOTSums[$dataRows["id"]] += $dataRows["value"];
				}
			}

			foreach ($employeeOTs as $empId => $data)
			{
				if ($employeeDTWTDiffSums[$empId] > 0 && $employeeOTSums[$empId] > 0)
				{
					foreach ($data as $day => $otVal)
					{
						if ($otVal >= $employeeDTWTDiffSums[$empId])
						{
							$tableValues[$empId][$day]["ot_used_for_dt_wt"] = $employeeDTWTDiffSums[$empId];
							$employeeDTWTDiffSums[$empId] = 0;
						} else {
							$tableValues[$empId][$day]["ot_used_for_dt_wt"] = $otVal;
							$employeeDTWTDiffSums[$empId] = $employeeDTWTDiffSums[$empId] - $otVal;
						}
					}
				}
			}
			$SQL = "TRUNCATE `payroll_transfer_dt_wt_diff`;";
			$conn->createCommand($SQL)->execute();
			$insertSQL = "INSERT INTO `payroll_transfer_dt_wt_diff` (`emp_id`, `date`, `ot_used_for_dt_wt`, `worktime_ot_add`) VALUES";
			$foundInsert = false;
            foreach ($tableValues as $empId => $data)
            {
                foreach ($data as $day => $val)
                {
					$foundInsert = true;
                    $insertSQL .= "('" . $empId . "', '" . $day . "', " . $val["ot_used_for_dt_wt"] . ", " . $val["worktime_ot_add"] . "),\n";
                }
            }
            $insertSQL = substr_replace($insertSQL, ";", -2);
            if ($foundInsert) { $conn->createCommand($insertSQL)->execute(); }
			$SQL_NBMunkr = "
				SELECT
					`employee_contract_id`,
					`date_day_of_week`,
					`payroll_emp_id` AS `id`,
					'' AS `rel`,
					`wage_type`,
					`schedule_type`,
					`wsu_type_of_daytype`,
					`workgroup_name`,
					`is_public_holiday`,
					`is_holiday_workday`,
					`is_holiday_transferred_holiday`,
					`state_type_id`,
					IF(
						`has_absence` = 1 AND `state_type_transfer_id` IS NOT NULL AND `is_restday` = 0
						,
						`state_type_transfer_id`
						,
						`timedata_type`
					) AS `timedata_type`,
					`daily_worktime`,
					concat('',
						TRUNCATE(
							`dt_daily_worktime`/3600
						,0)
					) AS hours,
					concat('',
						TRUNCATE(
							`dt_daily_worktime_origi`/3600
						,0)
					) AS hours_origi,
					`date`,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `to`,
					`search_filter_from`,
					`search_filter_to`,
					mnth__has_allowance,
					`employee_calc_status`,
					`day_transferable`,
					`mnth__total_error_code`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";

		}
        else {
			if ($this->quitingEmployee == "yes") {
				$eccheck = "AND `ec_valid_to` BETWEEN '{$valid_from}' AND '{$valid_to}'";
				if ($cust === "DREHER" || $cust === "HUMANBIO" || $cust === "CARLZEISS") {
					$eccheck = "AND `ec_valid_to` BETWEEN '{$valid_from}' AND '{$valid_to}' AND `date` <= `ec_valid_to`";
				}
				if ($cust === "FLEX") {
					$eccheck = "";
				}
			}
            elseif ($this->quitingEmployee == "withoutQuiters") {
				$eccheck = "AND `date` <= `ec_valid_to` AND `ec_valid_to` >= `search_filter_to`";
			}
            else {
				$eccheck = "";
				if ((int)App::getSetting("ptr_OnlyQuitingEmployees")) {
					$eccheck = "AND `date` <= `ec_valid_to` AND `ec_valid_to` >= `search_filter_to`";
				}
			}
			if ($cust === "NEMAK") {
				$restDayCheck = "AND `is_restday` = 0";
			} else {
				$restDayCheck = "";
			}
			if ($cust === "VIDEOTON" || $cust === "HUMANBIO" || $cust === "KUEHNENAGEL" || $cust === "HAFNER"
                || $cust === "EIF" || $cust === "HANON"  || $cust === "HAVI"  || $cust === "HOPI") {
				$hours = "
					REPLACE( CONCAT('',FORMAT(dt_daily_worktime/3600, 2)), '.',  ',') AS hours,
				";
			}
            elseif($cust === "CARLZEISS") {
                $hours = "
                concat('',
                            IF(
                                TRUNCATE(dt_daily_worktime/3600,0) = TRUNCATE(dt_daily_worktime/3600,1),
                                CONCAT('',TRUNCATE(dt_daily_worktime/3600,0)),
                                REPLACE( CONCAT('',TRUNCATE(FLOOR(dt_daily_worktime/3600 * 2) / 2,1)), '.',  ',')
                            )
                            ) AS hours,
                            ";
            }
            else
            {
				$hours = "
					concat('',
						TRUNCATE(
							`dt_daily_worktime`/3600
						,0)
					) AS hours,
				";
			}
			$SQL_NBMunkr = "
				SELECT
					`company_id`,
					`employee_contract_id`,                    
                    `ec_daily_worktime`,
					`date_day_of_week`,					
					`payroll_emp_id` AS `id`,
					'' AS `rel`,
					`inside_type_id`,
					`schedule_type`,
					`wsu_type_of_daytype`,
					`is_restday`,
					`is_public_holiday`,
					`is_holiday_workday`,
					`is_holiday_transferred_holiday`,
					`state_type_id`,
					`has_absence`,
					`state_type_transfer_id`,
					`is_state_type_worktime`,
					IF(
						`has_absence` = 1 AND `state_type_transfer_id` IS NOT NULL {$restDayCheck}
						,
						`state_type_transfer_id`
						,
						`timedata_type`
					) AS `timedata_type`,
					`daily_worktime`,
					{$hours}
					concat('',
						TRUNCATE(
							`dt_daily_worktime_origi`/3600
						,0)
					) AS hours_origi,
					`date`,
					`dt__base_worktime_sum`,
					`dt__base_abstime_sum`,
					`dt_daily_worktime`,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `to`,
					`search_filter_from`,
					`search_filter_to`,
					mnth__has_allowance,
					`employee_calc_status`,
					`day_transferable`,
					`wg_daily_worktime`,
					`mnth__total_error_code`,
					`option2`,
					`dt__total_wtej_sum`,
					`dt__total_otej_sum`,
					`wage_type`,
					`daytype_id`,
					`dt__error_code`,
					`fullname`,
					`unit_name`,
					`employee_contract_number`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}



		$SQL = "
			CREATE TEMPORARY TABLE IF NOT EXISTS
				`temp_payroll_transfer_NBMunkr`
					(
						KEY IDX_ecid_sff_sft (`employee_contract_id`, `search_filter_from`, `search_filter_to`),
						KEY IDX_id (`id`)
					)
				ENGINE=MyISAM
			AS (
				$SQL_NBMunkr
			);
		";

		$conn->createCommand($SQL)->execute();

        switch ($cust)
        {
            case "KATLAS" : $sqlNBMunkr = $this->getNBMunkrKatlas(); break;
            case "NEMAK" : $sqlNBMunkr = $this->getNBMunkrNemak(); break;
            case "BT" : $sqlNBMunkr = $this->getNBMunkrBt(); break;
            case "HUMANBIO" : $sqlNBMunkr = $this->getNBMunkrHumanbio(); break;
            case "KUEHNENAGEL" : $sqlNBMunkr = $this->getNBMunkrHumanbio(); break;
            case "DREHER" : $sqlNBMunkr = $this->getNBMunkrDreher(); break;
            case "CARLZEISS" : $sqlNBMunkr = $this->getNBMunkrCarlZeiss(); break;
            case "SANMINA" : $sqlNBMunkr = $this->getNBMunkrSanmina(); break;
            case "FLEX" : $sqlNBMunkr = $this->getNBMunkrFlex(); break;
            case "SERTEC" : $sqlNBMunkr = $this->getNBMunkrSertec(); break;
            case "VIDEOTON" : $sqlNBMunkr = $this->getNBMunkrSertec(); break;
            case "STX" : $sqlNBMunkr = $this->getNBMunkrStx(); break;
            case "PERI" : $sqlNBMunkr = $this->getNBMunkrPeri(); break;
            case "AUTONEUM" : $sqlNBMunkr = $this->getNBMunkrAutoneum(); break;
            case "HAFNER" : $sqlNBMunkr = $this->getNBMunkrHafnerShuRefmon(); break;
            case "EIF" : $sqlNBMunkr = $this->getNBMunkrHafnerShuRefmon(); break;
            case "CYMENT" : $sqlNBMunkr = $this->getNBMunkrHafnerShuRefmon(); break;
            case "REFMON" : $sqlNBMunkr = $this->getNBMunkrHafnerShuRefmon(); break;
            case "ANDAPRESENT": $sqlNBMunkr = $this->getNBMunkrAndapresent(); break;
            case "BOURNS": $sqlNBMunkr = $this->getNBMunkrBourns(); break;
            case "MAGNA": $sqlNBMunkr = $this->getNBMunkrMagna(); break;
            case "VISTRA": $sqlNBMunkr = $this->getNBMunkrMagna(); break;
            case "HANON": $sqlNBMunkr = $this->getNBMunkrHanon(); break;
            case "HAVI": $sqlNBMunkr = $this->getNBMunkrHavi(); break;
            case "HOPI": $sqlNBMunkr = $this->getNBMunkrHopi(); break;
            default: $sqlNBMunkr = $this->getNBMunkrDefault();
        }

		if (!$this->correctLastMonth && !$this->correctLastTwoMonth)
		{
			$res_NBMunkr_CSV = $conn->createCommand($sqlNBMunkr)->queryAll();
			if ($thisMonthPtr["ptrCorrection"]) {
				foreach ($res_NBMunkr_CSV as $schedule) {
					$currentLockedData[$schedule["id"]]["schedule"][$schedule["from"]] = $schedule["schedule_type"];
				}
			}

			$fileNameNBMunkr = "NBMunkr$fileDate.csv";
			$csv = ArrayToCsv::getCsvText($res_NBMunkr_CSV);
			$csv = iconv("UTF-8", "ISO-8859-1//TRANSLIT", $csv);
			$fs = new FS;
			$fs->disableMySQLStore();
			$fs->setFileGroupID($fgID);
			$fs->uploadTextFileFromContent($fileNameNBMunkr, $csv, "text/csv");
			$fileNameNBMunkr_fileId = $fs->getSmallFileID() . "_" .$fileNameNBMunkr;
			if ($writeFiles) {
				$file = fopen($path.DIRECTORY_SEPARATOR.$fileNameNBMunkr, 'w');
				fwrite($file, $csv);
				fclose($file);
			}
		}

		if ($cust === "KATLAS")
		{
			$SQL_NBMunkrOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(employee_calc_payroll.`date`,'%Y.%m.%d.') AS `date`,
					IF(`is_public_holiday` = '1' AND ((`wage_type` = 'OB' AND `dt__worktime_sum` = 0 AND `date_day_of_week` NOT IN ('7', '1')) OR employee_calc_payroll.`workgroup_name` LIKE '%24/72%'),
					concat('',
						REPLACE(w.`daily_worktime`, '.',  ',')
					),
					IF(`state_type_id` IS NOT NULL,
						concat('',
							IF(
								TRUNCATE(dt__worktime_sum/3600,0) = TRUNCATE(dt__worktime_sum/3600,1),
								CONCAT('',TRUNCATE(dt__worktime_sum/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__worktime_sum/3600 * 2) / 2,1)), '.',  ',')
							)
						),
						concat('',
							IF(
								TRUNCATE((dt__base_worktime_sum + worktime_ot_add)/3600,0) = TRUNCATE((dt__base_worktime_sum + worktime_ot_add)/3600,1),
								CONCAT('',TRUNCATE((dt__base_worktime_sum + worktime_ot_add)/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR((dt__base_worktime_sum + worktime_ot_add)/3600 * 2) / 2,1)), '.',  ',')
							)
						)
					)) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				LEFT JOIN
					`payroll_transfer_dt_wt_diff` dt_wt_diff ON
							dt_wt_diff.`emp_id` = employee_calc_payroll.`payroll_emp_id`
						AND dt_wt_diff.`date` = employee_calc_payroll.`date`
				LEFT JOIN
					`workgroup` w ON
							w.`workgroup_id` = employee_calc_payroll.`workgroup_id`
						AND w.`status` = " . Status::PUBLISHED . "
						AND employee_calc_payroll.`date` BETWEEN w.`valid_from` AND w.`valid_to`
				WHERE
					employee_calc_payroll.`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "BT") {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					'0', '1', '3', '4'
				UNION
				SELECT
					`company_id`,
					`id`,
					`from`,
					TRUNCATE(`hours`,0)
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
		}
        elseif ($cust === "HUMANBIO" || $cust === "KUEHNENAGEL") {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
					'' AS company_id,
					`id`,
					`rel`,
					`from`,
					`hours` as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
		}
        elseif ($cust === "DREHER") {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
					`company_id`,
					`id`,
					IFNULL(`option2`, '1'),
					`from`,
					TRUNCATE(`hours`,0)
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
		}
        elseif ($cust === "CARLZEISS") {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
					'' AS `company_id`,
					`id`,
					`rel`,
					`from`,
					`hours`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
		}
        elseif ($cust === "SANMINA") {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					IF(`is_public_holiday` = '1' AND `date_day_of_week` NOT IN ('7', '1') AND `wage_type` = 'OB', TRUNCATE(IF(`wg_daily_worktime`= 43200, 28800, `wg_daily_worktime`)/3600,0), TRUNCATE(`hours`,0))
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
		}
        elseif ($cust === "FLEX") {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `from`,
					CONCAT('',FORMAT(`dt_daily_worktime`/3600, 1)) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					employee_calc_payroll.`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "XYLEM") {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					IF(`is_public_holiday` = '1' AND `date_day_of_week` NOT IN ('7', '1'), TRUNCATE(`wg_daily_worktime`,0), TRUNCATE(`hours`,0)) as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
		}
        elseif ($cust === "VIDEOTON" || $cust === "EIF") {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					`hours` as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
		}
        elseif ($cust === "HANON") {
            $SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					IF( (`wage_type` = 'OB' AND `is_public_holiday` = 1 AND `date_day_of_week` IN (2,3,4,5,6) AND `is_restday` = 1), 
					    REPLACE( CONCAT('', FORMAT(`ec_daily_worktime`/3600, 2)), '.',  ','),
					    `hours`
					) as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        elseif ($cust === "HAVI") {
            $SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					CASE 
					    WHEN (`wage_type` = 'OB' AND `is_public_holiday` = 1 AND `is_restday` = 1) THEN 
					        CASE 
					            WHEN `date_day_of_week` IN (1,7) THEN '0,00' 
					            ELSE  REPLACE( CONCAT('', FORMAT(`ec_daily_worktime`/3600, 2)), '.',  ',')
                            END 
					    ELSE `hours`
					END as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        elseif ($cust === "HAFNER") {
            $SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					IF(`state_type_id` = '29337d9204baca9588942e162d229087', '0,00', `hours`) as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        elseif ($cust === "STX") {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					`fullname`,
					`unit_name`,
					`id`,
					`rel`,
					`from`,
					TRUNCATE(`hours`,0) as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        elseif ($cust === "AUTONEUM") {
            $SQL_NBMunkrOra_CSV = "
                SELECT
                    `id`,
                    `rel`,
                    `from`,
                    IF(employee_calc_payroll.`is_holiday` = '1' AND ((employee_calc_payroll.`wage_type` = 'OB' AND `dt__worktime_sum` = 0 AND employee_calc_payroll.`date_day_of_week` NOT IN ('7', '1')) OR employee_calc_payroll.`workgroup_name` LIKE '%24/72%'),
                    concat('',
                        REPLACE(w.`daily_worktime`, '.',  ',')
                    ),
                    TRUNCATE(`hours`,0)
                    ) AS hours
                FROM
                    `temp_payroll_transfer_NBMunkr`
                LEFT JOIN
                    `temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll ON
                    employee_calc_payroll.`date` = `from`
                    AND employee_calc_payroll.`payroll_emp_id` = `id`
                LEFT JOIN
                    `workgroup` w ON
                            w.`workgroup_id` = employee_calc_payroll.`workgroup_id`
                        AND w.`status` = " . Status::PUBLISHED . "
                        AND employee_calc_payroll.`date` BETWEEN w.`valid_from` AND w.`valid_to`
                WHERE
                   1

            ";
        }
        elseif ($cust === "LEGRAND") {
            $SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					IF(schedule_type = 3, daily_worktime, TRUNCATE(`hours`,0)) as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        elseif ($cust === "PERI") {
            $SQL_NBMunkrOra_CSV = "SELECT
                            `id`,
                            `from`,
                            TRUNCATE(`hours`,0) as hours
                        FROM
                            `temp_payroll_transfer_NBMunkr`
                        WHERE
                            1 /*`date` <= CURDATE()*/
                    ";
        }
        elseif ($cust === "BOURNS") {
            $SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`employee_contract_number`,
					`from`,
					if( `state_type_id` IN (
                            'def32968390fb987c823da0cbf7d3bd8', /* Igayolatlan*/
                            '269b59b4efbbb4ef1d527492dc06cb60', /* Igazolt de nem fizetett*/
                            '628834878c5bc72f60283e37865678b6'  /* Fizetésnélküli szabadságon*/
					    ) OR (`has_absence` = 1 AND `is_state_type_worktime` = 1),
					    REPLACE( CONCAT('',FORMAT(`dt_daily_worktime`/3600, 0)), '.',  ','),
					    REPLACE( CONCAT('',FORMAT(`dt__base_worktime_sum`/3600, 2)), '.',  ',')
					) as hours					
					
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        elseif ($cust === "MAGNA") {
            $SQL_NBMunkrOra_CSV = "
				SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
					'' AS `company_id`,
					`id`,
					`rel`,
					`from`,
					CASE 
					    WHEN `wage_type` = 'OB' AND `is_public_holiday` = 1 THEN 
					        CASE 
					            WHEN `wsu_type_of_daytype` = 'WORKDAY' THEN
					                IF(`date_day_of_week` IN (1, 7), 
					                    REPLACE( CONCAT('',FORMAT(`ec_daily_worktime`/3600, 0)), '.',  ','),
					                    REPLACE( CONCAT('',FORMAT(`dt_daily_worktime`/3600, 0)), '.',  ',')
					                )
					            ELSE 
					                IF(`date_day_of_week` IN (1, 7),					                    
					                    0,
					                    REPLACE( CONCAT('',FORMAT(`ec_daily_worktime`/3600, 0)), '.',  ',')
					                )
					            END
					    ELSE TRUNCATE(`hours`,0) 
					END as hours
				
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        elseif ($cust === "VISTRA") {
            $SQL_NBMunkrOra_CSV = "
				SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
					'' AS `company_id`,
					`id`,
					`rel`,
					`from`,
					TRUNCATE(`hours`,0) as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        elseif ($cust === "REFMON") {
            $SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					IF(wage_type = 'OB' AND is_public_holiday = 1 AND date_day_of_week NOT IN (1, 7),
					    REPLACE( CONCAT('',FORMAT(`ec_daily_worktime`/3600, 0)), '.',  ','),
					    TRUNCATE(`hours`, 0) 
					) as hours
					
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        elseif ($cust === "HOPI") {
            $SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`hours`,
					`from`,
					`to`
					
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
        }
        else {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					TRUNCATE(`hours`,0) as hours
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
		}

		if ($forceOriginalSchedule) {
			$SQL_NBMunkrOra_CSV = "
				SELECT
					`id`,
					`rel`,
					`from`,
					TRUNCATE(`hours_origi`,0)
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`date` <= CURDATE()*/
			";
		}

		if (!$this->correctLastMonth && !$this->correctLastTwoMonth)
		{
			$res_NBMunkrOra_CSV = $conn->createCommand($SQL_NBMunkrOra_CSV)->queryAll();
			if ($thisMonthPtr["ptrCorrection"]) {
				foreach ($res_NBMunkrOra_CSV as $schedHours) {
					$currentLockedData[$schedHours["id"]]["schedHours"][$schedHours["from"]] = $schedHours["hours"];
				}
			}

			$fileNameNBMunkrOra = "NBMunkrOra$fileDate.csv";
			$csv = ArrayToCsv::getCsvText($res_NBMunkrOra_CSV);
			$csv = iconv("UTF-8", "ISO-8859-1//TRANSLIT", $csv);
			$fs = new FS;
			$fs->disableMySQLStore();
			$fs->setFileGroupID($fgID);
			$fs->uploadTextFileFromContent($fileNameNBMunkrOra, $csv, "text/csv");
			$fileNameNBMunkrOra_fileId = $fs->getSmallFileID() . "_" .$fileNameNBMunkrOra;
			if ($writeFiles) {
				$file = fopen($path.DIRECTORY_SEPARATOR.$fileNameNBMunkrOra, 'w');
				fwrite($file, $csv);
				fclose($file);
			}
		}

		if ($cust === "KATLAS" || $cust === "FLEX" || $cust === "SERTEC" || $cust === "HUMANBIO" || $cust === "KUEHNENAGEL") {
			$SQL2 = "IF(`timedata_type` = '3', '1', `timedata_type`) AS `timedata_type`,";
		}
        elseif ($cust === "VIDEOTON") {
			$SQL2 = "IF(`timedata_type` = '3' AND (`date_day_of_week` IN ('7', '1') OR (`dt__total_wtej_sum` + `dt__total_otej_sum`) <= 0), '1', `timedata_type`) AS `timedata_type`,";
		}
        elseif ($cust === "SANMINA") {
			$SQL2 = "
				IF(`timedata_type` = '3' AND `wage_type` = 'OB',
					IF((`dt__total_wtej_sum` + `dt__total_otej_sum`) > 0, '3', '1'),
					IF(`daytype_id` IS NULL AND `timedata_type` IN ('1', '2', '3'),
						'30',
						IF(`dt__error_code` = 'ptr_error_scheduled-n_worked-n_absence', '30', `timedata_type`)
					)
				),";
		}
        elseif($cust === "HANON") {
            $SQL2 = '
                CASE
                    WHEN `has_absence` = 1 AND `state_type_transfer_id` IS NOT NULL
						THEN `state_type_transfer_id`							
                    WHEN `wage_type` = "OB" 
                            AND `is_public_holiday` = 1
                            AND `date_day_of_week` IN (2, 3, 4, 5, 6)
                            AND `wsu_type_of_daytype` = "WORKDAY"
                        THEN 1  
                    WHEN `wage_type` = "OB" 
                            AND `is_public_holiday` = 1 
                            AND `dt__base_worktime_sum` > 0 
                        THEN 3
                    WHEN (`wsu_type_of_daytype` = "RESTDAY" OR `is_public_holiday` = 1) 
                            AND `dt__base_worktime_sum` > 0 
                        THEN 2
                    ELSE 1
				END AS timedata_type,';
        }
        elseif($cust === "HAVI" || $cust = 'HOPI') {
            $SQL2 = '
                CASE
                    WHEN `has_absence` = 1 AND `state_type_transfer_id` IS NOT NULL
						THEN `state_type_transfer_id`												
                    ELSE 1
				END AS timedata_type,';
        }
        else {
			$SQL2 = "IF(`is_holiday_transferred_holiday` = 1 AND (`dt__base_worktime_sum` - `dt__base_abstime_sum` > 0), 1, `timedata_type`) AS timedata_type,";
		}

		if ($thisMonthPtr["ptrCorrection"]) {
			$SQL2 .= " IFNULL(`state_type_id`, 'no_state_type') AS `state_type_id`, ";
		}

		if ($cust === "DREHER")
		{
			$SQL_NBIdoadat_CSV = "
				SELECT
					'0', '1', '2', '3', '4', '5'
				UNION
				SELECT
					`company_id`,
					`id`,
					IFNULL(`option2`, '1'),
					{$SQL2}
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
		}
        elseif ($cust === "BT") {
			$SQL_NBIdoadat_CSV = "
				SELECT
					'0', '1', '3', '4', '5'
				UNION
				SELECT
					`company_id`,
					`id`,
					{$SQL2}
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
		}
        elseif ($cust === "HUMANBIO" || $cust === "KUEHNENAGEL" || $cust === "CARLZEISS") {
			$SQL_NBIdoadat_CSV = "
				SELECT
					'0', '1', '2', '3', '4', '5'
				UNION
				SELECT
					'' AS `company_id`,
					`id`,
					`rel`,
					{$SQL2}
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
		}
        elseif ($cust === "ECKERLE-KK") {
			$SQL_NBIdoadat_CSV = "
				SELECT
					'1', '2', '3', '4', '5'
				UNION
				SELECT
					`id`,
					`rel`,
					{$SQL2}
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
		}
        elseif ($cust === "STX") {
			$SQL_NBIdoadat_CSV = "
				SELECT
					`fullname`,
					`unit_name`,
					`id`,
					`rel`,
					{$SQL2}
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
		}
        elseif ($cust === "PERI") {
            $SQL_NBIdoadat_CSV = "
				SELECT
					`id`,
					{$SQL2}
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
        }
        elseif ($cust === "BOURNS" || $cust === "ANDAPRESENT") {
            $SQL_NBIdoadat_CSV = "
				SELECT
					`id`,
					'' AS `number`,
					{$SQL2}
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
        }
        elseif ($cust === "MAGNA") {
            $SQL_NBIdoadat_CSV = "
                SELECT
					'1', '2', '3', '4', '5'
				UNION
				SELECT
					`id`,
					`rel`,
					CASE 
					    WHEN `wage_type` = 'OB' AND `is_public_holiday` = 1 THEN 
					        IF(`date_day_of_week` IN (2, 3, 4, 5, 6) AND `wsu_type_of_daytype` = 'WORKDAY',
					            3,
					            1
					        )
					    ELSE
                            IF(`is_holiday_transferred_holiday` = 1 
                                   AND (`dt__base_worktime_sum` - `dt__base_abstime_sum` > 0), 
                                1, 
                                `timedata_type`
                            ) 
					    END AS 'timedata_type',
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
        }
        elseif ($cust === "VISTRA") {
            $SQL_NBIdoadat_CSV = "
                SELECT
					'1', '2', '3', '4', '5'
				UNION
				SELECT
					`id`,
					`rel`,
					IF(`is_holiday_transferred_holiday` = 1 AND (`dt__base_worktime_sum` - `dt__base_abstime_sum` > 0), 1, `timedata_type`) AS 'timedata_type',
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
        }
        else {
			$SQL_NBIdoadat_CSV = "
				SELECT
					`id`,
					`rel`,
					{$SQL2}
					`from`,
					`to`
				FROM
					`temp_payroll_transfer_NBMunkr`
				WHERE
					1 /*`day_transferable` = 1*/
			";
		}

		$res_NBIdoadat_CSV = $conn->createCommand($SQL_NBIdoadat_CSV)->queryAll();
		$prevMonthNbIdoadatArr = [];
		if ($thisMonthPtr["ptrCorrection"]) {
			$idoadat = [];
			foreach ($res_NBIdoadat_CSV as $timeType) {
				$currentLockedData[$timeType["id"]]["workType"][$timeType["from"]] = ["timedata" => $timeType["timedata_type"], "state_type_id" => $timeType["state_type_id"]];
				unset($timeType["state_type_id"]);
				$idoadat[] = $timeType;
			}
		}
        elseif ($lastMonthIdoadat != "" && $cust != "KUEHNENAGEL" && $cust != "MAGNA" && $cust != "CARLZEISS" && $cust != "LUFTHANSA" && $cust != "VISTRA")
        {
			$idoadatPrevMonth = [];
			$idoadat = [];
			$idoadat[0] = $res_NBIdoadat_CSV[0];
			$vFromIa = new DateTime($valid_to);
			$vFromIa->modify('first day of this month');
			for ($i = 1; $i <= count($res_NBIdoadat_CSV); $i++)
			{
				$actDate = new DateTime(str_replace(".", "-", mb_substr($res_NBIdoadat_CSV[$i][4], 0, -1)));
				if ($res_NBIdoadat_CSV[$i][3] != "1" && $res_NBIdoadat_CSV[$i][3] != "3")
				{
					// Ebben a hónapban távollét
					if ($actDate >= $vFromIa) {
						$idoadat[] = $res_NBIdoadat_CSV[$i];
					} else {
						// Előző hónapban távollét, de előző átadásban nem volt az (/ vagy másfajta távollét volt)
						foreach ($lastMonthIdoadat as $rows)
						{
							if ($rows[1] == $res_NBIdoadat_CSV[$i][1] && $rows[4] == $res_NBIdoadat_CSV[$i][4])
							{
								// Munkanap vagy ünnep nap volt vagy (pihinap / távollét módosult "másfajta" távollétre)
								if ($rows[3] == "1" || $rows[3] == "3" || $rows[3] != $res_NBIdoadat_CSV[$i][3]) {
									$idoadat[] = $res_NBIdoadat_CSV[$i];
									$idoadatPrevMonth[] = $res_NBIdoadat_CSV[$i][1];
								}
							}
						}
					}
				} else {
					// Előző hónapban munkanap v ünnep, de előző átadásban nem az volt
					if ($actDate < $vFromIa) {
						foreach ($lastMonthIdoadat as $rows)
						{
							if ($rows[1] == $res_NBIdoadat_CSV[$i][1] && $rows[4] == $res_NBIdoadat_CSV[$i][4]) {
								if ($rows[3] != "1" && $rows[3] != "3") {
									$idoadat[] = $res_NBIdoadat_CSV[$i];
									$idoadatPrevMonth[] = $res_NBIdoadat_CSV[$i][1];
								}
							}
						}
					}
				}
			}
		}
        elseif ($cust === "KUEHNENAGEL" || $cust === "MAGNA" || $cust === "CARLZEISS" || $cust === "VISTRA") {
			$idoadat = [];
			$idoadat[0] = $res_NBIdoadat_CSV[0];
			$prevMonthNbIdoadatArr[0] = $res_NBIdoadat_CSV[0];
			$vFromIa = new DateTime($valid_to);
			$vFromIa->modify('first day of this month');
			for ($i = 1; $i <= count($res_NBIdoadat_CSV); $i++)
			{
				$actDate = new DateTime(str_replace(".", "-", mb_substr($res_NBIdoadat_CSV[$i][4], 0, -1)));
				if ($actDate >= $vFromIa) {
					$idoadat[] = $res_NBIdoadat_CSV[$i];
				} else {
					$prevMonthNbIdoadatArr[] = $res_NBIdoadat_CSV[$i];
				}
			}
		}
        elseif ($cust === "LUFTHANSA") {
            $sourcesValidFrom = \DateTimeImmutable::createFromFormat('Y-m-d', $originalValidFrom);
            $lastPtrValidFrom = (clone $sourcesValidFrom)->modify('-1 month');
            $lastPtr = [];
            $lastPtrFirstRow = [0, 1, 2, 3, 4];
            unset($lastMonthIdoadat[0]);
            foreach($lastMonthIdoadat as $row) {
                $rowDate = \DateTimeImmutable::createFromFormat('Y.m.d.', $row[4]);
                if ($rowDate < $lastPtrValidFrom || $rowDate > $sourcesValidFrom) {
                    continue;
                }
                $lastPtr[$row[1]][$row[4]] = $row[3];
            }

            $idoadat = [];
            foreach ($res_NBIdoadat_CSV as $key => $item) {
                if (isset($lastPtr[$item['id']][$item['from']])) {
                    if ($lastPtr[$item['id']][$item['from']] === $item['timedata_type']) {
                        unset($lastPtr[$item['id']][$item['from']]);
                    } else {
                        $lastPtr[$item['id']][$item['from']] = $item['timedata_type'];
                    }
                }
                $rowDate = \DateTimeImmutable::createFromFormat('Y.m.d.', $item['from']);
                if ($rowDate < $sourcesValidFrom || $item['timedata_type'] === "1") {
                    continue;
                }
                $idoadat[] = $item;
            }

            $prevMonthNbIdoadatArr[] = $lastPtrFirstRow;
            foreach ($lastPtr as $employeeContractId => $items) {
                foreach ($items as $date => $timeType) {
                    $temp = [];
                    $temp[] = $employeeContractId;
                    $temp[] = '';
                    $temp[] = $timeType;
                    $temp[] = $date;
                    $temp[] = $date;
                    $prevMonthNbIdoadatArr[] = $temp;
                }
            }
        }
        else {
			$idoadat = $res_NBIdoadat_CSV;
		}

		$fileNameNBIdoadat = "NBIdoadat$fileDate.csv";
        $this->createInNexonPayroll($idoadat, $fgID, $fileNameNBIdoadat, $writeFiles, $path);

		//TODO: időadat előző - NBIdoadat_elozo
		if (!empty($prevMonthNbIdoadatArr))
		{
			$fileNameNBIdoadatPrev = "NBIdoadat_elozo.csv";
            $this->createInNexonPayroll($prevMonthNbIdoadatArr, $fgID, $fileNameNBIdoadatPrev, $writeFiles, $path);
		}

		if ($cust === "KATLAS")
		{
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(employee_calc_payroll.`date`,'%Y.%m.%d.') AS `date`,
					IF(`is_public_holiday` = '1' AND ((`wage_type` = 'OB' AND `dt__worktime_sum` = 0 AND `date_day_of_week` NOT IN ('7', '1')) OR employee_calc_payroll.`workgroup_name` LIKE '%24/72%'),
					concat('',
						REPLACE(w.`daily_worktime`, '.',  ',')
					),
					IF(`state_type_id` IS NOT NULL,
						concat('',
							IF(
								TRUNCATE(dt_daily_worktime/3600,0) = TRUNCATE(dt_daily_worktime/3600,1),
								CONCAT('',TRUNCATE(dt_daily_worktime/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt_daily_worktime/3600 * 2) / 2,1)), '.',  ',')
							)
						),
						concat('',
							IF(
								TRUNCATE((dt__base_worktime_sum + worktime_ot_add)/3600,0) = TRUNCATE((dt__base_worktime_sum + worktime_ot_add)/3600,1),
								CONCAT('',TRUNCATE((dt__base_worktime_sum + worktime_ot_add)/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR((dt__base_worktime_sum + worktime_ot_add)/3600 * 2) / 2,1)), '.',  ',')
							)
						)
					)) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				LEFT JOIN
					`payroll_transfer_dt_wt_diff` dt_wt_diff ON
							dt_wt_diff.`emp_id` = employee_calc_payroll.`payroll_emp_id`
						AND dt_wt_diff.`date` = employee_calc_payroll.`date`
				LEFT JOIN
					`workgroup` w ON
							w.`workgroup_id` = employee_calc_payroll.`workgroup_id`
						AND w.`status` = " . Status::PUBLISHED . "
						AND employee_calc_payroll.`date` BETWEEN w.`valid_from` AND w.`valid_to`
				WHERE
					employee_calc_payroll.`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "BT") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					'0', '1', '3', '4'
				UNION
				SELECT
					`company_id`,
					`payroll_emp_id` AS `id`,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF(`date` < '" . $ptrCreateDate->format("Y-m-d") . "',
						concat('',
							IF(
								TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
								CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
							)
						),
						concat('',
							IF(
								TRUNCATE(dt_daily_worktime/3600,0) = TRUNCATE(dt_daily_worktime/3600,1),
								CONCAT('',TRUNCATE(dt_daily_worktime/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt_daily_worktime/3600 * 2) / 2,1)), '.',  ',')
							)
						)
					)
					AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "HUMANBIO") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
					'' AS `company_id`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF(`date` < '" . $ptrCreateDate->format("Y-m-d") . "',
						IF(`state_type_id` = '1ecfd16854c92211d6278a918038903d', CONCAT('',FORMAT(dt_daily_worktime/3600, 2)), CONCAT('',FORMAT(dt__base_worktime_sum/3600, 2))),
						CONCAT('',FORMAT(dt_daily_worktime/3600, 2))
					)
					AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "KUEHNENAGEL") {
            $ptrCreateDateKuehnenagel =  clone $ptrCreateDate;
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
					'' AS `company_id`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					REPLACE(
						IF(`date` < '" . $ptrCreateDateKuehnenagel->modify("+1 day")->format("Y-m-d") . "',
							IF(`state_type_id` = '1ecfd16854c92211d6278a918038903d', CONCAT('',FORMAT(dt_daily_worktime/3600, 6)), CONCAT('',FORMAT(dt__base_worktime_sum/3600, 6))),
							CONCAT('',FORMAT(dt_daily_worktime/3600, 6))
						),
						'.',
						','
					)
					AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "DREHER") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
					`company_id`,
					`payroll_emp_id` AS `id`,
					IFNULL(`option2`, '1') AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF(`date` < '" . $ptrCreateDate->format("Y-m-d") . "',
						concat('',
							IF(
								TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
								CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
							)
						),
						concat('',
							IF(
								TRUNCATE(dt_daily_worktime/3600,0) = TRUNCATE(dt_daily_worktime/3600,1),
								CONCAT('',TRUNCATE(dt_daily_worktime/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt_daily_worktime/3600 * 2) / 2,1)), '.',  ',')
							)
						)
					)
					AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "CARLZEISS") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
					'' AS `company_id`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF(`date` < '" . $ptrCreateDate->format("Y-m-d") . "',
						concat('',
							IF(
								TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
								CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
							)
						),
						concat('',
							IF(
								TRUNCATE(dt_daily_worktime/3600,0) = TRUNCATE(dt_daily_worktime/3600,1),
								CONCAT('',TRUNCATE(dt_daily_worktime/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt_daily_worktime/3600 * 2) / 2,1)), '.',  ',')
							)
						)
					)
					AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "SANMINA") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF (`is_public_holiday` = '1' AND `wage_type` = 'OB' AND `daily_worktime` < 8, `daily_worktime`,
                        IF( `dt__error_code` = 'ptr_error_scheduled-n_worked-n_absence' AND `is_public_holiday` <> '1',
                            
                            CONCAT('',TRUNCATE(`dt_daily_worktime`/3600,1)),
                            IF( `wage_type` = 'MoLo' AND `is_public_holiday` = '1',
                                CONCAT('',TRUNCATE(IF(`full_day_absence` = '1', `dt_daily_worktime`, `dt__base_worktime_sum`)/3600,1)),
                                IF( `is_public_holiday` = '1' AND `date_day_of_week` NOT IN ('7', '1'),
                                       CONCAT('',TRUNCATE(IF(`dt__base_worktime_sum` > 0, `dt__base_worktime_sum`, IF(`wg_daily_worktime`= 43200, 28800, `wg_daily_worktime`))/3600,1)),
                                        CONCAT('',TRUNCATE(IF(`full_day_absence` = '1', `dt_daily_worktime`, `dt__base_worktime_sum`)/3600,1))
                                    )
                                )
                            )
					)
					AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "ROSENBERGER") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					
					concat('',
						IF(
							TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
							CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
							REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
						)
					
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "FLEX") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					CONCAT('',FORMAT(IF(`is_state_type_worktime` <> 1 AND `state_type_id` IS NOT NULL, `dt__base_abstime_sum`, `dt__base_worktime_sum`)/3600, 1)) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "NEMAK") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF (SEC_TO_HOUR_2_DECIMAL_COMMA(TIME_TO_SEC(SEC_TO_TIME(FLOOR(dt__base_worktime_sum/900) * 900))) = '', 0,
						SEC_TO_HOUR_2_DECIMAL_COMMA(TIME_TO_SEC(SEC_TO_TIME(FLOOR(dt__base_worktime_sum/900) * 900)))
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "XYLEM") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					concat('',
						IF(`is_public_holiday` = '1' AND `date_day_of_week` NOT IN ('7', '1'),
							IF(
								TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
								CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
							),
							IF(
								TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
								CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
							)
						)
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "ECKERLE-KK") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					'1', '2', '3', '4'
				UNION
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					concat('',
						IF(
							TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
							CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
							REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
						)
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "FELINA") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					concat('',
						REPLACE(CONCAT('',dt__base_worktime_sum/3600), '.',  ',')
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "SERTEC") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF(workgroup_id IN ('277', 'f39679d405796ab345d320326874c2a6')
                       , IF(`dt__worktime_sum` > 0, TRUNCATE(`wg_daily_worktime` / 3600, 0), 0)
                       , CONCAT('',
                            IF(
                                TRUNCATE(IF(`is_public_holiday` = 1 AND `date_day_of_week` NOT IN ('7', '1') AND `wage_type` = 'OB', `wg_daily_worktime`, `dt__worktime_sum`)/3600,0) = TRUNCATE(IF(`is_public_holiday` = 1 AND `date_day_of_week` NOT IN ('7', '1') AND `wage_type` = 'OB', `wg_daily_worktime`, `dt__worktime_sum`)/3600,1),
                                CONCAT('',TRUNCATE(IF(`is_public_holiday` = 1 AND `date_day_of_week` NOT IN ('7', '1') AND `wage_type` = 'OB', `wg_daily_worktime`, `dt__worktime_sum`)/3600,0)),
                                REPLACE( CONCAT('',TRUNCATE(FLOOR(IF(`is_public_holiday` = 1 AND `date_day_of_week` NOT IN ('7', '1') AND `wage_type` = 'OB', `wg_daily_worktime`, `dt__worktime_sum`)/3600 * 2) / 2,1)), '.',  ',')
                            )
                        )
					 ) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "VIDEOTON") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					REPLACE( CONCAT('',FORMAT(IF(`state_type_transfer_id` IS NOT NULL AND `state_type_id` IS NOT NULL, `ec_daily_worktime`, `dt__base_worktime_sum`)/3600, 2)), '.',  ',') AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "STX") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`fullname`,
					`unit_name`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					concat('',
						IF(
							TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
							CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
							REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
						)
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "AUTONEUM") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					concat('',
                    IF(`is_public_holiday` = '1' AND ((`wage_type` = 'OB' AND `dt__worktime_sum` = 0 AND `date_day_of_week` NOT IN ('7', '1')) OR employee_calc_payroll.`workgroup_name` LIKE '%24/72%'),
					concat('',
						REPLACE(ROUND(`wg_daily_worktime` / 3600, 0), '.',  ',')
					),
						IF(dt__base_worktime_sum > 0, REPLACE(ROUND(`wg_daily_worktime` / 3600, 0), '.',  ','), '0')	
					)) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif (in_array($cust, ["CYMENT"])) {
            $SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,				
					REPLACE( CONCAT('',FORMAT(`dt__base_worktime_sum`/3600, 2)), '.',  ',') AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        elseif (in_array($cust, ["HAVI"])) {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					REPLACE( 
					    CONCAT('',
					        FORMAT(
                                IF( wage_type = 'OB' 
                                       AND `is_public_holiday` = 1 
                                       AND `date_day_of_week` NOT IN (1,7),
                                    `ec_daily_worktime`,
                                    `dt__base_worktime_sum`
                                ) / 3600, 
                                2
					        )
					    ), 
					    '.',  
					    ','
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}
        elseif ($cust === "REFMON") {
            $SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,				
					CASE `is_public_holiday` 
					    WHEN 1 THEN 
					        CASE `wage_type` 
					            WHEN  'OB' THEN
					                CASE 
					                    WHEN date_day_of_week IN ('7', '1') THEN '0,00'
					                    ELSE REPLACE( CONCAT('',FORMAT(`ec_daily_worktime`/3600, 2)), '.',  ',')
					                END	                
					            ELSE '0,00'
					        END					         
				        ELSE
					        REPLACE( CONCAT('',FORMAT(`dt__base_worktime_sum`/3600, 2)), '.',  ',') 
					END AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        elseif ($cust === "HANON") {
            $SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF( (`wage_type` = 'OB' AND `is_public_holiday` = 1 AND `date_day_of_week` IN (2,3,4,5,6) AND `is_restday` = 1), 
					    REPLACE( CONCAT('',FORMAT(`ec_daily_worktime`/3600, 2)), '.',  ','),
					    REPLACE( CONCAT('',FORMAT(`dt__base_worktime_sum`/3600, 2)), '.',  ',') 
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        elseif ($cust === "HAFNER"){
            $SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF(`state_type_id` = '29337d9204baca9588942e162d229087', '0,00', REPLACE( CONCAT('',FORMAT(`dt__base_worktime_sum`/3600, 2)), '.',  ',')) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        elseif ($cust === "LENSA") {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF(
						state_type_transfer_id IS NOT NULL,
						concat('',
							IF(
								TRUNCATE(ec_daily_worktime/3600,0) = TRUNCATE(ec_daily_worktime/3600,1),
								CONCAT('',TRUNCATE(ec_daily_worktime/3600,0)),
								REPLACE( CONCAT('',TRUNCATE(FLOOR(ec_daily_worktime/3600 * 2) / 2,1)), '.',  ',')
							)
						),
						IF(
							is_public_holiday = 1,
							'0',
							IF(
								`date_day_of_week` IN ('7', '1'),
								'0',
								concat('',
									IF(
										TRUNCATE(ec_daily_worktime/3600,0) = TRUNCATE(ec_daily_worktime/3600,1),
										CONCAT('',TRUNCATE(ec_daily_worktime/3600,0)),
										REPLACE( CONCAT('',TRUNCATE(FLOOR(ec_daily_worktime/3600 * 2) / 2,1)), '.',  ',')
									)
								)
							)
						)
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		
        }
        elseif ($cust === "LEGRAND") {
            $SQL_NBIdoadatOra_CSV = "
            SELECT
                `payroll_emp_id` AS `id`,
                '' AS rel,
                DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
                concat('',
                    IF(schedule_type = 3, daily_worktime,
                        IF(
                            TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
                            CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
                            REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
                            )
                    )
                ) AS hours
            FROM
                `temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
            WHERE
                `date` BETWEEN `search_filter_from` AND `search_filter_to`
                {$eccheck}
                    AND `payroll_emp_id` IS NOT NULL
            ";

        }
        elseif ($cust === "PERI") {
            $SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					concat('',
						IF(
							TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
							CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
							REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
						)
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        elseif ($cust === "MANNHUMMEL") {
            $SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
                    CONCAT('',TRUNCATE(value_sum_payroll/3600,0)) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        elseif ($cust === "EIF") {
            $SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					IF (isnull(employee_calc_payroll.state_type_id), 
						REPLACE( CONCAT('',FORMAT(`employee_calc_payroll`.dt__base_worktime_sum/3600, 2)), '.',  ',')
						,REPLACE( CONCAT('',FORMAT(`employee_calc_payroll`.dt_daily_worktime/3600, 2)), '.',  ',')
					)
						 AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        elseif ($cust === "BOURNS") {
            $SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					employee_contract_number AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					if( `state_type_id` IN (
                            'def32968390fb987c823da0cbf7d3bd8', /* Igayolatlan*/
                            '269b59b4efbbb4ef1d527492dc06cb60', /* Igazolt de nem fizetett*/
                            '628834878c5bc72f60283e37865678b6'  /* Fizetésnélküli szabadságon*/
					    ) OR (`has_absence` = 1 AND `is_state_type_worktime` = 1),
					    REPLACE( CONCAT('',FORMAT(`dt_daily_worktime`/3600, 0)), '.',  ','),
					    REPLACE( CONCAT('',FORMAT(`dt__base_worktime_sum`/3600, 2)), '.',  ',')
					) as hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        elseif ($cust === "MAGNA") {
            $SQL_NBIdoadatOra_CSV = "
                SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
				    '' AS 'company_id',
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					CASE
					    WHEN `wage_type` = 'OB' 
                         AND `is_public_holiday` = 1 
					     AND `date_day_of_week` IN (2, 3, 4, 5, 6)
                         AND `wsu_type_of_daytype` = 'RESTDAY' THEN  
					        CONCAT('',TRUNCATE(ec_daily_worktime/3600,0))
					    ELSE
                            CONCAT('',
                                IF(
                                    TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
                                    CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
                                    REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
                                )
                            ) 
					    END AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        elseif ($cust === "VISTRA") {
            $SQL_NBIdoadatOra_CSV = "
                SELECT
					'0', '1', '2', '3', '4'
				UNION
				SELECT
				    '' AS 'company_id',
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					concat('',
						IF(
							TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
							CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
							REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
						)
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
        }
        else {
			$SQL_NBIdoadatOra_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					DATE_FORMAT(`date`,'%Y.%m.%d.') AS `date`,
					concat('',
						IF(
							TRUNCATE(dt__base_worktime_sum/3600,0) = TRUNCATE(dt__base_worktime_sum/3600,1),
							CONCAT('',TRUNCATE(dt__base_worktime_sum/3600,0)),
							REPLACE( CONCAT('',TRUNCATE(FLOOR(dt__base_worktime_sum/3600 * 2) / 2,1)), '.',  ',')
						)
					) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt` employee_calc_payroll
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
			";
		}

		if (!$this->correctLastMonth && !$this->correctLastTwoMonth)
		{
			$res_NBIdoadatOra_CSV = $conn->createCommand($SQL_NBIdoadatOra_CSV)->queryAll();
			if ($thisMonthPtr["ptrCorrection"]) {
				foreach ($res_NBIdoadatOra_CSV as $workHours) {
					$currentLockedData[$workHours["id"]]["workHours"][$workHours["date"]] = $workHours["hours"];
				}
			}

			$fileNameNBIdoadatOra = "NBIdoadatOra$fileDate.csv";
			$csv = ArrayToCsv::getCsvText($res_NBIdoadatOra_CSV);
			$csv = iconv("UTF-8", "ISO-8859-1//TRANSLIT", $csv);
			$fs = new FS;
			$fs->disableMySQLStore();
			$fs->setFileGroupID($fgID);
			$fs->uploadTextFileFromContent($fileNameNBIdoadatOra, $csv, "text/csv");
			if ($writeFiles) {
				$file = fopen($path.DIRECTORY_SEPARATOR.$fileNameNBIdoadatOra, 'w');
				fwrite($file, $csv);
				fclose($file);
			}
		}

        $NBPBeosztExcention = ['DREHER', 'BT', 'HUMANBIO', 'KUEHNENAGEL', 'STX', 'MANNHUMMEL', 'PERI', 'HAFNER', 'EIF', 'HANON', 'BOURNS', 'SKUBA', 'LUFTHANSA', 'ANDAPRESENT', 'HOPI'];
		if (!in_array($cust, $NBPBeosztExcention))
		{
			$SQL_NBPBeoszt_CSV = "
				SELECT
					`id`,
					`rel`,
					mnth__has_allowance,
					MIN(`from`),
					MAX(`to`)
				FROM
					`temp_payroll_transfer_NBMunkr`
				GROUP BY
					employee_contract_id,
						search_filter_from,
							search_filter_to
			";

			if ($cust === "FLEX")
			{
				$SQL_NBPBeoszt_CSV = "
					SELECT
						`id`,
						`rel`,
						-- mnth__has_allowance,
						IF(`daytype_id` IS NOT NULL, 1 , 0) AS beoszt,
						`date` AS fromDate,
						`date` AS toDate
					FROM
						`temp_payroll_transfer_NBMunkr`
					GROUP BY
						employee_contract_id,
							`date`
				";
			}

			$res_NBPBeoszt_CSV = $conn->createCommand($SQL_NBPBeoszt_CSV)->queryAll();

			$fileNameNBPBeoszt = "NBPBeoszt$fileDate.csv";
            $this->createInNexonPayroll($res_NBPBeoszt_CSV, $fgID, $fileNameNBPBeoszt, $writeFiles, $path);

		}

		if ($cust === "DREHER")
		{
			$SQL_NBKifizTavol_CSV = "
				SELECT
					'0' as `company_id`, '1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
					`company_id`,
					`payroll_emp_id` AS `id`,
					IFNULL(`option2`, '1') AS rel,
					`transfer_id_2`,
					0 AS `sum`, 0 AS `percent`,

					/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
					CONCAT('-', IF(
						TRUNCATE(SUM(`value_sum_payroll`)/3600,0) = TRUNCATE(SUM(`value_sum_payroll`)/3600,1),
						CONCAT('',TRUNCATE(SUM(`value_sum_payroll`)/3600,0)),
						REPLACE( CONCAT('',TRUNCATE(CEIL(SUM(`value_sum_payroll`)/3600 * 2) / 2,1)), '.',  ',')
					)) AS hours,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IN ('IGT', 'ILT')
						AND `payroll_emp_id` IS NOT NULL
						AND `value_sum_payroll` > 0
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', `value_sum_payroll`)
			";

			$res_NBKifizTavol_CSV = $conn->createCommand($SQL_NBKifizTavol_CSV)->queryAll();

			$fileNameNBKifizTav = "NBKifiz_Tavollet$fileDate.csv";
			$csv = ArrayToCsv::getCsvText($res_NBKifizTavol_CSV);
			$csv = iconv("UTF-8", "ISO-8859-1", $csv);
			$fs = new FS;
			$fs->disableMySQLStore();
			$fs->setFileGroupID($fgID);
			$fs->uploadTextFileFromContent($fileNameNBKifizTav, $csv, "text/csv");
			if ($writeFiles) {
				$file = fopen($path.DIRECTORY_SEPARATOR.$fileNameNBKifizTav, 'w');
				fwrite($file, $csv);
				fclose($file);
			}
		}

		if ($cust === "HUMANBIO")
		{
			$SQL_NBTortNapiTav_CSV = "
			SELECT
					'1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `day`, '5' as `hours`
				UNION
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `day`,
					CONCAT('',FORMAT(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`)/3600), 2)) AS hours
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
						`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
					AND `transfer_id_2` IS NOT NULL
					AND `payroll_emp_id` IS NOT NULL
					AND IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
					AND `transfer_id_2` = '27'
				GROUP BY
					`date`,
					`payroll_emp_id`,
					CONVERT(`transfer_id_2` USING BINARY)
			";

			$res_NBTortNapiTav_CSV = $conn->createCommand($SQL_NBTortNapiTav_CSV)->queryAll();

			$fileNameNBTortNapiTav = "NBTortNapiTav$fileDate.csv";
			$csv = ArrayToCsv::getCsvText($res_NBTortNapiTav_CSV);
			$csv = iconv("UTF-8", "ISO-8859-1", $csv);
			$fs = new FS;
			$fs->disableMySQLStore();
			$fs->setFileGroupID($fgID);
			$fs->uploadTextFileFromContent($fileNameNBTortNapiTav, $csv, "text/csv");
			if ($writeFiles) {
				$file = fopen($path.DIRECTORY_SEPARATOR.$fileNameNBTortNapiTav, 'w');
				fwrite($file, $csv);
				fclose($file);
			}
		}

		if ($cust === "DREHER" || $cust === "BT")
		{
			$selectSQL = "
				IF(`transfer_id_2` IN ('MJV', 'MJB', 'MJK'), ROUND(`new_value_sum_payroll`), '0') AS `sum`,
				0 AS `percent`,

				/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
				IF(`transfer_id_2` IN ('MJV', 'MJB', 'MJK'), '0',
					IF(
						TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,1),
						CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0)),
						REPLACE( CONCAT('',TRUNCATE(CEIL(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ',')
					)
				) AS hours,
			";
			$whereSQL = "
				/*AND IF(`transfer_id_2` IN ('MJK') AND `date` < '" . $ptrCreateDate->format("Y-m-d") . "',
					`value`,
					IF(`transfer_id_2` IN ('MJK') AND `date` >= '" . $ptrCreateDate->format("Y-m-d") . "',
						IF(`wsu_type_of_daytype` = 'WORKDAY', 1, 0),
						`value_sum_payroll`)
					) > 0*/
				AND IF(`new_value_sum_payroll` > 0, `new_value_sum_payroll`, `value_sum_payroll`) > 0
				AND `transfer_id_2` NOT IN ('IGT', 'ILT')
			";
		}
        elseif ($cust === "NEMAK")
        {
			$selectSQL = "
				0 AS `sum`, 0 AS `percent`,

				/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
				IF(
					TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,1),
					CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0)),
					IF(`transfer_id_2` IN ('CK', 'CK20'),
						REPLACE( CONCAT('',TRUNCATE(CEIL(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ','),
						REPLACE( CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2 / 2,2)), '.',  ',')
					)
				) AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
				AND `transfer_id_2` <> 'Rk' AND `transfer_id_2` <> 'RkKK'
			";
		}
        elseif ($cust === "FELINA")
        {
			$selectSQL = "
				0 AS `sum`, 0 AS `percent`,

				concat('',
					REPLACE(CONCAT('',IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`)/3600), '.',  ',')
				) AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
		}
        elseif ($cust === "SCHENKER")
        {
			$selectSQL = "
				IF(`transfer_id_2` IN ('BT'), ROUND(`new_value_sum_payroll`), '0') AS `sum`,
				0 AS `percent`,

				/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
				IF(`transfer_id_2` IN ('BT'), '0',
					IF(
						TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,1),
						CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0)),
						REPLACE( CONCAT('',TRUNCATE(CEIL(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ',')
					)
				) AS hours,
			";
			$whereSQL = "
				AND `transfer_id_2` NOT IN ('EPAK', 'EPAM')
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
		}
        elseif ($cust === "ECKERLE-KK")
        {
			$selectSQL = "
				'' AS `sum`, '' AS `percent`,

				/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
				IF(
					TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,1),
					CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0)),
					IF(`transfer_id_2` = 'CK',
						REPLACE( CONCAT('',TRUNCATE(CEIL(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ','),
						REPLACE( CONCAT('',TRUNCATE(FLOOR(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ',')
					)
				) AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
		}
        elseif ($cust === "VIDEOTON" || $cust === "HAFNER" || $cust === "EIF" || $cust === "HANON") {
			$selectSQL = "
				0 AS `sum`, 0 AS `percent`,
				REPLACE( CONCAT('',FORMAT(SUM(IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`)/3600), 2)), '.',  ',') AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
		}
        elseif ($cust === "KUEHNENAGEL") {
			$selectSQL = "
				0 AS `sum`, 0 AS `percent`,
				REPLACE( CONCAT('',FORMAT(SUM(IF(`new_value_sum_payroll` <> 0,CAST(`new_value_sum_payroll` AS UNSIGNED),CAST(`value_sum_payroll` AS UNSIGNED)))/3600, 9)), '.',  ',') AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
		}
        elseif ($cust === "HUMANBIO") {
			$selectSQL = "
				0 AS `sum`, 0 AS `percent`,

				SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`)) AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
				AND `transfer_id_2` <> '27'
			";
		}
        elseif ($cust === "AUTONEUM") {
			$selectSQL = "
				0 AS `sum`, 0 AS `percent`,

				/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
				IF(
					TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0, IF(`transfer_id_2` IN ('W12,5', 'W20', 'W30'), `new_value_sum_payroll` * 60, `new_value_sum_payroll`), `value_sum_payroll`))/3600,0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,IF(`transfer_id_2` IN ('W12,5', 'W20', 'W30'), `new_value_sum_payroll` * 60, `new_value_sum_payroll`), `value_sum_payroll`))/3600,1),
					CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,IF(`transfer_id_2` IN ('W12,5', 'W20', 'W30'), `new_value_sum_payroll` * 60, `new_value_sum_payroll`), `value_sum_payroll`))/3600,0)),
					IF(`transfer_id_2` = 'CK',
						REPLACE( CONCAT('',TRUNCATE(CEIL(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ','),
						REPLACE( CONCAT('',TRUNCATE(FLOOR(SUM(IF(`new_value_sum_payroll` > 0,IF(`transfer_id_2` IN ('W12,5', 'W20', 'W30'), `new_value_sum_payroll` * 60, `new_value_sum_payroll`), `value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ',')
					)
				) AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
		}
        elseif ($cust === "AQUINCUM") {
			$selectSQL = "
				0 AS `sum`, 0 AS `percent`,

				/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
				IF(`transfer_id_2` IN ('CÉ', 'CM'),
					REPLACE( CONCAT('',FORMAT(SUM(IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`)/3600), 2)), '.',  ','),
					IF(
						TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,1),
						CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0)),
						IF(`transfer_id_2` = 'CK',
							REPLACE( CONCAT('',TRUNCATE(CEIL(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ','),
							REPLACE( CONCAT('',TRUNCATE(FLOOR(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ',')
						)
					)
				) AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
		}
        elseif ($cust === "LEGRAND") {
			$selectSQL = "
				IF(`transfer_id_2` = 'UK', SUM(`new_value_sum_payroll`), 0) AS `sum`,
				IF(`transfer_id_2` = 'UK', '', 0) AS `percent`,

				/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
					IF(`transfer_id_2` = 'UK',
						'',
						IF(
							TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0, `new_value_sum_payroll`, `value_sum_payroll`))/3600, 0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0, `new_value_sum_payroll`, `value_sum_payroll`))/3600, 1),
							CONCAT('', TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0, `new_value_sum_payroll`, `value_sum_payroll`))/3600, 0)),
							IF(`transfer_id_2` = 'CK',
								REPLACE( CONCAT('', TRUNCATE(CEIL(SUM(IF(`new_value_sum_payroll` > 0, `new_value_sum_payroll`, `value_sum_payroll`))/3600 * 2) / 2, 1)), '.',  ','),
								REPLACE( CONCAT('', TRUNCATE(FLOOR(SUM(IF(`new_value_sum_payroll` > 0, `new_value_sum_payroll`, `value_sum_payroll`))/3600 * 2) / 2, 1)), '.',  ',')
							)
						)
					) AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";

        }
        elseif ($cust === "PERI") {
            $selectSQL = "
            IF(`transfer_id_2` = 'T2', 
                100, 
                IF(`transfer_id_2` = 'T1', 50, '')
            ) AS `sum`,
            /* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
				IF(
					TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,1),
					CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0)),
					IF(`transfer_id_2` = 'CK',
						REPLACE( CONCAT('',TRUNCATE(CEIL(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ','),
						REPLACE( CONCAT('',TRUNCATE(FLOOR(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ',')
					)
				) AS hours,
        ";
        $whereSQL = "
            AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
        ";
		}
        elseif ($cust === "ROSSMANN") {
            $selectSQL = "
				0 AS `sum`, 0 AS `percent`,
				REPLACE( 
				    CONCAT('', ROUND(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600, 2)),
				     '.',  ','
				    ) AS hours,
			";
            $whereSQL = "
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
        }
        elseif ($cust === "LUFTHANSA") {
            $selectSQL = "
				'' AS `sum`, '' AS `percent`,
				REPLACE( 
				    CONCAT('', ROUND(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600, 2)),
				     '.',  ','
				    ) AS hours,
			";
            $whereSQL = "
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
        }
        elseif ($cust === "SANMINA") {
            $selectSQL = "
				0 AS `sum`, 0 AS `percent`,
                
                IF(`transfer_id_2` = 'MAK', ROUND(`new_value_sum_payroll`/3600, 2),
				REPLACE( 
				    CONCAT('', ROUND(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600, 2)),
				     '.',  ','
				    )) AS hours,
			";
            $whereSQL = "
				
			";
        }
        elseif ($cust === "BOURNS" || $cust === 'VISTRA' || $cust === 'ANDAPRESENT') {
            $selectSQL = "
				0 AS `sum`, 0 AS `percent`,
				REPLACE(
                    CONCAT('', 
                        TRUNCATE(
                            SUM(
                                IF(`new_value_sum_payroll` > 0,
                                    `new_value_sum_payroll`,
                                    `value_sum_payroll`
                                )
                            ) / 3600, 
                            2
                        )
                    ),
                    '.',  
                    ','
                ) AS hours,
			";
            $whereSQL = '
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			';
        }
        elseif ($cust === "HAVI") {
            $selectSQL = "
				0 AS `sum`, 0 AS `percent`,
				REPLACE(
                    CONCAT('', 
                        TRUNCATE(
                            SUM(
                                IF(`transfer_id_2` IN ('ÁI', 'AIP'), 
                                    ABS(
                                        IF(`new_value_sum_payroll` > 0,
                                            `new_value_sum_payroll`,
                                            `value_sum_payroll`
                                        )
                                    ),
                                    IF(`new_value_sum_payroll` > 0,
                                        `new_value_sum_payroll`,
                                        `value_sum_payroll`
                                    )
                                )
                            ) / 3600, 
                            2
                        )
                    ),
                    '.',  
                    ','
                ) AS hours,
			";
            $whereSQL = "
				AND IF(`transfer_id_2` IN ('ÁI', 'AIP'), 
				        ABS(
				            IF(`new_value_sum_payroll` > 0,
                                `new_value_sum_payroll`,
                                `value_sum_payroll`
                            )
                        ),
                        IF(`new_value_sum_payroll` > 0,
                            `new_value_sum_payroll`,
                            `value_sum_payroll`
                        )
                    ) > 0
			";
        } else {
			$selectSQL = "
				0 AS `sum`, 0 AS `percent`,

				/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
				IF(
					TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,1),
					CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0)),
					IF(`transfer_id_2` = 'CK',
						REPLACE( CONCAT('',TRUNCATE(CEIL(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ','),
						REPLACE( CONCAT('',TRUNCATE(FLOOR(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ',')
					)
				) AS hours,
			";
			$whereSQL = "
				AND IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0
			";
		}

		if ($cust === "KATLAS") {
			$SQL_NBKifiz_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					'' AS `sum`, '' AS `percent`,

					/* egésznél 0 tizedesjegy, törtnél 0,5-re kerekítve */
					IF (`transfer_id_2` IN ('TA'),
						IF(
							TRUNCATE(SUM(`value_sum_payroll` - `ot_used_for_dt_wt`)/3600,0) = TRUNCATE(SUM(`value_sum_payroll` - `ot_used_for_dt_wt`)/3600,1),
							CONCAT('',TRUNCATE(SUM(`value_sum_payroll` - `ot_used_for_dt_wt`)/3600,0)),
							REPLACE( CONCAT('',TRUNCATE(FLOOR(SUM(`value_sum_payroll` - `ot_used_for_dt_wt`)/3600 * 2) / 2,1)), '.',  ',')
						),
						IF(
							TRUNCATE(SUM(IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0) = TRUNCATE(SUM(IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,1),
							CONCAT('',TRUNCATE(SUM(IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600,0)),
							REPLACE( CONCAT('',TRUNCATE(FLOOR(SUM(IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`))/3600 * 2) / 2,1)), '.',  ',')
						)
					)	AS hours,

					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, employee_calc_payroll.`date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, employee_calc_payroll.`date`) ,'%Y.%m.%d.') AS `to`,
					'' AS c1,
					'' AS c2,
					'' AS c3,
					'' AS c4,
					'' AS c5
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid` employee_calc_payroll
				LEFT JOIN
					`payroll_transfer_dt_wt_diff` dt_wt_diff ON
							dt_wt_diff.`emp_id` = employee_calc_payroll.`payroll_emp_id`
						AND dt_wt_diff.`date` = employee_calc_payroll.`date`
				WHERE
					employee_calc_payroll.`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						AND IF(`transfer_id_2` IN ('TA'), `value_sum_payroll` - `ot_used_for_dt_wt`, IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`)) <> 0
				GROUP BY
					IF(`total_sum_2`, '', employee_calc_payroll.`date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` <> 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "BT") {
			$SQL_NBKifiz_CSV = "
				SELECT
					'0' as `company_id`, '1' as `id`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
					`company_id`,
					`payroll_emp_id` AS `id`,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "KUEHNENAGEL") {
			$SQL_NBKifiz_CSV = "
				SELECT
					'0' as `company_id`, '1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
					'' as `company_id`,
					`payroll_emp_id` AS `id`,
					'' as `rel`,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "DREHER") {
			$SQL_NBKifiz_CSV = "
				SELECT
					'0' as `company_id`, '1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
					`company_id`,
					`payroll_emp_id` AS `id`,
					IFNULL(`option2`, '1') AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "CARLZEISS") {
			$SQL_NBKifiz_CSV = "
				SELECT
					'0' as `company_id`, '1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
					'' AS `company_id`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "FLEX") {
			$SQL_NBKifiz_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					0 AS `sum`, 0 AS `percent`,
					CONCAT('',FORMAT(SUM(IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`)/3600), 1)) AS hours,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`,
					'' AS c1,
					'' AS c2,
					'' AS c3,
					'' AS c4,
					'' AS c5
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						AND IF(`transfer_id_2` <> 'KLDI', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`) > 0, 1 = 1)
						AND `transfer_id_2` <> 'MH'
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "ECKERLE-KK") {
			$SQL_NBKifiz_CSV = "
				SELECT
					'1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "SCHENKER") {
			$SQL_NBKifiz_CSV = "
				SELECT
					'0' as `company_id`, '1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
					'' AS `company_id`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "HUMANBIO") {
			$SQL_NBKifiz_CSV = "
				SELECT
					'0' as `company_id`, '1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
					sub.company_id,
					sub.id,
					sub.rel,
					sub.transfer_id_2,
					sub.sum,
					sub.percent,
					CONCAT('',FORMAT(SUM(sub.hours/3600), 2)) AS hours,
					sub.from,
					sub.to
				FROM (
					SELECT
						'' AS `company_id`,
						`payroll_emp_id` AS `id`,
						'' AS rel,
						IF(LEFT(`transfer_id_2`, 3) = 'MAN', SUBSTRING(`transfer_id_2`, 4), `transfer_id_2`) AS transfer_id_2,
						{$selectSQL}
						DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
						DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
					FROM
						`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
					WHERE
						`date` BETWEEN `search_filter_from` AND `search_filter_to`
						{$eccheck}
							AND `transfer_id_2` IS NOT NULL
							AND `payroll_emp_id` IS NOT NULL
							{$whereSQL}
					GROUP BY
						IF(`total_sum_2`, '', `date`),
						CONVERT(`transfer_id_2` USING BINARY),
						`payroll_emp_id`,
						IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
				) as sub
				GROUP BY sub.id, sub.transfer_id_2, sub.from, sub.to
			";
		}
        elseif ($cust === "STX") {
			$SQL_NBKifiz_CSV = "
				SELECT
					`fullname`,
					`unit_name`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`,
					'' AS c1,
					'' AS c2,
					'' AS c3,
					'' AS c4,
					'' AS c5
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "LEGRAND") {
			$SQL_NBKifiz_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`,
					'' AS c1,
					'' AS c2,
					'' AS c3,
					'' AS c4,
					'' AS c5
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}
        elseif ($cust === "PERI") {
            $SQL_NBKifiz_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
                    `transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`,
                    workgroup_id
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
        }
        elseif ($cust === "LUFTHANSA") {
            $SQL_NBKifiz_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT(`date` ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT(`date` ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
	                IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
					
			";
        }
        elseif ($cust === "MAGNA") {
            $SQL_NBKifiz_CSV = "
                SELECT
					'0' as `company_id`, '1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
				    '' as `company_id`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
        }
        elseif ($cust === "VISTRA") {
            $SQL_NBKifiz_CSV = "
                SELECT
					'0' as `company_id`, '1' as `id`, '2' as `rel`, '3' as `transfer_id_2`, '4' as `sum`, '5' as `percent`, '6' as `hours`, '7' as `from`, '8' as `to`
				UNION
				SELECT
				    '' as `company_id`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
        }
        else {
			$SQL_NBKifiz_CSV = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					{$selectSQL}
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_from`, `date`) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT( IF(`total_sum_2`, `search_filter_to`, `date`) ,'%Y.%m.%d.') AS `to`,
					'' AS c1,
					'' AS c2,
					'' AS c3,
					'' AS c4,
					'' AS c5
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						{$whereSQL}
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`,
					IF(`total_sum_2`, '', IF(`new_value_sum_payroll` > 0,`new_value_sum_payroll`,`value_sum_payroll`))
			";
		}

		$res_NBKifiz_CSV = $conn->createCommand($SQL_NBKifiz_CSV)->queryAll();
        $resultNbKifizCorr = [];

        if ($cust === "LUFTHANSA") {
            $lastPtrValidTo = (clone $sourcesValidFrom)->modify('-1 day');
            $lastPtr = [];
            foreach($lastMonthPtr as $item) {
                $itemDate = \DateTimeImmutable::createFromFormat('Y.m.d.', $item['from']);
                if ($itemDate < $lastPtrValidFrom || $itemDate > $sourcesValidFrom) {
                    continue;
                }
                $lastPtr[$item['id']][$item['from']][$item['transfer_id_2']] = $item;
            }

            $nbkifiz = [];
            $lastPtrKifiz = [];
            foreach ($res_NBKifiz_CSV as $row) {
                $rId = $row['id'];
                $rFrom = $row['from'];
                $rTransferId = $row['transfer_id_2'];
                $rowDate = \DateTimeImmutable::createFromFormat('Y.m.d.', $rFrom);
                $rowHour = (float) str_replace(',', '.', $row['hours']);

                if ($rowDate >= $lastPtrValidFrom && $rowDate < $sourcesValidFrom) {
                    if (!isset($lastPtrKifiz[$rId][$rTransferId]['hours'])) {
                        $lastPtrKifiz[$rId][$rTransferId]['hours'] = 0;
                    }

                    if (!isset($lastPtr[$rId][$rFrom][$rTransferId])) {
                        $lastPtrKifiz[$rId][$rTransferId]['hours'] += $rowHour;
                    }

                    if (isset($lastPtr[$rId][$rFrom][$rTransferId])
                        && (float) str_replace(',', '.', $lastPtr[$rId][$rFrom][$rTransferId]['hours']) !== $rowHour) {
                        $lastPtrKifiz[$rId][$rTransferId]['hours'] += $rowHour;
                    }
                }
                if ($rowDate < $sourcesValidFrom) {
                    continue;
                }
                if (!isset($nbkifiz[$rId][$rTransferId]['hours'])) {
                    $nbkifiz[$rId][$rTransferId]['hours'] = 0;
                }
                $nbkifiz[$rId][$rTransferId]['hours'] += $rowHour;
            }

            $res_NBKifiz_CSV = [];
            foreach ($nbkifiz as $key => $transferIds) {
                foreach ($transferIds as $transferId => $hour) {
                    $temp = [];
                    $temp[] = $key;
                    $temp[] = '';
                    $temp[] = $transferId;
                    $temp[] = '';
                    $temp[] = '';
                    $temp[] = $hour["hours"];
                    $temp[] = $originalValidFrom;
                    $temp[] = $valid_to;
                    $res_NBKifiz_CSV[] = $temp;
                }
            }

            foreach ($lastPtrKifiz as $key => $transferIds) {
                foreach ($transferIds as $transferId => $hour) {
                    if ($hour["hours"] === 0) {
                        continue;
                    }
                    $temp = [];
                    $temp[] = $key;
                    $temp[] = '';
                    $temp[] = $transferId;
                    $temp[] = '';
                    $temp[] = '';
                    $temp[] = $hour["hours"];
                    $temp[] = $lastPtrValidFrom->format("Y.m.d.");
                    $temp[] = $lastPtrValidTo->format("Y.m.d.");
                    $resultNbKifizCorr[] = $temp;
                }
            }

        }
		if ($cust === "FLEX") {
			$SQLUNION = "
				SELECT
					sub.`payroll_emp_id` AS `id`,
					'' AS rel,
					'MH' AS `transfer_id_2`,
					'0' AS `sum`,
					'0' AS `percent`,
					CONCAT('-',FORMAT(sub.`wl_hour`/3600, 1)) AS `hours`,
					DATE_FORMAT(sub.`search_filter_to`,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT(sub.`search_filter_to`,'%Y.%m.%d.') AS `to`,
					'' AS c1,
					'' AS c2,
					'' AS c3,
					'' AS c4,
					'' AS c5
				FROM
					(SELECT
						tid.`payroll_emp_id`,
						tid.`search_filter_to`,
						IF(SUM(IF(tid.`new_value_sum_payroll` > 0, tid.`new_value_sum_payroll`,tid.`value_sum_payroll`)) > TIME_TO_SEC(mwrl.`limit`), '1', '0') AS `worked_less`,
						SUM(IF(tid.`new_value_sum_payroll` > 0, tid.`new_value_sum_payroll`, tid.`value_sum_payroll`)) AS `wl_hour`
					FROM
						`temp_payroll_transfer_employee_calc_payroll_data_dt_tid` tid
					LEFT JOIN
						`app_settings` app ON
								app.`valid` = 1
							AND app.`setting_id` = 'missing_worktime_report_limit_group'
					LEFT JOIN
						`missing_worktime_report_limit` mwrl ON
								CASE
									WHEN app.`setting_value` = 'employee.company_id' THEN mwrl.`group_id` = tid.`company_id`
									WHEN app.`setting_value` = 'employee.payroll_id' THEN mwrl.`group_id` = tid.`payroll_id`
									WHEN app.`setting_value` = 'employee.company_org_group1_id' THEN mwrl.`group_id` = tid.`company_org_group1_id`
									WHEN app.`setting_value` = 'employee.company_org_group2_id' THEN mwrl.`group_id` = tid.`company_org_group2_id`
									WHEN app.`setting_value` = 'employee.company_org_group3_id' THEN mwrl.`group_id` = tid.`company_org_group3_id`
									WHEN app.`setting_value` = 'employee_contract.workgroup_id' THEN mwrl.`group_id` = tid.`workgroup_id`
									WHEN app.`setting_value` = 'employee_contract.employee_position_id' THEN mwrl.`group_id` = tid.`employee_position_id`
								END
							AND mwrl.`status` = " . Status::PUBLISHED . "
					WHERE
							`date` BETWEEN `search_filter_from` AND `search_filter_to`
						{$eccheck}
						AND `transfer_id_2` IS NOT NULL
						AND `payroll_emp_id` IS NOT NULL
						AND `value_sum_payroll` > 0
						AND `transfer_id_2` = 'MH'
					GROUP BY
						CONVERT(`transfer_id_2` USING BINARY),
						`payroll_emp_id`
					) AS sub
				WHERE sub.`worked_less` = '1'
			";
			$resFlex = $conn->createCommand($SQLUNION)->queryAll();
			$res_NBKifiz_CSV = array_merge($res_NBKifiz_CSV, $resFlex);
		}

		if ($cust === "NEMAK")
		{
			$SQLUNION = "
				SELECT
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					0 AS `sum`,
					0 AS `percent`,
					1 AS hours,
					DATE_FORMAT(`date` ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT(`date` ,'%Y.%m.%d.') AS `to`,
					'' AS c1,
					'' AS c2,
					'' AS c3,
					'' AS c4,
					'' AS c5,
					`employee_contract_id`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
						AND (`transfer_id_2` = 'Rk' OR `transfer_id_2` = 'RkKK')
				GROUP BY
					IF(`total_sum_2`, '', `date`),
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`
			";
			$resNemak = $conn->createCommand($SQLUNION)->queryAll();

			$finalArr = [];
			$sumArr = [];
			$rkkkArr = [];
			$rkkkEcIdEmpId = [];
			foreach ($resNemak as $data)
			{
				if ($data["transfer_id_2"] == "Rk")
				{
					if (isset($sumArr[$data["id"]])) {
						$sumArr[$data["id"]]["hours"] += 1;
					} else {
						$sumArr[$data["id"]] = $data;
					}
				} else if ($data["transfer_id_2"] == "RkKK") {
					$rkkkArr[$data["employee_contract_id"]][] = str_replace(".", "-", mb_substr($data["from"], 0, -1));
					$rkkkEcIdEmpId[$data["employee_contract_id"]] = $data["id"];
				}
			}

			$ecids = array_keys($rkkkArr);
			$gews = new GetEmployeeWorkSchedule($valid_from, $valid_to, $ecids, true);
			$workSchedule = $gews->get();
			foreach ($rkkkArr as $ecId => $rkkkData)
			{
				$allRkKKDays = count($rkkkData);
				$entitled = 0;
				$entitledCurrent = 0;
				$i = 0;
				while ($i < $allRkKKDays)
				{
					$dateDt = new DateTime($rkkkData[$i]);
					$entitledCurrent = 1;
					$dateDt->modify('+ 1 day');
					while ($dateDt->format("Y-m-d") <= $rkkkData[$allRkKKDays-1] &&
					($workSchedule[$ecId][$dateDt->format("Y-m-d")]["used_type_of_daytype"] == "RESTDAY" || in_array($dateDt->format("Y-m-d"), $rkkkData))) {
						if ($entitledCurrent < 2) { $entitledCurrent++; }
						if (in_array($dateDt->format("Y-m-d"), $rkkkData)) { $i++; }
						$dateDt->modify('+ 1 day');
					}
					$i++;
					$entitled += $entitledCurrent;
				}

				if (isset($sumArr[$rkkkEcIdEmpId[$ecId]])) {
					$sumArr[$rkkkEcIdEmpId[$ecId]]["hours"] += $entitled;
				} else {
					$sumArr[$rkkkEcIdEmpId[$ecId]] =
					[
						"id"					=> $rkkkEcIdEmpId[$ecId],
						"rel"					=> '',
						"transfer_id_2"			=> "Rk",
						"sum"					=> 0,
						"percent"				=> 0,
						"hours"					=> $entitled,
						"from"					=> str_replace("-", ".", $valid_from) . ".",
						"to"					=> str_replace("-", ".", $valid_from) . ".",
						"c1"					=> '',
						"c2"					=> '',
						"c3"					=> '',
						"c4"					=> '',
						"c5"					=> '',
						"employee_contract_id"	=> $ecId
					];
				}
			}

			foreach ($sumArr as $data)
			{
				$data["from"] = str_replace("-", ".", $valid_from) . ".";
				$data["to"] = str_replace("-", ".", $valid_to) . ".";
				unset($data["employee_contract_id"]);
				if ($data["transfer_id_2"] == "Rk") {
					$finalArr[] = $data;
					$data["transfer_id_2"] = "Rka";
					$finalArr[] = $data;
				} else {
					$finalArr[] = $data;
				}
			}

			$res_NBKifiz_CSV = array_merge($res_NBKifiz_CSV, $finalArr);
		}

		if ($cust === "SCHENKER")
		{
			$SQLUNION = "
				SELECT
					'' AS `company_id`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					0 AS `sum`,
					0 AS `percent`,
					SUM(IF(`transfer_id_2` IN ('EPAM', 'EPAK'), 1, 0)) AS hours,
					DATE_FORMAT(`search_filter_from` ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT(LAST_DAY(`search_filter_from`) ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN `search_filter_from` AND LAST_DAY(`search_filter_from`)
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
						AND (`transfer_id_2` = 'EPAM' OR `transfer_id_2` = 'EPAK')
				GROUP BY
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`
			";
			$resSchenker1 = $conn->createCommand($SQLUNION)->queryAll();

			$SQLUNION2 = "
				SELECT
					'' AS `company_id`,
					`payroll_emp_id` AS `id`,
					'' AS rel,
					`transfer_id_2`,
					0 AS `sum`,
					0 AS `percent`,
					SUM(IF(`transfer_id_2` IN ('EPAM', 'EPAK'), 1, 0)) AS hours,
					DATE_FORMAT(CAST(DATE_FORMAT(`search_filter_to` ,'%Y-%m-01') as DATE) ,'%Y.%m.%d.') AS `from`,
					DATE_FORMAT(`search_filter_to` ,'%Y.%m.%d.') AS `to`
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt_tid`
				WHERE
					`date` BETWEEN CAST(DATE_FORMAT(`search_filter_to` ,'%Y-%m-01') as DATE) AND `search_filter_to`
					{$eccheck}
						AND `payroll_emp_id` IS NOT NULL
						AND (`transfer_id_2` = 'EPAM' OR `transfer_id_2` = 'EPAK')
				GROUP BY
					CONVERT(`transfer_id_2` USING BINARY),
					`payroll_emp_id`
			";
			$resSchenker2 = $conn->createCommand($SQLUNION2)->queryAll();

			$res_NBKifiz_CSV = array_merge($res_NBKifiz_CSV, $resSchenker1, $resSchenker2);
		}

        $travelCostCorrection = [];
        if ($cust === "KUEHNENAGEL_TEMPORARY_DELETED_250723") {
            $employeeData = [];
            $travelCostDays = $this->getTravelCostDays($originalValidFrom, $valid_to, $employeeData);
            $travelCostDaysNBKifizz = $this->getTravelCostDaysNBKifizz($originalValidFrom, $valid_to, $travelCostDays, $employeeData);
            $res_NBKifiz_CSV = array_merge($res_NBKifiz_CSV, $travelCostDaysNBKifizz);

            if ($this->locked) {
                PayrollTransferLockedMonthValues::payrollValuesSave($travelCostDaysNBKifizz, "NEXON", ['date' => 'from', 'employee_contract_id' => 'id']);
            }

            $lastMonthValidTo = (new \DateTime($originalValidFrom))->modify("-1 day");
            $lastMonthEmployeeData = [];
            $lastMonthTravelCostDays = $this->getTravelCostDays($valid_from, $lastMonthValidTo->format("Y-m-d"), $lastMonthEmployeeData);
            $travelCostDaysLastMonth = $this->getTravelCostDaysNBKifizz($valid_from, $lastMonthValidTo->format("Y-m-d"), $lastMonthTravelCostDays, $lastMonthEmployeeData);

            $travelIds = array_column($travelCostDaysLastMonth, 'id');
            $validFromDateTime = new \DateTime($valid_from);
            $lastMonthSavedPayrollValue = PayrollTransferLockedMonthValues::getPayrollValues($travelIds, 'NEXON', $validFromDateTime, $validFromDateTime);

            $travelCostCorrection = $this->getTravelCostCompare($travelCostDaysLastMonth, $lastMonthSavedPayrollValue);

        }

        if ($cust === "MAGNA") {
            $employeeData = [];
            $travelCostDays = $this->getTravelCostDays($originalValidFrom, $valid_to, $employeeData);
            $travelCostDaysNBKifizz = $this->getTravelCostDaysNBKifizzMagna($originalValidFrom, $valid_to, $travelCostDays, $employeeData);
            $res_NBKifiz_CSV = array_merge($res_NBKifiz_CSV, $travelCostDaysNBKifizz);
        }

		if ($thisMonthPtr["ptrCorrection"]) {
			foreach($res_NBKifiz_CSV as $bonus) {
				$currentLockedData[$bonus["id"]]["bonus"][$bonus["from"]][$bonus["transfer_id_2"]] = $bonus["hours"];
			}
		}

		if (($this->correctLastMonth || $this->correctLastTwoMonth) && $cust !== "LUFTHANSA")
		{
			$correctionArray = [];
			$correctionArray[] =
			[
				"company_id" => "0",
				"id" => "1",
				"rel" => "2",
				"transfer_id_2" => "3",
				"sum" => "4",
				"percent" => "5",
				"hours" => "6",
				"from" => "7",
				"to" => "8"
			];
			if ($cust === "BT") {
				unset($correctionArray[0]["rel"]);
			}
			$vTo = new DateTime($valid_to);
			$vTo->modify('last day of last month');
			$colCounter = 0;
			foreach ($res_NBKifiz_CSV as $key => $val)
			{
				if ($colCounter === 0) {
                    $colCounter = 1;
                    continue;
                }
                $valTo = substr($val["to"], 0, -1);
                $validTo = str_replace(".", "-", $valTo);
                $validToDate = new DateTime($validTo);

                if ($val["transfer_id_2"] != "MJV" && $val["transfer_id_2"] != "MJB" && $val["transfer_id_2"] != "BT")
                {
                    $found = false;
                    foreach ($lastMonthPtr as $row => $lmPtr)
                    {
                        $currHours	= str_replace(",", ".", $val["hours"]);
                        $lastHours	= str_replace(",", ".", $lmPtr["hours"]);
                        $currSum	= str_replace(",", ".", $val["sum"]);
                        $lastSum	= str_replace(",", ".", $lmPtr["sum"]);
                        if ($val["id"] == $lmPtr["id"] &&
                            $val["transfer_id_2"] == $lmPtr["transfer_id_2"] &&
                            $val["from"] == $lmPtr["from"] &&
                            $val["to"] == $lmPtr["to"] && $validToDate <= $vTo)
                        {
                            $lastMonthPtr[$row]["found"] = "1";
                            $found = true;
                            if ((float)$currHours != (float)$lastHours || (float)$currSum != (float)$lastSum)
                            {
                                $digSum	= strlen(substr(strrchr($currSum, "."), 1));
                                $digHour= strlen(substr(strrchr($currHours, "."), 1));
                                $diffS	= bcsub($currSum, $lastSum, $digSum);
                                $diffH	= bcsub($currHours, $lastHours, $digHour);
                                if ($cust === "BT") {
                                    $correctionArray[] =
                                    [
                                        "company_id" => $val["company_id"],
                                        "id" => $val["id"],
                                        "transfer_id_2" => $val["transfer_id_2"],
                                        "sum" => str_replace(".", ",", $diffS),
                                        "percent" => "0",
                                        "hours" => str_replace(".", ",", $diffH),
                                        "from" => $val["from"],
                                        "to" => $val["to"]
                                    ];
                                }
                                elseif ($cust === "KUEHNENAGEL") {
                                    if ($val["transfer_id_2"] == "ÉP") { $val["transfer_id_2"] = "Altido_NT_ÉP"; }
                                    if ($val["transfer_id_2"] == "T1") { $val["transfer_id_2"] = "Altido_NT_T1"; }
                                    if ($val["transfer_id_2"] == "T2") { $val["transfer_id_2"] = "Altido_NT_T2"; }
                                    if ($val["transfer_id_2"] == "TA") { $val["transfer_id_2"] = "Altido_NT_TA"; }
                                    if ($val["transfer_id_2"] == "CD") { $val["transfer_id_2"] = "Altido_NT_CD"; }
                                    if ($val["transfer_id_2"] == "VP") { $val["transfer_id_2"] = "Altido_NT_VP"; }
                                    $correctionArray[] =
                                    [
                                        "company_id" => $val["company_id"],
                                        "id" => $val["id"],
                                        "rel" => $val["rel"],
                                        "transfer_id_2" => $val["transfer_id_2"],
                                        "sum" => str_replace(".", ",", $diffS),
                                        "percent" => "0",
                                        "hours" => str_replace(".", ",", $diffH),
                                        "from" => $val["from"],
                                        "to" => $val["to"]
                                    ];
                                }
                                else {
                                    $correctionArray[] =
                                    [
                                        "company_id" => $val["company_id"],
                                        "id" => $val["id"],
                                        "rel" => $val["rel"],
                                        "transfer_id_2" => ($val["transfer_id_2"] == "CK" || $cust === "HUMANBIO") ? $val["transfer_id_2"] : "K" . $val["transfer_id_2"],
                                        "sum" => str_replace(".", ",", $diffS),
                                        "percent" => "0",
                                        "hours" => str_replace(".", ",", $diffH),
                                        "from" => $val["from"],
                                        "to" => $val["to"]
                                    ];
                                }
                            }
                        }
                    }

                    if (!$found && $validToDate <= $vTo)
                    {
                        if ($cust === "BT") {
                            $correctionArray[] =
                            [
                                "company_id" => $val["company_id"],
                                "id" => $val["id"],
                                "transfer_id_2" => $val["transfer_id_2"],
                                "sum" => $val["sum"],
                                "percent" => "0",
                                "hours" => $val["hours"],
                                "from" => $val["from"],
                                "to" => $val["to"]
                            ];
                        }
                        elseif ($cust === "KUEHNENAGEL") {
                            if ($val["transfer_id_2"] == "ÉP") { $val["transfer_id_2"] = "Altido_NT_ÉP"; }
                            if ($val["transfer_id_2"] == "T1") { $val["transfer_id_2"] = "Altido_NT_T1"; }
                            if ($val["transfer_id_2"] == "T2") { $val["transfer_id_2"] = "Altido_NT_T2"; }
                            if ($val["transfer_id_2"] == "TA") { $val["transfer_id_2"] = "Altido_NT_TA"; }
                            if ($val["transfer_id_2"] == "CD") { $val["transfer_id_2"] = "Altido_NT_CD"; }
                            if ($val["transfer_id_2"] == "VP") { $val["transfer_id_2"] = "Altido_NT_VP"; }
                            $correctionArray[] =
                            [
                                "company_id" => $val["company_id"],
                                "id" => $val["id"],
                                "rel" => $val["rel"],
                                "transfer_id_2" => $val["transfer_id_2"],
                                "sum" => $val["sum"],
                                "percent" => "0",
                                "hours" => $val["hours"],
                                "from" => $val["from"],
                                "to" => $val["to"]
                            ];
                        }
                        else {
                            $correctionArray[] =
                            [
                                "company_id" => $val["company_id"],
                                "id" => $val["id"],
                                "rel" => $val["rel"],
                                "transfer_id_2" => ($val["transfer_id_2"] == "CK" || $cust === "HUMANBIO"  || $cust === "VISTRA") ? $val["transfer_id_2"] : "K" . $val["transfer_id_2"],
                                "sum" => $val["sum"],
                                "percent" => "0",
                                "hours" => $val["hours"],
                                "from" => $val["from"],
                                "to" => $val["to"]
                            ];
                        }
                    }
                }

                if ($this->locked)
                {
                    $updateSQL = "
                        UPDATE `travel_settlement` ts
                        JOIN `employee_contract` ec ON ec.`employee_contract_id` = ts.`employee_contract_id` AND ec.`status` = " . Status::PUBLISHED . " AND (CURDATE() BETWEEN ec.`valid_from` AND ec.`valid_to`)
                        JOIN `employee` e ON e.`employee_id` = ec.`employee_id` AND e.`status` = " . Status::PUBLISHED . " AND (CURDATE() BETWEEN e.`valid_from` AND e.`valid_to`)
                        SET ts.`is_paid` = '1', ts.`modified_by` = '" . userID() . "', ts.`modified_on` = NOW()
                        WHERE e.`tax_number` = '" . $val["id"] . "' AND e.`tax_number` IS NOT NULL AND ts.`valid_from` <= '{$valid_to}' AND '{$originalValidFrom}' <= IFNULL(ts.`valid_to`, '" . App::getSetting("defaultEnd") . "')
                    ";
                    $conn->createCommand($updateSQL)->execute();
                }

                $validToCompare = new Datetime($valid_to);
                if ($validToDate->format("Y-m") < $validToCompare->format("Y-m")) {
                    unset($res_NBKifiz_CSV[$key]);
                }



			}

			$colCounter = 0;
			foreach ($lastMonthPtr as $lmPtr)
			{
				if ($colCounter === 0)
				{
                    $colCounter = 1;
                    continue;
                }
                if (!isset($lmPtr["found"]) && $lmPtr[0] != "0")
                {
                    if ($lmPtr["sum"] != "0")
                    {
                        $sum = "-" . $lmPtr["sum"];
                    } else {
                        $sum = "0";
                    }
                    if ($lmPtr["hours"] != "0") {
                        $hours = "-" . $lmPtr["hours"];
                    } else {
                        $hours = "0";
                    }
                    if ($cust === "BT") {
                        $correctionArray[] =
                        [
                            "company_id" => $lmPtr["company_id"],
                            "id" => $lmPtr["id"],
                            "transfer_id_2" => $lmPtr["transfer_id_2"],
                            "sum" => $sum,
                            "percent" => "0",
                            "hours" => $hours,
                            "from" => $lmPtr["from"],
                            "to" => $lmPtr["to"]
                        ];
                    }
                    else if ($cust === "KUEHNENAGEL") {
                        if ($lmPtr["transfer_id_2"] == "ÉP") { $lmPtr["transfer_id_2"] = "Altido_NT_ÉP"; }
                        if ($lmPtr["transfer_id_2"] == "T1") { $lmPtr["transfer_id_2"] = "Altido_NT_T1"; }
                        if ($lmPtr["transfer_id_2"] == "T2") { $lmPtr["transfer_id_2"] = "Altido_NT_T2"; }
                        if ($lmPtr["transfer_id_2"] == "TA") { $lmPtr["transfer_id_2"] = "Altido_NT_TA"; }
                        if ($lmPtr["transfer_id_2"] == "CD") { $lmPtr["transfer_id_2"] = "Altido_NT_CD"; }
                        if ($lmPtr["transfer_id_2"] == "VP") { $lmPtr["transfer_id_2"] = "Altido_NT_VP"; }
                        $correctionArray[] =
                        [
                            "company_id" => $lmPtr["company_id"],
                            "id" => $lmPtr["id"],
                            "rel" => $lmPtr["rel"],
                            "transfer_id_2" => $lmPtr["transfer_id_2"],
                            "sum" => $sum,
                            "percent" => "0",
                            "hours" => $hours,
                            "from" => $lmPtr["from"],
                            "to" => $lmPtr["to"]
                        ];
                    }
                    else {
                        $correctionArray[] =
                        [
                            "company_id" => $lmPtr["company_id"],
                            "id" => $lmPtr["id"],
                            "rel" => $lmPtr["rel"],
                            "transfer_id_2" => ($lmPtr["transfer_id_2"] == "CK" || $cust === "HUMANBIO") ? $lmPtr["transfer_id_2"] : "K" . $lmPtr["transfer_id_2"],
                            "sum" => $sum,
                            "percent" => "0",
                            "hours" => $hours,
                            "from" => $lmPtr["from"],
                            "to" => $lmPtr["to"]
                        ];
                    }
                }
			}
            //TODO: NBKifiz_Korr
			$fileNameNBKifizKorr = "NBKifiz_Korr$fileDate.csv";
			if ($this->correctLastTwoMonth) { $fileNameNBKifizKorr = "NBKifiz_elozo$fileDate.csv"; }
            $correctionArray = array_merge($correctionArray, $travelCostCorrection);
            $this->createInNexonPayroll($correctionArray, $fgID, $fileNameNBKifizKorr, $writeFiles, $path);

		}

		if (($this->correctLastMonth || $this->correctLastTwoMonth) && $cust !== "LUFTHANSA")
		{
			$payrollEmpIdsLastMonth = [];
			if (isset($correctionArray[1]) || !empty($idoadatPrevMonth)) {
				unset($correctionArray[0]);
				foreach ($correctionArray as $cA) {
					$payrollEmpIdsLastMonth[] = $cA["id"];
				}
				$payrollEmpIdsLastMonth = array_unique(array_merge($payrollEmpIdsLastMonth ?? [], $idoadatPrevMonth ?? []));
			}

			$vFromDt = new DateTime($valid_to);
			$vFromDt->modify('first day of this month');
			$whereSQL = " AND `date` >= '" . $valid_from . "'";

			//TODO: munkarend - NBMunkr
			$res_NBMunkr_CSV = $conn->createCommand($sqlNBMunkr . " WHERE 1=1" . $whereSQL)->queryAll();
			$toUnset = [];
			$prevMonthMrArr = [];
			foreach ($res_NBMunkr_CSV as $key => $munkrData)
			{
				if ($cust === "KUEHNENAGEL") {
					if ($key == "0") {
						$prevMonthMrArr[] = $munkrData;
					} else {
						$dateStart = new DateTime(mb_substr(str_replace(".", "-", $munkrData[4]), 0, -1));
						if ($dateStart < $vFromDt) {
							$prevMonthMrArr[] = $munkrData;
							$toUnset[] = $key;
						}
					}
				} else {
					if (!in_array($munkrData[1], $payrollEmpIdsLastMonth) && $key != "0") {
						$dateStart = new DateTime(mb_substr(str_replace(".", "-", $munkrData[4]), 0, -1));
						if ($dateStart < $vFromDt) {
							$toUnset[] = $key;
						}
					}
				}
			}
			foreach ($toUnset as $k) { unset($res_NBMunkr_CSV[$k]); }
			$fileNameNBMunkr = "NBMunkr$fileDate.csv";

            $this->createInNexonPayroll($res_NBMunkr_CSV, $fgID, $fileNameNBMunkr, $writeFiles, $path);

			//TODO: munkarend előző - NBMunkr_elozo
			if (!empty($prevMonthMrArr))
			{
				$fileNameNBMunkrPrev = "NBMunkr_elozo.csv";
                $this->createInNexonPayroll($prevMonthMrArr, $fgID, $fileNameNBMunkrPrev, $writeFiles, $path);

			}
			//TODO: munkarend óra - NBMunkrOra
			$res_NBMunkrOra_CSV = $conn->createCommand($SQL_NBMunkrOra_CSV . $whereSQL)->queryAll();
			$toUnset = [];
			$prevMonthMrOraArr = [];
			foreach ($res_NBMunkrOra_CSV as $key => $munkrOraData) {
                if ($cust === "KUEHNENAGEL") {
					if ($key == "0") {
						$prevMonthMrOraArr[] = $munkrOraData;
					} else {
						$dateStart = new DateTime(mb_substr(str_replace(".", "-", $munkrOraData[3]), 0, -1));
						if ($dateStart < $vFromDt) {
							$prevMonthMrOraArr[] = $munkrOraData;
							$toUnset[] = $key;
						}
					}
                } else {
					if (!in_array($munkrOraData[1], $payrollEmpIdsLastMonth) && $key != "0") {
						$dateStart = new DateTime(mb_substr(str_replace(".", "-", $munkrOraData[3]), 0, -1));
						if ($dateStart < $vFromDt) {
							$toUnset[] = $key;
						}
					}
				}
			}
			foreach ($toUnset as $k) { unset($res_NBMunkrOra_CSV[$k]); }
			$fileNameNBMunkrOra = "NBMunkrOra$fileDate.csv";

            $this->createInNexonPayroll($res_NBMunkrOra_CSV, $fgID, $fileNameNBMunkrOra, $writeFiles, $path);

			//TODO: munkarend óra előző - NBMunkrOra_elozo
			if (!empty($prevMonthMrOraArr))
			{
				$fileNameNBMunkrOraPrev = "NBMunkrOra_elozo.csv";
                $this->createInNexonPayroll($prevMonthMrOraArr, $fgID, $fileNameNBMunkrOraPrev, $writeFiles, $path);
			}
			//TODO: időadat óra - NBIdoadatOra
			$res_NBIdoadatOra_CSV = $conn->createCommand($SQL_NBIdoadatOra_CSV . $whereSQL)->queryAll();
			$toUnset = [];
			$prevMonthIdoadatOraArr = [];
			foreach ($res_NBIdoadatOra_CSV as $key => $idoOraData) {
				if ($cust === "KUEHNENAGEL") {
					if ($key == "0") {
						$prevMonthIdoadatOraArr[] = $idoOraData;
					} else {
						$dateStart = new DateTime(mb_substr(str_replace(".", "-", $idoOraData[3]), 0, -1));
						if ($dateStart < $vFromDt) {
							$prevMonthIdoadatOraArr[] = $idoOraData;
							$toUnset[] = $key;
						}
					}
                } else {
                    if (!in_array($idoOraData[1], $payrollEmpIdsLastMonth) && $key != "0") {
                        $dateStart = new DateTime(mb_substr(str_replace(".", "-", $idoOraData[3]), 0, -1));
                        if ($dateStart < $vFromDt) {
                            $toUnset[] = $key;
                        }
                    }
                }
			}
			foreach ($toUnset as $k) { unset($res_NBIdoadatOra_CSV[$k]); }
			$fileNameNBIdoadatOra = "NBIdoadatOra$fileDate.csv";
            $this->createInNexonPayroll($res_NBIdoadatOra_CSV, $fgID, $fileNameNBIdoadatOra, $writeFiles, $path);

			//TODO: időadat óra előző - NBIdoadatOra_elozo
			if (!empty($prevMonthIdoadatOraArr))
			{
				$fileNameNBIdoadatOraPrev = "NBIdoadatOra_elozo.csv";
                $this->createInNexonPayroll($prevMonthIdoadatOraArr, $fgID, $fileNameNBIdoadatOraPrev, $writeFiles, $path);
			}
		}

		$res_NBKifiz2_CSV = [];

		if ($cust === "XYLEM") {
    		foreach ($res_NBKifiz_CSV as $csv_key => $csv_line) {
				if ($csv_line["transfer_id_2"] === "UKT" || $csv_line["transfer_id_2"] === "1K") {
					if ($csv_line["transfer_id_2"] === "UKT") {
						$csv_line["sum"] = $csv_line["hours"];
						$csv_line["hours"] = '0';
					}
					$res_NBKifiz2_CSV[] = $csv_line;
					$res_NBKifiz_CSV[$csv_key] = null;
				}
			}
		}

        if ($cust === 'PERI') {
            $newNbkifiz = [];
            $numberFormat = new NumberFormatter("hu-HU", \NumberFormatter::DECIMAL);
            foreach($res_NBKifiz_CSV as $key => $value) {
                $workgroupId = $value['workgroup_id'];
                unset($value['workgroup_id']);
                if ($workgroupId == 'f0412d095a920af5ac20e0144575dd17' && $value['transfer_id_2'] == 'T1' && (int)$value['hours'] > 2) {
                    $hours = $numberFormat->parse($value['hours']);
                    for($i = 0; $i < (int)$hours; $i++) {
                        $newNbkifiz[] = [
                                        'id' => $value['id'], 
                                        'transfer_id_2' => ($i < 2 ? 'T1' : 'T2'), 
                                        'sum' => ($i < 2 ? '50' : '100'), 
                                        'hours' => 1, 
                                        'from' => $value['from'],
                                        'to' => $value['to']
                        ];    
                    }
                    if ($i < $hours) {
                        $newNbkifiz[] = [
                            'id' => $value['id'], 
                            'transfer_id_2' => 'T2', 
                            'sum' => '100', 
                            'hours' => (float)($hours - $i), 
                            'from' => $value['from'],
                            'to' => $value['to']
                        ];
                    }
                }
                else {
                    $newNbkifiz[] = $value;
                }


            }
            $res_NBKifiz_CSV = $newNbkifiz;
        }

        $NBPKifizExcention = ['SKUBA'];
        if (!in_array($cust, $NBPKifizExcention, true)) {
            $res_NBKifiz_CSV = array_values($res_NBKifiz_CSV);
            $fileNameNBKifiz = "NBKifiz$fileDate.csv";
            $this->createInNexonPayroll($res_NBKifiz_CSV, $fgID, $fileNameNBKifiz, $writeFiles, $path);
        }

        $nbKifizCorr = ['LUFTHANSA'];
        if (in_array($cust, $nbKifizCorr, true)) {
            $resultNbKifizCorr = array_values($resultNbKifizCorr);
            $fileNameNBKifiz = "NBKifiz_elozo$fileDate.csv";
            $this->createInNexonPayroll($resultNbKifizCorr, $fgID, $fileNameNBKifiz, $writeFiles, $path);
        }



		if (count($res_NBKifiz2_CSV)) {
			$fileNameNBKifiz2 = "NBKifiz2$fileDate.csv";
			$csv = ArrayToCsv::getCsvText($res_NBKifiz2_CSV);
			$csv = iconv("UTF-8", "ISO-8859-1", $csv);
			$fs = new FS;
			$fs->disableMySQLStore();
			$fs->setFileGroupID($fgID);
			$fs->uploadTextFileFromContent($fileNameNBKifiz2, $csv, "text/csv");
			$fileNameNBKifiz2_fileId = $fs->getSmallFileID() . "_" .$fileNameNBKifiz2;
			if ($writeFiles) {
				$file = fopen($path.DIRECTORY_SEPARATOR.$fileNameNBKifiz2, 'w');
				fwrite($file, $csv);
				fclose($file);
			}
		}

		if ($cust === "PERI") {
            $SQL_Travel_XLSX = "
				SELECT
					`fullname`,
					`tax_number`,
					`option2` AS whole_travel_distance_by_km,
					`option3` AS ft_km,
					SUM(
						IF(
							(`wsu_type_of_daytype` = 'WORKDAY' AND `state_type_id` IS NULL) 
							 OR
							 (`wsu_type_of_daytype` = 'RESTDAY' AND `value` > 0)
							, 
							1, 
							0)
					) AS working_days,
					CONCAT(
						SUM(`option2` * `option3` *
							IF(
							(`wsu_type_of_daytype` = 'WORKDAY' AND `state_type_id` IS NULL) 
							 OR
							 (`wsu_type_of_daytype` = 'RESTDAY' AND `value` > 0)
							, 1, 0)
						), ' Ft'
					) AS payable
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
						AND `payroll_emp_id` IS NOT NULL
				GROUP BY payroll_emp_id
			";

			$fileNameTravel = "Munkabajarasktgteriteslista" . $year . ".xlsx";
			$beforeTravelHeaderData = $this->beforeTravelHeader($monthName);
			$headerTravelData = $this->travelHeaderNames();
			$afterTravelHeaderData = $this->afterTravelHeader();
			$travelData = $conn->createCommand($SQL_Travel_XLSX)->queryAll();
			$travelFileData = array_merge($beforeTravelHeaderData, $headerTravelData, $travelData, $afterTravelHeaderData);
			$this->nexonUploadXlsxFile($fileNameTravel, $travelFileData, $prtID);
        }

		if ($cust === "PERI") {
			$SQL_HomeOffice_XLSX = "
				SELECT
					'' AS empty1,
					`fullname`,
					`tax_number`,
					SUM(
						IF(`state_type_id` = '17c82fbc41e2abf10dff3a857222ec14', 1, 0)
					) AS home_office,
					SUM(
						IF(`wsu_type_of_daytype` = 'WORKDAY' AND `state_type_id` IS NULL, 1, 0)
					) AS day_at_office,
					SUM(
						IF(`state_type_id` = 'a272f564576d443e7832587126b070aa', 1, 0)
					) AS onleave,
					SUM(
						IF(`has_absence` = '1' AND `state_type_id` <> '17c82fbc41e2abf10dff3a857222ec14', 1, 0)
					) AS other,
					'' AS summary,
					'' AS empty2,
					option1 AS whole_amount,
					'' AS ho_per_office,
					'' AS month_calculated_value,
					SUM(
						IF(`wsu_type_of_daytype` = 'WORKDAY', 1, 0)
					) AS number_of_working_days_per_month
				FROM
					`temp_payroll_transfer_employee_calc_payroll_data_dt`
				WHERE
					`date` BETWEEN `search_filter_from` AND `search_filter_to`
					AND `payroll_emp_id` IS NOT NULL
				GROUP BY `payroll_emp_id`
			";

			$res_HomeOffice_XLSX = $conn->createCommand($SQL_HomeOffice_XLSX)->queryAll();

			$numberOfWorkingDays = 0;

			foreach ($res_HomeOffice_XLSX as &$homeOffice)
			{
				$homeOffice['summary'] = $homeOffice['home_office'] + $homeOffice['day_at_office'] + $homeOffice['onleave'] + $homeOffice['other'];

                if (!empty($homeOffice['home_office']) && !empty($homeOffice['whole_amount']) && is_integer((int)$homeOffice['whole_amount'])) {
					$homeOffice['ho_per_office'] = number_format(($homeOffice['home_office'] / $homeOffice['number_of_working_days_per_month']) * 100, 1) . ' %';
					$convertToPercent = str_replace(',', '.', rtrim($homeOffice['ho_per_office'], '%'));
					$ratio = $convertToPercent / 100;
					$homeOffice["month_calculated_value"] = $homeOffice["whole_amount"] * $ratio . ' Ft';
				} else {
					$homeOffice['ho_per_office'] = number_format($homeOffice['home_office'], 1) . ' %';
					$homeOffice["month_calculated_value"] = $homeOffice['home_office'] . ' Ft';
				}

				$numberOfWorkingDays = $homeOffice['number_of_working_days_per_month'];
				unset($homeOffice['number_of_working_days_per_month']);
			}
	
			$fileNameHomeOffice = "Homeofficekoltsegterites" . $year . ".xlsx";
			$beforeHomeOfficeHeaderData = $this->beforeHomeOfficeHeader($monthName, $numberOfWorkingDays);
			$headerHomeOfficeData = $this->homeOfficeHeaderNames();
			$homeOfficeFileData = array_merge($beforeHomeOfficeHeaderData, $headerHomeOfficeData, $res_HomeOffice_XLSX);
			$this->nexonUploadXlsxFile($fileNameHomeOffice, $homeOfficeFileData, $prtID);
        }

        if ($cust === "HAVI") {
            $employeeIdSql = "SELECT `employee_id`, `tax_number`, `fullname`, `mnth__total_worktime_sum` FROM `temp_payroll_transfer_employee_calc_payroll_data` GROUP BY `employee_id`;";
            $employeeIdSqlResult = dbFetchAll($employeeIdSql, 'employee_id');
            $employeeIds = array_keys($employeeIdSqlResult);

            $employeeExt5 = new EmployeeExt5();
            $criteria = new CDbCriteria();
            $criteria->alias = 't';
            $criteria->select = "t.`employee_id`,
                                    t.`ext5_option1`,
                                    t.`ext5_option2`,
                                    t.`ext5_option3`,
                                    t.`ext5_option4`,
                                    t.`ext5_option5`,
                                    t.`ext5_option6`
                                    ";
            $criteria->condition = "'$valid' BETWEEN t.`valid_from` AND t.`valid_to`
                AND t.`status` = ". Status::PUBLISHED ."
                AND t.`employee_id` IN ('" . implode("', '", $employeeIds) . "')
            ";

            $employeeExt5Result = $employeeExt5->findAll($criteria);

            $xlsxResult = [];
            foreach($employeeExt5Result as $employeeId => $ext5) {
                if (empty($ext5)) {
                    continue;
                }
                $temp['fullname'] = $employeeIdSqlResult[$ext5->employee_id]['fullname'];
                $temp['taxNumber'] = $employeeIdSqlResult[$ext5->employee_id]['tax_number'];

                $monthWorktime = round($employeeIdSqlResult[$ext5->employee_id]['mnth__total_worktime_sum'] / 3600, 1);
                $inputTelj = str_replace('%', '', $ext5->ext5_option4);
                $inputTelj = (float)str_replace(',', '.', $inputTelj);
                $temp['teljesitmeny'] =round($monthWorktime * (int)$ext5->ext5_option1 * ($inputTelj / 100), 0);

                $inputPontos = str_replace('%', '', $ext5->ext5_option5);
                $inputPontos = (float)str_replace(',', '.', $inputPontos);
                $temp['pontos'] = round($monthWorktime * (int)$ext5->ext5_option2 * ($inputPontos / 100), 0);

                $inputKarb = str_replace('%', '', $ext5->ext5_option6);
                $inputKarb = (float)str_replace(',', '.', $inputKarb);
                $temp['karbantartas'] = round($monthWorktime * (int)$ext5->ext5_option3 * ($inputKarb / 100), 0);
                $xlsxResult[] = $temp;
            }

            $fileName = date("Y-m-d", strtotime($valid_to)). 'Mozgober.xlsx';
            $header[] = [date("Y-m", strtotime($valid_from)), 'Adószám', 'Teljesítmény prémium (60%)', 'Pontos munkavégzés prémium (25%)', 'Karbantartási selejt (15%)'];
            $fileResult = array_merge($header, $xlsxResult);
            $this->nexonUploadXlsxFile($fileName, $fileResult, $prtID);
        }
		if ($csgFileCreate)
		{
			$fileNameTTWAtoNEXON = "nxTIME.CSG";
			$fileTemplateTTWAtoNEXON = Yang::getBasePath().'/../protected/components/Grid2PayrollTransfer/controllers/PayrollTransfer/Templates/TemplateNxTimeCsg.txt';
			$nexonCsgPath = Yang::getParam("nexonCsgPath");
            if ($cust === 'ROSENBERGER') {
                $nexonCsgPath .= $folderDate;
            }
            $nexonCsgCodes = Yang::getParam("nexonCsgCodes");
            $nexonCsgVersion = Yang::getParam("nexonCsgVersion");
            $nexonCsgCompanyInfo = Yang::getParam("nexonCsgCompanyInfo");
            $csg = file_get_contents($fileTemplateTTWAtoNEXON);

            $csg = str_replace(
                array(
                    "{version}",
                    "{companyInfo}",
                    "{date}",
                    "{startDate}",
                    "{endDate}",
                    "{path}",
                    "{fileNameNBKifiz}",
                    "{fileNameNBMunkr}",
                    "{fileNameNBMunkrOra}",
                    "{fileNameNBIdoadat}",
                    "{fileNameNBIdoadatOra}",
                    "{fileNameNBPBeoszt}",
                    "{nexonCodes}"
                ),
                array(
                $nexonCsgVersion,
                $nexonCsgCompanyInfo,
                date("Y.m.d H:i:s"),
                date("Y.m.d", strtotime($valid_from)),
                date("Y.m.d", strtotime($valid_to)),
                $nexonCsgPath,
                $fileNameNBKifiz,
                $fileNameNBMunkr,
                $fileNameNBMunkrOra,
                $fileNameNBIdoadat,
                $fileNameNBIdoadatOra,
                $fileNameNBPBeoszt,
                $nexonCsgCodes
            ),
                $csg
            );

			$fs = new FS;
			$fs->disableMySQLStore();
			$fs->setFileGroupID($fgID);
			$fs->uploadTextFileFromContent($fileNameTTWAtoNEXON, $csg, "text/plain");
			if ($writeFiles) {
				$file = fopen($path.DIRECTORY_SEPARATOR.$fileNameTTWAtoNEXON, 'w');
				fwrite($file, $csg);
				fclose($file);
				$file = fopen($pointerFilePath.DIRECTORY_SEPARATOR.$fileNameTTWAtoNEXON, 'w');
				fwrite($file, $csg);
				fclose($file);
			}
		}

		if ($thisMonthPtr["ptrCorrection"] && $this->locked)
		{
			// Calculate corrections - $thisMonthPtr | $currentLockedData
			$corrections = [];

			// Schedule
			$scheduleTypes = [
				"1"	=> Dict::getValue("daytype_type_of_day_workday"),
				"2"	=> Dict::getValue("absence_type_restday"),
				"3"	=> Dict::getValue("feastday")
			];

			// Workday type
			$workdayTypesSQL = "
				SELECT
					ptc.`transfer_id` AS code,
					ptc.`content_value` AS state_type_id,
					d.`dict_value` AS note
				FROM
					`payroll_transfer_config` ptc
				JOIN
					`state_type` st ON
							st.`state_type_id` = ptc.`content_value`
						AND st.`status` = " . Status::PUBLISHED . "
				JOIN
					`dictionary` d ON
							d.`dict_id` = st.`name_dict_id`
						AND d.`valid` = '1'
						AND d.`lang` = '" . Dict::getLang() . "'
				WHERE
						ptc.`status` = " . Status::PUBLISHED . "
					AND ptc.`content_type` = 'state_type_id'
			";
			$workdayTypesRes = $conn->createCommand($workdayTypesSQL)->queryAll();
			$workdayTypes = [];
			foreach ($workdayTypesRes as $wDT) {
				$workdayTypes[$wDT["code"]][$wDT["state_type_id"]] = $wDT["note"];
			}
			$workdayTypes["1"]["no_state_type"] = Dict::getValue("card_type_normal");
			$workdayTypes["3"]["no_state_type"] = Dict::getValue("feastday");

			// Bonus
			$bonusTypesSQL = "
				SELECT
					ptc.`transfer_id` AS code,
					ptc.`note` AS note
				FROM
					`payroll_transfer_config` ptc
				WHERE
						ptc.`status` = " . Status::PUBLISHED . "
					AND ptc.`content_type` = 'inside_type_id'
			";
			$bonusTypesRes = $conn->createCommand($bonusTypesSQL)->queryAll();
			$bonusTypes = [];
			foreach ($bonusTypesRes as $bT) {
				$bonusTypes[$bT["code"]] = $bT["note"];
			}

			// Differences
			$resultsMinus = $this->arrayDiffAssocMultidimensional($thisMonthPtr, $currentLockedData);
			$resultsPlus = $this->arrayDiffAssocMultidimensional($currentLockedData, $thisMonthPtr);
			// Prev no longer: - hours
			foreach ($resultsMinus as $empId => $changes)
			{
				foreach ($changes as $type => $values)
				{
					switch ($type)
					{
						case "schedule":
							foreach ($values as $date => $val)
							{
								$corrections[] = [
									"emp_id"		=> $empId,
									"date"			=> $date,
									"value_change"	=> '-' . $thisMonthPtr[$empId]["schedHours"][$date],
									"payroll_code"	=> Dict::getValue("schedule") . ' - ' . $scheduleTypes[$val],
									"type"			=> "previous"
								];
							}
						break;
						case "schedHours":
							foreach ($values as $date => $val)
							{
								$corrections[] = [
									"emp_id"		=> $empId,
									"date"			=> $date,
									"value_change"	=> '-' . $val,
									"payroll_code"	=> Dict::getValue("scheduleHoursChange"),
									"type"			=> "previous"
								];
							}
						break;
						case "workType":
							foreach ($values as $date => $val)
							{
								$corrections[] = [
									"emp_id"		=> $empId,
									"date"			=> $date,
									"value_change"	=> '-' . $thisMonthPtr[$empId]["workHours"][$date],
									"payroll_code"	=> Dict::getValue("workType") . ' - ' . $workdayTypes[$thisMonthPtr[$empId]["workType"][$date]["timedata"]][$val["state_type_id"]],
									"type"			=> "previous"
								];
							}
						break;
						case "workHours":
							foreach ($values as $date => $val)
							{
								$corrections[] = [
									"emp_id"		=> $empId,
									"date"			=> $date,
									"value_change"	=> '-' . $val,
									"payroll_code"	=> Dict::getValue("workedHoursChange"),
									"type"			=> "previous"
								];
							}
						break;
						case "bonus":
							foreach ($values as $date => $transferIds)
							{
								foreach ($transferIds as $tId => $val)
								{
									$corrections[] = [
										"emp_id"		=> $empId,
										"date"			=> $date,
										"value_change"	=> '-' . $val,
										"payroll_code"	=> $bonusTypes[$tId],
										"type"			=> "previous"
									];
								}
							}
						break;
					}
				}
			}

			// New added
			foreach ($resultsPlus as $empId => $changes)
			{
				foreach ($changes as $type => $values)
				{
					switch ($type)
					{
						case "schedule":
							foreach ($values as $date => $val)
							{
								$corrections[] = [
									"emp_id"		=> $empId,
									"date"			=> $date,
									"value_change"	=> $currentLockedData[$empId]["schedHours"][$date],
									"payroll_code"	=> Dict::getValue("schedule") . ' - ' . $scheduleTypes[$val],
									"type"			=> "new"
								];
							}
						break;
						case "schedHours":
							foreach ($values as $date => $val)
							{
								$corrections[] = [
									"emp_id"		=> $empId,
									"date"			=> $date,
									"value_change"	=> $val,
									"payroll_code"	=> Dict::getValue("scheduleHoursChange"),
									"type"			=> "new"
								];
							}
						break;
						case "workType":
							foreach ($values as $date => $val)
							{
								$corrections[] = [
									"emp_id"		=> $empId,
									"date"			=> $date,
									"value_change"	=> $currentLockedData[$empId]["workHours"][$date],
									"payroll_code"	=> Dict::getValue("workType") . ' - ' . $workdayTypes[$currentLockedData[$empId]["workType"][$date]["timedata"]][$val["state_type_id"]],
									"type"			=> "new"
								];
							}
						break;
						case "workHours":
							foreach ($values as $date => $val)
							{
								$corrections[] = [
									"emp_id"		=> $empId,
									"date"			=> $date,
									"value_change"	=> $val,
									"payroll_code"	=> Dict::getValue("workedHoursChange"),
									"type"			=> "new"
								];
							}
						break;
						case "bonus":
							foreach ($values as $date => $transferIds)
							{
								foreach ($transferIds as $tId => $val)
								{
									$corrections[] = [
										"emp_id"		=> $empId,
										"date"			=> $date,
										"value_change"	=> $val,
										"payroll_code"	=> $bonusTypes[$tId],
										"type"			=> "new"
									];
								}
							}
						break;
					}
				}
			}

			// Save corrections to db
			if (!empty($corrections))
			{
				$insertSQL = "INSERT INTO `payroll_transfer_corrections` (`emp_id`, `date`, `value_change`, `payroll_code`, `type`, `payroll_transfer_id`, `status`, `created_by`, `created_on`) VALUES";
				$i = 0;
				foreach ($corrections as $corr)
				{
					if ($corr["emp_id"] != "" && $corr["date"] != "" && $corr["value_change"] != "" && $corr["value_change"] != "-")
					{
						$i++;
						$insertSQL .= " (
							'" . $corr["emp_id"] . "',
							'" . mb_substr(str_replace(".", "-", $corr["date"]), 0, -1) . "',
							" . str_replace(",", ".", $corr["value_change"]) . ",
							'" . $corr["payroll_code"] . "',
							'" . $corr["type"] . "',
							'" . $prtID . "',
							" . Status::PUBLISHED . ",
							'ptc_sys',
							NOW()
						),";
					}
				}

				if ($i > 0) { $conn->createCommand(substr($insertSQL, 0, -1) . ";")->execute(); }
			}
		}

	}

	private function nexonUploadXlsxFile($fileName, $data, $prtID)
    {
        $excelOutput = $this->xlsxWriterFromArrayToString(["EASE"], $data);

		$fs = new FS;
		$fs->disableMySQLStore();
		$fs->setFileGroupID($prtID);
		$fs->uploadFileFromContent($fileName, $excelOutput, 'application/vnd.ms-excel');
    }

	private function beforeTravelHeader($monthName)
	{
		$beforeHeader = [
			[''],
			[Dict::getValue("subsidy_for_commuting_to_work")],
			[''],
			[Dict::getValue("accounting_period") . ' ' . $monthName],
			['']
		];

		return $beforeHeader;
	}

	private function travelHeaderNames()
    {
        $header[0] = [
			Dict::getValue("name"),
			Dict::getValue("tax_number"),
			Dict::getValue("km_traveled_there_and_back"),
			Dict::getValue("option3"),
			Dict::getValue("number_of_working_days_worked"),
			Dict::getValue("payable")
		];

        return $header;
    }

	private function afterTravelHeader()
	{
		$afterHeader = [
			[''],
			[Dict::getValue("exact_company_name")],
			[Dict::getValue("company_address")]
		];

		return $afterHeader;
	}

	private function beforeHomeOfficeHeader($monthName, $numberOfWorkingDays)
	{
		$afterHeader = [
			[''],
			[$monthName],
			[''],
			[Dict::getValue("number_of_working_days_per_month") . ': ' . $numberOfWorkingDays],
			['']
		];

		return $afterHeader;
	}

	private function homeOfficeHeaderNames()
    {
        $header[0] = [
			'',
			Dict::getValue("name"),
			Dict::getValue("tax_number"),
			Dict::getValue("home_office_days"),
			Dict::getValue("days_spent_in_the_office"),
			Dict::getValue("onleave"),
			Dict::getValue("other"),
			Dict::getValue("sum"),
			'',
			Dict::getValue("total_net_cost"),
			Dict::getValue("ho_office"),
			Dict::getValue("monthly_cost")
		];

        return $header;
    }

	/**
	 * Get differences in two arrays
	 * @param array $array1
	 * @param array $array2
	 * @return array difference
	 */
	private function arrayDiffAssocMultidimensional(array $array1, array $array2)
	{
		$difference = [];
		foreach ($array1 as $key => $value)
		{
			if (is_array($value))
			{
				if (!array_key_exists($key, $array2)) {
					$difference[$key] = $value;
				} elseif (!is_array($array2[$key])) {
					$difference[$key] = $value;
				} else {
					$multidimensionalDiff = $this->arrayDiffAssocMultidimensional($value, $array2[$key]);
					if (count($multidimensionalDiff) > 0) {
    					$difference[$key] = $multidimensionalDiff;
					}
				}
			} else {
				if (!array_key_exists($key, $array2) || $array2[$key] !== $value) {
					$difference[$key] = $value;
				}
			}
		}
		return $difference;
	}
}
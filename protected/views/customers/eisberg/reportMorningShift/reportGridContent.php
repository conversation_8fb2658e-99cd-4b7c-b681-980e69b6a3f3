<?php
	$html = '
		<style type="text/css">

			.clear-float {
			    clear: both;
			}

            div.print {
                background-color: white;
            }

			table {
                border-collapse: collapse;
                border: none;
			}

			.header-logo {
                width: 170px;
				border-right: none;
			}

            .header-logo img {
                display: block;
                width: 160px;
                height: 70px;
                margin-bottom: 10px;
                padding-left: 6px;
            }

			table.main-table {
                width: 100%;
                margin-top: 2rem;
                font-size: 0.8rem;
                margin-bottom: 2rem;
				max-width: 1250px;
			}

			table.main-table tr td {
                border: 1px solid black;
                padding-left: 4px;
			}

            table.main-table tr.main-header td {
                text-align: center;
				background-color: lightgrey;
				text-align: center;
                font-weight: bold;
            }

            table.main-table td {
                padding-top: 0.2rem;
                padding-bottom: 0.2rem;
                vertical-align: top;
            }

            table.main-table tr.week-num td {
                font-weight: bold;
            }

			table td.num {
				text-align: right;
				padding-right: 2px;
			}

			table td.noborder,
			table td.cell-strong {
			    font-weight: bold;
			}

            table.noborder {
                margin-bottom: 2rem;
                width: 100%;
            }

            table.noborder td.noborder {
                font-weight:bold;
            }

			div.page-break {
				display: block;
				page-break-before: always;
				height: 0px;
			}

			.form {
				background: #fdfdfe;
				height: 100%;
			}

			table,
			div.report_screen,
			div.print {
				max-width: 1250px;
			}

        	.pdfBody .form {
				background: #fff;
			}

			.pdfBody table,
			.pdfBody div.report_screen,
			.pdfBody div.print {
				max-width: none;
			}

			.pdfBody table td {
			}
			
			.bordered td {
				border: 1px solid #000;
			}

			.center {
				text-align: center;
			}

			div.report_screen table.noborder td {
				border: 0px;
			}

			table.main-table tr td.column-fullname {
				white-space: nowrap;
			}
			
		</style>
	';

	$html .='
        <div class="form">
            <div class="report_screen">
                <div class="print" id="print">
                    <table style="margin-top: 12px">
                        <tr>
                            <td class="header-logo center" style="width: 30%; border: 0px;"><img src="/images/homeDashboard/logo/eisberg-logo.svg" style="display: block;" /></td>
                            <td style="width: 70%; border: 0px; white-space: nowrap; padding-top: 12px"><h1>' . Dict::getModuleValue('ttwa-wfm','menu_item_reportMorningShift_eisberg') . '</h1></td>
                        </tr>
                    </table>
                    <div>
    ';

    $headerRow = '';

    // Main table header

    $tableStart = '
        <table class="main-table">
            <tr class="main-header">
                <td>' . $header['dateWithTime'] . '</td>
                <td colspan="7">' . $header['date'] . '</td>
            </tr>
            <tr class="main-header">
                <td>' . $header['dateWithWeek'] . '</td>
                <td>' . $header['dayName'] . '</td>
                <td>' . $header['workTimeStart'] . '</td>
                <td>' . $header['workTimeEnd'] . '</td>
                <td>' . $header['dailyCompetency'] . '</td>
                <td>' . $header['secondaryTasks'] . '</td>
                <td>' . $header['note'] . '</td>
                <td>' . $header['balanceDowntimeOtPlan'] . '</td>
            </tr>
    ';

    $html .=$tableStart;
    $index = "idx";
    $xlsxData = [];
    $xlsxData[$index]['table_start'] = [
		[Dict::getValue('morningShiftList'), '','','','','', '', ''],
        [$header['dateWithTime'], $header['date'],'','','','', '', ''],
        [$header['dateWithWeek'], $header['dayName'], $header['workTimeStart'], $header['workTimeEnd'], $header['dailyCompetency'], $header['secondaryTasks'], $header['note'], $header['balanceDowntimeOtPlan']],
    ];
    
    foreach ($results as $key => $line)
	{
        $html .='
            <tr>
                <td class="column-fullname" style="width: 20%">' . $line['fullname'] . '</td>
                <td class="center" style="width: 7%"> ' . (($line['white_cap'] == 1) ? '<span style="font-weight: bolder; text-decoration: underline">X</span>' : 'X') . '</td>
                <td class="center" style="width: 12%"> ' . $line['workTimeStart'] . '</td>
                <td class="center" style="width: 12%"> ' . $line['workTimeEnd'] . '</td>
                <td style="width: 12%;"> ' . $line['area'] . '</td>
                <td style="width: 12%"> ' . implode(', ', $line['secondaryTasks']) . '</td>
                <td style="width: 12%"> ' . $line['note'] . '</td>
                <td class="center" style="font-weight: bold; width: 12%; background-color: '. $line['background_color'] .'"> ' . $line['balanceDowntimeOtPlan'] . '</td>
            </tr>
        ';

        $xlsxData[$index]['data'][$key] = [$line['fullname'], 'X' . (($line['white_cap'] == 1) ? '*' : '') , $line['workTimeStart'], $line['workTimeEnd'], $line['area'], implode(', ', $line['secondaryTasks']), $line['note'], $line['balanceDowntimeOtPlan']];
    }
    
	$html .='
				</table>
				<div style="height: 100px"></div>
			</div>
		</div>
	</div>
    ';

    if (!isset($_POST['generatePDF'])) {
        'HTML printing megoldás';{

            $printableTemplate = __DIR__."/reportGridContent.printable.html";
            if (file_exists($printableTemplate)) {

                $template = file_get_contents($printableTemplate);
                $contents = $html;
                $contents = strtr($contents,['<div class="page-break"></div><div class="page-break"></div>' => ""]);
                $contents = strtr($contents,['<div class="print" id="print"><div class="page-break"></div>' => ""]);
                $printhtm = strtr($template,['{{contents}}' => $contents]);

                $html .= '
                    <script>
                        setTimeout(function() {
                            console.log("Installing HTML print solution");
                            $("body").off("dblclick").on("dblclick",function() {
                                var printableHTML = ' . json_encode($printhtm) . ';
                                var h = window.open("","","width=1600, height=800, top=0, left=0");
                                h.document.write(printableHTML);
                                h.document.close();
                            });
                        },300);
                    </script>
                ';
            }
        }
        $_SESSION["tiptime"]["reportMorningShift"] = $xlsxData;
        
        echo $html;
    }

	$fileName = "content.html";
	$content = '
		<html>
		<head>
			<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		</head>
		<body>
			<div class="pdfBody">
				'.$html.'
			</div>
		</body>
		</html>
	';

	$controllerId = $this->getControllerID();

	$fs = new FS;
	$fs->disableMySQLStore();
	$fs->setFileGroupID("NodePDF_".$controllerId);
	$fs->deleteFilesByUserAndFileGroupID();
	$fs->uploadTextFileFromContent($this->getExportFileName().".html", $content, "text/html");
	$fileID = $fs->getFileID();

	$this->setExportFSFileID($fileID);

	if (isset($generatePDF) && $generatePDF) {
		$this->generatePDFFile($fileID, $results);
	}
?>
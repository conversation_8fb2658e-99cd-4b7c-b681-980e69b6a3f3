-- TTWA-BASE:
-- # RELEASE 1.19

UPDATE `_sql_version` SET `major_minor` = '1.19' WHERE `module` = 'ttwa-base';

UPDATE `_sql_version` SET `revision`=1, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -1-2024-04-05-08:25------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
	('employeeBenefitBalanceUpload', 'employeeBenefitBalanceUpload --- edit', 'A munkavállalói juttatási egyenleg betöltése felülethez ad jogot.', 'SZOF-2926', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
	('a5b6bd79e008725744118c7c46e10cda', 'employeeBenefitBalanceUpload', 'SZOF-2926', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`, `created_by`, `created_on`) VALUES
	('employeeBenefitBalanceUpload', 'uploaders/employeeBenefitBalanceUpload', NULL, 'view', NULL, 1, 'SZOF-2926', NOW());

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,`menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`) VALUES
	('employeeBenefitBalanceUpload','employeeBenefitBalanceUpload','ttwa-base','menu_item_employee_benefit_balance_upload','sub','/uploaders/employeeBenefitBalanceUpload/index','uploaders/employeeBenefitBalanceUpload','view','uploader',63);

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'menu_item_employee_benefit_balance_upload', 'Munkavállalói juttatási egyenleg betöltése', '1'),
	('en', 'ttwa-base', 'menu_item_employee_benefit_balance_upload', 'Employee benefit balance upload', '1'),
	('hu', 'ttwa-base', 'page_title_employee_benefit_balance_upload', 'Munkavállalói juttatási egyenleg betöltése', '1'),
	('en', 'ttwa-base', 'page_title_employee_benefit_balance_upload', 'Employee benefit balance upload', '1');

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
	('employeeBenefitBalanceModelAndColumn', 'EmployeeExt4;ext4_option1', 'string', 'A munkavállalói juttatási egyenleg tárolási helye az adatbázisban. (model;oszlop)', 'setting_employeeBenefitBalanceModelAndColumn', 1, 'SZOF-2926', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'setting_employeeBenefitBalanceModelAndColumn', 'A munkavállalói juttatási egyenleg tárolási helye az adatbázisban. (model;oszlop)', '1'),
    ('en', 'ttwa-base', 'setting_employeeBenefitBalanceModelAndColumn', 'The storage location of the employee benefit balance in the database. (model;column)', '1');

UPDATE `_sql_version` SET `revision`=2, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -2-2024-04-05-08:35------------------------------------------------

CREATE TABLE IF NOT EXISTS `employee_shop_balance` (
    `row_id` int unsigned NOT NULL AUTO_INCREMENT,
    `employee_contract_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Dolgozói szerződés egyedi azonosítója',
    `currency_code` varchar(3) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'HUF' COMMENT 'Pénznem kód',
    `paid_amount` int NOT NULL DEFAULT 0 COMMENT 'Fizetendő összeg',
    `valid_from` date NOT NULL COMMENT 'Érvényesség kezdete',
    `valid_to` date DEFAULT NULL COMMENT 'Érvényesség vége',
    `status` tinyint unsigned NOT NULL COMMENT 'Állapot',
    `created_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Készítette',
    `created_on` datetime NOT NULL COMMENT 'Készült',
    `modified_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Módosította',
    `modified_on` datetime DEFAULT NULL COMMENT 'Módosítva',
    PRIMARY KEY (`row_id`),
    KEY `IDX_employee_contract_id` (`employee_contract_id`),
    KEY `IDX_valid_from` (`valid_from`),
    KEY `IDX_valid_to` (`valid_to`),
    KEY `IDX_status` (`status`),
	KEY `IDX_created_on` (`created_on`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Munkavállalói juttatási egyenleg felhasználása';

UPDATE `_sql_version` SET `revision`=3, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -3-2024-04-09-16:15------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('standbyUpgradedVersionInWorktime', '0', 'string', 'Készenlét elrendelés metszi a munkaidőt', 'standbyUpgradedVersionInWorktime', 1, 'SZOF-2919', NOW());
INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'standbyUpgradedVersionInWorktime', 'Készenlét kiadható úgy, hogy metszi a munkaidőt, feltétele a standbyUpgradedVersion appsettings 1 ', '1'),
    ('en', 'ttwa-base', 'standbyUpgradedVersionInWorktime', 'Standby can be issued by cutting the working time, the condition is standbyUpgradedVersion appsettings 1', '1');

UPDATE `_sql_version` SET `revision`=4, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -3-2024-04-16-16:15------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'transaction', 'Tranzakció', '1'),
    ('en', 'ttwa-base', 'transaction', 'Transaction', '1'),
    ('hu', 'ttwa-base', 'dateOfTransaction', 'Tranzakció dátuma', '1'),
    ('en', 'ttwa-base', 'dateOfTransaction', 'Date of transaction', '1'),
    ('hu', 'ttwa-base', 'balanceAmountBeforeFilterValidFrom', 'Lekérdezési időszak előtti egyenleg', '1'),
    ('en', 'ttwa-base', 'balanceAmountBeforeFilterValidFrom', 'Balance amount before filter valid from', '1'),
    ('hu', 'ttwa-base', 'balanceAmountOnFilterValidTo', 'Lekérdezési időszak végi egyenleg', '1'),
    ('en', 'ttwa-base', 'balanceAmountOnFilterValidTo', 'Balance amount on filter valid to', '1');

UPDATE `_sql_version` SET `revision`=5, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -5-2024-04-16-16:10------------------------------------------------

update `employee_group` set group_id='job_task_id' where group_id='job_tasks_id';
update `employee_group_config` set group_id = 'job_task_id' where group_id = 'job_tasks_id';

UPDATE `_sql_version` SET `revision`=6, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -6-2024-04-24-15:10------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
    ('hu', 'ttwa-base', 'questionnaire_name', 'Kérdéssor neve'),
    ('en', 'ttwa-base', 'questionnaire_name', 'Questionnaire name');

UPDATE `_sql_version` SET `revision`=7, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -7-2024-04-26-09:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('setting_ptr_payroll30DiffCalcMode', '0', 'string', 'Payroll 30 számolás mód váltás', 'setting_ptr_payroll30DiffCalcMode', 1, 'SZOF-2919', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'setting_ptr_payroll30DiffCalcMode', 'Payroll 30 számolás mód váltás', '1'),
    ('en', 'ttwa-base', 'setting_ptr_payroll30DiffCalcMode', 'Payroll 30 calculate mode change', '1');

UPDATE `_sql_version` SET `revision`=8, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -8-2024-04-30-16:15------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'leader', 'Vezető', '1'),
    ('en', 'ttwa-base', 'leader', 'Leader', '1');

UPDATE `_sql_version` SET `revision`=9, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -9-2024-04-30-16:15------------------------------------------------

CREATE TABLE IF NOT EXISTS `employee_position_details` (
    `row_id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `employee_position_id` VARCHAR(32) COMMENT 'employee_position_id',
    `position_code` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Munkakör kódja',
    `position` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Munkakör',
    `position_english_name` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Munkakör angol megnevezése',
    `service_group` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Szolgálati csoport',
    `activity_description` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Tevékenység megnevezés',
    `feor_code` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'FEOR kód',
    `position_family` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Munkakörcsalád',
    `sub_position_family_code` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Almunkakörcsalád kódja',
    `sub_position_family_description` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Almunkakörcsalád megnevezése',
    `hay_grade` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Hay grade',
    `min_salary` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Min. bér',
    `classification_category` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Besorolási kategória',
    `monitor_in_front_of_work` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Monitor előtti munka',
    `referral_type` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Beutaló típusa',
    `ho_eligibility` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'HO jogosultság',
    `basic_it_profile` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'Alap IT profil',
    `status` TINYINT NOT NULL COMMENT 'status',
    `created_by` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'created_by',
    `created_on` DATETIME NOT NULL COMMENT 'created_on',
    `modified_by` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'modified_by',
    `modified_on` DATETIME COMMENT 'modified_on'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Munkakörhöz kapcsolt mezők';

INSERT INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
	('automaticLoadExt2FromEmployeePosition', '0', 'string', 'A munkakör részletes adatok automatikus betöltése az ext2 oldalra munkakör létrehozása vagy módosítása esetén.' ,'setting_automaticLoadExt2FromEmployeePosition', '1');
	
INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-ahp', 'setting_automaticLoadExt2FromEmployeePosition', 'A munkakör részletes adatok automatikus betöltése az ext2 oldalra munkakör létrehozása vagy módosítása esetén.', '1'),
    ('en', 'ttwa-ahp', 'setting_automaticLoadExt2FromEmployeePosition', 'The automatic load of employee position details to ext2 page in case of modify or creation of employee position.', '1');

UPDATE `_sql_version` SET `revision`=10, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -10-2024-05-02-08:00------------------------------------------------

INSERT INTO `app_settings` (`row_id`, `setting_id`, `setting_value`, `setting_type`, `dict_id`, `valid`) VALUES
    (NULL, 'summarySheet_show_extra_time_icon_when_has_absence', '0', 'string', 'summarySheet_show_extra_time_icon_when_has_absence', '1');

INSERT INTO `dictionary` (`row_id`, `lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    (NULL, 'hu', 'ttwa-base', 'summarySheet_show_extra_time_icon_when_has_absence', 'Túlóra és egyéb extraTime ikon megjelenítése összesítő lapon távollét alatt', '1'),
    (NULL, 'en', 'ttwa-base', 'summarySheet_show_extra_time_icon_when_has_absence', 'Show overtime and other extraTime icon when has absence', '1');

UPDATE `_sql_version` SET `revision`=11, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -11-2024-05-06-14:00------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Szakmunkástanuló' WHERE `dict_id` = 'employee_contract_type_learner' AND `module` = 'ttwa-base' AND `lang` = 'hu';

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'not_receive_benefits', 'Nem részesül ellátásban', 1),
    ('en', 'ttwa-base', 'not_receive_benefits', 'Not receive benefits', 1),
    ('hu', 'ttwa-base', 'receiving_discounted_pension_for_women', 'Nők kedvezményes nyugdíjában részesül', 1),
    ('en', 'ttwa-base', 'receiving_discounted_pension_for_women', 'Receiving a discounted pension for women', 1),
    ('hu', 'ttwa-base', 'receiving_old_age_pension', 'Öregségi nyugdíjban részesül', 1),
    ('en', 'ttwa-base', 'receiving_old_age_pension', 'Receiving an old-age pension', 1),
    ('hu', 'ttwa-base', 'public_servant', 'Közalkalmazott', 1),
    ('en', 'ttwa-base', 'public_servant', 'Public servant', 1),
    ('hu', 'ttwa-base', 'publicly_employed', 'Közfoglalkoztatott', 1),
    ('en', 'ttwa-base', 'publicly_employed', 'Publicly employed', 1),
    ('en', 'ttwa-base', 'wage_type_20231122131010384', 'Main occupation', 1),
    ('en', 'ttwa-base', 'wage_type_20231122154547600', 'Part time', 1),
    ('en', 'ttwa-base', 'wage_type_20231122154602738', 'Gate', 1),
    ('en', 'ttwa-base', 'wage_type_20231122154614547', 'Retired', 1),
    ('en', 'ttwa-base', 'wage_type_20231122154626612', 'Employee of the Iraqi representation', 1),
    ('en', 'ttwa-base', 'wage_type_20231122154637565', 'Public employment', 1),
    ('en', 'ttwa-base', 'wage_type_20231206101822747', 'Commission Agreement 10%', 1),
    ('en', 'ttwa-base', 'wage_type_20231206101844933', 'Commission Agreement 50%', 1),
    ('hu', 'ttwa-base', 'huf', 'HUF', 1),
    ('en', 'ttwa-base', 'huf', 'HUF', 1),
    ('hu', 'ttwa-base', 'eur', 'EUR', 1),
    ('en', 'ttwa-base', 'eur', 'EUR', 1);

UPDATE `_sql_version` SET `revision`=12, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -12-2024-05-07-11:00------------------------------------------------

INSERT INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
    ('addressCardNumberHide', 'employee/employeeAddressTab --- address_card_number_hide', 0 ,'Dolgozó kezelésnél a Cím tabon elrejti a Lakcím kártya mezőt', 'SZOF-3168', NOW());

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('addressCardNumberHide', 'employee/employeeAddressTab', 'address_card_number_hide', 1, 1, 'SZOF-3168', NOW());

UPDATE `_sql_version` SET `revision`=13, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -13-2024-05-08-11:00------------------------------------------------

INSERT INTO auth_acl (role_id, controller_id, column_name, operation_id, usergroup_id, access_right, login_need, created_by, created_on) VALUES
('employeecontrol_nowrgplimit', 'employeecontrol', NULL, 'noworkgrouplimitation', NULL, '1', '1', 'SZOF_3159', NOW()),
('employeecontrol_wrgplimitbyright', 'employeecontrol', NULL, 'workgroupbyuserright', NULL, '1', '1', 'SZOF_3159', NOW());

INSERT INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
('employeecontrol_nowrgplimit', 'employee --- noworkgrouplimitation', NULL, 'SZOF_3159', NOW()),
('employeecontrol_wrgplimitbyright', 'employee --- workgroupbyuserright', NULL, 'SZOF_3159', NOW());

UPDATE `_sql_version` SET `revision`=14, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -14-2024-05-09-14:00------------------------------------------------

UPDATE `user` u
    LEFT JOIN (SELECT DATABASE() AS db_neve) AS db_nev ON 1=1
SET
    u.`password`='d7418d79878132bb1882b8721244d8c46f74aeeedde51fa6cf24505b7a2e687617126314fa006cde0e77cc229e1ffe43cf01e62fd2fedf3a473ef6689246a090',
    u.`password_date` = NOW()
WHERE
    u.`username`='root'
  AND db_nev.db_neve <> 'la_ttwa_login';

UPDATE `_sql_version` SET `revision`=15, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -15-2024-05-09-13:20------------------------------------------------

CREATE TABLE `etl_row_hash` (
    `row_id` INT(10) NOT NULL AUTO_INCREMENT COMMENT 'Azonosító',
    `etl_row_hash` VARCHAR(128) NOT NULL COMMENT 'ETL Row Hash' COLLATE 'utf8mb3_unicode_ci',
    `etl_row_id` VARCHAR(128) NOT NULL COMMENT 'ETL Row ID' COLLATE 'utf8mb3_unicode_ci',
    `etl_type` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ETL Type' COLLATE 'utf8mb3_unicode_ci',
    `status` INT(10) NOT NULL COMMENT 'Állapot',
    `created_by` VARCHAR(32) NOT NULL COMMENT 'Készítette' COLLATE 'utf8mb3_unicode_ci',
    `created_on` DATETIME NOT NULL COMMENT 'Készült',
    `modified_by` VARCHAR(32) NULL DEFAULT NULL COMMENT 'Módosította' COLLATE 'utf8mb3_unicode_ci',
    `modified_on` DATETIME NULL DEFAULT NULL COMMENT 'Módosítva',
    PRIMARY KEY (`row_id`) USING BTREE,
    INDEX `IDX` (`etl_row_hash`, `etl_row_id`, `status`) USING BTREE
)
COMMENT='ETL Row Hash'
COLLATE='utf8mb3_general_ci'
ENGINE=InnoDB;

UPDATE `_sql_version` SET `revision`=16, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -16-2024-05-15-17:30------------------------------------------------

DROP TABLE IF EXISTS `_new_employee_address`;

CREATE TABLE `_new_employee_address` LIKE `employee_address`;

ALTER TABLE `_new_employee_address` ADD COLUMN `building` VARCHAR(64) NULL DEFAULT NULL COMMENT 'Épület' COLLATE utf8_unicode_ci AFTER `house_number`;
ALTER TABLE `_new_employee_address` ADD COLUMN `staircase` VARCHAR(64) NULL DEFAULT NULL COMMENT 'Lépcsőház' COLLATE utf8_unicode_ci AFTER `building`;
ALTER TABLE `_new_employee_address` ADD COLUMN `res_building` VARCHAR(64) NULL DEFAULT NULL COMMENT 'Tartózkodási hely - Épület' COLLATE utf8_unicode_ci AFTER `res_house_number`;
ALTER TABLE `_new_employee_address` ADD COLUMN `res_staircase` VARCHAR(64) NULL DEFAULT NULL COMMENT 'Tartózkodási hely - Lépcsőház' COLLATE utf8_unicode_ci AFTER `res_building`;
ALTER TABLE `_new_employee_address` ADD COLUMN `acc_building` VARCHAR(130) NULL DEFAULT NULL COLLATE utf8_unicode_ci AFTER `acc_house_number`;
ALTER TABLE `_new_employee_address` ADD COLUMN `acc_staircase` VARCHAR(130) NULL DEFAULT NULL COLLATE utf8_unicode_ci AFTER `acc_building`;

INSERT INTO `_new_employee_address` (
    `row_id`, `employee_id`, `address_card_number`, `full_address`, `country`, `zip_code`,
    `city`, `district`, `public_place_name`, `public_place_type`, `house_number`, `floor`, `door`,
    `res_full_address`, `res_country`, `res_zip_code`, `res_city`, `res_district`, `res_public_place_name`,
    `res_public_place_type`, `res_house_number`, `res_floor`, `res_door`, `note`, `valid_from`, `valid_to`,
    `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`, `accomodated`, `acc_country`,
    `acc_zip_code`, `acc_city`, `acc_district`, `acc_public_place_name`, `acc_public_place_type`, `acc_house_number`, `acc_floor`, `acc_door`
)
SELECT
    `row_id`, `employee_id`, `address_card_number`, `full_address`, `country`, `zip_code`,
    `city`, `district`, `public_place_name`, `public_place_type`, `house_number`, `floor`, `door`,
    `res_full_address`, `res_country`, `res_zip_code`, `res_city`, `res_district`, `res_public_place_name`,
    `res_public_place_type`, `res_house_number`, `res_floor`, `res_door`, `note`, `valid_from`, `valid_to`,
    `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`, `accomodated`, `acc_country`,
    `acc_zip_code`, `acc_city`, `acc_district`, `acc_public_place_name`, `acc_public_place_type`, `acc_house_number`, `acc_floor`, `acc_door`
FROM `employee_address`;

DROP TABLE `employee_address`;
RENAME TABLE `_new_employee_address` TO `employee_address`;

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'building', 'Épület', 1),
    ('en', 'ttwa-base', 'building', 'Building', 1),
    ('hu', 'ttwa-base', 'staircase', 'Lépcsőház', 1),
    ('en', 'ttwa-base', 'staircase', 'Staircase', 1),
    ('hu', 'ttwa-base', 'res_building', 'Tart. épület', 1),
    ('en', 'ttwa-base', 'res_building', 'Building of res.', 1),
    ('hu', 'ttwa-base', 'res_staircase', 'Tart. lépcsőház', 1),
    ('en', 'ttwa-base', 'res_staircase', 'Staircase of res.', 1);

UPDATE `_sql_version` SET `revision`=17, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -17-2024-05-17-14:00------------------------------------------------

INSERT INTO `app_settings` (`row_id`, `setting_id`, `setting_value`, `setting_type`, `dict_id`, `valid`) VALUES
    (NULL, 'summarisationCalculateMode', '0', 'string', 'summarisationCalculateMode', '1');

INSERT INTO `dictionary` (`row_id`, `lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    (NULL, 'hu', 'ttwa-base', 'summarisationCalculateMode', 'Összesítő oszlop számolás mód normál vagy praktiker által kért', '1'),
    (NULL, 'en', 'ttwa-base', 'summarisationCalculateMode', 'Total column calculation mode is normal or requested by the practitioner', '1');

UPDATE `_sql_version` SET `revision`=18, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -18-2024-05-22-14:00------------------------------------------------

CREATE TABLE IF NOT EXISTS `questionnaire_management` (
    `row_id` int unsigned NOT NULL AUTO_INCREMENT ,
    `questionnaire_id` VARCHAR(32) NOT NULL COMMENT 'Kérdőív azonosító' COLLATE 'utf8mb4_unicode_ci',
    `questionnaire_name` VARCHAR(512) NULL DEFAULT NULL COMMENT 'Kérdőív szövege' COLLATE 'utf8mb4_unicode_ci',
    `question_id` VARCHAR(32) NOT NULL COMMENT 'Kérdés azonosító' COLLATE 'utf8mb4_unicode_ci',
    `question_order` INT(10) UNSIGNED NOT NULL COMMENT 'Kérdés rendezése',
    `question_text` VARCHAR(512) NULL DEFAULT NULL COMMENT 'Kérdés szövege' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_id_1` VARCHAR(32) NULL DEFAULT NULL COMMENT 'Válaszlehetőség azonosítója 1' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_text_1` VARCHAR(512) NULL DEFAULT NULL COMMENT 'Válasz szövege 1' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_is_right_1` TINYINT(1) DEFAULT 0 COMMENT 'Helyes válasz1?',
    `question_answer_id_2` VARCHAR(32) NULL DEFAULT NULL COMMENT 'Válaszlehetőség azonosítója 2' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_text_2` VARCHAR(512) NULL DEFAULT NULL COMMENT 'Válasz szövege 2' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_is_right_2` TINYINT(1) DEFAULT 0 COMMENT 'Helyes válasz2?',
    `question_answer_id_3` VARCHAR(32) NULL DEFAULT NULL COMMENT 'Válaszlehetőség azonosítója 3' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_text_3` VARCHAR(512) NULL DEFAULT NULL COMMENT 'Válasz szövege 3' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_is_right_3` TINYINT(1) DEFAULT 0 COMMENT 'Helyes válasz3?',
    `question_answer_id_4` VARCHAR(32) NULL DEFAULT NULL COMMENT 'Válaszlehetőség azonosítója 4' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_text_4` VARCHAR(512) NULL DEFAULT NULL COMMENT 'Válasz szövege 4' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_is_right_4` TINYINT(1) DEFAULT 0 COMMENT 'Helyes válasz4?',
    `question_answer_id_5` VARCHAR(32) NULL DEFAULT NULL COMMENT 'Válaszlehetőség azonosítója 5' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_text_5` VARCHAR(512) NULL DEFAULT NULL COMMENT 'Válasz szövege 5' COLLATE 'utf8mb4_unicode_ci',
    `question_answer_is_right_5` TINYINT(1) DEFAULT 0 COMMENT 'Helyes válasz5?',
    `valid_from` date NOT NULL COMMENT 'Érvényesség kezdete',
    `valid_to` date DEFAULT NULL COMMENT 'Érvényesség vége',
    `status` tinyint unsigned NOT NULL COMMENT 'Állapot',
    `created_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Készítette',
    `created_on` datetime NOT NULL COMMENT 'Készült',
    `modified_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Módosította',
    `modified_on` datetime DEFAULT NULL COMMENT 'Módosítva',
    PRIMARY KEY (`row_id`),
    KEY `IDX_questionnaire_id` (`questionnaire_id`),
    KEY `IDX_question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Kérdéssorok';

UPDATE `_sql_version` SET `revision`=19, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -19-2024-05-22-08:55------------------------------------------------

ALTER TABLE `questionnaire_answer`
	ADD COLUMN `correct_answer` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Helyes válasz hash-e' AFTER `question_answer_response`;

UPDATE `_sql_version` SET `revision`=20, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -20-2024-05-23-11:00------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
	('questionnaireManagement', 'questionnaireManagement --- edit', 'A kérdéssorok szerkesztése felülethez ad jogot.', 'SZOF-3309', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
	('a5b6bd79e008725744118c7c46e10cda', 'questionnaireManagement', 'SZOF-3309', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`, `created_by`, `created_on`) VALUES
	('questionnaireManagement', 'questionnaireManagement', NULL, 'view', NULL, 1, 'SZOF-3309', NOW());

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,`menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`) VALUES
	('questionnaireManagement','questionnaireManagement','ttwa-base','menu_item_questionnaire_management','sub','/questionnaireManagement/index','questionnaireManagement','view','config',68);

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'menu_item_questionnaire_management', 'Kérdéssorok szerkesztése', '1'),
	('en', 'ttwa-base', 'menu_item_questionnaire_management', 'Questionnaire management', '1'),
	('hu', 'ttwa-base', 'page_title_questionnaire_management', 'Kérdéssorok szerkesztése', '1'),
	('en', 'ttwa-base', 'page_title_questionnaire_management', 'Questionnaire management', '1'),
    ('hu', 'ttwa-base', 'export_file_questionnaire_management', 'kerdessorok_szerkesztese', '1'),
	('en', 'ttwa-base', 'export_file_questionnaire_management', 'questionnaire_management', '1');

UPDATE `_sql_version` SET `revision`=21, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -21-2024-05-24-08:45------------------------------------------------

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES
('wfm/missingworktime', 'emp_id', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'fullname', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'hire_date', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'termination_date', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'employee_type', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'company_org_group2', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'mep', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'location', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'work_area', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'cost_center', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'primary_organization', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'workgroup', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'manager_1_id', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'manager_1_name', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'manager_2_id', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'manager_2_name', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'manager_3_id', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'manager_3_name', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_1', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_2', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_3', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_4', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_5', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_6', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_7', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_8', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_9', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_10', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_11', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'month_12', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'sum_subtract', 'ALL', 2, 'SZOF-3233', now()),
('wfm/missingworktime', 'sum_not_subtract', 'ALL', 2, 'SZOF-3233', now());

UPDATE `_sql_version` SET `revision`=22, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -22-2024-05-24-08:45------------------------------------------------

ALTER TABLE app_settings MODIFY setting_value TEXT NOT NULL;

UPDATE `_sql_version` SET `revision`=23, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -23-2024-05-28-15:00------------------------------------------------
-- PHPExecuteChange('Version_20240527132408157414_313433517');

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`)
VALUES ('hu', 'ttwa-wwm', 'wearing_time', 'Kihordási idő'),
       ('en', 'ttwa-wwm', 'wearing_time', 'Warranty'),
       ('hu', 'ttwa-wwm', 'wearing_time_type', 'Kihordási idő mértékegysége'),
       ('en', 'ttwa-wwm', 'wearing_time_type', 'Wearing time type')
ON DUPLICATE KEY UPDATE `dict_value` = VALUES (`dict_value`);

UPDATE `_sql_version` SET `revision`=24, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -24-2024-05-29-17:00------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Grouping 2. uploader' WHERE `dict_id` = 'menu_item_company_org_group2_upload' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Grouping 3. uploader' WHERE `dict_id` = 'menu_item_company_org_group3_upload' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=25, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -25-2024-06-04-10:45------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
    ('extendedAbsenceExpenditure', 'extendedAbsenceExpenditure --- view', '0', 'Délutáni beosztás lista kimutatás', 'SZOF-3242', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('a5b6bd79e008725744118c7c46e10cda', 'extendedAbsenceExpenditure', 'SZOF-3242', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('extendedAbsenceExpenditure', 'extendedAbsenceExpenditure', 'view', '1', '1', 'SZOF-3242', NOW());

INSERT IGNORE INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
    ('extendedAbsenceExpenditure', 'extendedAbsenceExpenditure', 'menu_item_extended_absence_expenditure', 'SZOF-3242', NOW());

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`, `menu_item_name`, `menu_modul`, `menu_label`, `menu_item_css_class`, `menu_url`, `menu_visible`, `menu_visible_operation`, `menu_item_parent_id`, `menu_order`) VALUES
    ('extendedAbsenceExpenditure', 'extendedAbsenceExpenditure', 'ttwa-base', 'menu_item_extended_absence_expenditure', 'sub', '/extendedAbsenceExpenditure/index', 'extendedAbsenceExpenditure', 'view', '4', '4');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'menu_item_extended_absence_expenditure', 'Szabadság kiadás', '1'),
    ('en', 'ttwa-base', 'menu_item_extended_absence_expenditure', 'Absence expenditure', '1'),
    ('hu', 'ttwa-base', 'page_title_extended_absence_expenditure', 'Szabadság kiadás', '1'),
    ('en', 'ttwa-base', 'page_title_extended_absence_expenditure', 'Absence expenditure', '1');

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
    ('extendedAbsenceExpenditure', 'employee_name', NULL, 'ALL', '2', NULL, 'SZOF-3242', NOW()),
    ('extendedAbsenceExpenditure', 'emp_id', NULL, 'ALL', '2', NULL, 'SZOF-3242', NOW()),
    ('extendedAbsenceExpenditure', 'company_name', NULL, 'ALL', '2', NULL, 'SZOF-3242', NOW()),
    ('extendedAbsenceExpenditure', 'msg', NULL, 'ALL', '2', NULL, 'SZOF-3242', NOW());

INSERT IGNORE INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('extendedAbsenceExpenditure', 'group', 'WORKGROUP', 'combo', '1', '2', 'SZOF-3242', NOW()),
    ('extendedAbsenceExpenditure', 'group', 'UNIT', 'combo', '1', '2', 'SZOF-3242', NOW()),
    ('extendedAbsenceExpenditure', 'group', 'COST', 'combo', '1', '2', 'SZOF-3242', NOW()),
    ('extendedAbsenceExpenditure', 'group', 'EMPLOYEECONTRACT', 'combo', '1', '2', 'SZOF-3242', NOW());

UPDATE `dictionary` SET `dict_value` = 'Távollét művelet' WHERE `dict_id` = 'absence_operation' AND `module` = 'ttwa-ahp' AND `lang` = 'hu';

UPDATE `_sql_version` SET `revision`=26, `updated_on`=NOW() WHERE `module`='ttwa-base';

-- VERSION -26-2024-06-07-17:00------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
    ('hu', 'ttwa-base', 'unknown', 'Ismeretlen'),
    ('en', 'ttwa-base', 'unknown', 'Unknown');

UPDATE `event_type` SET `dict_id` = 'unknown', `modified_by` = 'SZOF-2972', `modified_on` = NOW() WHERE `event_type_name` = 'unknown';
UPDATE `event_type` SET `event_type_id` = 'unknown', `modified_by` = 'SZOF-2972', `modified_on` = NOW() WHERE `event_type_id` = 'unkown' AND `event_type_name` = 'unknown';

UPDATE `_sql_version` SET `revision`=27, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -27-2024-06-11-15:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('piramis_employeeextrahours', '0', 'string', 'Piramis bérátadásban használja az employee_extra_hours', 'piramis_employeeextrahours', 1, 'SZOF-3595', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'piramis_employeeextrahours', 'Piramis bérátadásban használja az employee_extra_hours', '1'),
    ('en', 'ttwa-base', 'piramis_employeeextrahours', 'Use employee_extra_hours in a pyramid wage transfer', '1');

UPDATE `_sql_version` SET `revision`=28, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -28-2024-06-28-15:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('calcToDeleteRegNotList', '0', 'string', 'A számolás által 4 -es státuszra tett regeket ne jelenítse meg a Összesítőlap', 'calcToDeleteRegNotList', 1, 'SZOF-3595', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'calcToDeleteRegNotList', 'A számolás által 4 -es státuszra tett regeket ne jelenítse meg a Összesítőlap', '1'),
    ('en', 'ttwa-base', 'calcToDeleteRegNotList', 'Do not display regs set to status 4 by counting on the Summary Sheet', '1');

UPDATE `_sql_version` SET `revision`=29, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -29-2024-07-03-15:00------------------------------------------------

CREATE TABLE `custom_field_config`
(
    `row_id`            int unsigned NOT NULL AUTO_INCREMENT,
    `table_name`        varchar(128) NOT NULL,
    `field_identifier`  varchar(128) NOT NULL,
    `dict_id`           varchar(128) NOT NULL,
    `field_type`        varchar(128) NOT NULL,
    `field_params`      JSON NOT NULL,
    `status`            int unsigned NOT NULL,
    `created_by`        varchar(32) NOT NULL,
    `created_on`        datetime NOT NULL,
    `modified_by`       varchar(32) DEFAULT NULL,
    `modified_on`       datetime DEFAULT NULL,

    PRIMARY KEY (`row_id`),
    KEY `IDX_table_name_field_name_status` (`table_name`,`field_identifier`,`status`)

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  COLLATE = utf8_unicode_ci
;

CREATE TABLE `custom_field`
(
    `row_id`            int unsigned NOT NULL AUTO_INCREMENT,
    `identifier`        varchar(128) NOT NULL,
    `field_identifier`  varchar(128) NOT NULL,
    `field_value`       longtext NULL,
    `valid_from`        datetime NOT NULL,
    `valid_to`          datetime NULL,
    `status`            int unsigned NOT NULL,
    `created_by`        varchar(32) NOT NULL,
    `created_on`        datetime NOT NULL,
    `modified_by`       varchar(32) DEFAULT NULL,
    `modified_on`       datetime DEFAULT NULL,

    PRIMARY KEY (`row_id`),
    KEY `IDX_field_identifier_identifier_valid_from` (`field_identifier`, `identifier`, `valid_from`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8
  COLLATE = utf8_unicode_ci
;

UPDATE `_sql_version` SET `revision`=30, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -30-2024-07-04-15:00------------------------------------------------

INSERT IGNORE INTO `option_config` (`option_id`, `type`, `status`, `created_by`, `created_on`) VALUES
    ('ext2_option41', 'ed', 7, 'system', NOW()),
    ('ext2_option42', 'ed', 7, 'system', NOW()),
    ('ext2_option43', 'ed', 7, 'system', NOW()),
    ('ext2_option44', 'ed', 7, 'system', NOW()),
    ('ext2_option45', 'ed', 7, 'system', NOW()),
    ('ext2_option46', 'ed', 7, 'system', NOW()),
    ('ext2_option47', 'ed', 7, 'system', NOW()),
    ('ext2_option48', 'ed', 7, 'system', NOW()),
    ('ext2_option49', 'ed', 7, 'system', NOW()),
    ('ext2_option50', 'ed', 7, 'system', NOW()),
    ('ext2_option51', 'ed', 7, 'system', NOW()),
    ('ext2_option52', 'ed', 7, 'system', NOW()),
    ('ext2_option53', 'ed', 7, 'system', NOW()),
    ('ext2_option54', 'ed', 7, 'system', NOW()),
    ('ext2_option55', 'ed', 7, 'system', NOW()),
    ('ext2_option56', 'ed', 7, 'system', NOW()),
    ('ext2_option57', 'ed', 7, 'system', NOW()),
    ('ext2_option58', 'ed', 7, 'system', NOW()),
    ('ext2_option59', 'ed', 7, 'system', NOW()),
    ('ext2_option60', 'ed', 7, 'system', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'ext2_option41', 'Option41', 1),
    ('en', 'ttwa-base', 'ext2_option41', 'Option41', 1),
    ('hu', 'ttwa-base', 'ext2_option42', 'Option42', 1),
    ('en', 'ttwa-base', 'ext2_option42', 'Option42', 1),
    ('hu', 'ttwa-base', 'ext2_option43', 'Option43', 1),
    ('en', 'ttwa-base', 'ext2_option43', 'Option43', 1),
    ('hu', 'ttwa-base', 'ext2_option44', 'Option44', 1),
    ('en', 'ttwa-base', 'ext2_option44', 'Option44', 1),
    ('hu', 'ttwa-base', 'ext2_option45', 'Option45', 1),
    ('en', 'ttwa-base', 'ext2_option45', 'Option45', 1),
    ('hu', 'ttwa-base', 'ext2_option46', 'Option46', 1),
    ('en', 'ttwa-base', 'ext2_option46', 'Option46', 1),
    ('hu', 'ttwa-base', 'ext2_option47', 'Option47', 1),
    ('en', 'ttwa-base', 'ext2_option47', 'Option47', 1),
    ('hu', 'ttwa-base', 'ext2_option48', 'Option48', 1),
    ('en', 'ttwa-base', 'ext2_option48', 'Option48', 1),
    ('hu', 'ttwa-base', 'ext2_option49', 'Option49', 1),
    ('en', 'ttwa-base', 'ext2_option49', 'Option49', 1),
    ('hu', 'ttwa-base', 'ext2_option50', 'Option50', 1),
    ('en', 'ttwa-base', 'ext2_option50', 'Option50', 1),
    ('hu', 'ttwa-base', 'ext2_option51', 'Option51', 1),
    ('en', 'ttwa-base', 'ext2_option51', 'Option51', 1),
    ('hu', 'ttwa-base', 'ext2_option52', 'Option52', 1),
    ('en', 'ttwa-base', 'ext2_option52', 'Option52', 1),
    ('hu', 'ttwa-base', 'ext2_option53', 'Option53', 1),
    ('en', 'ttwa-base', 'ext2_option53', 'Option53', 1),
    ('hu', 'ttwa-base', 'ext2_option54', 'Option54', 1),
    ('en', 'ttwa-base', 'ext2_option54', 'Option54', 1),
    ('hu', 'ttwa-base', 'ext2_option55', 'Option55', 1),
    ('en', 'ttwa-base', 'ext2_option55', 'Option55', 1),
    ('hu', 'ttwa-base', 'ext2_option56', 'Option56', 1),
    ('en', 'ttwa-base', 'ext2_option56', 'Option56', 1),
    ('hu', 'ttwa-base', 'ext2_option57', 'Option57', 1),
    ('en', 'ttwa-base', 'ext2_option57', 'Option57', 1),
    ('hu', 'ttwa-base', 'ext2_option58', 'Option58', 1),
    ('en', 'ttwa-base', 'ext2_option58', 'Option58', 1),
    ('hu', 'ttwa-base', 'ext2_option59', 'Option59', 1),
    ('en', 'ttwa-base', 'ext2_option59', 'Option59', 1),
    ('hu', 'ttwa-base', 'ext2_option60', 'Option60', 1),
    ('en', 'ttwa-base', 'ext2_option60', 'Option60', 1);

DROP TABLE IF EXISTS `_new_employee_ext2`;

CREATE TABLE `_new_employee_ext2` LIKE `employee_ext2`;

ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option41` varchar(128) default null AFTER `ext2_option40`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option42` varchar(128) default null AFTER `ext2_option41`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option43` varchar(128) default null AFTER `ext2_option42`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option44` varchar(128) default null AFTER `ext2_option43`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option45` varchar(128) default null AFTER `ext2_option44`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option46` varchar(128) default null AFTER `ext2_option45`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option47` varchar(128) default null AFTER `ext2_option46`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option48` varchar(128) default null AFTER `ext2_option47`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option49` varchar(128) default null AFTER `ext2_option48`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option50` varchar(128) default null AFTER `ext2_option49`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option51` varchar(128) default null AFTER `ext2_option50`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option52` varchar(128) default null AFTER `ext2_option51`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option53` varchar(128) default null AFTER `ext2_option52`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option54` varchar(128) default null AFTER `ext2_option53`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option55` varchar(128) default null AFTER `ext2_option54`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option56` varchar(128) default null AFTER `ext2_option55`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option57` varchar(128) default null AFTER `ext2_option56`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option58` varchar(128) default null AFTER `ext2_option57`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option59` varchar(128) default null AFTER `ext2_option58`;
ALTER TABLE `_new_employee_ext2` ADD COLUMN `ext2_option60` varchar(128) default null AFTER `ext2_option59`;

INSERT INTO `_new_employee_ext2` (`row_id`, `employee_id`, `ext2_option1`, `ext2_option2`, `ext2_option3`, `ext2_option4`, `ext2_option5`, `ext2_option6`,
                                  `ext2_option7`, `ext2_option8`, `ext2_option9`, `ext2_option10`, `ext2_option11`, `ext2_option12`, `ext2_option13`, `ext2_option14`,
                                  `ext2_option15`, `ext2_option16`, `ext2_option17`, `ext2_option18`, `ext2_option19`, `ext2_option20`, `ext2_option21`, `ext2_option22`,
                                  `ext2_option23`, `ext2_option24`, `ext2_option25`, `ext2_option26`, `ext2_option27`, `ext2_option28`, `ext2_option29`, `ext2_option30`,
                                  `ext2_option31`, `ext2_option32`, `ext2_option33`, `ext2_option34`, `ext2_option35`, `ext2_option36`, `ext2_option37`, `ext2_option38`,
                                  `ext2_option39`, `ext2_option40`, `valid_from`, `valid_to`, `status`, `created_by`,
                                  `created_on`, `modified_by`, `modified_on`, `pre_row_id`)
SELECT
    `row_id`, `employee_id`, `ext2_option1`, `ext2_option2`, `ext2_option3`, `ext2_option4`, `ext2_option5`, `ext2_option6`,
    `ext2_option7`, `ext2_option8`, `ext2_option9`, `ext2_option10`, `ext2_option11`, `ext2_option12`, `ext2_option13`, `ext2_option14`,
    `ext2_option15`, `ext2_option16`, `ext2_option17`, `ext2_option18`, `ext2_option19`, `ext2_option20`, `ext2_option21`, `ext2_option22`,
    `ext2_option23`, `ext2_option24`, `ext2_option25`, `ext2_option26`, `ext2_option27`, `ext2_option28`, `ext2_option29`, `ext2_option30`,
    `ext2_option31`, `ext2_option32`, `ext2_option33`, `ext2_option34`, `ext2_option35`, `ext2_option36`, `ext2_option37`, `ext2_option38`,
    `ext2_option39`, `ext2_option40`, `valid_from`, `valid_to`, `status`, `created_by`,
    `created_on`, `modified_by`, `modified_on`, `pre_row_id`
FROM `employee_ext2`;

DROP TABLE `employee_ext2`;

RENAME TABLE `_new_employee_ext2` TO `employee_ext2`;

UPDATE `_sql_version` SET `revision`=31, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -31-2024-07-09-17:00------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'absence_planner_footer_name', 'Alapszabadság', '1'),
    ('en', 'ttwa-base', 'absence_planner_footer_name', 'Leave', '1');

UPDATE `_sql_version` SET `revision`=32, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -32-2024-07-11-14:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('radarChartYaxisMinLevelExtra', '-1', 'string', 'Radar diagram Y tengely extra minimum érték', 'radarChartYaxisMinLevelExtra', 1, 'SZOF-3563', NOW()),
    ('radarChartYaxisMaxLevelExtra', '1', 'string', 'Radar diagram Y tengely extra maximum érték', 'radarChartYaxisMaxLevelExtra', 1, 'SZOF-3563', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'radarChartYaxisMinLevelExtra', 'Radar diagram Y tengely extra minimum érték', '1'),
    ('en', 'ttwa-base', 'radarChartYaxisMinLevelExtra', 'Radar chart Y axis extra minimum value', '1'),
    ('hu', 'ttwa-base', 'radarChartYaxisMaxLevelExtra', 'Radar diagram Y tengely extra maximum érték', '1'),
    ('en', 'ttwa-base', 'radarChartYaxisMaxLevelExtra', 'Radar chart Y axis extra maximum value', '1');

UPDATE `_sql_version` SET `revision`=33, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -33-2024-07-19-14:15------------------------------------------------

UPDATE `auth_role` SET `description` = 'Kibővített szabadság kiadás', `modified_by` = 'SZOF-3459', `modified_on` = NOW() WHERE `role_id` = 'extendedAbsenceExpenditure' AND `role_name` = 'extendedAbsenceExpenditure --- view';

UPDATE `_sql_version` SET `revision`=34, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -34-2024-07-19-15:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('disappearLogoutButton', '0', 'string', 'Eltűnteti a kijelentkezés gombot.', 'setting_disappearLogoutButton', 1, 'SZOF-3785', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'setting_disappearLogoutButton', 'Eltűnteti a kijelentkezés gombot', '1'),
    ('en', 'ttwa-base', 'setting_disappearLogoutButton', 'Makes the logout button disappear', '1');

UPDATE `_sql_version` SET `revision`=35, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -35-2024-07-25-14:00------------------------------------------------

CREATE TABLE `employee_ext7` (
    `row_id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `employee_id` VARCHAR(32) NOT NULL COMMENT 'employee_id' COLLATE 'utf8_unicode_ci',
    `ext7_option1` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option1' COLLATE 'utf8_unicode_ci',
    `ext7_option2` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option2' COLLATE 'utf8_unicode_ci',
    `ext7_option3` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option3' COLLATE 'utf8_unicode_ci',
    `ext7_option4` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option4' COLLATE 'utf8_unicode_ci',
    `ext7_option5` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option5' COLLATE 'utf8_unicode_ci',
    `ext7_option6` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option6' COLLATE 'utf8_unicode_ci',
    `ext7_option7` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option7' COLLATE 'utf8_unicode_ci',
    `ext7_option8` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option8' COLLATE 'utf8_unicode_ci',
    `ext7_option9` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option9' COLLATE 'utf8_unicode_ci',
    `ext7_option10` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option10' COLLATE 'utf8_unicode_ci',
    `ext7_option11` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option11' COLLATE 'utf8_unicode_ci',
    `ext7_option12` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option12' COLLATE 'utf8_unicode_ci',
    `ext7_option13` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option13' COLLATE 'utf8_unicode_ci',
    `ext7_option14` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option14' COLLATE 'utf8_unicode_ci',
    `ext7_option15` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option15' COLLATE 'utf8_unicode_ci',
    `ext7_option16` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option16' COLLATE 'utf8_unicode_ci',
    `ext7_option17` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option17' COLLATE 'utf8_unicode_ci',
    `ext7_option18` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option18' COLLATE 'utf8_unicode_ci',
    `ext7_option19` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option19' COLLATE 'utf8_unicode_ci',
    `ext7_option20` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option20' COLLATE 'utf8_unicode_ci',
    `ext7_option21` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option21' COLLATE 'utf8_unicode_ci',
    `ext7_option22` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option22' COLLATE 'utf8_unicode_ci',
    `ext7_option23` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option23' COLLATE 'utf8_unicode_ci',
    `ext7_option24` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option24' COLLATE 'utf8_unicode_ci',
    `ext7_option25` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option25' COLLATE 'utf8_unicode_ci',
    `ext7_option26` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option26' COLLATE 'utf8_unicode_ci',
    `ext7_option27` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option27' COLLATE 'utf8_unicode_ci',
    `ext7_option28` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option28' COLLATE 'utf8_unicode_ci',
    `ext7_option29` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option29' COLLATE 'utf8_unicode_ci',
    `ext7_option30` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option30' COLLATE 'utf8_unicode_ci',
    `ext7_option31` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option31' COLLATE 'utf8_unicode_ci',
    `ext7_option32` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option32' COLLATE 'utf8_unicode_ci',
    `ext7_option33` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option33' COLLATE 'utf8_unicode_ci',
    `ext7_option34` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option34' COLLATE 'utf8_unicode_ci',
    `ext7_option35` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option35' COLLATE 'utf8_unicode_ci',
    `ext7_option36` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option36' COLLATE 'utf8_unicode_ci',
    `ext7_option37` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option37' COLLATE 'utf8_unicode_ci',
    `ext7_option38` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option38' COLLATE 'utf8_unicode_ci',
    `ext7_option39` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option39' COLLATE 'utf8_unicode_ci',
    `ext7_option40` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option40' COLLATE 'utf8_unicode_ci',
    `ext7_option41` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option41' COLLATE 'utf8_unicode_ci',
    `ext7_option42` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option42' COLLATE 'utf8_unicode_ci',
    `ext7_option43` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option43' COLLATE 'utf8_unicode_ci',
    `ext7_option44` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option44' COLLATE 'utf8_unicode_ci',
    `ext7_option45` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option45' COLLATE 'utf8_unicode_ci',
    `ext7_option46` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option46' COLLATE 'utf8_unicode_ci',
    `ext7_option47` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option47' COLLATE 'utf8_unicode_ci',
    `ext7_option48` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option48' COLLATE 'utf8_unicode_ci',
    `ext7_option49` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option49' COLLATE 'utf8_unicode_ci',
    `ext7_option50` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option50' COLLATE 'utf8_unicode_ci',
    `ext7_option51` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option51' COLLATE 'utf8_unicode_ci',
    `ext7_option52` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option52' COLLATE 'utf8_unicode_ci',
    `ext7_option53` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option53' COLLATE 'utf8_unicode_ci',
    `ext7_option54` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option54' COLLATE 'utf8_unicode_ci',
    `ext7_option55` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option55' COLLATE 'utf8_unicode_ci',
    `ext7_option56` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option56' COLLATE 'utf8_unicode_ci',
    `ext7_option57` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option57' COLLATE 'utf8_unicode_ci',
    `ext7_option58` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option58' COLLATE 'utf8_unicode_ci',
    `ext7_option59` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option59' COLLATE 'utf8_unicode_ci',
    `ext7_option60` VARCHAR(128) NULL DEFAULT NULL COMMENT 'ext7_option60' COLLATE 'utf8_unicode_ci',
    `valid_from` DATE NOT NULL COMMENT 'Érvényesség kezdete',
    `valid_to` DATE NULL DEFAULT NULL COMMENT 'Érvényesség vége',
    `status` TINYINT(3) UNSIGNED NOT NULL COMMENT 'Állapot',
    `created_by` VARCHAR(32) NOT NULL COMMENT 'Készítette' COLLATE 'utf8_unicode_ci',
    `created_on` DATETIME NOT NULL COMMENT 'Készült',
    `modified_by` VARCHAR(32) NULL DEFAULT NULL COMMENT 'Módosította' COLLATE 'utf8_unicode_ci',
    `modified_on` DATETIME NULL DEFAULT NULL COMMENT 'Módosítva',
    `pre_row_id` INT(10) NULL DEFAULT NULL,
    PRIMARY KEY (`row_id`) USING BTREE,
    INDEX `IDX_employee_id` (`employee_id`, `valid_from`, `valid_to`, `status`) USING BTREE
)
COMMENT='Dolgozói adatok 6'
COLLATE='utf8_unicode_ci'
ENGINE=InnoDB
AUTO_INCREMENT=1
;

INSERT INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
    ('employee/employeeext7Tab', 'employee/employeeext7Tab --- view/add/modify/delete', 'A dolgozó kezelésbe elérhető a további adatok VII', 'SZOF-3874', NOW());

INSERT INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('a5b6bd79e008725744118c7c46e10cda', 'employee/employeeext7Tab', 'SZOF-3874', NOW());

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`, `created_by`, `created_on`) VALUES
    ('employee/employeeext7Tab', 'employee/employeeext7Tab', NULL, 'view', NULL, 1, 'SZOF-3874', NOW()),
    ('employee/employeeext7Tab', 'employee/employeeext7Tab', NULL, 'add', NULL, 1, 'SZOF-3874', NOW()),
    ('employee/employeeext7Tab', 'employee/employeeext7Tab', NULL, 'modify', NULL, 1, 'SZOF-3874', NOW()),
    ('employee/employeeext7Tab', 'employee/employeeext7Tab', NULL, 'delete', NULL, 1, 'SZOF-3874', NOW());

INSERT INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
    ('employeetabs/employeeext7', 'Employeeext7', 'tab_employeetabs_employeeext7', 'SZOF-3874', NOW());

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'tab_employeetabs_employeeext7', 'További adatok VII.', 1),
    ('en', 'ttwa-base', 'tab_employeetabs_employeeext7', 'Personal data VII.', 1);

INSERT INTO `option_config` (`option_id`, `type`, `status`, `created_by`, `created_on`) VALUES
    ('ext7_option1', 'ed', 2, 'system', NOW()),
    ('ext7_option2', 'ed', 7, 'system', NOW()),
    ('ext7_option3', 'ed', 7, 'system', NOW()),
    ('ext7_option4', 'ed', 7, 'system', NOW()),
    ('ext7_option5', 'ed', 7, 'system', NOW()),
    ('ext7_option6', 'ed', 7, 'system', NOW()),
    ('ext7_option7', 'ed', 7, 'system', NOW()),
    ('ext7_option8', 'ed', 7, 'system', NOW()),
    ('ext7_option9', 'ed', 7, 'system', NOW()),
    ('ext7_option10', 'ed', 7, 'system', NOW()),
    ('ext7_option11', 'ed', 7, 'system', NOW()),
    ('ext7_option12', 'ed', 7, 'system', NOW()),
    ('ext7_option13', 'ed', 7, 'system', NOW()),
    ('ext7_option14', 'ed', 7, 'system', NOW()),
    ('ext7_option15', 'ed', 7, 'system', NOW()),
    ('ext7_option16', 'ed', 7, 'system', NOW()),
    ('ext7_option17', 'ed', 7, 'system', NOW()),
    ('ext7_option18', 'ed', 7, 'system', NOW()),
    ('ext7_option19', 'ed', 7, 'system', NOW()),
    ('ext7_option20', 'ed', 7, 'system', NOW()),
    ('ext7_option21', 'ed', 7, 'system', NOW()),
    ('ext7_option22', 'ed', 7, 'system', NOW()),
    ('ext7_option23', 'ed', 7, 'system', NOW()),
    ('ext7_option24', 'ed', 7, 'system', NOW()),
    ('ext7_option25', 'ed', 7, 'system', NOW()),
    ('ext7_option26', 'ed', 7, 'system', NOW()),
    ('ext7_option27', 'ed', 7, 'system', NOW()),
    ('ext7_option28', 'ed', 7, 'system', NOW()),
    ('ext7_option29', 'ed', 7, 'system', NOW()),
    ('ext7_option30', 'ed', 7, 'system', NOW()),
    ('ext7_option31', 'ed', 7, 'system', NOW()),
    ('ext7_option32', 'ed', 7, 'system', NOW()),
    ('ext7_option33', 'ed', 7, 'system', NOW()),
    ('ext7_option34', 'ed', 7, 'system', NOW()),
    ('ext7_option35', 'ed', 7, 'system', NOW()),
    ('ext7_option36', 'ed', 7, 'system', NOW()),
    ('ext7_option37', 'ed', 7, 'system', NOW()),
    ('ext7_option38', 'ed', 7, 'system', NOW()),
    ('ext7_option39', 'ed', 7, 'system', NOW()),
    ('ext7_option40', 'ed', 7, 'system', NOW()),
    ('ext7_option41', 'ed', 7, 'system', NOW()),
    ('ext7_option42', 'ed', 7, 'system', NOW()),
    ('ext7_option43', 'ed', 7, 'system', NOW()),
    ('ext7_option44', 'ed', 7, 'system', NOW()),
    ('ext7_option45', 'ed', 7, 'system', NOW()),
    ('ext7_option46', 'ed', 7, 'system', NOW()),
    ('ext7_option47', 'ed', 7, 'system', NOW()),
    ('ext7_option48', 'ed', 7, 'system', NOW()),
    ('ext7_option49', 'ed', 7, 'system', NOW()),
    ('ext7_option50', 'ed', 7, 'system', NOW()),
    ('ext7_option51', 'ed', 7, 'system', NOW()),
    ('ext7_option52', 'ed', 7, 'system', NOW()),
    ('ext7_option53', 'ed', 7, 'system', NOW()),
    ('ext7_option54', 'ed', 7, 'system', NOW()),
    ('ext7_option55', 'ed', 7, 'system', NOW()),
    ('ext7_option56', 'ed', 7, 'system', NOW()),
    ('ext7_option57', 'ed', 7, 'system', NOW()),
    ('ext7_option58', 'ed', 7, 'system', NOW()),
    ('ext7_option59', 'ed', 7, 'system', NOW()),
    ('ext7_option60', 'ed', 7, 'system', NOW());

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'ext7_option1', 'Option1', 1),
    ('en', 'ttwa-base', 'ext7_option1', 'Option1', 1),
    ('hu', 'ttwa-base', 'ext7_option2', 'Option2', 1),
    ('en', 'ttwa-base', 'ext7_option2', 'Option2', 1),
    ('hu', 'ttwa-base', 'ext7_option3', 'Option3', 1),
    ('en', 'ttwa-base', 'ext7_option3', 'Option3', 1),
    ('hu', 'ttwa-base', 'ext7_option4', 'Option4', 1),
    ('en', 'ttwa-base', 'ext7_option4', 'Option4', 1),
    ('hu', 'ttwa-base', 'ext7_option5', 'Option5', 1),
    ('en', 'ttwa-base', 'ext7_option5', 'Option5', 1),
    ('hu', 'ttwa-base', 'ext7_option6', 'Option6', 1),
    ('en', 'ttwa-base', 'ext7_option6', 'Option6', 1),
    ('hu', 'ttwa-base', 'ext7_option7', 'Option7', 1),
    ('en', 'ttwa-base', 'ext7_option7', 'Option7', 1),
    ('hu', 'ttwa-base', 'ext7_option8', 'Option8', 1),
    ('en', 'ttwa-base', 'ext7_option8', 'Option8', 1),
    ('hu', 'ttwa-base', 'ext7_option9', 'Option9', 1),
    ('en', 'ttwa-base', 'ext7_option9', 'Option9', 1),
    ('hu', 'ttwa-base', 'ext7_option10', 'Option10', 1),
    ('en', 'ttwa-base', 'ext7_option10', 'Option10', 1),
    ('hu', 'ttwa-base', 'ext7_option11', 'Option11', 1),
    ('en', 'ttwa-base', 'ext7_option11', 'Option11', 1),
    ('hu', 'ttwa-base', 'ext7_option12', 'Option12', 1),
    ('en', 'ttwa-base', 'ext7_option12', 'Option12', 1),
    ('hu', 'ttwa-base', 'ext7_option13', 'Option13', 1),
    ('en', 'ttwa-base', 'ext7_option13', 'Option13', 1),
    ('hu', 'ttwa-base', 'ext7_option14', 'Option14', 1),
    ('en', 'ttwa-base', 'ext7_option14', 'Option14', 1),
    ('hu', 'ttwa-base', 'ext7_option15', 'Option15', 1),
    ('en', 'ttwa-base', 'ext7_option15', 'Option15', 1),
    ('hu', 'ttwa-base', 'ext7_option16', 'Option16', 1),
    ('en', 'ttwa-base', 'ext7_option16', 'Option16', 1),
    ('hu', 'ttwa-base', 'ext7_option17', 'Option17', 1),
    ('en', 'ttwa-base', 'ext7_option17', 'Option17', 1),
    ('hu', 'ttwa-base', 'ext7_option18', 'Option18', 1),
    ('en', 'ttwa-base', 'ext7_option18', 'Option18', 1),
    ('hu', 'ttwa-base', 'ext7_option19', 'Option19', 1),
    ('en', 'ttwa-base', 'ext7_option19', 'Option19', 1),
    ('hu', 'ttwa-base', 'ext7_option20', 'Option20', 1),
    ('en', 'ttwa-base', 'ext7_option20', 'Option20', 1),
    ('hu', 'ttwa-base', 'ext7_option21', 'Option21', 1),
    ('en', 'ttwa-base', 'ext7_option21', 'Option21', 1),
    ('hu', 'ttwa-base', 'ext7_option22', 'Option22', 1),
    ('en', 'ttwa-base', 'ext7_option22', 'Option22', 1),
    ('hu', 'ttwa-base', 'ext7_option23', 'Option23', 1),
    ('en', 'ttwa-base', 'ext7_option23', 'Option23', 1),
    ('hu', 'ttwa-base', 'ext7_option24', 'Option24', 1),
    ('en', 'ttwa-base', 'ext7_option24', 'Option24', 1),
    ('hu', 'ttwa-base', 'ext7_option25', 'Option25', 1),
    ('en', 'ttwa-base', 'ext7_option25', 'Option25', 1),
    ('hu', 'ttwa-base', 'ext7_option26', 'Option26', 1),
    ('en', 'ttwa-base', 'ext7_option26', 'Option26', 1),
    ('hu', 'ttwa-base', 'ext7_option27', 'Option27', 1),
    ('en', 'ttwa-base', 'ext7_option27', 'Option27', 1),
    ('hu', 'ttwa-base', 'ext7_option28', 'Option28', 1),
    ('en', 'ttwa-base', 'ext7_option28', 'Option28', 1),
    ('hu', 'ttwa-base', 'ext7_option29', 'Option29', 1),
    ('en', 'ttwa-base', 'ext7_option29', 'Option29', 1),
    ('hu', 'ttwa-base', 'ext7_option30', 'Option30', 1),
    ('en', 'ttwa-base', 'ext7_option30', 'Option30', 1),
    ('hu', 'ttwa-base', 'ext7_option31', 'Option31', 1),
    ('en', 'ttwa-base', 'ext7_option31', 'Option31', 1),
    ('hu', 'ttwa-base', 'ext7_option32', 'Option32', 1),
    ('en', 'ttwa-base', 'ext7_option32', 'Option32', 1),
    ('hu', 'ttwa-base', 'ext7_option33', 'Option33', 1),
    ('en', 'ttwa-base', 'ext7_option33', 'Option33', 1),
    ('hu', 'ttwa-base', 'ext7_option34', 'Option34', 1),
    ('en', 'ttwa-base', 'ext7_option34', 'Option34', 1),
    ('hu', 'ttwa-base', 'ext7_option35', 'Option35', 1),
    ('en', 'ttwa-base', 'ext7_option35', 'Option35', 1),
    ('hu', 'ttwa-base', 'ext7_option36', 'Option36', 1),
    ('en', 'ttwa-base', 'ext7_option36', 'Option36', 1),
    ('hu', 'ttwa-base', 'ext7_option37', 'Option37', 1),
    ('en', 'ttwa-base', 'ext7_option37', 'Option37', 1),
    ('hu', 'ttwa-base', 'ext7_option38', 'Option38', 1),
    ('en', 'ttwa-base', 'ext7_option38', 'Option38', 1),
    ('hu', 'ttwa-base', 'ext7_option39', 'Option39', 1),
    ('en', 'ttwa-base', 'ext7_option39', 'Option39', 1),
    ('hu', 'ttwa-base', 'ext7_option40', 'Option40', 1),
    ('en', 'ttwa-base', 'ext7_option40', 'Option40', 1),
    ('hu', 'ttwa-base', 'ext7_option41', 'Option41', 1),
    ('en', 'ttwa-base', 'ext7_option41', 'Option41', 1),
    ('hu', 'ttwa-base', 'ext7_option42', 'Option42', 1),
    ('en', 'ttwa-base', 'ext7_option42', 'Option42', 1),
    ('hu', 'ttwa-base', 'ext7_option43', 'Option43', 1),
    ('en', 'ttwa-base', 'ext7_option43', 'Option43', 1),
    ('hu', 'ttwa-base', 'ext7_option44', 'Option44', 1),
    ('en', 'ttwa-base', 'ext7_option44', 'Option44', 1),
    ('hu', 'ttwa-base', 'ext7_option45', 'Option45', 1),
    ('en', 'ttwa-base', 'ext7_option45', 'Option45', 1),
    ('hu', 'ttwa-base', 'ext7_option46', 'Option46', 1),
    ('en', 'ttwa-base', 'ext7_option46', 'Option46', 1),
    ('hu', 'ttwa-base', 'ext7_option47', 'Option47', 1),
    ('en', 'ttwa-base', 'ext7_option47', 'Option47', 1),
    ('hu', 'ttwa-base', 'ext7_option48', 'Option48', 1),
    ('en', 'ttwa-base', 'ext7_option48', 'Option48', 1),
    ('hu', 'ttwa-base', 'ext7_option49', 'Option49', 1),
    ('en', 'ttwa-base', 'ext7_option49', 'Option49', 1),
    ('hu', 'ttwa-base', 'ext7_option50', 'Option50', 1),
    ('en', 'ttwa-base', 'ext7_option50', 'Option50', 1),
    ('hu', 'ttwa-base', 'ext7_option51', 'Option51', 1),
    ('en', 'ttwa-base', 'ext7_option51', 'Option51', 1),
    ('hu', 'ttwa-base', 'ext7_option52', 'Option52', 1),
    ('en', 'ttwa-base', 'ext7_option52', 'Option52', 1),
    ('hu', 'ttwa-base', 'ext7_option53', 'Option53', 1),
    ('en', 'ttwa-base', 'ext7_option53', 'Option53', 1),
    ('hu', 'ttwa-base', 'ext7_option54', 'Option54', 1),
    ('en', 'ttwa-base', 'ext7_option54', 'Option54', 1),
    ('hu', 'ttwa-base', 'ext7_option55', 'Option55', 1),
    ('en', 'ttwa-base', 'ext7_option55', 'Option55', 1),
    ('hu', 'ttwa-base', 'ext7_option56', 'Option56', 1),
    ('en', 'ttwa-base', 'ext7_option56', 'Option56', 1),
    ('hu', 'ttwa-base', 'ext7_option57', 'Option57', 1),
    ('en', 'ttwa-base', 'ext7_option57', 'Option57', 1),
    ('hu', 'ttwa-base', 'ext7_option58', 'Option58', 1),
    ('en', 'ttwa-base', 'ext7_option58', 'Option58', 1),
    ('hu', 'ttwa-base', 'ext7_option59', 'Option59', 1),
    ('en', 'ttwa-base', 'ext7_option59', 'Option59', 1),
    ('hu', 'ttwa-base', 'ext7_option60', 'Option60', 1),
    ('en', 'ttwa-base', 'ext7_option60', 'Option60', 1);

UPDATE `_sql_version` SET `revision`=36, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -36-2024-08-07-16:30------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'country_al', 'Albánia', 1),
    ('en', 'ttwa-base', 'country_al', 'Albania', 1),
    ('hu', 'ttwa-base', 'country_and', 'Andorra', 1),
    ('en', 'ttwa-base', 'country_and', 'Andorra', 1),
    ('hu', 'ttwa-base', 'country_at', 'Ausztria', 1),
    ('en', 'ttwa-base', 'country_at', 'Austria', 1),
    ('hu', 'ttwa-base', 'country_by', 'Fehéroroszország', 1),
    ('en', 'ttwa-base', 'country_by', 'Belarus', 1),
    ('hu', 'ttwa-base', 'country_b', 'Belgium', 1),
    ('en', 'ttwa-base', 'country_b', 'Belgium', 1),
    ('hu', 'ttwa-base', 'country_ba', 'Bosznia-Hercegovina', 1),
    ('en', 'ttwa-base', 'country_ba', 'Bosnia and Herzegovina', 1),
    ('hu', 'ttwa-base', 'country_bg', 'Bulgária', 1),
    ('en', 'ttwa-base', 'country_bg', 'Bulgaria', 1),
    ('hu', 'ttwa-base', 'country_hr', 'Horvátország', 1),
    ('en', 'ttwa-base', 'country_hr', 'Croatia', 1),
    ('hu', 'ttwa-base', 'country_cy', 'Ciprus', 1),
    ('en', 'ttwa-base', 'country_cy', 'Cyprus', 1),
    ('hu', 'ttwa-base', 'country_cz', 'Csehország', 1),
    ('en', 'ttwa-base', 'country_cz', 'Czech Republic', 1),
    ('hu', 'ttwa-base', 'country_dk', 'Dánia', 1),
    ('en', 'ttwa-base', 'country_dk', 'Denmark', 1),
    ('hu', 'ttwa-base', 'country_ee', 'Észtország', 1),
    ('en', 'ttwa-base', 'country_ee', 'Estonia', 1),
    ('hu', 'ttwa-base', 'country_fi', 'Finnország', 1),
    ('en', 'ttwa-base', 'country_fi', 'Finland', 1),
    ('hu', 'ttwa-base', 'country_fr', 'Franciaország', 1),
    ('en', 'ttwa-base', 'country_fr', 'France', 1),
    ('hu', 'ttwa-base', 'country_de', 'Németország', 1),
    ('en', 'ttwa-base', 'country_de', 'Germany', 1),
    ('hu', 'ttwa-base', 'country_gr', 'Görögország', 1),
    ('en', 'ttwa-base', 'country_gr', 'Greece', 1),
    ('hu', 'ttwa-base', 'country_hu', 'Magyarország', 1),
    ('en', 'ttwa-base', 'country_hu', 'Hungary', 1),
    ('hu', 'ttwa-base', 'country_is', 'Izland', 1),
    ('en', 'ttwa-base', 'country_is', 'Iceland', 1),
    ('hu', 'ttwa-base', 'country_ie', 'Írország', 1),
    ('en', 'ttwa-base', 'country_ie', 'Ireland', 1),
    ('hu', 'ttwa-base', 'country_it', 'Olaszország', 1),
    ('en', 'ttwa-base', 'country_it', 'Italy', 1),
    ('hu', 'ttwa-base', 'country_lv', 'Lettország', 1),
    ('en', 'ttwa-base', 'country_lv', 'Latvia', 1),
    ('hu', 'ttwa-base', 'country_li', 'Liechtenstein', 1),
    ('en', 'ttwa-base', 'country_li', 'Liechtenstein', 1),
    ('hu', 'ttwa-base', 'country_lt', 'Litvánia', 1),
    ('en', 'ttwa-base', 'country_lt', 'Lithuania', 1),
    ('hu', 'ttwa-base', 'country_lu', 'Luxemburg', 1),
    ('en', 'ttwa-base', 'country_lu', 'Luxembourg', 1),
    ('hu', 'ttwa-base', 'country_mt', 'Málta', 1),
    ('en', 'ttwa-base', 'country_mt', 'Malta', 1),
    ('hu', 'ttwa-base', 'country_mol', 'Moldova', 1),
    ('en', 'ttwa-base', 'country_mol', 'Moldova', 1),
    ('hu', 'ttwa-base', 'country_mc', 'Monaco', 1),
    ('en', 'ttwa-base', 'country_mc', 'Monaco', 1),
    ('hu', 'ttwa-base', 'country_me', 'Montenegró', 1),
    ('en', 'ttwa-base', 'country_me', 'Montenegro', 1),
    ('hu', 'ttwa-base', 'country_mk', 'Észak-Macedónia', 1),
    ('en', 'ttwa-base', 'country_mk', 'North Macedonia', 1),
    ('hu', 'ttwa-base', 'country_no', 'Norvégia', 1),
    ('en', 'ttwa-base', 'country_no', 'Norway', 1),
    ('hu', 'ttwa-base', 'country_pl', 'Lengyelország', 1),
    ('en', 'ttwa-base', 'country_pl', 'Poland', 1),
    ('hu', 'ttwa-base', 'country_pt', 'Portugália', 1),
    ('en', 'ttwa-base', 'country_pt', 'Portugal', 1),
    ('hu', 'ttwa-base', 'country_ro', 'Románia', 1),
    ('en', 'ttwa-base', 'country_ro', 'Romania', 1),
    ('hu', 'ttwa-base', 'country_ru', 'Oroszország', 1),
    ('en', 'ttwa-base', 'country_ru', 'Russia', 1),
    ('hu', 'ttwa-base', 'country_sm', 'San Marino', 1),
    ('en', 'ttwa-base', 'country_sm', 'San Marino', 1),
    ('hu', 'ttwa-base', 'country_srb', 'Szerbia', 1),
    ('en', 'ttwa-base', 'country_srb', 'Serbia', 1),
    ('hu', 'ttwa-base', 'country_sk', 'Szlovákia', 1),
    ('en', 'ttwa-base', 'country_sk', 'Slovakia', 1),
    ('hu', 'ttwa-base', 'country_si', 'Szlovénia', 1),
    ('en', 'ttwa-base', 'country_si', 'Slovenia', 1),
    ('hu', 'ttwa-base', 'country_es', 'Spanyolország', 1),
    ('en', 'ttwa-base', 'country_es', 'Spain', 1),
    ('hu', 'ttwa-base', 'country_se', 'Svédország', 1),
    ('en', 'ttwa-base', 'country_se', 'Sweden', 1),
    ('hu', 'ttwa-base', 'country_ch', 'Svájc', 1),
    ('en', 'ttwa-base', 'country_ch', 'Switzerland', 1),
    ('hu', 'ttwa-base', 'country_ua', 'Ukrajna', 1),
    ('en', 'ttwa-base', 'country_ua', 'Ukraine', 1),
    ('hu', 'ttwa-base', 'country_gbr', 'Egyesült Királyság', 1),
    ('en', 'ttwa-base', 'country_gbr', 'United Kingdom', 1),
    ('hu', 'ttwa-base', 'country_vat', 'Vatikán', 1),
    ('en', 'ttwa-base', 'country_vat', 'Vatican', 1),
    ('hu', 'ttwa-base', 'nat_vat', 'Vatikáni', 1),
    ('en', 'ttwa-base', 'nat_vat', 'Vatican', 1);

UPDATE `_sql_version` SET `revision`=37, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -37-2024-08-12-13:30------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Alapszabadságok feltöltése' WHERE `dict_id` = 'menu_item_base_absence_upload' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Payroll approval' WHERE `dict_id` = 'payroll_transfer_approve' AND `lang` = 'en' AND `module` = 'ttwa-base';

UPDATE `_sql_version` SET `revision`=38, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -38-2024-09-03-13:30------------------------------------------------

CREATE TABLE IF NOT EXISTS `process_acknowledgment` (
    `row_id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `day` DATE NOT NULL,
    `employee_contract_id` VARCHAR(32) NOT NULL,
    `process_id` VARCHAR(32) NOT NULL,
    `process_identifier` VARCHAR(32) NOT NULL,
    `acknowledgement_status` ENUM('approved','rejected') NOT NULL,
    `status` INT(10) NOT NULL,
    `created_by` VARCHAR(32) NOT NULL,
    `created_on` DATETIME NOT NULL,
    `modified_by` VARCHAR(32) NULL DEFAULT NULL,
    `modified_on` DATETIME NULL DEFAULT NULL,
    PRIMARY KEY (`row_id`) USING BTREE
) COLLATE='utf8mb3_general_ci' ENGINE=InnoDB;

UPDATE `_sql_version` SET `revision`=39, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -39-2024-09-05-10:00------------------------------------------------

ALTER TABLE process_acknowledgment
    ENGINE=InnoDB,
    DEFAULT CHARSET=utf8,
    COLLATE=utf8_unicode_ci;

ALTER TABLE process_acknowledgment
    MODIFY employee_contract_id VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    MODIFY process_id VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    MODIFY process_identifier VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    MODIFY acknowledgement_status VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    MODIFY created_by VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    MODIFY modified_by VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci;

UPDATE `_sql_version` SET `revision`=40, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -40-2024-09-06-11:10------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
    ('reportAcknowledgedOvertimeStmt', 'reportAcknowledgedOvertimeStmt --- view', '0', 'Tudomásul vett túlóra kimutatás', 'SZOF-3950', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('a5b6bd79e008725744118c7c46e10cda', 'reportAcknowledgedOvertimeStmt', 'SZOF-3950', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('reportAcknowledgedOvertimeStmt', 'reportAcknowledgedOvertimeStmt', 'view', '1', '1', 'SZOF-3950', NOW());

INSERT IGNORE INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
    ('reportAcknowledgedOvertimeStmt', 'reportAcknowledgedOvertimeStmt', 'menu_item_report_acknowledged_overtime_stmt', 'SZOF-3950', NOW());

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`, `menu_item_name`, `menu_modul`, `menu_label`, `menu_item_css_class`, `menu_url`, `menu_visible`, `menu_visible_operation`, `menu_item_parent_id`, `menu_order`) VALUES
    ('reportAcknowledgedOvertimeStmt', 'reportAcknowledgedOvertimeStmt', 'ttwa-base', 'menu_item_report_acknowledged_overtime_stmt', 'sub', '/reportAcknowledgedOvertimeStmt/index', 'reportAcknowledgedOvertimeStmt', 'view', '82', '217');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'menu_item_report_acknowledged_overtime_stmt', 'Tudomásul vett túlóra kimutatás', '1'),
    ('en', 'ttwa-base', 'menu_item_report_acknowledged_overtime_stmt', 'Acknowledged overtime statement', '1'),
    ('hu', 'ttwa-base', 'page_title_report_acknowledged_overtime_stmt', 'Tudomásul vett túlóra kimutatás', '1'),
    ('en', 'ttwa-base', 'page_title_report_acknowledged_overtime_stmt', 'Acknowledged overtime statement', '1'),
    ('hu', 'ttwa-base', 'notice_in_advice', 'Előre tudomásul vett', '1'),
    ('en', 'ttwa-base', 'notice_in_advice', 'Notice in advice', '1'),
    ('hu', 'ttwa-base', 'did_not_take_notice', 'Nem tudomásul vett', '1'),
    ('en', 'ttwa-base', 'did_not_take_notice', 'Did not take notice', '1'),
    ('hu', 'ttwa-base', 'rejected', 'Elutasított', '1'),
    ('en', 'ttwa-base', 'rejected', 'Rejected', '1');

INSERT IGNORE INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('reportAcknowledgedOvertimeStmt', 'date', 'EMPLOYEE_WITH_YEARMONTH', 'combo', '0', '2', 'SZOF-3950', NOW()),
    ('reportAcknowledgedOvertimeStmt', 'group', 'COMPANY', 'combo', '0', '2', 'SZOF-3950', NOW()),
    ('reportAcknowledgedOvertimeStmt', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2', 'SZOF-3950', NOW()),
    ('reportAcknowledgedOvertimeStmt', 'group', 'PAYROLL', 'combo', '0', '2', 'SZOF-3950', NOW()),
    ('reportAcknowledgedOvertimeStmt', 'group', 'WORKGROUP', 'combo', '0', '2', 'SZOF-3950', NOW()),
    ('reportAcknowledgedOvertimeStmt', 'group', 'UNIT', 'combo', '0', '2', 'SZOF-3950', NOW()),
    ('reportAcknowledgedOvertimeStmt', 'group', 'COMPANY_ORG_GROUP2', 'combo', '0', '2', 'SZOF-3950', NOW()),
    ('reportAcknowledgedOvertimeStmt', 'group', 'EMPLOYEECONTRACT', 'auto', '0', '2', 'SZOF-3950', NOW());

UPDATE `_sql_version` SET `revision`=41, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -41-2024-09-10-11:00------------------------------------------------

DELETE FROM `auth_role` WHERE `role_id` = 'reportAcknowledgedOvertimeStmt';
DELETE FROM `auth_role_in_group` WHERE `role_id` = 'reportAcknowledgedOvertimeStmt';
DELETE FROM `auth_acl` WHERE `role_id` = 'reportAcknowledgedOvertimeStmt';
DELETE FROM `auth_controller` WHERE `controller_id` = 'reportAcknowledgedOvertimeStmt';
DELETE FROM `menu_item_table` WHERE `menu_item_id` = 'reportAcknowledgedOvertimeStmt';
DELETE FROM `dictionary` WHERE `dict_id` IN ('menu_item_report_acknowledged_overtime_stmt', 'page_title_report_acknowledged_overtime_stmt', 'notice_in_advice', 'did_not_take_notice', 'rejected');
DELETE FROM `search_filter` WHERE `controller_id` = 'reportAcknowledgedOvertimeStmt';

UPDATE `_sql_version` SET `revision`=42, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -42-2024-09-12-10:00------------------------------------------------

INSERT INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
    ('isExcelExportEnabledOnApprovePage', '0', 'string', 'Jóváhagyás oldalon lehessen -e excelt exportálni' ,'setting_isExcelExportEnabledOnApprovePage', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
     ('hu', 'ttwa-base', 'setting_isExcelExportEnabledOnApprovePage', 'Jóváhagyás oldalon lehessen -e excelt exportálni', '1'),
     ('en', 'ttwa-base', 'setting_isExcelExportEnabledOnApprovePage', 'Is excel export enabled on Approve page', '1'),
     ('hu', 'ttwa-base', 'expired', 'Lejárt', '1'),
     ('en', 'ttwa-base', 'expired', 'Expired', '1'),
     ('hu', 'ttwa-base', 'signal', 'Jelzés', '1'),
     ('en', 'ttwa-base', 'signal', 'Signal', '1');

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES
    ('approve', 'alert_signal_export', 'ALL', 2, 'SZOF-3952', now());

UPDATE `_sql_version` SET `revision`=43, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -43-2024-09-13-12:00------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'duration_employment', 'Munkaviszony időtartama', '1'),
    ('en', 'ttwa-base', 'duration_employment', 'Duration of employment', '1'),
    ('hu', 'ttwa-base', 'academic_hours', 'Tárgyidőszaki órák', '1'),
    ('en', 'ttwa-base', 'academic_hours', 'Academic hours', '1'),
    ('hu', 'ttwa-base', 'term_days', 'Tárgyidőszaki napok', '1'),
    ('en', 'ttwa-base', 'term_days', 'Term days', '1'),
    ('hu', 'ttwa-base', 'rolled_hours', 'Göngyölt órák', '1'),
    ('en', 'ttwa-base', 'rolled_hours', 'Rolled hours', '1'),
    ('hu', 'ttwa-base', 'rolled_days', 'Göngyölt napok', '1'),
    ('en', 'ttwa-base', 'rolled_days', 'Rolled days', '1'),
    ('hu', 'ttwa-base', 'paid_rolled_hours_frame', 'Kifizetett göngyölt órák keret', '1'),
    ('en', 'ttwa-base', 'paid_rolled_hours_frame', 'Paid rolled hours frame', '1'),
    ('hu', 'ttwa-base', 'paid_extraordinary_hours_overtime', 'Kifizetett rendkívüli órák/túlóra', '1'),
    ('en', 'ttwa-base', 'paid_extraordinary_hours_overtime', 'Paid extraordinary hours/overtime', '1'),
    ('hu', 'ttwa-base', 'total_paid_rolled_hours', 'Kifizetett göngyölt órák összesen', '1'),
    ('en', 'ttwa-base', 'total_paid_rolled_hours', 'Total paid rolled hours', '1'),
    ('hu', 'ttwa-base', 'maximum_annual_overtime_limit', 'Maximum éves túlórakeret', '1'),
    ('en', 'ttwa-base', 'maximum_annual_overtime_limit', 'Maximum annual overtime limit', '1'),
    ('hu', 'ttwa-base', 'annual_allowance', 'Éves szabadságkeret', '1'),
    ('en', 'ttwa-base', 'annual_allowance', 'Annual vacation allowance', '1'),
    ('hu', 'ttwa-base', 'vacation_taken', 'Kivett szabadság', '1'),
    ('en', 'ttwa-base', 'vacation_taken', 'Vacation taken', '1'),
    ('hu', 'ttwa-base', 'planned_leave', 'Betervezett szabadság', '1'),
    ('en', 'ttwa-base', 'planned_leave', 'Planned leave', '1'),
    ('hu', 'ttwa-base', 'requested_leave', 'Igényelt szabadság', '1'),
    ('en', 'ttwa-base', 'requested_leave', 'Requested leave', '1'),
    ('hu', 'ttwa-base', 'remaining_freedom', 'Fennmaradó szabadság', '1'),
    ('en', 'ttwa-base', 'remaining_freedom', 'Remaining freedom', '1');

UPDATE `_sql_version` SET `revision`=44, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -44-2024-09-16-12:00------------------------------------------------

INSERT INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('attendanceSheetRoundTotalValueToHalfHour', '0', 'string', 'Jelenléti íven a mindösszesen sorban lévő értéket fél órára kerekíti' ,'setting_attendanceSheetRoundTotalValueToHalfHour', '1', 'SZOF-4088', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
     ('hu', 'ttwa-base', 'setting_attendanceSheetRoundTotalValueToHalfHour', 'Jelenléti íven a mindösszesen sorban lévő értéket fél órára kerekíti', '1'),
     ('en', 'ttwa-base', 'setting_attendanceSheetRoundTotalValueToHalfHour', 'The total value is rounded up to half an hour at attendance sheet', '1');

UPDATE `_sql_version` SET `revision`=45, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -45-2024-09-16-15:00------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
    ('employeeDataPersonalIdCardHide', 'employee/employeeExtTab --- personal_id_card_number',0 ,'Dolgozó kezelésnél a személyi mező elrejtése', 'SZOF-4086', NOW()),
    ('employeeDataPassportNumberHide', 'employee/employeeExtTab --- passport_number',0 ,'Dolgozó kezelésnél az útlevél mező elrejtése', 'SZOF-4086', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('employeeDataPersonalIdCardHide', 'employee/employeeExtTab', 'personal_id_card_number', 1, 1, 'SZOF-4086', NOW()),
    ('employeeDataPassportNumberHide', 'employee/employeeExtTab', 'passport_number', 1, 1, 'SZOF-4086', NOW());

UPDATE `_sql_version` SET `revision`=46, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -46-2024-09-16-16:00------------------------------------------------

INSERT INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
    ('automaticLoadExtFromFeorCode', '0', 'string', 'A FEOR adatok automatikus betöltése az ext oldalra FEOR kód létrehozása vagy módosítása esetén.' ,'setting_automaticLoadExtFromFeorCode', '1'),
    ('automaticLoadExtFromHrBookletCategoryCode', '0', 'string', 'A besorolási kategória adatok automatikus betöltése az ext oldalra besorolási kategória kód létrehozása vagy módosítása esetén.' ,'setting_automaticLoadExtFromHrBookletCategoryCode', '1'),
    ('automaticLoadExtFromQualificationLevelCode', '0', 'string', 'A képzettségi szint adatok automatikus betöltése az ext oldalra képzettségi szint kód létrehozása vagy módosítása esetén.' ,'setting_automaticLoadExtFromQualificationLevelCode', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-ahp', 'setting_automaticLoadExtFromFeorCode', 'A FEOR adatok automatikus betöltése az ext oldalra FEOR kód létrehozása vagy módosítása esetén.', '1'),
    ('en', 'ttwa-ahp', 'setting_automaticLoadExtFromFeorCode', 'The automatic load of FEOR data to ext page in case of modify or creation of FEOR code.', '1'),
    ('hu', 'ttwa-ahp', 'setting_automaticLoadExtFromHrBookletCategoryCode', 'A besorolási kategória adatok automatikus betöltése az ext oldalra besorolási kategória kód létrehozása vagy módosítása esetén.', '1'),
    ('en', 'ttwa-ahp', 'setting_automaticLoadExtFromHrBookletCategoryCode', 'The automatic load of HR booklet data to ext page in case of modify or creation of HR booklet code.', '1'),
    ('hu', 'ttwa-ahp', 'setting_automaticLoadExtFromQualificationLevelCode', 'A képzettségi szint adatok automatikus betöltése az ext oldalra képzettségi szint kód létrehozása vagy módosítása esetén.', '1'),
    ('en', 'ttwa-ahp', 'setting_automaticLoadExtFromQualificationLevelCode', 'The automatic load of qualification level data to ext page in case of modify or creation of qualification level code.', '1');

UPDATE `_sql_version` SET `revision`=47, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -47-2024-09-19-11:00------------------------------------------------

CREATE INDEX IDX_employee_contract_id_status_day ON process_acknowledgment (`employee_contract_id`, `status`, `day`);
CREATE INDEX IDX_employee_contract_day_process_identifier_status_process_id ON process_acknowledgment (`employee_contract_id`,`day`, `process_identifier`, `status`, `process_id`);

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('feature_processAcknowledgment_settings', '{"acknowledgment-status": false}', 'string', 'Alapvetően Flexnek készült feature, (Munkavállalói tudomásulvétel felülethez kapcsolodó) - Összesítőlap, dolgozó éves távollét, több havi távolléte, távollétek jóváhagyása hover+ikonok', 'feature_processAcknowledgment_settings',1, 'SZOF-3949', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'feature_processAcknowledgment_settings', 'Alapvetően Flexnek készült feature, (Munkavállalói tudomásulvétel felülethez kapcsolodó)', '1'),
    ('en', 'ttwa-base', 'feature_processAcknowledgment_settings', 'Basically a feature made for Flex, (related to the Employee Acknowledgment interface)', '1'),
    ('en', 'ttwa-base', 'approved_process_acknowledgment_wso', 'Jóváhagyott, tudomásul vett túlóra', '1'),
    ('en', 'ttwa-base', 'approved_process_acknowledgment_wso', 'Approved, acknowledged overtime', '1'),
    ('hu', 'ttwa-base', 'pending_process_acknowledgment_wso', 'Jóváhagyásra váró túlóra', '1'),
    ('en', 'ttwa-base', 'pending_process_acknowledgment_wso', 'Overtime pending approval', '1'),
    ('hu', 'ttwa-base', 'acknowledged_process_acknowledgment', 'Tudomásul véve', '1'),
    ('en', 'ttwa-base', 'acknowledged_process_acknowledgment', 'Acknowledging', '1'),
    ('hu', 'ttwa-base', 'unacknowledged_process_acknowledgment', 'Nem tudomásul véve', '1'),
    ('en', 'ttwa-base', 'unacknowledged_process_acknowledgment', 'Acknowledging', '1'),
    ('en', 'ttwa-base', 'button_acknowledgment', 'Tudomásulvétel', '1'),
    ('en', 'ttwa-base', 'button_acknowledgment', 'Not acknowledging', '1');

UPDATE `_sql_version` SET `revision`=48, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -48-2024-09-20-13:00------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'disableLdapAuth', 'Kikapcsolja az LDAP autentikációt?', '1'),
	('en', 'ttwa-base', 'disableLdapAuth', 'Do you want to disable LDAP authentication?', '1');

UPDATE `_sql_version` SET `revision`=49, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -49-2024-09-24-12:45------------------------------------------------

INSERT IGNORE INTO `public_holiday` (`holidaydate`, `country`, `name_dict_id`, `name`, `type`, `chdate`, `company_id`, `status`, `created_by`, `created_on`) VALUES
    ('2025-01-01', 'hu', 'public_holiday_hun_newyear', 'Újév', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-03-15', 'hu', 'public_holiday_hun_march_15th', '1848-as forradalom', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-04-18', 'hu', 'public_holiday_hun_good_friday', 'Nagypéntek', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-04-20', 'hu', 'public_holiday_hun_easter', 'Húsvét vasárnap', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-04-21', 'hu', 'public_holiday_hun_easter_monday', 'Húsvéthétfő', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-05-01', 'hu', 'public_holiday_hun_international_workers_day', 'Munka ünnepe', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-05-02', 'hu', 'public_holiday_hun_transferred_restday', 'Pihenőnap 2025-05-17 helyett', '5', '2025-05-17', 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-05-17', 'hu', 'public_holiday_hun_transferred_workday', 'Áthelyezett munkanap 2025-05-02 helyett', 'D', '2025-05-02', 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-06-08', 'hu', 'public_holiday_hun_whitsun', 'Pünkösd', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-06-09', 'hu', 'public_holiday_hun_whitsun_monday', 'Pünkösdhétfő', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-08-20', 'hu', 'public_holiday_hun_august_20th', 'Államalapítás ünnepe', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-10-18', 'hu', 'public_holiday_hun_transferred_workday', 'Áthelyezett munkanap 2025-10-24 helyett', 'D', '2025-10-24', 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-10-23', 'hu', 'public_holiday_hun_october_23th', '1956-os forradalom', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-10-24', 'hu', 'public_holiday_hun_transferred_restday', 'Pihenőnap 2025-10-18 helyett', '5', '2025-10-18', 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-11-01', 'hu', 'public_holiday_hun_all_saints_day', 'Mindszentek', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-12-13', 'hu', 'public_holiday_hun_transferred_workday', 'Áthelyezett munkanap 2025-12-24 helyett', 'D', '2025-12-24', 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-12-24', 'hu', 'public_holiday_hun_transferred_restday', 'Pihenőnap 2025-12-13 helyett', '5', '2025-12-13', 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-12-25', 'hu', 'public_holiday_hun_christmasday1', 'Karácsony', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW()),
    ('2025-12-26', 'hu', 'public_holiday_hun_christmasday2', 'Karácsony', '3', NULL, 'ALL', 2, 'SZOF-4132', NOW());

UPDATE `_sql_version` SET `revision`=50, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -50-2024-09-24-13:00------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'nat_ai', 'Anguillai', 1),
    ('en', 'ttwa-base', 'nat_ai', 'Anguillian', 1),
    ('hu', 'ttwa-base', 'nat_aq', 'Antarktiszi', 1),
    ('en', 'ttwa-base', 'nat_aq', 'Antarctican', 1),
    ('hu', 'ttwa-base', 'nat_bv', 'Bouvet-szigeti', 1),
    ('en', 'ttwa-base', 'nat_bv', 'Bouvet Islander', 1),
    ('hu', 'ttwa-base', 'nat_coi', 'Komorosi', 1),
    ('en', 'ttwa-base', 'nat_coi', 'Comorian', 1),
    ('hu', 'ttwa-base', 'nat_cvi', 'Cape verdei', 1),
    ('en', 'ttwa-base', 'nat_cvi', 'Cape Verdean', 1),
    ('hu', 'ttwa-base', 'nat_io', 'Brit indiai-óceáni területi', 1),
    ('en', 'ttwa-base', 'nat_io', 'British Indian Ocean Territory', 1),
    ('hu', 'ttwa-base', 'nat_ki', 'Kiribati', 1),
    ('en', 'ttwa-base', 'nat_ki', 'I-Kiribati', 1),
    ('hu', 'ttwa-base', 'nat_mh', 'Marschall-szigeteki', 1),
    ('en', 'ttwa-base', 'nat_mh', 'Marshallese', 1),
    ('hu', 'ttwa-base', 'nat_mq', 'Martiniquei', 1),
    ('en', 'ttwa-base', 'nat_mq', 'Martiniquais', 1),
    ('hu', 'ttwa-base', 'nat_nam', 'Namibiai', 1),
    ('en', 'ttwa-base', 'nat_nam', 'Namibian', 1),
    ('hu', 'ttwa-base', 'nat_nru', 'Naurui', 1),
    ('en', 'ttwa-base', 'nat_nru', 'Nauruan', 1),
    ('hu', 'ttwa-base', 'nat_nu', 'Niuei', 1),
    ('en', 'ttwa-base', 'nat_nu', 'Niuean', 1),
    ('hu', 'ttwa-base', 'nat_pm', 'St-pierre és miqueloni', 1),
    ('en', 'ttwa-base', 'nat_pm', 'Saint-Pierrais and Miquelonnais', 1),
    ('hu', 'ttwa-base', 'nat_pn', 'Pitcairn-szigeteki', 1),
    ('en', 'ttwa-base', 'nat_pn', 'Pitcairn Islander', 1),
    ('hu', 'ttwa-base', 'nat_pw', 'Palaui', 1),
    ('en', 'ttwa-base', 'nat_pw', 'Palauan', 1),
    ('hu', 'ttwa-base', 'nat_re', 'Réunioni', 1),
    ('en', 'ttwa-base', 'nat_re', 'Réunionese', 1),
    ('hu', 'ttwa-base', 'nat_sh', 'Szent ilonai', 1),
    ('en', 'ttwa-base', 'nat_sh', 'Saint Helenian', 1),
    ('hu', 'ttwa-base', 'nat_sj', 'Svalbardi', 1),
    ('en', 'ttwa-base', 'nat_sj', 'Svalbardian', 1),
    ('hu', 'ttwa-base', 'nat_swa', 'Szváziföldi', 1),
    ('en', 'ttwa-base', 'nat_swa', 'Swazi', 1),
    ('hu', 'ttwa-base', 'nat_tc', 'Turks és caicos-szigeteki', 1),
    ('en', 'ttwa-base', 'nat_tc', 'Turks and Caicos Islander', 1),
    ('hu', 'ttwa-base', 'nat_tk', 'Tokelau-szigeteki', 1),
    ('en', 'ttwa-base', 'nat_tk', 'Tokelauan', 1),
    ('hu', 'ttwa-base', 'nat_um', 'Amerikai csendes-óceáni-szigeteki', 1),
    ('en', 'ttwa-base', 'nat_um', 'United States Minor Outlying Islands', 1),
    ('hu', 'ttwa-base', 'nat_un', 'Unoi', 1),
    ('en', 'ttwa-base', 'nat_un', 'United Nations', 1),
    ('hu', 'ttwa-base', 'nat_vu', 'Vanuatui', 1),
    ('en', 'ttwa-base', 'nat_vu', 'Ni-Vanuatu', 1),
    ('hu', 'ttwa-base', 'nat_wafu', 'Wallis és futunai', 1),
    ('en', 'ttwa-base', 'nat_wafu', 'Wallisian and Futunan', 1),
    ('hu', 'ttwa-base', 'nat_wsh', 'Nyugat-szaharai', 1),
    ('en', 'ttwa-base', 'nat_wsh', 'Sahrawi', 1),
    ('hu', 'ttwa-base', 'nat_yt', 'Mayottei', 1),
    ('en', 'ttwa-base', 'nat_yt', 'Mahoran', 1),
    ('hu', 'ttwa-base', 'country_ai', 'Anguilla', 1),
    ('en', 'ttwa-base', 'country_ai', 'Anguilla', 1),
    ('hu', 'ttwa-base', 'country_aq', 'Antarktisz', 1),
    ('en', 'ttwa-base', 'country_aq', 'Antarctica', 1),
    ('hu', 'ttwa-base', 'country_bv', 'Bouvet-sziget', 1),
    ('en', 'ttwa-base', 'country_bv', 'Bouvet Island', 1),
    ('hu', 'ttwa-base', 'country_km', 'Komoros', 1),
    ('en', 'ttwa-base', 'country_km', 'Comoros', 1),
    ('hu', 'ttwa-base', 'country_cv', 'Zöld-foki Köztársaság', 1),
    ('en', 'ttwa-base', 'country_cv', 'Cape Verde', 1),
    ('hu', 'ttwa-base', 'country_io', 'Brit indiai-óceáni terület', 1),
    ('en', 'ttwa-base', 'country_io', 'British Indian Ocean Territory', 1),
    ('hu', 'ttwa-base', 'country_ki', 'Kiribati', 1),
    ('en', 'ttwa-base', 'country_ki', 'Kiribati', 1),
    ('hu', 'ttwa-base', 'country_mh', 'Marschall-szigetek', 1),
    ('en', 'ttwa-base', 'country_mh', 'Marshall Islands', 1),
    ('hu', 'ttwa-base', 'country_mq', 'Martinique', 1),
    ('en', 'ttwa-base', 'country_mq', 'Martinique', 1),
    ('hu', 'ttwa-base', 'country_na', 'Namíbia', 1),
    ('en', 'ttwa-base', 'country_na', 'Namibia', 1),
    ('hu', 'ttwa-base', 'country_nr', 'Nauru', 1),
    ('en', 'ttwa-base', 'country_nr', 'Nauru', 1),
    ('hu', 'ttwa-base', 'country_nu', 'Niue', 1),
    ('en', 'ttwa-base', 'country_nu', 'Niue', 1),
    ('hu', 'ttwa-base', 'country_pm', 'St-Pierre és Miquelon', 1),
    ('en', 'ttwa-base', 'country_pm', 'Saint Pierre and Miquelon', 1),
    ('hu', 'ttwa-base', 'country_pn', 'Pitcairn-szigetek', 1),
    ('en', 'ttwa-base', 'country_pn', 'Pitcairn Islands', 1),
    ('hu', 'ttwa-base', 'country_pw', 'Palau', 1),
    ('en', 'ttwa-base', 'country_pw', 'Palau', 1),
    ('hu', 'ttwa-base', 'country_re', 'Réunion', 1),
    ('en', 'ttwa-base', 'country_re', 'Réunion', 1),
    ('hu', 'ttwa-base', 'country_sh', 'Szent Ilona', 1),
    ('en', 'ttwa-base', 'country_sh', 'Saint Helena', 1),
    ('hu', 'ttwa-base', 'country_sj', 'Svalbard', 1),
    ('en', 'ttwa-base', 'country_sj', 'Svalbard', 1),
    ('hu', 'ttwa-base', 'country_sz', 'Szváziföld', 1),
    ('en', 'ttwa-base', 'country_sz', 'Eswatini', 1),
    ('hu', 'ttwa-base', 'country_tc', 'Turks és Caicos-szigetek', 1),
    ('en', 'ttwa-base', 'country_tc', 'Turks and Caicos Islands', 1),
    ('hu', 'ttwa-base', 'country_tk', 'Tokelau-szigetek', 1),
    ('en', 'ttwa-base', 'country_tk', 'Tokelau', 1),
    ('hu', 'ttwa-base', 'country_um', 'Amerikai csendes-óceáni-szigetek', 1),
    ('en', 'ttwa-base', 'country_um', 'United States Minor Outlying Islands', 1),
    ('hu', 'ttwa-base', 'country_un', 'UNO', 1),
    ('en', 'ttwa-base', 'country_un', 'United Nations', 1),
    ('hu', 'ttwa-base', 'country_vu', 'Vanuatu', 1),
    ('en', 'ttwa-base', 'country_vu', 'Vanuatu', 1),
    ('hu', 'ttwa-base', 'country_wafu', 'Wallis és Futuna', 1),
    ('en', 'ttwa-base', 'country_wafu', 'Wallis and Futuna', 1),
    ('hu', 'ttwa-base', 'country_eh', 'Nyugat-Szahara', 1),
    ('en', 'ttwa-base', 'country_eh', 'Western Sahara', 1),
    ('hu', 'ttwa-base', 'country_yt', 'Mayotte', 1),
    ('en', 'ttwa-base', 'country_yt', 'Mayotte', 1);

UPDATE `_sql_version` SET `revision`=51, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -51-2024-10-04-11:00------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
    ('employeeContractEmployeePositionHide', 'employee/employeeContractTab --- employee_position',0 ,'Dolgozó kezelésnél a munkakör mező elrejtése a szerződések tabról', 'SZOF-4264', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('employeeContractEmployeePositionHide', 'employee/employeeContractTab', 'employee_position', 1, 1, 'SZOF-4264', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'country_afg', 'Afganisztán', 1),
    ('en', 'ttwa-base', 'country_afg', 'Afghanistan', 1),
    ('hu', 'ttwa-base', 'country_atg', 'Antigua és Barbuda', 1),
    ('en', 'ttwa-base', 'country_atg', 'Antigua and Barbuda', 1),
    ('hu', 'ttwa-base', 'country_ai', 'Anguilla', 1),
    ('en', 'ttwa-base', 'country_ai', 'Anguilla', 1),
    ('hu', 'ttwa-base', 'country_alg', 'Algéria', 1),
    ('en', 'ttwa-base', 'country_alg', 'Algeria', 1),
    ('hu', 'ttwa-base', 'country_nlant', 'Holland Antillák', 1),
    ('en', 'ttwa-base', 'country_nlant', 'Netherlands Antilles', 1),
    ('hu', 'ttwa-base', 'country_ago', 'Angola', 1),
    ('en', 'ttwa-base', 'country_ago', 'Angola', 1),
    ('hu', 'ttwa-base', 'country_aq', 'Antarktisz', 1),
    ('en', 'ttwa-base', 'country_aq', 'Antarctica', 1),
    ('hu', 'ttwa-base', 'country_eg', 'Egyiptomi', 1),
    ('en', 'ttwa-base', 'country_eg', 'Egypt', 1),
    ('hu', 'ttwa-base', 'country_arg', 'Argentina', 1),
    ('en', 'ttwa-base', 'country_arg', 'Argentina', 1),
    ('hu', 'ttwa-base', 'country_am', 'Örményország', 1),
    ('en', 'ttwa-base', 'country_am', 'Armenia', 1),
    ('hu', 'ttwa-base', 'country_aus', 'Ausztrália', 1),
    ('en', 'ttwa-base', 'country_aus', 'Australia', 1),
    ('hu', 'ttwa-base', 'country_abw', 'Aruba', 1),
    ('en', 'ttwa-base', 'country_abw', 'Aruba', 1),
    ('hu', 'ttwa-base', 'country_aze', 'Azerbajdzsán', 1),
    ('en', 'ttwa-base', 'country_aze', 'Azerbaijan', 1),
    ('hu', 'ttwa-base', 'country_bh', 'Bahrein', 1),
    ('en', 'ttwa-base', 'country_bh', 'Bahrain', 1),
    ('hu', 'ttwa-base', 'country_brb', 'Barbados', 1),
    ('en', 'ttwa-base', 'country_brb', 'Barbados', 1),
    ('hu', 'ttwa-base', 'country_bi', 'Burundi', 1),
    ('en', 'ttwa-base', 'country_bi', 'Burundi', 1),
    ('hu', 'ttwa-base', 'country_bj', 'Benin', 1),
    ('en', 'ttwa-base', 'country_bj', 'Benin', 1),
    ('hu', 'ttwa-base', 'country_bgd', 'Banglades', 1),
    ('en', 'ttwa-base', 'country_bgd', 'Banglades', 1),
    ('hu', 'ttwa-base', 'country_bhs', 'Bahama-szigetek', 1),
    ('en', 'ttwa-base', 'country_bhs', 'Bahamas', 1),
    ('hu', 'ttwa-base', 'country_bt', 'Bhután', 1),
    ('en', 'ttwa-base', 'country_bt', 'Bhutan', 1),
    ('hu', 'ttwa-base', 'country_bf', 'Burkina Faso', 1),
    ('en', 'ttwa-base', 'country_bf', 'Burkina Faso', 1),
    ('hu', 'ttwa-base', 'country_ber', 'Bermuda', 1),
    ('en', 'ttwa-base', 'country_ber', 'Bermuda', 1),
    ('hu', 'ttwa-base', 'country_bur', 'Burma', 1),
    ('en', 'ttwa-base', 'country_bur', 'Burma', 1),
    ('hu', 'ttwa-base', 'country_bo', 'Bolívia', 1),
    ('en', 'ttwa-base', 'country_bo', 'Bolivia', 1),
    ('hu', 'ttwa-base', 'country_bw', 'Botswana', 1),
    ('en', 'ttwa-base', 'country_bw', 'Botswana', 1),
    ('hu', 'ttwa-base', 'country_bra', 'Brazília', 1),
    ('en', 'ttwa-base', 'country_bra', 'Brazil', 1),
    ('hu', 'ttwa-base', 'country_bz', 'Belize', 1),
    ('en', 'ttwa-base', 'country_bz', 'Belize', 1),
    ('hu', 'ttwa-base', 'country_kh', 'Kambodzsa', 1),
    ('en', 'ttwa-base', 'country_kh', 'Cambodia', 1),
    ('hu', 'ttwa-base', 'country_can', 'Kanada', 1),
    ('en', 'ttwa-base', 'country_can', 'Canada', 1),
    ('hu', 'ttwa-base', 'country_cf', 'Közép-Afrikai Köztársaság', 1),
    ('en', 'ttwa-base', 'country_cf', 'Central African Republic', 1),
    ('hu', 'ttwa-base', 'country_aucc', 'Kókusz-Szigetek', 1),
    ('en', 'ttwa-base', 'country_aucc', 'Cocos Islands', 1),
    ('hu', 'ttwa-base', 'country_td', 'Csád', 1),
    ('en', 'ttwa-base', 'country_td', 'Chad', 1),
    ('hu', 'ttwa-base', 'country_chn', 'Kína', 1),
    ('en', 'ttwa-base', 'country_chn', 'China', 1),
    ('hu', 'ttwa-base', 'country_cl', 'Chile', 1),
    ('en', 'ttwa-base', 'country_cl', 'Chile', 1),
    ('hu', 'ttwa-base', 'country_cm', 'Kamerun', 1),
    ('en', 'ttwa-base', 'country_cm', 'Cameroon', 1),
    ('hu', 'ttwa-base', 'country_cog', 'Kongó', 1),
    ('en', 'ttwa-base', 'country_cog', 'Congo', 1),
    ('hu', 'ttwa-base', 'country_coi', 'Komoros', 1),
    ('en', 'ttwa-base', 'country_coi', 'Comoros', 1),
    ('hu', 'ttwa-base', 'country_co', 'Kolumbia', 1),
    ('en', 'ttwa-base', 'country_co', 'Columbia', 1),
    ('hu', 'ttwa-base', 'country_cr', 'Costa Rica', 1),
    ('en', 'ttwa-base', 'country_cr', 'Costa Rica', 1),
    ('hu', 'ttwa-base', 'country_cu', 'Kuba', 1),
    ('en', 'ttwa-base', 'country_cu', 'Cuba', 1),
    ('hu', 'ttwa-base', 'country_cvi', 'Zöld-Foki Köztársaság', 1),
    ('en', 'ttwa-base', 'country_cvi', 'Republic of Cape Verde', 1),
    ('hu', 'ttwa-base', 'country_auch', 'Karácsony-Sziget', 1),
    ('en', 'ttwa-base', 'country_auch', 'Christmas Island', 1),
    ('hu', 'ttwa-base', 'country_dj', 'Dzsibuti', 1),
    ('en', 'ttwa-base', 'country_dj', 'Djibouti', 1),
    ('hu', 'ttwa-base', 'country_do', 'Dominikai Köztársaság', 1),
    ('en', 'ttwa-base', 'country_do', 'Dominican Republic', 1),
    ('hu', 'ttwa-base', 'country_dubai', 'Dubaj', 1),
    ('en', 'ttwa-base', 'country_dubai', 'Dubai', 1),
    ('hu', 'ttwa-base', 'country_ec', 'Ecuador', 1),
    ('en', 'ttwa-base', 'country_ec', 'Ecuador', 1),
    ('hu', 'ttwa-base', 'country_gq', 'Egyenlítői Guinea', 1),
    ('en', 'ttwa-base', 'country_gq', 'Equatorial Guinea', 1),
    ('hu', 'ttwa-base', 'country_er', 'Eritrea', 1),
    ('en', 'ttwa-base', 'country_er', 'Eritrea', 1),
    ('hu', 'ttwa-base', 'country_et', 'Etiópia', 1),
    ('en', 'ttwa-base', 'country_et', 'Ethiopia', 1),
    ('hu', 'ttwa-base', 'country_frgy', 'Francia Guyana', 1),
    ('en', 'ttwa-base', 'country_frgy', 'French Guiana', 1),
    ('hu', 'ttwa-base', 'country_fj', 'Fidzsi-szigetek', 1),
    ('en', 'ttwa-base', 'country_fj', 'Fiji Islands', 1),
    ('hu', 'ttwa-base', 'country_gbfi', 'Falkland-Szigetek', 1),
    ('en', 'ttwa-base', 'country_gbfi', 'Falkland Islands', 1),
    ('hu', 'ttwa-base', 'country_fro', 'Faroe-szigetek', 1),
    ('en', 'ttwa-base', 'country_fro', 'Faroe Islands', 1),
    ('hu', 'ttwa-base', 'country_ga', 'Gabon', 1),
    ('en', 'ttwa-base', 'country_ga', 'Gabon', 1),
    ('hu', 'ttwa-base', 'country_gm', 'Gambia', 1),
    ('en', 'ttwa-base', 'country_gm', 'Gambia', 1),
    ('hu', 'ttwa-base', 'country_gw', 'Guinea Bissau', 1),
    ('en', 'ttwa-base', 'country_gw', 'Guinea Bissau', 1),
    ('hu', 'ttwa-base', 'country_ge', 'Grúzia', 1),
    ('en', 'ttwa-base', 'country_ge', 'Georgia', 1),
    ('hu', 'ttwa-base', 'country_gh', 'Ghána', 1),
    ('en', 'ttwa-base', 'country_gh', 'Ghana', 1),
    ('hu', 'ttwa-base', 'country_gbz', 'Gibraltár', 1),
    ('en', 'ttwa-base', 'country_gbz', 'Gibraltar', 1),
    ('hu', 'ttwa-base', 'country_gl', 'Grönland', 1),
    ('en', 'ttwa-base', 'country_gl', 'Greenland', 1),
    ('hu', 'ttwa-base', 'country_gd', 'Grenada', 1),
    ('en', 'ttwa-base', 'country_gd', 'Grenade', 1),
    ('hu', 'ttwa-base', 'country_gt', 'Guatemala', 1),
    ('en', 'ttwa-base', 'country_gt', 'Guatemala', 1),
    ('hu', 'ttwa-base', 'country_gn', 'Guinea', 1),
    ('en', 'ttwa-base', 'country_gn', 'Guinea', 1),
    ('hu', 'ttwa-base', 'country_gy', 'Guyana', 1),
    ('en', 'ttwa-base', 'country_gy', 'Guyana', 1),
    ('hu', 'ttwa-base', 'country_ht', 'Haiti', 1),
    ('en', 'ttwa-base', 'country_ht', 'Haiti', 1),
    ('hu', 'ttwa-base', 'country_hk', 'Hong Kong', 1),
    ('en', 'ttwa-base', 'country_hk', 'Hong Kong', 1),
    ('hu', 'ttwa-base', 'country_heart', 'Heart-Sziget', 1),
    ('en', 'ttwa-base', 'country_heart', 'Heart Island', 1),
    ('hu', 'ttwa-base', 'country_hn', 'Honduras', 1),
    ('en', 'ttwa-base', 'country_hn', 'Honduras', 1),
    ('hu', 'ttwa-base', 'country_ci', 'Elefántcsontpart', 1),
    ('en', 'ttwa-base', 'country_ci', 'Ivory Coast', 1),
    ('hu', 'ttwa-base', 'country_in', 'India', 1),
    ('en', 'ttwa-base', 'country_in', 'India', 1),
    ('hu', 'ttwa-base', 'country_id', 'Indonézia', 1),
    ('en', 'ttwa-base', 'country_id', 'Indonesia', 1),
    ('hu', 'ttwa-base', 'country_ir', 'Irán', 1),
    ('en', 'ttwa-base', 'country_ir', 'Iran', 1),
    ('hu', 'ttwa-base', 'country_iq', 'Irak', 1),
    ('en', 'ttwa-base', 'country_iq', 'Iraq', 1),
    ('hu', 'ttwa-base', 'country_il', 'Izrael', 1),
    ('en', 'ttwa-base', 'country_il', 'Israel', 1),
    ('hu', 'ttwa-base', 'country_jm', 'Jamaica', 1),
    ('en', 'ttwa-base', 'country_jm', 'Jamaica', 1),
    ('hu', 'ttwa-base', 'country_jor', 'Jordánia', 1),
    ('en', 'ttwa-base', 'country_jor', 'Jordan', 1),
    ('hu', 'ttwa-base', 'country_jp', 'Japán', 1),
    ('en', 'ttwa-base', 'country_jp', 'Japan', 1),
    ('hu', 'ttwa-base', 'country_kz', 'Kazahsztán', 1),
    ('en', 'ttwa-base', 'country_kz', 'Kazakhstan', 1),
    ('hu', 'ttwa-base', 'country_ke', 'Kenya', 1),
    ('en', 'ttwa-base', 'country_ke', 'Kenya', 1),
    ('hu', 'ttwa-base', 'country_kr', 'Dél-Korea', 1),
    ('en', 'ttwa-base', 'country_kr', 'South Korea', 1),
    ('hu', 'ttwa-base', 'country_nk', 'Észak-Korea', 1),
    ('en', 'ttwa-base', 'country_nk', 'North Korea', 1),
    ('hu', 'ttwa-base', 'country_kw', 'Kuvait', 1),
    ('en', 'ttwa-base', 'country_kw', 'Kuwait', 1),
    ('hu', 'ttwa-base', 'country_ky', 'Kajmán-Szigetek', 1),
    ('en', 'ttwa-base', 'country_ky', 'Cayman Islands', 1),
    ('hu', 'ttwa-base', 'country_kg', 'Kirgízia', 1),
    ('en', 'ttwa-base', 'country_kg', 'Kirghizia', 1),
    ('hu', 'ttwa-base', 'country_la', 'Laosz', 1),
    ('en', 'ttwa-base', 'country_la', 'Laos', 1),
    ('hu', 'ttwa-base', 'country_lr', 'Libéria', 1),
    ('en', 'ttwa-base', 'country_lr', 'Liberia', 1),
    ('hu', 'ttwa-base', 'country_ly', 'Líbia', 1),
    ('en', 'ttwa-base', 'country_ly', 'Libya', 1),
    ('hu', 'ttwa-base', 'country_lc', 'Saint Lucia', 1),
    ('en', 'ttwa-base', 'country_lc', 'Saint Lucia', 1),
    ('hu', 'ttwa-base', 'country_lb', 'Libanon', 1),
    ('en', 'ttwa-base', 'country_lb', 'Lebanon', 1),
    ('hu', 'ttwa-base', 'country_ls', 'Lesotho', 1),
    ('en', 'ttwa-base', 'country_ls', 'Lesotho', 1),
    ('hu', 'ttwa-base', 'country_lk', 'Sri Lanka', 1),
    ('en', 'ttwa-base', 'country_lk', 'Sri Lanka', 1),
    ('hu', 'ttwa-base', 'country_mg', 'Madagaszkár', 1),
    ('en', 'ttwa-base', 'country_mg', 'Madagascar', 1),
    ('hu', 'ttwa-base', 'country_mr', 'Mauritánia', 1),
    ('en', 'ttwa-base', 'country_mr', 'Mauritania', 1),
    ('hu', 'ttwa-base', 'country_mv', 'Maldiv-szigetek', 1),
    ('en', 'ttwa-base', 'country_mv', 'Maldives', 1),
    ('hu', 'ttwa-base', 'country_mx', 'Mexikó', 1),
    ('en', 'ttwa-base', 'country_mx', 'Mexico', 1),
    ('hu', 'ttwa-base', 'country_ml', 'Mali', 1),
    ('en', 'ttwa-base', 'country_ml', 'Mali', 1),
    ('hu', 'ttwa-base', 'country_mys', 'Malaysia', 1),
    ('en', 'ttwa-base', 'country_mys', 'Malaysia', 1),
    ('hu', 'ttwa-base', 'country_mw', 'Malawi', 1),
    ('en', 'ttwa-base', 'country_mw', 'Malawi', 1),
    ('hu', 'ttwa-base', 'country_mn', 'Mongólia', 1),
    ('en', 'ttwa-base', 'country_mn', 'Mongolia', 1),
    ('hu', 'ttwa-base', 'country_mo', 'Makaó', 1),
    ('en', 'ttwa-base', 'country_mo', 'Macau', 1),
    ('hu', 'ttwa-base', 'country_ma', 'Marokkó', 1),
    ('en', 'ttwa-base', 'country_ma', 'Morocco', 1),
    ('hu', 'ttwa-base', 'country_mz', 'Mozambik', 1),
    ('en', 'ttwa-base', 'country_mz', 'Mozambique', 1),
    ('hu', 'ttwa-base', 'country_mu', 'Mauritius', 1),
    ('en', 'ttwa-base', 'country_mu', 'Mauritius', 1),
    ('hu', 'ttwa-base', 'country_mm', 'Myanmar', 1),
    ('en', 'ttwa-base', 'country_mm', 'Myanmar', 1),
    ('hu', 'ttwa-base', 'country_nh', 'Új-Kaledónia', 1),
    ('en', 'ttwa-base', 'country_nh', 'New Caledonia', 1),
    ('hu', 'ttwa-base', 'country_np', 'Nepál', 1),
    ('en', 'ttwa-base', 'country_np', 'Nepal', 1),
    ('hu', 'ttwa-base', 'country_nl', 'Hollandia', 1),
    ('en', 'ttwa-base', 'country_nl', 'Netherlands', 1),
    ('hu', 'ttwa-base', 'country_nf', 'Norfolk-Sziget', 1),
    ('en', 'ttwa-base', 'country_nf', 'Norfolk Island', 1),
    ('hu', 'ttwa-base', 'country_ng', 'Niger', 1),
    ('en', 'ttwa-base', 'country_ng', 'Niger', 1),
    ('hu', 'ttwa-base', 'country_ni', 'Nicaragua', 1),
    ('en', 'ttwa-base', 'country_ni', 'Nicaragua', 1),
    ('hu', 'ttwa-base', 'country_nig', 'Nigéria', 1),
    ('en', 'ttwa-base', 'country_nig', 'Nigeria', 1),
    ('hu', 'ttwa-base', 'country_ot', 'Egyéb ország', 1),
    ('en', 'ttwa-base', 'country_ot', 'Other country', 1),
    ('hu', 'ttwa-base', 'country_nz', 'Új-Zéland', 1),
    ('en', 'ttwa-base', 'country_nz', 'New Zealand', 1),
    ('hu', 'ttwa-base', 'country_om', 'Oman', 1),
    ('en', 'ttwa-base', 'country_om', 'Oman', 1),
    ('hu', 'ttwa-base', 'country_pk', 'Pakisztán', 1),
    ('en', 'ttwa-base', 'country_pk', 'Pakistan', 1),
    ('hu', 'ttwa-base', 'country_pa', 'Panama', 1),
    ('en', 'ttwa-base', 'country_pa', 'Panama', 1),
    ('hu', 'ttwa-base', 'country_py', 'Paraguay', 1),
    ('en', 'ttwa-base', 'country_py', 'Paraguay', 1),
    ('hu', 'ttwa-base', 'country_pe', 'Peru', 1),
    ('en', 'ttwa-base', 'country_pe', 'Peru', 1),
    ('hu', 'ttwa-base', 'country_pf', 'Francia polinézia', 1),
    ('en', 'ttwa-base', 'country_pf', 'French Polynesia', 1),
    ('hu', 'ttwa-base', 'country_ph', 'Fülöp-szigetek', 1),
    ('en', 'ttwa-base', 'country_ph', 'Philippines', 1),
    ('hu', 'ttwa-base', 'country_pg', 'Pápua Új-Guinea', 1),
    ('en', 'ttwa-base', 'country_pg', 'Papua New Guinea', 1),
    ('hu', 'ttwa-base', 'country_pr', 'Puerto Rico', 1),
    ('en', 'ttwa-base', 'country_pr', 'Puerto Rico', 1),
    ('hu', 'ttwa-base', 'country_qa', 'Katar', 1),
    ('en', 'ttwa-base', 'country_qa', 'Qatar', 1),
    ('hu', 'ttwa-base', 'country_za', 'Dél-Afrikai Köztársaság', 1),
    ('en', 'ttwa-base', 'country_za', 'Republic of South Africa', 1),
    ('hu', 'ttwa-base', 'country_rw', 'Ruanda', 1),
    ('en', 'ttwa-base', 'country_rw', 'Rwanda', 1),
    ('hu', 'ttwa-base', 'country_sv', 'Salvador', 1),
    ('en', 'ttwa-base', 'country_sv', 'Salvador', 1),
    ('hu', 'ttwa-base', 'country_sa', 'Szaud-Arábia', 1),
    ('en', 'ttwa-base', 'country_sa', 'Saudi Arabia', 1),
    ('hu', 'ttwa-base', 'country_sb', 'Salamon-Szigetek', 1),
    ('en', 'ttwa-base', 'country_sb', 'Solomon Islands', 1),
    ('hu', 'ttwa-base', 'country_sn', 'Szenegál', 1),
    ('en', 'ttwa-base', 'country_sn', 'Senegal', 1),
    ('hu', 'ttwa-base', 'country_sc', 'Seychelle-szigetek', 1),
    ('en', 'ttwa-base', 'country_sc', 'Seychelles Islands', 1),
    ('hu', 'ttwa-base', 'country_sg', 'Szingapúr', 1),
    ('en', 'ttwa-base', 'country_sg', 'Singapore', 1),
    ('hu', 'ttwa-base', 'country_sl', 'Sierra Leone', 1),
    ('en', 'ttwa-base', 'country_sl', 'Sierra Leone', 1),
    ('hu', 'ttwa-base', 'country_sm', 'San Marino', 1),
    ('en', 'ttwa-base', 'country_sm', 'San Marino', 1),
    ('hu', 'ttwa-base', 'country_so', 'Szomália', 1),
    ('en', 'ttwa-base', 'country_so', 'Somalia', 1),
    ('hu', 'ttwa-base', 'country_vn', 'Vietnam', 1),
    ('en', 'ttwa-base', 'country_vn', 'Vietnam', 1),
    ('hu', 'ttwa-base', 'country_st', 'Sao Tomé', 1),
    ('en', 'ttwa-base', 'country_st', 'Sao Tome', 1),
    ('hu', 'ttwa-base', 'country_sd', 'Szudan', 1),
    ('en', 'ttwa-base', 'country_sd', 'Szudan', 1),
    ('hu', 'ttwa-base', 'country_sr', 'Suriname', 1),
    ('en', 'ttwa-base', 'country_sr', 'Suriname', 1),
    ('hu', 'ttwa-base', 'country_sy', 'Szíria', 1),
    ('en', 'ttwa-base', 'country_sy', 'Syria', 1),
    ('hu', 'ttwa-base', 'country_tz', 'Tanzánia', 1),
    ('en', 'ttwa-base', 'country_tz', 'Tanzania', 1),
    ('hu', 'ttwa-base', 'country_th', 'Thaiföld', 1),
    ('en', 'ttwa-base', 'country_th', 'Thailand', 1),
    ('hu', 'ttwa-base', 'country_tj', 'Tadzsikisztán', 1),
    ('en', 'ttwa-base', 'country_tj', 'Tajikistan', 1),
    ('hu', 'ttwa-base', 'country_tm', 'Türkmenisztán', 1),
    ('en', 'ttwa-base', 'country_tm', 'Turkmenistan', 1),
    ('hu', 'ttwa-base', 'country_tg', 'Togo', 1),
    ('en', 'ttwa-base', 'country_tg', 'Togo', 1),
    ('hu', 'ttwa-base', 'country_to', 'Tonga', 1),
    ('en', 'ttwa-base', 'country_to', 'Tonga', 1),
    ('hu', 'ttwa-base', 'country_tt', 'Trinidad és Tobago', 1),
    ('en', 'ttwa-base', 'country_tt', 'Trinidad and Tobago', 1),
    ('hu', 'ttwa-base', 'country_tn', 'Tunézia', 1),
    ('en', 'ttwa-base', 'country_tn', 'Tunisia', 1),
    ('hu', 'ttwa-base', 'country_tr', 'Törökország', 1),
    ('en', 'ttwa-base', 'country_tr', 'Turkey', 1),
    ('hu', 'ttwa-base', 'country_tv', 'Tuvalu', 1),
    ('en', 'ttwa-base', 'country_tv', 'Tuvalu', 1),
    ('hu', 'ttwa-base', 'country_tw', 'Tajvan', 1),
    ('en', 'ttwa-base', 'country_tw', 'Taiwan', 1),
    ('hu', 'ttwa-base', 'country_ae', 'Egyesült Arab Emirátus', 1),
    ('en', 'ttwa-base', 'country_ae', 'United Arab Emirates', 1),
    ('hu', 'ttwa-base', 'country_ug', 'Uganda', 1),
    ('en', 'ttwa-base', 'country_ug', 'Uganda', 1),
    ('hu', 'ttwa-base', 'country_xk', 'Koszovó', 1),
    ('en', 'ttwa-base', 'country_xk', 'Kosovo', 1),
    ('hu', 'ttwa-base', 'country_uy', 'Uruguay', 1),
    ('en', 'ttwa-base', 'country_uy', 'Uruguay', 1),
    ('hu', 'ttwa-base', 'country_us', 'Amerikai Egyesült Államok', 1),
    ('en', 'ttwa-base', 'country_us', 'United States of America', 1),
    ('hu', 'ttwa-base', 'country_uz', 'Üzbegisztán', 1),
    ('en', 'ttwa-base', 'country_uz', 'Uzbekistan', 1),
    ('hu', 'ttwa-base', 'country_svin', 'Saint Vincent', 1),
    ('en', 'ttwa-base', 'country_svin', 'Saint Vincent', 1),
    ('hu', 'ttwa-base', 'country_ve', 'Venezuela', 1),
    ('en', 'ttwa-base', 'country_ve', 'Venezuela', 1),
    ('hu', 'ttwa-base', 'country_bvi', 'Brit Virgin szigetek', 1),
    ('en', 'ttwa-base', 'country_bvi', 'British Virgin Islands', 1),
    ('hu', 'ttwa-base', 'country_wf', 'Nyugat-Afrika', 1),
    ('en', 'ttwa-base', 'country_wf', 'West Africa', 1),
    ('hu', 'ttwa-base', 'country_ws', 'Nyugat-Szamoa', 1),
    ('en', 'ttwa-base', 'country_so', 'Western Samoa', 1),
    ('hu', 'ttwa-base', 'country_ye', 'Jemen', 1),
    ('en', 'ttwa-base', 'country_ye', 'Yemen', 1),
    ('hu', 'ttwa-base', 'country_zm', 'Zambia', 1),
    ('en', 'ttwa-base', 'country_zm', 'Zambia', 1),
    ('hu', 'ttwa-base', 'country_zw', 'Zimbabwe', 1),
    ('en', 'ttwa-base', 'country_zw', 'Zimbabwe', 1),
    ('hu', 'ttwa-base', 'country_zr', 'Zaire', 1),
    ('en', 'ttwa-base', 'country_zr', 'Zaire', 1);

UPDATE `_sql_version` SET `revision`=52, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -52-2024-10-07-13:30------------------------------------------------

INSERT IGNORE INTO `placeholder` (`placeholder`, `model`, `field_name`, `status`, `created_by`, `created_on`) VALUES
	('hour_salary', 'EmployeeSalary', 'personal_hour_salary', '2', 'SZOF-4240', NOW());

UPDATE `_sql_version` SET `revision`=53, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -53-2024-10-09-10:30------------------------------------------------

INSERT INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
('automaticLoadOfExtPositionData', '0', 'string', 'A munkakörhöz kapcsolt adatok automatikus betöltése a további adatok oldalon.' ,'setting_automaticLoadOfExtPositionData', '1'),
('automaticLoadOfExtEngOrgName', '0', 'string', 'Szervezeti egység angol nevének automatikus betöltése a további adatok oldalon.' ,'setting_automaticLoadOfExtEngOrgName', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-base', 'setting_automaticLoadOfExtPositionData', 'A munkakörhöz kapcsolt adatok automatikus betöltése a további adatok oldalon.', 1),
('en', 'ttwa-base', 'setting_automaticLoadOfExtPositionData', 'Automatic loading of postion related data on the additional data page.', 1),
('hu', 'ttwa-base', 'setting_automaticLoadOfExtEngOrgName', 'Szervezeti egység angol nevének automatikus betöltése.', 1),
('en', 'ttwa-base', 'setting_automaticLoadOfExtEngOrgName', 'Automatic loading of the English name of the organization on the additional data page.', 1);

UPDATE `_sql_version` SET `revision`=54, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -54-2024-10-09-11:30------------------------------------------------

INSERT INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
('automaticLoadOfCostCode', '0', 'string', 'A költséghey kód automatikus betöltése a költséghely/viselő oldalon.' ,'setting_automaticLoadOfCostCode', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-base', 'setting_automaticLoadOfCostCode', 'A költséghey kód automatikus betöltése a költséghely/viselő oldalon.', 1),
('en', 'ttwa-base', 'setting_automaticLoadOfCostCode', 'Automatic loading of cost code on the cost page.', 1);

UPDATE `_sql_version` SET `revision`=55, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -55-2024-10-09-16:30------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Rögzített dátum' WHERE `dict_id` = 'hire_date' AND `module` = 'ttwa-wfm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Megszűnés dátuma' WHERE `dict_id` = 'termination_date' AND `module` = 'ttwa-wfm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Dolgozó típusa' WHERE `dict_id` = 'employee_type' AND `module` = 'ttwa-wfm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Munkaterület' WHERE `dict_id` = 'work_area' AND `module` = 'ttwa-wfm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Költségviselő' WHERE `dict_id` = 'cost_center' AND `module` = 'ttwa-wfm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Elsődleges szervezet' WHERE `dict_id` = 'primary_organization' AND `module` = 'ttwa-wfm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Menedzser azonosító {number}' WHERE `dict_id` = 'manager_id' AND `module` = 'ttwa-wfm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Menedzser {number} név' WHERE `dict_id` = 'manager_name' AND `module` = 'ttwa-wfm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Hét {number}' WHERE `dict_id` = 'week_with_number' AND `module` = 'ttwa-wfm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Hibák' WHERE `dict_id` = 'errors' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Days in past' WHERE `dict_id` = 'minus_days' AND `module` IN ('ttwa-wfm', 'ttwa-ahp') AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Days in future' WHERE `dict_id` = 'plus_days' AND `module` IN ('ttwa-wfm', 'ttwa-ahp') AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Set individual work schedule view' WHERE `dict_id` = 'page_title_work_schedule_by_group_max_days' AND `module` = 'ttwa-wfm' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Set individual work schedule view' WHERE `dict_id` = 'menu_item_work_schedule_by_group_max_days' AND `module` = 'ttwa-wfm' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Available hours' WHERE `dict_id` = 'ordered_hour' AND `module` = 'ttwa-wfm' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Annual absence list' WHERE `dict_id` = 'menu_item_annual_leave_list' AND `module` = 'ttwa-ahp' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Annual absence list' WHERE `dict_id` = 'page_title_annual_leave_list' AND `module` = 'ttwa-ahp' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Absence of workers in {month} {year}' WHERE `dict_id` = 'page_title_ahp_month' AND `module` = 'ttwa-ahp' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Absence permission' WHERE `dict_id` = 'menu_item_absence_release' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Absence permission' WHERE `dict_id` = 'page_title_absence_release' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Absence permission' WHERE `dict_id` = 'menu_item_extended_absence_expenditure' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Absence permission' WHERE `dict_id` = 'page_title_extended_absence_expenditure' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Leave and base absence types' WHERE `dict_id` = 'page_title_linkAtToBat' AND `module` = 'ttwa-ahp' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Leave and base absence types' WHERE `dict_id` = 'menu_item_link_at_to_bat' AND `module` = 'ttwa-ahp' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Public holidays' WHERE `dict_id` = 'page_title_public_holiday' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Menu handling for role group' WHERE `dict_id` = 'page_title_menu_role_in_group' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Menu handling for role group' WHERE `dict_id` = 'menu_item_menu_role_in_group' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost center management' WHERE `dict_id` = 'page_title_cost' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost center data' WHERE `dict_id` = 'menu_item_cost' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'User role upload' WHERE `dict_id` = 'page_title_approver_upload' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'User role upload' WHERE `dict_id` = 'menu_item_approver_upload' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost bearer data' WHERE `dict_id` = 'menu_item_cost_center' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost bearer management' WHERE `dict_id` = 'page_title_cost_center' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost bearer name' WHERE `dict_id` = 'cost_center_name' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost center uploader' WHERE `dict_id` = 'menu_item_cost_upload' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost center uploader' WHERE `dict_id` = 'page_title_cost_upload' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost bearer uploader' WHERE `dict_id` = 'menu_item_cost_center_upload' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost bearer uploader' WHERE `dict_id` = 'page_title_cost_center_upload' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cost bearer name' WHERE `dict_id` = 'cost_center_name' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Upload workschedule by unit' WHERE `dict_id` = 'menu_item_work_schedule_by_unit' AND `module` = 'ttwa-wfm' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Upload workschedule by unit' WHERE `dict_id` = 'page_title_work_schedule_by_unit' AND `module` = 'ttwa-wfm' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Monthly absences of workers' WHERE `dict_id` = 'page_title_work_schedule_by_unit' AND `module` = 'ttwa-ahp' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=56, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -56-2024-10-09-17:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('workedTimeReportStateTypesIgnore', '', 'string', 'WorkedTimeReport kimutatásban kivételre teszi az itt felsorolt state_type_id -kat', 'workedTimeReportStateTypesIgnore', 1, 'SZOF-4279', NOW());
INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'workedTimeReportStateTypesIgnore', 'WorkedTimeReport kimutatásban kivételre teszi az itt felsorolt state_type_id -kat', '1'),
    ('en', 'ttwa-base', 'workedTimeReportStateTypesIgnore', 'WorkedTimeReport makes an exception in the statement listed here state_type_id', '1');

UPDATE `_sql_version` SET `revision`=57, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -57-2024-10-21-17:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
    ('processAcknowledgementActivitiesEmail', '[]', 'string', 'default: [], Dolgozói tudomásulvétel, jóváhagyott tevékenységekről küldjön e-mail a felsorolt tevékenység azonosítók szerint (ProcessAcknowledgmentEnum)' ,'processAcknowledgementActivitiesEmail', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'process_acknowledgment_new_rec_subject', 'Tudomásul vételre váró tételek az Ease++ rendszerben', '1'),
    ('en', 'ttwa-base', 'process_acknowledgment_new_rec_subject', 'Items pending in the Ease++ system', '1'),
    ('hu', 'ttwa-base', 'processAcknowledgementActivitiesEmail', ' Dolgozói tudomásulvétel, jóváhagyott tevékenységekről küldjön e-mail a felsorolt tevékenység azonosítók szerint (ProcessAcknowledgmentEnum)', '1'),
    ('en', 'ttwa-base', 'processAcknowledgementActivitiesEmail', 'Employee acknowledgment, send an e-mail about approved activities according to the listed activity identifiers (ProcessAcknowledgmentEnum)', '1');

UPDATE `_sql_version` SET `revision`=58, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -58-2024-10-24-10:25------------------------------------------------

INSERT INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
('allowSaveEmployeeDocsWithoutFile', '0', 'string', 'Dokumentum rögzíthető fájl feltöltés nélkül.' ,'setting_saveEmployeeDocsWithoutFile', '1'),
('allowSaveLabourDocsWithoutFile', '0', 'string', 'Munkaügyi dokumentum rögzíthető fájl feltöltés nélkül.' ,'setting_saveLabourDocsWithoutFile', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-base', 'setting_saveEmployeeDocsWithoutFile', 'Dokumentum rögzíthető fájl feltöltés nélkül.', 1),
('en', 'ttwa-base', 'setting_saveEmployeeDocsWithoutFile', 'Document can be saved without uploading a file.', 1),
('hu', 'ttwa-base', 'setting_saveLabourDocsWithoutFile', 'Munkaügyi dokumentum rögzíthető fájl feltöltés nélkül.', 1),
('en', 'ttwa-base', 'setting_saveLabourDocsWithoutFile', 'Labour document can be saved without uploading a file.', 1);

UPDATE `_sql_version` SET `revision`=59, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -59-2024-10-28-20:30------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
    ('documentManagement', 'documentManagement --- view/add/delete', 'A Dokumentumkezelés felülethez ad jogot ez a szerepkörcsoport-elem.', 'SZOF-4145', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('documentManagement', 'documentManagement', 'view', 1, 1, 'SZOF-4145', NOW()),
	('documentManagement', 'documentManagement', 'add', 1, 1, 'SZOF-4145', NOW()),
	('documentManagement', 'documentManagement', 'delete', 1, 1, 'SZOF-4145', NOW());

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,`menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`) VALUES
	('documentManagement', 'menu_item_document_management', 'ttwa-base', 'menu_item_document_management', 'sub', '/documentManagement/index', 'documentManagement',
	'view', '162', 20);

INSERT IGNORE INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `status`, `created_by`, `created_on`) VALUES
	('documentManagement', 'date', 'EMPLOYEE_WITH_DATE', 'combo', 2, 'SZOF-4145', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'menu_item_document_management', 'Dokumentumkezelés', '1'),
    ('en', 'ttwa-base', 'menu_item_document_management', 'Document management', '1'),
    ('hu', 'ttwa-base', 'page_title_document_management', 'Dokumentumkezelés', '1'),
    ('en', 'ttwa-base', 'page_title_document_management', 'Document management', '1'),
	('hu', 'ttwa-base', 'documentName', 'Dokumentum egyedi neve', '1'),
    ('en', 'ttwa-base', 'documentName', 'Unique name of document', '1');

UPDATE `_sql_version` SET `revision`=60, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -60-2024-09-30-08:45------------------------------------------------

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`,	`login_need`, `created_by`, `created_on`) VALUES
    ('documentViewer', 'documentViewer', 'view', '1', '1', 'SZOF-4145', NOW());

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
    ('documentViewer', 'documentViewer --- view', 'Feltöltött dokumentumok megtekintése', 'SZOF-4145', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'menu_item_document_viewer', 'Dokumentumok', 1),
	('en', 'ttwa-base', 'menu_item_document_viewer', 'Documents', 1),
    ('hu', 'ttwa-base', 'page_title_document_viewer', 'Dokumentumok', 1),
	('en', 'ttwa-base', 'page_title_document_viewer', 'Documents', 1);   

UPDATE `_sql_version` SET `revision`=61, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -61-2024-10-01-10:45------------------------------------------------

ALTER TABLE `employee_salary`
	CHANGE COLUMN `shift` `shift` TINYINT UNSIGNED NULL COMMENT 'Műszakos' AFTER `personal_hour_salary`;

	UPDATE `_sql_version` SET `revision`=62, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -62-2024-10-30-10:45------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('pl', 'ttwa-base', 'locked', 'Zablokowany', '1'),
    ('pl', 'ttwa-base', 'cardnum', 'Numer karty', '1'),
    ('pl', 'ttwa-base', 'employee_position_id', 'Zakres pracy', '1'),
    ('pl', 'ttwa-base', 'ec_note', 'Notatka', '1'),
    ('pl', 'ttwa-base', 'menu_item_company', 'Lokalizacja', '1'),
    ('pl', 'ttwa-base', 'page_title_company', 'Lokalizacja', '1'),
    ('pl', 'ttwa-base', 'company_id_name', 'Identyfikator lokalizacji', '1'),
    ('pl', 'ttwa-base', 'company_name', 'Nazwa lokalizacji', '1'),
    ('pl', 'ttwa-base', 'registration_number', 'Numer rejestracyjny', '1'),
    ('pl', 'ttwa-base', 'vat_number', 'Numer VAT', '1'),
    ('pl', 'ttwa-base', 'menu_item_company_upload', 'Wgrywający lokalizację', '1'),
    ('pl', 'ttwa-base', 'page_title_company_upload', 'Wgrywający lokalizację', '1'),
    ('pl', 'ttwa-base', 'password_change', 'Zmiana hasła', '1'),
    ('pl', 'ttwa-base', 'employee_id', 'Unikalny identyfikator pracownika', '1'),
    ('pl', 'ttwa-base', 'lang', 'JĘzyk', '1'),
    ('pl', 'ttwa-base', 'user_id', 'Identyfikator użytkownika', '1'),
    ('pl', 'ttwa-base', 'add', 'Dodatek', '1'),
    ('pl', 'ttwa-base', 'modify', 'Redagowanie', '1'),
    ('pl', 'ttwa-base', 'delete', 'Usunięcie', '1'),
    ('pl', 'ttwa-base', 'disableLdapAuth', 'Czy chcesz wyłączyć uwierzytelnianie LDAP?', '1'),
    ('pl', 'ttwa-base', 'ask_password_change', 'Żądanie zmiany hasła', '1'),
    ('pl', 'ttwa-base', 'unblock_pin', 'Odblokuj PIN', '1'),
    ('pl', 'ttwa-base', 'export', 'Eksport', '1'),
    ('pl', 'ttwa-base', 'menu_item_user_upload', 'Wgrywanie użytkownika', '1'),
    ('pl', 'ttwa-base', 'page_title_user_upload', 'Wgrywanie użytkownika', '1'),
    ('pl', 'ttwa-base', 'lang_en', 'Angielski', '1'),
    ('pl', 'ttwa-base', 'lang_hu', 'Węgierski', '1'),
    ('pl', 'ttwa-base', 'lang_wtt_settings_en', 'Angielski', '1'),
    ('pl', 'ttwa-base', 'lang_wtt_settings_hu', 'Węgierski', '1'),
    ('pl', 'ttwa-base', 'january', 'Styczeń', '1'),
    ('pl', 'ttwa-base', 'february', 'Luty', '1'),
    ('pl', 'ttwa-base', 'march', 'Marzec', '1'),
    ('pl', 'ttwa-base', 'april', 'Kwiecień', '1'),
    ('pl', 'ttwa-base', 'may', 'Maj', '1'),
    ('pl', 'ttwa-base', 'june', 'Czerwiec', '1'),
    ('pl', 'ttwa-base', 'july', 'Lipiec', '1'),
    ('pl', 'ttwa-base', 'august', 'Sierpień', '1'),
    ('pl', 'ttwa-base', 'september', 'Wrzesień', '1'),
    ('pl', 'ttwa-base', 'october', 'Październik', '1'),
    ('pl', 'ttwa-base', 'november', 'Listopad', '1'),
    ('pl', 'ttwa-base', 'december', 'Grudzień', '1'),
    ('pl', 'ttwa-base', 'monday', 'Poniedziałek', '1'),
    ('pl', 'ttwa-base', 'tuesday', 'Wtorek', '1'),
    ('pl', 'ttwa-base', 'wednesday', 'Środa', '1'),
    ('pl', 'ttwa-base', 'thursday', 'Czwartek', '1'),
    ('pl', 'ttwa-base', 'friday', 'Piątek', '1');

UPDATE `_sql_version` SET `revision`=63, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -63-2024-10-31-11:00------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'employee_docs_note', 'Megjegyzés', 1),
    ('en', 'ttwa-base', 'employee_docs_note', 'Note', 1);

UPDATE `_sql_version` SET `revision`=64, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -64-2024-10-30-09:15------------------------------------------------

INSERT INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
   ('showDownloadButtonOnEmployeeDocsTab', '0', 'string', 'Letöltés-gomb megjelenítése a dokumentumok/munkaügyi dokumentumok füleken.' ,'setting_showDownloadButtonOnEmployeeDocsTab', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'setting_showDownloadButtonOnEmployeeDocsTab', 'Letöltés-gomb megjelenítése a dokumentumok/munkaügyi dokumentumok füleken.', 1),
    ('en', 'ttwa-base', 'setting_showDownloadButtonOnEmployeeDocsTab', 'Show download button on employee docs tab.', 1);

UPDATE `_sql_version` SET `revision`=65, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -65-2024-10-31-09:30------------------------------------------------

CREATE TABLE IF NOT EXISTS `data_translation` (
	`row_id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Sor azonosító',
	`entity_type` VARCHAR(128) NOT NULL COMMENT 'Entitás típusa',
	`entity_id` VARCHAR(64) NOT NULL COMMENT 'Entitás azonosító',
	`lang` VARCHAR(8) NOT NULL COMMENT 'Nyelv',
	`transl_value` VARCHAR(512) NOT NULL COMMENT 'Fordítás',
	`valid_from` DATE NOT NULL COMMENT 'Érvényesség kezdete',
	`valid_to` DATE NULL DEFAULT NULL COMMENT 'Érvényesség vége',
	`status` TINYINT(3) UNSIGNED NOT NULL COMMENT 'Állapot',
	`created_by` VARCHAR(32) NOT NULL COMMENT 'Készítette',
	`created_on` DATETIME NOT NULL COMMENT 'Készült',
	`modified_by` VARCHAR(32) NULL DEFAULT NULL COMMENT 'Módosította',
	`modified_on` DATETIME NULL DEFAULT NULL COMMENT 'Módosítva',
	PRIMARY KEY (`row_id`),
	INDEX `entity_type` (`entity_type`, `entity_id`, `lang`, `valid_from`, `valid_to`, `status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Adatmezők fordítása';

UPDATE `_sql_version` SET `revision`=66, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -66-2024-11-05-09:00------------------------------------------------

ALTER TABLE `company` ADD COLUMN `identifier` TEXT DEFAULT NULL COLLATE utf8_unicode_ci AFTER `company_id`;
ALTER TABLE `unit` ADD COLUMN `identifier` TEXT DEFAULT NULL COLLATE utf8_unicode_ci AFTER `payroll_id`;
-- ALTER TABLE `job_tasks` ADD COLUMN `identifier` TEXT DEFAULT NULL COLLATE utf8_unicode_ci AFTER `job_task_id`;

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
    ('company', 'identifier', NULL, 'ALL', '5', NULL, 'KSK', NOW()),
    ('unit', 'identifier', NULL, 'ALL', '5', NULL, 'KSK', NOW()),
    ('job_tasks', 'identifier', NULL, 'ALL', '5', NULL, 'KSK', NOW());

UPDATE `_sql_version` SET `revision`=67, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -67-2024-11-08-15:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
    ('summarySheet_lockInsideTypeExeption', '0', 'string', 'Összesítő lapon bekapcsolható mely jogcímek jelenjenek meg egy nap megnyitásakor.' ,'setting_summarySheet_lockInsideTypeExeption', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'setting_summarySheet_lockInsideTypeExeption', 'Összesítő lapon bekapcsolható mely jogcímek jelenjenek meg egy nap megnyitásakor.', 1),
    ('en', 'ttwa-base', 'setting_summarySheet_lockInsideTypeExeption', 'On summary page, you can turn on which legal titles should be displayed when opening a day.', 1);

UPDATE `_sql_version` SET `revision`=68, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -68-2024-11-11-11:30------------------------------------------------

UPDATE
    `app_settings`
SET 
    `setting_id` = 'allowSaveLabourDocsWithoutFileAndNote',
    `dict_id` = 'setting_saveLabourDocsWithoutFileAndNote'
WHERE `setting_id` = 'allowSaveLabourDocsWithoutFile';

UPDATE
    `dictionary`
SET
    `dict_id` = 'setting_saveLabourDocsWithoutFileAndNote',
    `dict_value` = 'Munkaügyi dokumentum rögzíthető fájl feltöltés nélkül és megjegyzés nélkül.'
WHERE `dict_id` = 'setting_saveLabourDocsWithoutFile'
    AND `lang` = 'hu';

UPDATE
    `dictionary`
SET
    `dict_id` = 'setting_saveLabourDocsWithoutFileAndNote',
    `dict_value` = 'Labour document can be saved without uploading a file and without a note.'
WHERE `dict_id` = 'setting_saveLabourDocsWithoutFile'
    AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=69, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -69-2024-11-13-12:00------------------------------------------------

ALTER TABLE `user`
    ADD COLUMN `quick_logout` TINYINT UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Gyors kijelentkezés' AFTER `receive_email`;

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('allowQuickLogout', '0', 'string', 'Gyors kijelentkezés', 'setting_allowQuickLogout', 1, 'SZOF-4108', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'setting_allowQuickLogout', 'Gyors kijelentkezés', '1'),
    ('en', 'ttwa-base', 'setting_allowQuickLogout', 'Quick logout', '1');

UPDATE `_sql_version` SET `revision`=70, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -70-2024-11-15-11:00------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('pl', 'ttwa-base', 'epp_software_name-main', 'ease++', '1'),
    ('pl', 'ttwa-base', 'employee_contract_type_indefinite', 'Umowa na czas nieokreślony', '1'),
    ('pl', 'ttwa-base', 'employee_contract_type_firm', 'Kontrakt konkretny', '1'),
    ('pl', 'ttwa-base', 'employee_contract_type_learner', 'Praktykant-uczeń', '1'),
    ('pl', 'ttwa-base', 'employee_contract_type_trainee', 'Umowa stażysty', '1'),
    ('pl', 'ttwa-base', 'employee_contract_type_OKJ', 'OKJ', '1'),
    ('pl', 'ttwa-base', 'wage_type_ob', 'Stawka godzinowa', '1'),
    ('pl', 'ttwa-base', 'wage_type_molo', 'Miesięczne wynagrodzenie', '1'),
    ('pl', 'ttwa-base', 'wage_type_tgeha', 'Płatność zgodnie z tabelą opłat', '1'),
    ('pl', 'ttwa-base', 'wage_type_prlo', 'Wynagrodzenie premium', '1'),
    ('pl', 'ttwa-base', 'wage_type_aush', 'Wydźwignąć', '1'),
    ('pl', 'ttwa-base', 'wage_type_sove', 'Inny stosunek prawny', '1'),
    ('pl', 'ttwa-base', 'wage_type_leilo', 'Płaca wydajnościowa', '1'),
    ('pl', 'ttwa-base', 'immediate_termination_probation_period_emp', 'Natychmiastowe rozwiązanie umowy o pracę przez pracownika w okresie próbnym', '1'),
    ('pl', 'ttwa-base', 'immediate_termination_probation_period', 'Natychmiastowe rozwiązanie umowy o pracę przez pracodawcę w okresie próbnym', '1'),
    ('pl', 'ttwa-base', 'employer_termination', 'Rozwiązanie umowy przez pracodawcę', '1'),
    ('pl', 'ttwa-base', 'immediate_termination_employee', 'Rozwiązanie umowy przez pracownika ze skutkiem natychmiastowym', '1'),
    ('pl', 'ttwa-base', 'immediate_termination_employer', 'Wypowiedzenie przez pracodawcę ze skutkiem natychmiastowym', '1'),
    ('pl', 'ttwa-base', 'termination_fixedterm_employment', 'Rozwiązanie stosunku pracy na czas określony', '1'),
    ('pl', 'ttwa-base', 'common_understanding_employee', 'Układ zbiorowy z inicjatywy pracowników', '1'),
    ('pl', 'ttwa-base', 'joint_agreement_employer', 'Wspólne porozumienie z inicjatywy pracodawcy', '1'),
    ('pl', 'ttwa-base', 'retirement', 'Emerytura', '1'),
    ('pl', 'ttwa-base', 'death', 'Śmierć', '1'),
    ('pl', 'ttwa-base', 'internal_relocation', 'Przeprowadzka wewnętrzna', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_to', ' z ', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_found', 'Wyniki', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_records', 'Wyniki', '1'),
    ('pl', 'ttwa-base', 'to', 'aż do', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_of', ' do, Razem: ', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_results', 'Wyniki', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_page', 'Strona', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_perpage', 'linii na stronę', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_first', 'Pierwsza strona', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_previous', 'Poprzednia strona', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_next', 'Następna strona', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_last', 'Ostatnia strona', '1'),
    ('pl', 'ttwa-base', 'dhtmlxGrid_notfound', 'Nie ma żadnych wyników', '1'),
    ('pl', 'ttwa-base', 'prev_password', 'Poprzednie hasło', '1'),
    ('pl', 'ttwa-base', 'new_password', 'Nowe hasło', '1'),
    ('pl', 'ttwa-base', 'password_strength', 'Siła hasła', '1'),
    ('pl', 'ttwa-base', 'setting_passwordStrength', 'Siła hasła', '1'),
    ('pl', 'ttwa-base', 'new_password_again', 'Znowu nowe hasło', '1'),
    ('pl', 'ttwa-base', 'new_password_strength', 'Nowa siła hasła', '1'),
    ('hu', 'ttwa-base', 'lang_pl', 'Lengyel', '1'),
    ('en', 'ttwa-base', 'lang_pl', 'Polish', '1'),
    ('pl', 'ttwa-base', 'lang_pl', 'Polski', '1'),
    ('pl', 'ttwa-base', 'saturday', 'Sobota', '1'),
    ('pl', 'ttwa-base', 'sunday', 'Niedziela', '1'),
    ('pl', 'ttwa-base', 'status_wait_for_approval', 'Oczekuje na akceptację', '1'),
    ('pl', 'ttwa-base', 'status_active', 'Aktywny', '1'),
    ('pl', 'ttwa-base', 'status_archived', 'Zarchiwizowano', '1'),
    ('pl', 'ttwa-base', 'status_deleted', 'Usunięto', '1'),
    ('pl', 'ttwa-base', 'status_locked', 'Zablokowany', '1'),
    ('pl', 'ttwa-base', 'email', 'Email', '1'),
    ('pl', 'ttwa-base', 'e-mail', 'E-mail', '1'),
    ('pl', 'ttwa-base', 'useruploadpasswordsuccess', 'Wpisane <b>hasło</b> spełnia warunki bezpieczeństwa!', '1'),
    ('pl', 'ttwa-wtt', 'warehouseTimeTracking_process_id', 'Warehouse Time Tracking', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_type_productive', 'Produktywny', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_type_project', 'Projekt', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_type_absence', 'Brak', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_type_breaktime', 'Pauza', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_group_type_of_unit_rk', 'Paleta', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_group_type_of_unit_kam', 'Ciężarówka', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_group_type_of_unit_pora', 'PHOUR', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_group_type_of_unit_m3', 'M3', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_group_type_of_unit_sor', 'WIERSZ', '1'),
    ('pl', 'ttwa-wtt', 'wtt_activity_group_type_of_unit_db', 'PC', '1'),
    ('hu', 'ttwa-wtt', 'lang_wtt_settings_pl', 'Lengyel', '1'),
    ('en', 'ttwa-wtt', 'lang_wtt_settings_pl', 'Polish', '1'),
    ('pl', 'ttwa-wtt', 'lang_wtt_settings_pl', 'Polski', '1'),
    ('pl', 'ttwa-wtt', 'wtt_quickmenu_activityreviewself', 'Przegląd moich aktywności', '1');

UPDATE `_sql_version` SET `revision`=71, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -71-2024-11-25-15:00------------------------------------------------

ALTER TABLE `menu_item_table` MODIFY COLUMN `menu_url` VARCHAR(255) DEFAULT NULL;

UPDATE `_sql_version` SET `revision`=72, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -72-2024-11-28-13:00------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
    ('employeePlaceOfBirthHide', 'employee/employeeExtTab --- place_of_birth', 0 ,'Dolgozó kezelésnél az születés helye mező elrejtése', 'SZOF-4585', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('employeePlaceOfBirthHide', 'employee/employeeExtTab', 'place_of_birth', 1, 1, 'SZOF-4585', NOW());

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
    ('employeeMothersNameHide', 'employee/employeeExtTab --- mothers_name', 0 ,'Dolgozó kezelésnél az anyja neve mező elrejtése', 'SZOF-4585', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('employeeMothersNameHide', 'employee/employeeExtTab', 'mothers_name', 1, 1, 'SZOF-4585', NOW());

UPDATE `_sql_version` SET `revision`=73, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -73-2024-12-02-10:00------------------------------------------------

ALTER TABLE `company_org_group1` MODIFY `company_org_group_name` VARCHAR(256) DEFAULT NULL;
ALTER TABLE `company_org_group2` MODIFY `company_org_group_name` VARCHAR(256) DEFAULT NULL;
ALTER TABLE `company_org_group3` MODIFY `company_org_group_name` VARCHAR(256) DEFAULT NULL;
ALTER TABLE `cost` MODIFY `cost_name` VARCHAR(256) DEFAULT NULL;
ALTER TABLE `cost` MODIFY `button_caption` VARCHAR(256) DEFAULT NULL;
ALTER TABLE `unit` MODIFY `unit_name` VARCHAR(256) DEFAULT NULL;
ALTER TABLE `payroll` MODIFY `payroll_name` VARCHAR(256) DEFAULT NULL;
ALTER TABLE `workgroup` MODIFY `workgroup_name` VARCHAR(256) DEFAULT NULL;

UPDATE `_sql_version` SET `revision`=74, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -74-2024-12-03-12:30------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '1',
    `modified_by` = 'SZOF-4523',
    `modified_on` = NOW(),
    `note` = 'Prev value: 0'
WHERE `setting_id` = 'hasMobileLayout';

UPDATE `_sql_version` SET `revision`=75, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -75-2024-12-05-12:30------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('documentGenerateUseEmployeeGroup', '0', 'string', 'Dokumentum generálás a csoportosítás tabról veszi az értékeket.' ,'setting_documentGenerateUseEmployeeGroup', '1', 'SZOF-4539', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
    ('hu', 'ttwa-base', 'setting_documentGenerateUseEmployeeGroup', 'Dokumentum generálás a csoportosítás tabról veszi az értékeket.'),
    ('en', 'ttwa-base', 'setting_documentGenerateUseEmployeeGroup', 'Document generation take the values from the employee group tab.');

UPDATE `_sql_version` SET `revision`=76, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -76-2024-12-05-16:00------------------------------------------------

CREATE TABLE `user_notification` (
     `row_id` int unsigned NOT NULL AUTO_INCREMENT,
     `user_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'User id',
     `user_notification_type_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'User notification type id',
     `notification` json DEFAULT NULL COMMENT 'Notification',
     `action_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Action url',
     `action_alt_url` varchar(256) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Action alt url',
     `level` tinyint unsigned NOT NULL DEFAULT '1' COMMENT 'Level: 1-info,2-warning,3-danger,4-success',
     `send_email` tinyint unsigned NOT NULL DEFAULT '0' COMMENT 'Send email',
     `is_read` tinyint unsigned NOT NULL DEFAULT '0' COMMENT 'Is read',
     `valid` tinyint unsigned NOT NULL DEFAULT '1' COMMENT 'Is valid',
     `created_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'Created by',
     `created_on` datetime DEFAULT NULL COMMENT 'Created on',
     `modified_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'Modified by',
     `modified_on` datetime DEFAULT NULL COMMENT 'Modified on',
     PRIMARY KEY (`row_id`),
     KEY `IDX_user_id` (`user_id`),
     KEY `IDX_user_notification_type_id` (`user_notification_type_id`),
     KEY `IDX_action_url` (`action_url`),
     KEY `IDX_action_alt_url` (`action_alt_url`),
     KEY `IDX_level` (`level`),
     KEY `IDX_send_email` (`send_email`),
     KEY `IDX_is_read` (`is_read`),
     KEY `IDX_valid` (`valid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT 'User notification';

CREATE TABLE `user_notification_type` (
    `row_id` int unsigned NOT NULL AUTO_INCREMENT,
    `user_notification_type_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'User notification type id',
    `dict_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Dictionary id',
    `note` varchar(512) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Note',
    `valid` tinyint unsigned NOT NULL DEFAULT '1' COMMENT 'Is valid',
    `created_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'Created by',
    `created_on` datetime DEFAULT NULL COMMENT 'Created on',
    `modified_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT 'Modified by',
    `modified_on` datetime DEFAULT NULL COMMENT 'Modified on',
    PRIMARY KEY (`row_id`),
    UNIQUE KEY `IDX_user_notification_type_id_valid` (`user_notification_type_id`,`valid`),
    KEY `IDX_dict_id` (`dict_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

INSERT IGNORE INTO `user_notification_type` (`user_notification_type_id`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('passwordExpire', 'notification_passwordExpire' , '1', 'SZOF-4109', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
    ('hu', 'ttwa-base', 'notification_passwordExpire', 'Jelszó lejár.'),
    ('en', 'ttwa-base', 'notification_passwordExpire', 'Password expires.');

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('allowUserNotification', '0', 'string', 'Allow notification for users' ,'setting_allowUserNotification', '1', 'SZOF-4109', NOW());

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('warningUserPasswordExpireDays', '14', 'string', 'Warning user before password expire in days' ,'setting_warningUserPasswordExpireDays', '1', 'SZOF-4109', NOW());

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('actionChangeUserPassword', 'pwexpdialog(\'/userpwchange/addContent\',\'2\',\'Password change\');', 'string', 'Action change user password' ,'setting_actionChangeUserPassword', '1', 'SZOF-4109', NOW());

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('actionAltChangeUserPassword', 'userSettings.openPasswordForm();', 'string', 'Action alternative change user password' ,'setting_actionAltChangeUserPassword', '1', 'SZOF-4109', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
    ('hu', 'ttwa-base', 'setting_allowUserNotification', 'Engedélyezi a felhasználó figyelmeztetését.'),
    ('en', 'ttwa-base', 'setting_allowUserNotification', 'Allow notification for users.'),
    ('hu', 'ttwa-base', 'setting_warningUserPasswordExpireDays', 'Felhasználó figyelmeztetése jelszó lejáratára.'),
    ('en', 'ttwa-base', 'setting_warningUserPasswordExpireDays', 'Warning user expire password.'),
    ('hu', 'ttwa-base', 'setting_actionChangeUserPassword', 'Felhasználó jelszó változtatás action.'),
    ('en', 'ttwa-base', 'setting_actionChangeUserPassword', 'User password change action.'),
    ('hu', 'ttwa-base', 'setting_actionAltChangeUserPassword', 'Felhasználó alternatív jelszó változtatás action.'),
    ('en', 'ttwa-base', 'setting_actionAltChangeUserPassword', 'User alternative password change action.');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
    ('hu', 'ttwa-base', 'notifications', 'Értesítések'),
    ('en', 'ttwa-base', 'notifications', 'Notifications'),
    ('hu', 'ttwa-base', 'notification', 'Értesítés'),
    ('en', 'ttwa-base', 'notification', 'Notification'),
    ('hu', 'ttwa-base', 'alteration', 'Megváltoztatás'),
    ('en', 'ttwa-base', 'alteration', 'Alteration'),
    ('hu', 'ttwa-base', 'notificationWarningPasswordAlteration', 'A jelszó {expirationDay} napon belül lejár, kérem megváltoztatni!'),
    ('en', 'ttwa-base', 'notificationWarningPasswordAlteration', 'Your password will expire in {expirationDay} days, please change it!'),
    ('hu', 'ttwa-base', 'notificationWarningPasswordAlterationToday', 'A jelszó a mai napon lejár, kérem megváltoztatni!'),
    ('en', 'ttwa-base', 'notificationWarningPasswordAlterationToday', 'Your password will expire today, please change it!'),
    ('hu', 'ttwa-base', 'notificationWarningExpiringPasswordEmailTitle', 'Figyelmeztetés lejáró jelszóra'),
    ('en', 'ttwa-base', 'notificationWarningExpiringPasswordEmailTitle', 'Warning expiring password'),
    ('hu', 'ttwa-base', 'notificationWarningExpiringPasswordEmailBody', 'Tisztelt Hölgyem/Uram!<br/><br/>A Login ease++ és WTT szoftver jelszó {expirationDay} napon belül lejár. Kérem, lépjen be a szoftverbe és változtassa meg!'),
    ('en', 'ttwa-base', 'notificationWarningExpiringPasswordEmailBody', 'Honourable Lady/Sir!<br/><br/>The Login ease++ and WTT software password expires in {expirationDay} day. Please, enter the software and change!'),
    ('hu', 'ttwa-base', 'notificationWarningExpiringPasswordTodayEmailBody', 'Tisztelt Hölgyem/Uram!<br/><br/>A Login ease++ és WTT szoftver jelszó a mai napon lejár. Kérem, lépjen be a szoftverbe és változtassa meg!'),
    ('en', 'ttwa-base', 'notificationWarningExpiringPasswordTodayEmailBody', 'Honourable Lady/Sir!<br/><br/>The Login ease++ and WTT software password expires today. Please, enter the software and change!');

UPDATE `_sql_version` SET `revision`=77, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -77-2024-12-07-10:00------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
    ('hu', 'ttwa-base', 'user_upload_finished', 'A felhasználók feltöltése befejeződött.</br>Kérjük, ellenőrizze az a feltöltött adatokat!'),
    ('en', 'ttwa-base', 'user_upload_finished', 'Upload of users is completed.</br>Please, check the uploaded datas!');

UPDATE `_sql_version` SET `revision`=78, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -78-2024-12-16-10:30------------------------------------------------

UPDATE `dictionary` SET dict_value = 'Intervallum ütközés: a megadott időszakban a felhasználónak már van jogosultsága a kiválasztott csoportra.' WHERE `dict_id` = 'error_approver_level_use' AND `lang` = 'hu';
UPDATE `dictionary` SET dict_value = 'Interval conflict: the user already has right for the selected group during the specified period.' WHERE `dict_id` = 'appro' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=79, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -79-2025-01-08-15:10------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'approved_process_acknowledgment_wso', 'Jóváhagyott, tudomásul vett túlóra', '1'),
    ('hu', 'ttwa-base', 'button_acknowledgment', 'Tudomásulvétel', '1'),
    ('hu', 'ttwa-base', 'process_ackwnodlegment_save_status', 'Tudomásulvétel', '1'),
    ('en', 'ttwa-base', 'process_ackwnodlegment_save_status', 'Process Acknowledgment', '1');

UPDATE `_sql_version` SET `revision`=80, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -80-2025-01-09-16:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
    ('showEmployeeDataInNotificationEmail', '0', 'string', 'Adatokat is mutatunk a dolgozónak kiküldött email értesítésben.' ,'setting_showEmployeeDataInNotificationEmail', '1', 'SZOF-4763', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'setting_showEmployeeDataInNotificationEmail', 'Adatokat is mutatunk a dolgozónak kiküldött email értesítésben.', '1'),
    ('en', 'ttwa-base', 'setting_showEmployeeDataInNotificationEmail', 'Show employee data in notification email.', '1'),
    ('hu', 'ttwa-base', 'notification_before_one_month_expired', 'Automatikus értesítés érkezett a Digitális HR rendszerből:<br>A következő munkatárs {field_name} lejárati dátuma: {field_value}', '1'),
    ('en', 'ttwa-base', 'notification_before_one_month_expired', 'Automatic notification has been received from the Digital HR system:<br>The expiration date of the following employee {field_name} is: {field_value}', '1'),
    ('hu', 'ttwa-base', 'notification_message_end_doctor_text', 'Kérjük, hogy az orvosi érvényességi meghosszabbítása miatt jelentkezz be foglalkozás egészségügyi vizsgálatra. Az eredményt kérjük juttasd el a HR Osztályra.<br>Lejárt érvényesség esetén a gépkezelői feladat nem végezhető el.<br>Köszönjük!<br>HR Osztály', '1'),
    ('en', 'ttwa-base', 'notification_message_end_doctor_text', 'Please register for an occupational health examination to extend your medical validity. Please send the results to the HR Department.<br>If your validity has expired, you will not be able to perform the machine operator job.<br>Thank you!<br>HR Department', '1'),
    ('hu', 'ttwa-base', 'notification_message_end_driving_licence_text', 'Kérjük, hogy a jogosítvány érvényességének meghosszabbítása miatt jelentkezz be foglalkozás egészségügyi vizsgálatra. Az eredményt kérjük juttasd el a HR Osztályra.<br>Lejárt érvényesség esetén a munkakör nem végezhető el.<br>Köszönjük!<br>HR Osztály', '1'),
    ('en', 'ttwa-base', 'notification_message_end_driving_licence_text', 'Please register for an occupational health examination to extend the validity of your driving license. Please send the results to the HR Department.<br>If the validity has expired, the job cannot be performed.<br>Thank you!<br>HR Department', '1'),
    ('hu', 'ttwa-base', 'notification_message_end_gki_validity_text', 'Kérjük, hogy a jogosítvány érvényességének meghosszabbítása miatt tedd meg a szükséges lépéseket. Az eredményt kérjük juttasd el a HR Osztályra.<br>Lejárt érvényesség esetén a  munkakör nem végezhető el.<br>Köszönjük!<br>HR Osztály', '1'),
    ('en', 'ttwa-base', 'notification_message_end_gki_validity_text', 'Please take the necessary steps to extend the validity of your license. Please forward the results to the HR Department.<br>If the validity has expired, the job cannot be performed.<br>Thank you!<br>HR Department', '1'),
    ('hu', 'ttwa-base', 'notification_message_end_health_text', 'Kérjük, hogy az orvosi érvényességi meghosszabbítása miatt jelentkezz be foglalkozás egészségügyi vizsgálatra. Az eredményt kérjük juttasd el a HR Osztályra.<br>Lejárt érvényesség esetén a munkakör nem végezhető el.<br>Köszönjük!<br>HR Osztály', '1'),
    ('en', 'ttwa-base', 'notification_message_end_health_text', 'Please register for an occupational health examination to extend the validity of your medical certificate. Please send the results to the HR Department.<br>If the validity period has expired, the position cannot be performed.<br>Thank you!<br>HR Department', '1');

UPDATE `_sql_version` SET `revision`=81, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -81-2025-01-23-09:00------------------------------------------------

CREATE TABLE IF NOT EXISTS `penny_wtime_webiv_test` (
	`row_id` INT NOT NULL AUTO_INCREMENT,
	`sync_id` INT NOT NULL,
	`nev` VARCHAR(512) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`taj` VARCHAR(9) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`munkakor` VARCHAR(512) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`uzlet_kod` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`uzlet_nev` VARCHAR(512) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`nap` DATE NULL DEFAULT NULL,
	`nap_jelleg` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`hianyzas` INT NULL DEFAULT NULL,
	`muszak_kezd` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`muszak_vege` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`terv_nap_jelleg` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`terv_nap_hianyzas` INT NULL DEFAULT NULL,
	`terv_nap_kezdes` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`terv_nap_vege` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`status` SMALLINT NOT NULL,
	`created_by` VARCHAR(32) NOT NULL COLLATE 'utf8mb4_general_ci',
	`created_on` DATETIME NOT NULL,
	`modified_by` VARCHAR(32) NULL DEFAULT NULL COLLATE 'utf8mb4_general_ci',
	`modified_on` DATETIME NULL DEFAULT NULL,
	PRIMARY KEY (`row_id`) USING BTREE
)
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

UPDATE `_sql_version` SET `revision`=82, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -82-2025-01-24-09:00------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'notification_message_end_health_text', 'Kérjük, hogy az orvosi érvényességi meghosszabbítása miatt jelentkezz be foglalkozás egészségügyi vizsgálatra. Az eredményt kérjük juttasd el a HR Osztályra.<br>Lejárt érvényesség esetén a munkakör nem végezhető el.<br>Köszönjük!<br>HR Osztály', '1'),
    ('en', 'ttwa-base', 'notification_message_end_health_text', 'Please register for an occupational health examination to extend the validity of your medical certificate. Please send the results to the HR Department.<br>If the validity period has expired, the position cannot be performed.<br>Thank you!<br>HR Department', '1'),
    ('hu', 'ttwa-base', 'notification_message_end_pulmonary_filter_text', 'Kérjük, hogy vegyél részt tüdőszűrő vizsgálaton, az eredményt kérjük juttasd el a HR Osztályra.<br>Lejárt érvényesség esetén a munkakör nem végezhető el.<br>Köszönjük!<br>HR Osztály', '1'),
    ('en', 'ttwa-base', 'notification_message_end_pulmonary_filter_text', 'Please take part in a lung screening test, please send the results to the HR Department.<br>If the validity has expired, the job cannot be performed.<br>Thank you!<br>HR Department', '1');

UPDATE `_sql_version` SET `revision`=83, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -83-2025-01-27-16:30------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'notification_at_month_expired', 'Automatikus értesítés érkezett a Digitális HR rendszerből:<br>A {field_name} az alábbi hónap: {field_value}, vagyis ennek a hónapnak a bérkifizetéséből kerül levonásra az utolsó részlet.', '1'),
    ('en', 'ttwa-base', 'notification_at_month_expired', 'Automatic notification has been received from the Digital HR system:<br>{field_name} is the following month: {field_value}, meaning that the last installment will be deducted from the salary payment for this month.', '1'),
    ('hu', 'ttwa-base', 'notification_at_date_expired', 'Automatikus értesítés érkezett a Digitális HR rendszerből:<br>A {field_name} az alábbi dátum: {field_value},', '1'),
    ('en', 'ttwa-base', 'notification_at_date_expired', 'Automatic notification has been received from the Digital HR system:<br>{field_name} is the following date: {field_value},', '1'),
    ('hu', 'ttwa-base', 'notification_message_end_no_further_action', 'További tennivaló nincsen, kérdés esetén fordulj a HR osztályhoz.<br>Köszönjük!<br>HR Osztály', '1'),
    ('en', 'ttwa-base', 'notification_message_end_no_further_action', 'There is no further action required, please contact HR if you have any questions.<br>Thank you!<br>HR Department', '1');

UPDATE `_sql_version` SET `revision`=84, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -84-2025-01-28-10:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
    ('automaticFieldsLoadInEmployeeUpload', '0', 'string', 'További adatok betöltésénél, automatikusan betölti a nem megadott oszlopok bent lévő értékét is.' ,'setting_automaticFieldsLoadInEmployeeUpload', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'setting_automaticFieldsLoadInEmployeeUpload', 'További adatok betöltésénél, automatikusan betölti a nem megadott oszlopok bent lévő értékét is.', 1),
    ('en', 'ttwa-base', 'setting_automaticFieldsLoadInEmployeeUpload', 'When loading additional data, it automatically loads the values in unspecified columns.', 1);

UPDATE `_sql_version` SET `revision`=85, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -85-2025-01-28-10:30------------------------------------------------

ALTER TABLE `cost` ADD COLUMN `identifier` TEXT DEFAULT NULL COLLATE utf8_unicode_ci AFTER `cost_id`;
ALTER TABLE `cost_center` ADD COLUMN `identifier` TEXT DEFAULT NULL COLLATE utf8_unicode_ci AFTER `cost_center_id`;
ALTER TABLE `payroll` ADD COLUMN `identifier` TEXT DEFAULT NULL COLLATE utf8_unicode_ci AFTER `payroll_id`;

INSERT INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
    ('cost', 'cost_id', NULL, 'ALL', 2, NULL, 'system', NOW(), NULL, NULL, NULL),
    ('cost', 'cost_name', NULL, 'ALL', 2, NULL, 'system', NOW(), NULL, NULL, NULL),
    ('cost', 'identifier', NULL, 'ALL', 5, NULL, 'system', NOW(), NULL, NULL, NULL),
    ('cost', 'company_id', NULL, 'ALL', 2, NULL, 'system', NOW(), NULL, NULL, NULL),
    ('cost', 'payroll_id', NULL, 'ALL', 2, NULL, 'system', NOW(), NULL, NULL, NULL),
    ('cost', 'button_caption', NULL, 'ALL', 2, NULL, 'system', NOW(), NULL, NULL, NULL),
    ('cost', 'note', NULL, 'ALL', 2, NULL, 'system', NOW(), NULL, NULL, NULL),
    ('cost', 'valid_from', NULL, 'ALL', 2, NULL, 'system', NOW(), NULL, NULL, NULL),
    ('cost', 'valid_to', NULL, 'ALL', 2, NULL, 'system', NOW(), NULL, NULL, NULL),
    ('cost', 'button_img_id', NULL, 'ALL', 2, NULL, 'system', NOW(), NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=86, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -86-2025-02-12-09:00------------------------------------------------

UPDATE `user` u
    LEFT JOIN (SELECT DATABASE() AS db_neve) AS db_nev ON 1=1
    SET
        u.`password`='13a454ac6225dc3c6eaf68e171cccab1cb80e00c57ae5c7268dbc70fa5f6a74ebd536aa703de394b3bc5df093796a162f10134cbd332da2750b285d0026abc3d',
        u.`password_date` = NOW()
WHERE
    u.`username`='root'
  AND db_nev.db_neve <> 'la_ttwa_login';

UPDATE `_sql_version` SET `revision`=87, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -87-2025-03-04-09:00------------------------------------------------

INSERT IGNORE INTO `placeholder` (`placeholder`, `model`, `field_name`, `status`, `created_by`, `created_on`) VALUES
	('cost_name', 'Cost', 'cost_name', '2', 'SZOF-4671', NOW()),
    ('cost_center_name', 'CostCenter', 'cost_center_name', '2', 'SZOF-4671', NOW());

UPDATE `_sql_version` SET `revision`=88, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -88-2025-03-05-13:00------------------------------------------------

alter table daytype
    add company_id varchar(32) null after row_id;

alter table daytype
    add payroll_id varchar(32) null after company_id;

UPDATE `_sql_version` SET `revision`=89, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -89-2025-03-06-14:00------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
    ('nexonApiClientParams', '{}', 'string', 'Nexon Api Client param', 'setting_nexonApiClientParams', 1);

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'setting_nexonApiClientParams', 'client függvény bővítése paraméterekkel', 1),
    ('en', 'ttwa-base', 'setting_nexonApiClientParams', 'client extending a function with parameters', 1);

UPDATE `_sql_version` SET `revision`=90, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -90-2025-04-01-10:30------------------------------------------------

CREATE TABLE IF NOT EXISTS `notification_on_change_config` (
    `row_id` int unsigned NOT NULL AUTO_INCREMENT,
    `entity_name` varchar(128) NOT NULL COMMENT 'Entitás név',
    `config` JSON NOT NULL COMMENT 'Konfiguráció',
    `status` tinyint unsigned NOT NULL COMMENT 'Állapot',
    `created_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Készítette',
    `created_on` datetime NOT NULL COMMENT 'Készült',
    `modified_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'Módosította',
    `modified_on` datetime DEFAULT NULL COMMENT 'Módosítva',
    PRIMARY KEY (`row_id`),
    KEY `IDX_entity_name` (`entity_name`),
    KEY `IDX_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Értesítés konfiguráció adatváltozás esetén';

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`) VALUES
   ('notificationOnChangeModels', '', 'string', 'Azoknak a modelleknek a listája, amelyek adatváltozása esetén értesítést küldünk.' ,'setting_notificationOnChangeModels', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
     ('hu', 'ttwa-base', 'setting_notificationOnChangeModels', 'Azoknak a modelleknek a listája, amelyek adatváltozása esetén értesítést küldünk.', 1),
     ('en', 'ttwa-base', 'setting_notificationOnChangeModels', 'The list of models for which a notification is sent in case of data changes.', 1),
     ('hu', 'ttwa-base', 'notification_config_not_found', 'Értesítési konfiguráció nem található.', 1),
     ('en', 'ttwa-base', 'notification_config_not_found', 'Notification config is not found.', 1),
     ('hu', 'ttwa-base', 'notification_handler_class_not_found', 'Értesítést kezelő osztály nem található.', 1),
     ('en', 'ttwa-base', 'notification_handler_class_not_found', 'Notification handler class is not found.', 1);

UPDATE `_sql_version` SET `revision`=91, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -91-2025-05-08-13:30------------------------------------------------

ALTER TABLE `employee_address` MODIFY COLUMN `address_card_number` VARCHAR(128);

UPDATE `_sql_version` SET `revision`=92, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -92-2025-06-20-10:00------------------------------------------------

alter table work_schedule_used add correct_calculation int default 0 null after full_work_time;
alter table employee_calc_used_daytype add correct_calculation int default 0 null;

UPDATE work_schedule_used SET correct_calculation=0 WHERE correct_calculation != 0;
UPDATE employee_calc_used_daytype SET correct_calculation=0 WHERE correct_calculation != 0;

UPDATE `_sql_version` SET `revision`=93, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -93-2025-07-02-10:00------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '1',
    `modified_on` = NOW()
WHERE
        `setting_id` = 'daytype_follow_changes_wsu'
    AND `modified_by` IS NULL
    AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=94, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -94-2025-07-22-11:10------------------------------------------------

INSERT INTO `app_settings` (
    `setting_id`,
    `setting_value`,
    `setting_type`,
    `note`,
    `dict_id`,
    `valid`
) VALUES (
         'baseAbsenceTypeMapping',
         '{"2000000311":"8d92474d33de2925e59dbeb01e7602ad","2000000312":"23c07705702ce522f327469d989ea1d5","**********":"","**********":"3124666222842d616949b952976b1238","**********":"cadd7a06f1f11d545d82f9cfccafdc52","**********":"","**********":"","**********":"5cb1f0666d19d9eb290457bebd45d0cd","**********":"","**********":"","**********":"","**********":"63a1079c69d79753cd623ee2620687a1","**********":"99d171c9188a94a197aa2e8c3f22fb04","**********":"fb69360c9117a13d81c07c03a394fd5a","**********":"e35b75ed4f59c57f5601951dc0080ad6","**********":"","**********":"base_absence_type_health_harmful","**********":"base_absence_type_jubilee","**********":"","**********":"","**********":"2ad60682e08ca21e57594e05c8b86e85","**********":"","**********":"0f44e4b35cb4265a827289eb4152af89"}',
         'string',
         'Lehetséges szabadságtípusok azonosító-alapú mappingje szinkronizáláshoz (employee_base_absence.base_absence_type_id).',
         'setting_base_absenceTypeMapping',
         1
        );

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-ahp', 'setting_base_absenceTypeMapping', 'A lehetséges szabadságtípusok azonosító-alapú mappingje szinkronizáláshoz.', '1'),
    ('en', 'ttwa-ahp', 'setting_base_absenceTypeMapping', 'Mapping of possible absence types by identifier for synchronization.', '1');

INSERT INTO `app_settings` (
    `setting_id`,
    `setting_value`,
    `setting_type`,
    `note`,
    `dict_id`,
    `valid`
) VALUES (
         'identifierMapping',
         '{"emp_id": "EmployeeNumber"}',
         'string',
         'Technikai azonosítók mappingje - meghatározza, hogy melyik mező milyen azonosítót használjon (pl. emp_id -> PersonId)',
         'setting_identifierMapping',
         1
         );

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-ahp', 'setting_identifierMapping', 'Technikai azonosítók mappingje', '1'),
    ('en', 'ttwa-ahp', 'setting_identifierMapping', 'Technical identifier mapping', '1');

UPDATE `_sql_version` SET `revision`=95, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -95-2025-07-23-10:30------------------------------------------------

INSERT IGNORE INTO `app_settings` (`setting_id`, `setting_value`, `setting_type`, `note`, `dict_id`, `valid`, `created_by`, `created_on`) VALUES
	('baberSnycCreatUserJoinEmployeeWithField', '', 'string', 'A Baber szinkronban a felhasználó létrehozásakor a dolgozó valós emp_id melyik mezőben van (ha üres akkor employeeUploadIdField setting).', 'setting_baberSnycCreatUserJoinEmployeeWithField', 1, 'SZOF-5584', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'setting_baberSnycCreatUserJoinEmployeeWithField', 'A Baber szinkronban a felhasználó létrehozásakor a dolgozó valós emp_id melyik mezőben van (ha üres akkor employeeUploadIdField setting).', '1'),
    ('en', 'ttwa-base', 'setting_baberSnycCreatUserJoinEmployeeWithField', 'In the Baber sync, when creating a user, the employee’s actual emp_id is in which field (if empty, then the employeeUploadIdField setting is used).', '1');

UPDATE `_sql_version` SET `revision`=96, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -96-2025-08-29-14:45------------------------------------------------

ALTER TABLE `employee_group`
    CHANGE COLUMN `group_value` `group_value` VARCHAR(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Csoport érték';

UPDATE `_sql_version` SET `revision`=97, `updated_on`=NOW() WHERE `module` = 'ttwa-base';

-- VERSION -97-2025-09-10-17:30------------------------------------------------


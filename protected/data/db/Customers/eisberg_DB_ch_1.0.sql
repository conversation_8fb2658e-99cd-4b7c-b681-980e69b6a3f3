-- # RELEASE 1.0

INSERT INTO `_sql_version` (`id`, `module`, `major_minor`, `revision`, `updated_on`)
VALUES (NULL, 'c_eisberg', '1.0', '0', '2021-10-18 12:00:00') ON DUPLICATE KEY UPDATE `module`=VALUES(`module`), `major_minor`=VALUES(`major_minor`), `revision`=VALUES(`revision`);

UPDATE `_sql_version` SET `revision`=1, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -1--2021.10.18.-12:00---------------------------------------------------------------

UPDATE dictionary SET dict_value='Fehér'          WHERE dict_id = 'ext2_option1' AND lang = 'hu' ;
UPDATE dictionary SET dict_value='White'          WHERE dict_id = 'ext2_option1' AND lang = 'en' ;
UPDATE dictionary SET dict_value='Labor/Operátor' WHERE dict_id = 'ext2_option2' AND lang = 'hu' ;
UPDATE dictionary SET dict_value='Lab/Operator'   WHERE dict_id = 'ext2_option2' AND lang = 'en' ;
UPDATE dictionary SET dict_value='Igény'          WHERE dict_id = 'ext2_option3' AND lang = 'hu' ;
UPDATE dictionary SET dict_value='Need'           WHERE dict_id = 'ext2_option3' AND lang = 'en' ;
UPDATE dictionary SET dict_value='Tiszta övezet'  WHERE dict_id = 'ext2_option4' AND lang = 'hu' ;
UPDATE dictionary SET dict_value='Clear area'     WHERE dict_id = 'ext2_option4' AND lang = 'en' ;
UPDATE dictionary SET dict_value='Fémdetektor'    WHERE dict_id = 'ext2_option5' AND lang = 'hu' ;
UPDATE dictionary SET dict_value='Metal detector' WHERE dict_id = 'ext2_option5' AND lang = 'en' ;
UPDATE dictionary SET dict_value='Csoport fix'    WHERE dict_id = 'ext2_option6' AND lang = 'hu' ;
UPDATE dictionary SET dict_value='Group fixed'    WHERE dict_id = 'ext2_option6' AND lang = 'en' ;

UPDATE `_sql_version` SET `revision`=2, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -2--2021.11.11.-14:00---------------------------------------------------------------

UPDATE app_settings SET setting_value='0', note=concat('prev.val:1 DEV-11395',note)  WHERE setting_id='showMenuOnHover';

UPDATE `_sql_version` SET `revision`=3, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -3--2021.11.29.-11:00---------------------------------------------------------------

UPDATE `option_config` SET `type` = 'combo' WHERE `option_id` IN ('option2', 'option3', 'option4', 'option5', 'option6', 'option7');
INSERT INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
	('option2', '0', '1', 'no', '1'),
	('option2', '1', '0', 'yes', '1'),
	('option3', '0', '1', 'no', '1'),
	('option3', '1', '0', 'yes', '1'),
	('option4', '0', '1', 'no', '1'),
	('option4', '1', '0', 'yes', '1'),
	('option5', '0', '1', 'no', '1'),
	('option5', '1', '0', 'yes', '1'),
	('option6', '0', '1', 'no', '1'),
	('option6', '1', '0', 'yes', '1'),
	('option7', '0', '1', 'no', '1'),
	('option7', '1', '0', 'yes', '1');
UPDATE `employee_ext` SET `option2` = '0', `option3` = '0', `option4` = '0', `option5` = '0', `option6` = '0', `option7` = '0' WHERE `status` = 2;
UPDATE `dictionary` SET `dict_value` = 'Műszakpótlék 30%' WHERE `dict_id` = 'option2' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Shift bonus 30%' WHERE `dict_id` = 'option2' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Műszakpótlék 15%' WHERE `dict_id` = 'option3' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Shift bonus 15%' WHERE `dict_id` = 'option3' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Vasárnapi pótlék 50%' WHERE `dict_id` = 'option4' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Sunday bonus 50%' WHERE `dict_id` = 'option4' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Ünnepnapi pótlék 50%' WHERE `dict_id` = 'option5' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Holiday bonus 50%' WHERE `dict_id` = 'option5' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Túlóra pótlék 50%' WHERE `dict_id` = 'option6' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Overtime bonus 50%' WHERE `dict_id` = 'option6' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Túlóra pótlék 100%' WHERE `dict_id` = 'option7' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Overtime bonus 100%' WHERE `dict_id` = 'option7' AND `lang` = 'en' AND `module` = 'ttwa-base';

CREATE TABLE IF NOT EXISTS temp_employee_ext_option_for_payroll (
    `emp_id` VARCHAR(4) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    `option2` VARCHAR(2) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    `option3` VARCHAR(2) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    `option4` VARCHAR(2) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    `option5` VARCHAR(2) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    `option6` VARCHAR(2) CHARACTER SET utf8 COLLATE utf8_unicode_ci,
    `option7` VARCHAR(2) CHARACTER SET utf8 COLLATE utf8_unicode_ci
);
INSERT INTO temp_employee_ext_option_for_payroll VALUES
    ('336','1','0','1','1','1','1'),
    ('515','1','0','1','1','1','1'),
    ('356','1','0','1','1','1','1'),
    ('406','1','0','1','1','1','1'),
    ('396','1','0','1','1','1','1'),
    ('S416','0','0','1','1','1','0'),
    ('353','1','0','1','1','1','1'),
    ('S174','1','0','1','1','1','1'),
    ('357','1','0','1','1','1','1'),
    ('523','1','0','1','1','1','1'),
    ('284','1','0','1','1','1','1'),
    ('180','0','0','0','0','0','0'),
    ('293','0','0','0','0','0','0'),
    ('440','1','0','1','1','1','1'),
    ('227','1','0','1','1','1','1'),
    ('490','1','0','1','1','1','1'),
    ('434','1','0','1','1','1','1'),
    ('488','1','0','1','1','1','1'),
    ('389','1','0','1','1','1','1'),
    ('298','1','0','1','1','1','1'),
    ('11','1','0','1','1','1','1'),
    ('320','1','0','1','1','1','1'),
    ('391','1','0','1','1','1','1'),
    ('268','1','0','1','1','1','1'),
    ('14','0','0','0','0','0','0'),
    ('482','1','0','1','1','0','0'),
    ('549','1','0','1','1','1','1'),
    ('102','1','0','1','1','0','0'),
    ('287','0','0','0','0','0','0'),
    ('343','0','1','1','1','1','1'),
    ('18','0','0','0','0','0','0'),
    ('409','1','0','1','1','1','1'),
    ('495','1','0','1','1','1','1'),
    ('493','1','0','1','1','1','1'),
    ('22','1','0','1','1','0','0'),
    ('543','1','0','1','1','1','1'),
    ('24','1','0','1','1','1','1'),
    ('492','0','0','1','1','1','0'),
    ('508','1','0','1','1','1','1'),
    ('167','1','0','1','1','1','1'),
    ('28','1','0','1','1','1','1'),
    ('297','1','0','1','1','1','1'),
    ('466','0','0','0','1','0','0'),
    ('S421','1','0','1','1','1','1'),
    ('435','1','0','1','1','1','1'),
    ('88','0','0','0','0','0','0'),
    ('480','1','0','1','1','1','1'),
    ('511','0','0','0','0','0','0'),
    ('352','1','0','1','1','1','1'),
    ('34','1','0','1','1','1','1'),
    ('403','1','0','1','1','1','1'),
    ('37','1','0','1','1','0','0'),
    ('536','0','0','0','0','0','0'),
    ('560','1','0','1','1','1','1'),
    ('344','1','0','1','1','1','1'),
    ('44','1','0','1','1','1','1'),
    ('45','1','0','1','1','0','0'),
    ('92','1','0','1','1','1','1'),
    ('350','1','0','1','1','1','1'),
    ('S419','1','0','1','1','1','1'),
    ('506','1','0','1','1','1','1'),
    ('225','1','0','1','1','1','1'),
    ('507','1','0','1','1','1','1'),
    ('47','1','0','1','1','1','1'),
    ('457','0','0','0','0','0','0'),
    ('257','1','0','1','1','1','1'),
    ('259','1','0','1','1','1','1'),
    ('438','0','0','0','0','0','0'),
    ('283','1','0','1','1','1','1'),
    ('551','1','0','1','1','1','1'),
    ('358','1','0','1','1','1','1'),
    ('50','0','0','0','0','0','0'),
    ('477','1','0','1','1','1','1'),
    ('274','1','0','1','1','1','1'),
    ('216','1','0','1','1','0','0'),
    ('196','0','0','1','1','1','0'),
    ('316','0','0','0','0','0','0'),
    ('504','1','0','1','1','1','1'),
    ('S429','1','0','1','1','1','1'),
    ('286','1','0','1','1','1','1'),
    ('386','1','0','1','1','1','1'),
    ('539','1','0','1','1','1','1'),
    ('304','1','0','1','1','1','1'),
    ('554','1','0','1','1','1','1'),
    ('106','1','0','1','1','1','1'),
    ('260','1','0','1','1','1','1'),
    ('513','1','0','1','1','1','1'),
    ('56','0','0','0','0','0','0'),
    ('422','1','0','1','1','1','1'),
    ('456','1','0','1','1','1','1'),
    ('159','1','0','1','1','1','1'),
    ('402','1','0','1','1','1','1'),
    ('380','0','0','0','0','0','0'),
    ('401','0','0','1','1','1','0'),
    ('57','1','0','1','1','0','0'),
    ('347','1','0','1','1','1','1'),
    ('177','0','0','0','0','0','0'),
    ('437','0','0','0','0','0','0'),
    ('516','1','0','1','1','1','1'),
    ('330','1','0','1','1','1','1'),
    ('93','1','0','1','1','1','1'),
    ('558','1','0','1','1','0','0'),
    ('281','0','0','0','1','0','0'),
    ('153','1','0','1','1','1','0'),
    ('114','0','0','1','1','1','0'),
    ('474','1','0','1','1','0','0'),
    ('420','1','0','1','1','1','1'),
    ('247','1','0','1','1','1','1'),
    ('S427','1','0','1','1','1','1'),
    ('552','1','0','1','1','1','1'),
    ('S431','1','0','1','1','1','1'),
    ('555','1','0','1','1','1','1'),
    ('331','1','0','1','1','1','1'),
    ('242','1','0','1','1','1','1'),
    ('374','1','0','1','1','1','1'),
    ('66','0','0','0','1','0','1'),
    ('505','1','0','1','1','1','1'),
    ('471','1','0','1','1','1','1'),
    ('472','0','0','1','1','1','0'),
    ('553','1','0','1','1','1','1'),
    ('384','1','0','1','1','1','1'),
    ('418','0','0','0','0','0','0'),
    ('556','1','0','1','1','1','1'),
    ('69','1','0','1','1','1','1'),
    ('485','1','0','1','1','0','0'),
    ('230','0','0','0','1','0','1'),
    ('486','1','0','1','1','0','0'),
    ('294','0','0','0','0','0','0'),
    ('354','1','0','1','1','0','0'),
    ('332','1','0','1','1','1','1'),
    ('465','1','0','1','1','1','1'),
    ('425','1','0','1','1','1','1'),
    ('385','1','0','1','1','1','1'),
    ('464','0','0','0','1','0','0'),
    ('73','1','0','1','1','1','1'),
    ('497','1','0','1','1','1','1'),
    ('421','0','0','0','0','0','0'),
    ('248','1','0','1','1','1','1'),
    ('468','1','0','1','1','1','1'),
    ('262','1','0','1','1','1','1'),
    ('363','1','0','1','1','1','1'),
    ('289','1','0','1','1','1','1'),
    ('364','0','1','1','1','1','1'),
    ('517','0','0','1','1','1','0'),
    ('76','0','0','0','0','0','0'),
    ('S400','0','1','1','1','1','1'),
    ('137','0','0','0','0','0','0'),
    ('540','1','0','1','1','1','1'),
    ('162','0','0','0','0','0','0'),
    ('387','1','0','1','1','1','1'),
    ('136','1','0','1','1','0','0'),
    ('276','0','0','0','0','0','0'),
    ('537','0','0','0','0','0','0'),
    ('290','1','0','1','1','1','1'),
    ('310','1','0','1','1','0','0'),
    ('23','1','0','1','1','1','1'),
    ('455','1','0','1','1','1','1'),
    ('395','1','0','1','1','1','1'),
    ('542','1','0','1','1','1','1'),
    ('273','1','0','1','1','1','1'),
    ('335','0','0','0','0','0','0'),
    ('481','1','0','1','1','1','1'),
    ('308','1','0','1','1','1','1'),
    ('87','0','0','1','1','1','0'),
    ('207','1','0','1','1','0','0'),
    ('454','1','0','1','1','1','1'),
    ('557','1','0','1','1','1','1'),
    ('341','0','0','0','1','0','0'),
    ('424','1','0','1','1','1','1'),
    ('301','1','0','1','1','1','1'),
    ('221','1','0','1','1','1','1'),
    ('500','0','1','1','1','1','1'),
    ('S413','1','0','1','1','1','1'),
    ('313','1','0','1','1','1','1'),
    ('496','0','0','1','1','1','0'),
    ('518','1','0','1','1','1','1'),
    ('291','1','0','1','1','1','1'),
    ('263','1','0','1','1','1','1'),
    ('203','1','0','0','1','1','1'),
    ('214','1','0','0','1','1','1'),
    ('31','1','0','0','1','1','1'),
    ('224','1','0','0','1','1','1'),
    ('126','1','0','0','1','1','1'),
    ('521','1','0','0','1','1','1'),
    ('S432','1','0','1','1','1','1'),
    ('559','0','0','0','0','0','0'),
    ('501','1','0','1','1','1','1');

UPDATE `employee_ext` ex
LEFT JOIN `employee` e ON
		e.`employee_id` = ex.`employee_id`
	AND e.`status` = 2
	AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '2038-01-01')
LEFT JOIN `temp_employee_ext_option_for_payroll` tp ON
		tp.`emp_id` = e.`emp_id`
SET
	ex.`option2` = tp.`option2`,
	ex.`option3` = tp.`option3`,
	ex.`option4` = tp.`option4`,
	ex.`option5` = tp.`option5`,
	ex.`option6` = tp.`option6`,
	ex.`option7` = tp.`option7`,
	ex.`modified_by` = 'SN-DEV-11400',
	ex.`modified_on` = NOW()
WHERE ex.`status` = 2 AND CURDATE() BETWEEN ex.`valid_from` AND IFNULL(ex.`valid_to`, '2038-01-01') AND tp.`emp_id` IS NOT NULL;
INSERT INTO `employee_ext` (`employee_id`, `option2`, `option3`, `option4`, `option5`, `option6`, `option7`, `status`, `valid_from`, `valid_to`, `created_by`, `created_on`)
SELECT e.`employee_id`, tp.`option2`, tp.`option3`, tp.`option4`, tp.`option5`, tp.`option6`, tp.`option7`, 2, e.`valid_from`, e.`valid_to`, 'SN-DEV-11400', NOW()
FROM `temp_employee_ext_option_for_payroll` tp
JOIN `employee` e ON
		e.`emp_id` = tp.`emp_id`
	AND e.`status` = 2
	AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '2038-01-01')
LEFT JOIN `employee_ext` ex ON
		ex.`employee_id` = e.`employee_id`
	AND ex.`status` = 2
	AND CURDATE() BETWEEN ex.`valid_from` AND IFNULL(ex.`valid_to`, '2038-01-01')
WHERE ex.`row_id` IS NULL;

DROP TABLE IF EXISTS `temp_employee_ext_option_for_payroll`;

UPDATE `app_settings` SET `setting_value` = 'LOGA', `note` = 'Default value, SN-DEV-11400' WHERE `setting_id` = 'ptr_default_mode';
UPDATE `app_settings` SET `setting_value` = 'EISBERG', `note` = 'Default value, SN-DEV-11400' WHERE `setting_id` = 'ptr_customer';
UPDATE `app_settings` SET `setting_value` = '0', `note` = 'Default value, SN-DEV-11400' WHERE `setting_id` = 'ptrCorrectLastMonth';
UPDATE `app_settings` SET `setting_value` = 'employee.emp_id', `note` = 'Default value, SN-DEV-11400' WHERE `setting_id` = 'ptr_payrollEmpId';
TRUNCATE `payroll_transfer_config`;
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `null_value`, `select_value`, `select_as`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('LOGA', '252', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`option2` = \"1\" AND (data.`inside_type_id` LIKE \"%du2%\" OR data.`inside_type_id` LIKE \"%ej%\")', 'Műszakpótlék 30%', 2),
	('LOGA', '250', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`option3` = \"1\" AND (data.`inside_type_id` LIKE \"%du2%\" OR data.`inside_type_id` LIKE \"%ej%\")', 'Műszakpótlék 15%', 2),
	('LOGA', '228', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`option4` = \"1\" AND data.`date_day_of_week` = 1 AND (data.`inside_type_id` LIKE \"wt%\" OR data.`inside_type_id` LIKE \"ot%\")', 'Vasárnapi pótlék 50%', 2),
	('LOGA', '225', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`option5` = \"1\" AND data.`is_public_holiday` = 1 AND (data.`inside_type_id` LIKE \"wt%\" OR data.`inside_type_id` LIKE \"ot%\")', 'Ünnepnapi pótlék 50%', 2),
	('LOGA', '209', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`option6` = \"1\" AND data.`inside_type_id` LIKE \"ot%\" AND data.`inside_type_id` NOT LIKE \"otw%\" AND data.`inside_type_id` NOT LIKE \"otstw%\"', 'Túlóra 50%', 2),
	('LOGA', '213', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`option7` = \"1\" AND (data.`inside_type_id` LIKE \"otw%\" OR data.`inside_type_id` LIKE \"otstw%\")', 'Túlóra 100%', 2);

UPDATE `_sql_version` SET `revision`=4, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -4--2021.12.02.-15:15---------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'eisberg', `note` = 'DEV-11495, default: demo' WHERE `setting_id` IN ('cplatform_skin','cplatform_client');
UPDATE `app_settings` SET `setting_value` = '1', `note` = CONCAT('DEV-11495, default: 0, ', note) WHERE `setting_id` = 'useRestdayRequest';
UPDATE `cplatform_button_config` SET `status` = 5 WHERE `button_id` NOT IN ('dashbutton14','dashbutton15');
UPDATE `cplatform_button_config` SET `classes` = 'holidayButton', `button_text_dict_id` = 'cplatform_holiday', `navigation` = '1', `navigation_url` = '/ahp/absenceplanneryearview/index',`status` = 2 WHERE `button_id` = 'dashbutton14';
UPDATE `cplatform_button_config` SET `classes` = 'shiftButton', `button_text_dict_id` = 'cplatform_shift', `navigation` = '1', `navigation_url` = '/wfm/workscheduleByUser/index?layout=eisberg',`status` = 2 WHERE `button_id` = 'dashbutton15';

UPDATE `_sql_version` SET `revision`=5, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -5-2021-12-11-10:30------------------------------------------------

UPDATE `option_config` SET `type` = 'combo' WHERE `option_id` IN ('option1', 'option8', 'option9', 'option10');
INSERT INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
	('option1', '0', '1', 'no', '1'),
	('option1', '1', '0', 'yes', '1'),
	('option8', '0', '1', 'no', '1'),
	('option8', '1', '0', 'yes', '1'),
	('option9', '0', '1', 'no', '1'),
	('option9', '1', '0', 'yes', '1'),
	('option10', '0', '1', 'no', '1'),
	('option10', '1', '0', 'yes', '1');
UPDATE `employee_ext` SET `option1` = '0', `option8` = '0', `option9` = '0', `option10` = '0', `passport_number` = '0' WHERE `status` = 2;
UPDATE `dictionary` SET `dict_value` = 'Túlóra alap' WHERE `dict_id` = 'option1' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Overtime base' WHERE `dict_id` = 'option1' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Fizetett ünnep' WHERE `dict_id` = 'option8' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Paid national holiday' WHERE `dict_id` = 'option8' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Vasárnapi pótlék TMK 50%' WHERE `dict_id` = 'option9' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Sunday bonus TMK 50%' WHERE `dict_id` = 'option9' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Készenlét 20%' WHERE `dict_id` = 'option10' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Standby 20%' WHERE `dict_id` = 'option10' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Készenlét 20%' WHERE `dict_id` = 'option10' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Standby 20%' WHERE `dict_id` = 'option10' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Éjszakai takarító - átalány' WHERE `dict_id` = 'passport_number' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'At night cleaner - flat rate' WHERE `dict_id` = 'passport_number' AND `lang` = 'en' AND `module` = 'ttwa-base';

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `null_value`, `select_value`, `select_as`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('LOGA', '214', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`option1` = \"1\" AND (data.`inside_type_id` LIKE \"ot%\" OR data.`inside_type_id` LIKE \"paidot%\")', 'Túlóra alap', 2),
	('LOGA', '500', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`option8` = \"1\" AND (data.`inside_type_id` LIKE \"ot%\" OR data.`inside_type_id` LIKE \"wt%\") AND data.`is_public_holiday` = 1', 'Fizetett ünnep', 2),
	('LOGA', '130', 'inside_type_id', NULL, 0, 'data.`dt__schedule_sum`', 'new_value_sum_payroll', NULL, 0, 0, NULL, 'data.`state_type_id` = \"1ecfd16854c92211d6278a918038903d\" AND data.`dt__schedule_sum` > 0', 'Állásidő', 2);

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
    ('LOGA', 'URL', 'state_type_id', NULL, 'a272f564576d443e7832587126b070aa', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'URL', 'state_type_id', NULL, '8cb3945a0a8b1afa9d064d064584e5d4', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, '(áthozott) Szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'URL', 'state_type_id', NULL, 'f691735315003fcaee9470c752d126f6', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, '(életkor szerinti pót) Szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'APASZ', 'state_type_id', NULL, 'ebf4ac30e7dc238fd2f4bc86332e0675', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Apák munkaidő kedvezménye', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'FIGTA', 'state_type_id', NULL, 'f4f63ae4dd65cd97ee1f409d8b620806', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fizetett igazolt távollét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'FN', 'state_type_id', NULL, '628834878c5bc72f60283e37865678b6', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fizetés nélküli szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'IGLAN', 'state_type_id', NULL, 'def32968390fb987c823da0cbf7d3bd8', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Igazolatlan távollét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'NIG', 'state_type_id', NULL, '269b59b4efbbb4ef1d527492dc06cb60', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Nem fizetett igazolt távollét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'BETEG', 'state_type_id', NULL, '3ad610cfce362896dbb4b11858dfae40', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Betegszabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'EURL', 'state_type_id', NULL, 'bfa221167d9944ce0ffcc8dffbbaf589', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Egyéb pótszabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('LOGA', 'EURL', 'state_type_id', NULL, 'fceedab36bb56f3a59665972fa0a7d54', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Rendkívüli szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=6, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -6-2022-01-06-10:00------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-wfm', 'restdayRequestDeleteSuccess', 'A pihenőnap visszavonása sikeres volt.', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-wfm', 'restdayRequestDeleteSuccess', 'Your restday revoke was successful.', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-wfm', 'restdayRequestDeleteError', 'A pihenőnap visszavonása nem sikerült.', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-wfm', 'restdayRequestDeleteError', 'Your restday revoke was unsuccessful.', '1');

UPDATE `_sql_version` SET `revision`=7, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -7-2022-01-19-17:00------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` = '130';
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_value`, `null_value`, `select_value`, `select_as`, `weekday`, `is_restday`, `is_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`) VALUES
	('LOGA', '244', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`passport_number` = \"Igen\" AND (data.`inside_type_id` LIKE \"wt%\" OR data.`inside_type_id` LIKE \"ot%\")', 'Éjszakai takarító átalány', 2),
	('LOGA', '228', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`option9` = \"1\" AND (data.`inside_type_id` LIKE \"ot%\" OR data.`inside_type_id` LIKE \"wt%\") AND data.`date_day_of_week` = 1', 'Vasárnapi pótlék TMK 50%', 2),
    ('LOGA', '230', 'inside_type_id', NULL, 0, 'data.`schedule_standby_full_time`', 'new_value_sum_payroll', NULL, 0, 0, NULL, 'data.`option10` = \"1\" AND data.`schedule_standby_full_time` > 0', 'Készenlét 20%', 2),
    ('LOGA', '680', 'inside_type_id', NULL, 0, 'data.`dt__schedule_sum`', 'new_value_sum_payroll', NULL, 0, 0, NULL, 'data.`state_type_id` = \"absence_type_baby_care_fee\" AND data.`dt__schedule_sum` > 0', 'CSED (első fél év)', 2),
    ('LOGA', '209', 'inside_type_id', NULL, 0, NULL, NULL, NULL, 0, 0, NULL, 'data.`inside_type_id` LIKE \"paidot%\"', 'Keretben zárolt túlóra', 2);

UPDATE `_sql_version` SET `revision`=8, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -8-2022-01-25-15:00------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1' WHERE (`setting_id` = 'wsbbgShowJustAbsence');

UPDATE `_sql_version` SET `revision`=9, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -9-2022-01-31-14:30------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 1 WHERE `setting_id` = 'wfm/WorkScheduleByGroupController_daytype_without_border' AND `valid` = 1;
UPDATE `app_settings` SET `setting_value` = 1 WHERE `setting_id` = 'wfm/WorkScheduleByGroupController_ifRestdayFillHalfCellWhenIsAbsence' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=10, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -10-2022-02-17-14:00------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '4' WHERE `setting_id` = 'legalRestDayIncrease' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=11, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -11-2022-04-12-14:00------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Költség típus' WHERE `dict_id` = 'ext2_option8' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Cost type' WHERE `dict_id` = 'ext2_option8' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=12, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -12-2022-04-12-13:00------------------------------------------------

INSERT INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES ('customers/eisberg/attendanceSheet', 'AttendanceSheet', 'menu_item_attendanceSheet', 'DEV-11508', '2021-12-17 10:00:00');

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES ('eisbergAttendanceSheet', 'customers/eisberg/attendanceSheet', 'view', '1', '1', 'DEV-11508', '2021-12-16 10:00:00');

INSERT INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES ('eisbergAttendanceSheet', 'customers/eisberg/attendanceSheet --- view', '0', 'Jelenléti ív', 'DEV-11508', '2021-12-17 10:00:00');

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-wfm', 'menu_item_attendancesheet_eisberg', 'Jelenléti ív', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-wfm', 'menu_item_attendancesheet_eisberg', 'Attendancesheet', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-wfm', 'page_title_attendancesheet_eisberg', 'Jelenléti ív', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-wfm', 'page_title_attendancesheet_eisberg', 'Attendancesheet', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-wfm', 'attendancesheet_eisberg_header', 'JELENLÉTI ÍV', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-wfm', 'attendancesheet_eisberg_header', 'ATTENDANCE SHEET', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'tg_prog', 'Tg.Prog.', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'tg_prog', 'Tg.Prog.', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'eisberg_option2', '30% Műszak pótlék', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'eisberg_option2', '30% Shift bonus', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'eisberg_option3', '15% Műszak pótlék', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'eisberg_option3', 'Shift bonus 15%', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'eisberg_option4', 'Vasárnapi pótlék', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'eisberg_option4', 'Sunday bonus', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'eisberg_option5', 'Munkaszüneti napi pótlék', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'eisberg_option5', 'Holiday bonus', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'payment_management_for_period', 'Számfejtési időszak', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'payment_management_for_period', 'Payment management for period', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'accounting_days', 'Számfejtési napok', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'accounting_days', 'Accounting days', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'accounting_time', 'Számfejtés ideje', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'accounting_time', 'Accounting time', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'correction_accounting_period', 'Korrekciós számfejtési időszak', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'correction_accounting_period', 'Correction accounting period', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'correction_accounting_time', 'Korrekciós számfejtés ideje', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'correction_accounting_time', 'Correction accounting time', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'personal_code', 'Személyi kód', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'personal_code', 'Personal code', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'time_accounting', 'Időszámfejtés', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'time_accounting', 'Time accounting', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'time_bill', 'Időszámla', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'time_bill', 'Time bill', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'time_bill_monthly', 'Időszámla havi', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'time_bill_monthly', 'Time bill monthly', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'bill', 'Számla', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'bill', 'Bill', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'hours_of_overtime_entitlement', 'Túlóra jogosult órái', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'hours_of_overtime_entitlement', 'Hours of overtime entitlement', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'holiday_weekday_per_hour', 'Munkaszüneti nap hétköznap/óra', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'holiday_weekday_per_hour', 'Holiday weekday per hour', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'hours_payable', 'Kifizetendő óra', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'hours_payable', 'Hours payable', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'eisberg_work_start', 'kezdete', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'eisberg_work_start', 'start', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'eisberg_work_end', 'vége', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'eisberg_work_end', 'end', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'eisberg_period', 'Időtartam', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'eisberg_period', 'Period', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'eisberg_worked_hours', 'Ledolgozott óra napi', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'eisberg_worked_hours', 'Worked hours by day', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'eisberg_daily_balance', 'Napi egyenleg', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'eisberg_daily_balance', 'Daily balance', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'daily_wrapped_individual_adaptability', 'Göngyölt napi egyenleg', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'daily_wrapped_individual_adaptability', 'Daily wrapped individual adaptability', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('hu', 'ttwa-base', 'work_hour_day_shift', 'Délelőttös óra', '1');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES ('en', 'ttwa-base', 'work_hour_day_shift', 'Work hour day shift', '1');

INSERT INTO `menu_item_table` (`menu_item_id`, `menu_item_name`, `menu_modul`, `menu_label`, `menu_item_css_class`, `menu_url`, `menu_visible`, `menu_visible_operation`, `menu_item_parent_id`, `menu_order`) VALUES ('customers/eisberg/attendanceSheet', 'customers/eisberg/attendanceSheet', 'ttwa-wfm', 'menu_item_attendancesheet_eisberg', 'sub', '/customers/eisberg/attendanceSheet/index', 'customers/eisberg/attendanceSheet', 'view', '82', '158');

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('customers/eisberg/attendanceSheet', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2', 'system', '2022-04-22 09:00:00');

UPDATE `_sql_version` SET `revision`=13, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -13-2022-04-22-08:30------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev value: 0, DEV-12678' WHERE `setting_id` = 'usedWorktimeFrom' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=14, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -14-2022-05-17-14:00------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'page_title_payroll_evaluation', 'Bérszámfejtéhez táblázat kiértékelés', 1),
	('en', 'ttwa-base', 'page_title_payroll_evaluation', 'Payroll table evaluation', 1),
	('hu', 'ttwa-base', 'menu_item_payroll_evaluation', 'Bérszámfejtéhez táblázat kiértékelés', 1),
	('en', 'ttwa-base', 'menu_item_payroll_evaluation', 'Payroll table evaluation', 1);

INSERT INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
	('a5b6bd79e008725744118c7c46e10cda', 'payrollEvaluationView', 'SN-DEV-12778', NOW());

INSERT INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
	('payrollEvaluationView', 'customers/eisberg/payrollEvaluation --- view', '0', 'A bérszámfejtési táblázat kiértékelés menüponthot ad megtekinthetési jogot.', 'SN-DEV-12778', NOW());

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
	('payrollEvaluationView', 'customers/eisberg/payrollEvaluation', NULL, 'view', NULL, '1', '1', 'SN-DEV-12778', NOW());

INSERT INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,`menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`) VALUES
	('menu_item_payroll_evaluation', 'menu_item_payroll_evaluation', 'ttwa-base', 'menu_item_payroll_evaluation', 'sub', '/customers/eisberg/payrollEvaluation/index', 'customers/eisberg/payrollEvaluation', 'view', '82', 203);

INSERT INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
	('customers/eisberg/payrollEvaluation', 'payroll', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'fullname', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'emp_id', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'cost', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'cost_center', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'cost_type', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'comment', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'day', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'day_name', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'week_nr', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'month', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'nature', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'fact_area', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'worked_hours', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'ext_daily_worktime', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'ec_daily_worktime', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'worked_de', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'shift_bonus_30', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'shift_bonus_15', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'sunday_bonus', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'holiday_bonus', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'ext_daily_worktime_on_abs', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'overtime_100', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'is_restday', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'restday_daily_balance', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'restday_balance', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'downtime_ot_daily_balance', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'downtime_ot_balance', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'daily_balance_downtime_ot_plan', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW()),
	('customers/eisberg/payrollEvaluation', 'proc_prono', NULL, 'ALL', '2', NULL, 'SN-DEV-12778', NOW());

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-wfm', 'downtime_ot_daily_balance', 'Állásidő/túlóra Göngyölt napi adat számítás', 1),
	('en', 'ttwa-wfm', 'downtime_ot_daily_balance', 'Downtime/overtime rolled daily data calculation', 1),
	('hu', 'ttwa-wfm', 'downtime_ot_balance', 'Állásidő/túlóra göngyölt napi egyenleg', 1),
	('en', 'ttwa-wfm', 'downtime_ot_balance', 'Downtime/overtime rolled daily balance', 1),
	('hu', 'ttwa-wfm', 'daily_balance_downtime_ot_plan', 'Napi egyenleg állásidő/túlóra tervezéshez', 1),
	('en', 'ttwa-wfm', 'daily_balance_downtime_ot_plan', 'Daily balance for downtime/overtime planning', 1),
	('hu', 'ttwa-base', 'proc_prono', 'Proc/prono', 1),
	('en', 'ttwa-base', 'proc_prono', 'Proc/prono', 1);

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('customers/eisberg/payrollEvaluation', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2', 'SN-DEV-12778', NOW()),
    ('customers/eisberg/payrollEvaluation', 'date', 'EMPLOYEE_WITH_FROM_TO', 'combo', '0', '2', 'SN-DEV-12778', NOW());

UPDATE `_sql_version` SET `revision`=15, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -15-2022-06-08-16:00------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'page_title_attendance_evaluation', 'Jelenléti táblázat kiértékelés', 1),
	('en', 'ttwa-base', 'page_title_attendance_evaluation', 'Attendance table evaluation', 1),
	('hu', 'ttwa-base', 'menu_item_attendance_evaluation', 'Jelenléti táblázat kiértékelés', 1),
	('en', 'ttwa-base', 'menu_item_attendance_evaluation', 'Attendance table evaluation', 1);

INSERT INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
	('a5b6bd79e008725744118c7c46e10cda', 'attendanceEvaluationView', 'SN-DEV-12779', NOW());

INSERT INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
	('attendanceEvaluationView', 'customers/eisberg/attendanceEvaluation --- view', '0', 'A jelenléti táblázat kiértékelés menüponthot ad megtekinthetési jogot.', 'SN-DEV-12779', NOW());

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
	('attendanceEvaluationView', 'customers/eisberg/attendanceEvaluation', NULL, 'view', NULL, '1', '1', 'SN-DEV-12779', NOW());

INSERT INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,`menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`) VALUES
	('menu_item_attendance_evaluation', 'menu_item_attendance_evaluation', 'ttwa-base', 'menu_item_attendance_evaluation', 'sub', '/customers/eisberg/attendanceEvaluation/index', 'customers/eisberg/attendanceEvaluation', 'view', '82', 204);

INSERT INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
	('customers/eisberg/attendanceEvaluation', 'payroll', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'fullname', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'emp_id', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'cost', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'cost_center', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'cost_type', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'category_one', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'day', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'week_nr', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'month', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'nature', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'fact_area', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'worked_hours', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'ext_daily_worktime', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'ec_daily_worktime', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'downtime_overtime', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'workschedule', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'headcount', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'six_hour', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW()),
	('customers/eisberg/attendanceEvaluation', 'prono_headcount', NULL, 'ALL', '2', NULL, 'SN-DEV-12779', NOW());

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-wfm', 'six_hour', '6 órás', 1),
	('en', 'ttwa-wfm', 'six_hour', '6 hour', 1),
	('hu', 'ttwa-base', 'prono_headcount', 'Prono létszám', 1),
	('en', 'ttwa-base', 'prono_headcount', 'Prono headcount', 1),
	('hu', 'ttwa-wfm', 'downtime_overtime', 'Állásidő/túlóra', 1),
	('en', 'ttwa-wfm', 'downtime_overtime', 'Downtime/Overtime', 1),
	('hu', 'ttwa-base', 'category_one', 'Kategória 1', 1),
	('en', 'ttwa-base', 'category_one', 'Category 1', 1);

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('customers/eisberg/attendanceEvaluation', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2', 'SN-DEV-12779', NOW()),
    ('customers/eisberg/attendanceEvaluation', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2', 'SN-DEV-12779', NOW()),
    ('customers/eisberg/attendanceEvaluation', 'group', 'COMPANY_ORG_GROUP2', 'combo', '0', '2', 'SN-DEV-12779', NOW()),
    ('customers/eisberg/attendanceEvaluation', 'group', 'COMPANY_ORG_GROUP3', 'combo', '0', '2', 'SN-DEV-12779', NOW()),
    ('customers/eisberg/attendanceEvaluation', 'date', 'EMPLOYEE_WITH_FROM_TO', 'combo', '0', '2', 'SN-DEV-12779', NOW());

UPDATE `_sql_version` SET `revision`=16, `updated_on`=NOW() WHERE `module` = 'c_eisberg';


-- VERSION -16-2022-06-10-09:00------------------------------------------------

UPDATE
    `option_config`
SET
    `status` = 7,
    `modified_by` = 'DEV-13093',
    `modified_on` = NOW()
WHERE
    `status` = 2
    AND `option_id` IN (
    'ext2_option10',
    'ext2_option11',
    'ext2_option12',
    'ext2_option13',
    'ext2_option14',
    'ext2_option15',
    'ext2_option16',
    'ext2_option17',
    'ext2_option18',
    'ext2_option19',
    'ext2_option20',
    'ext2_option21',
    'ext2_option22',
    'ext2_option23',
    'ext2_option24',
    'ext2_option25',
    'ext2_option26',
    'ext2_option27',
    'ext2_option28',
    'ext2_option29',
    'ext2_option30',
    'ext2_option31',
    'ext2_option32',
    'ext2_option33',
    'ext2_option34',
    'ext2_option35',
    'ext2_option36',
    'ext2_option37',
    'ext2_option38',
    'ext2_option39',
    'ext2_option40',
    'ext2_option9',
    'ext3_option1',
    'ext3_option10',
    'ext3_option11',
    'ext3_option12',
    'ext3_option13',
    'ext3_option14',
    'ext3_option15',
    'ext3_option16',
    'ext3_option17',
    'ext3_option18',
    'ext3_option19',
    'ext3_option2',
    'ext3_option20',
    'ext3_option21',
    'ext3_option22',
    'ext3_option23',
    'ext3_option24',
    'ext3_option25',
    'ext3_option26',
    'ext3_option27',
    'ext3_option28',
    'ext3_option29',
    'ext3_option3',
    'ext3_option30',
    'ext3_option31',
    'ext3_option32',
    'ext3_option33',
    'ext3_option34',
    'ext3_option35',
    'ext3_option36',
    'ext3_option37',
    'ext3_option38',
    'ext3_option39',
    'ext3_option4',
    'ext3_option40',
    'ext3_option5',
    'ext3_option6',
    'ext3_option7',
    'ext3_option8',
    'ext3_option9',
    'ext4_option1',
    'ext4_option10',
    'ext4_option11',
    'ext4_option12',
    'ext4_option13',
    'ext4_option14',
    'ext4_option15',
    'ext4_option16',
    'ext4_option17',
    'ext4_option18',
    'ext4_option19',
    'ext4_option2',
    'ext4_option20',
    'ext4_option21',
    'ext4_option22',
    'ext4_option23',
    'ext4_option24',
    'ext4_option25',
    'ext4_option26',
    'ext4_option27',
    'ext4_option28',
    'ext4_option29',
    'ext4_option3',
    'ext4_option30',
    'ext4_option31',
    'ext4_option32',
    'ext4_option33',
    'ext4_option34',
    'ext4_option35',
    'ext4_option36',
    'ext4_option37',
    'ext4_option38',
    'ext4_option39',
    'ext4_option4',
    'ext4_option40',
    'ext4_option41',
    'ext4_option42',
    'ext4_option43',
    'ext4_option44',
    'ext4_option45',
    'ext4_option46',
    'ext4_option47',
    'ext4_option48',
    'ext4_option49',
    'ext4_option5',
    'ext4_option50',
    'ext4_option51',
    'ext4_option52',
    'ext4_option53',
    'ext4_option54',
    'ext4_option55',
    'ext4_option56',
    'ext4_option57',
    'ext4_option58',
    'ext4_option59',
    'ext4_option6',
    'ext4_option60',
    'ext4_option7',
    'ext4_option8',
    'ext4_option9',
    'ext5_option1',
    'ext5_option10',
    'ext5_option11',
    'ext5_option12',
    'ext5_option13',
    'ext5_option14',
    'ext5_option15',
    'ext5_option16',
    'ext5_option17',
    'ext5_option18',
    'ext5_option19',
    'ext5_option2',
    'ext5_option20',
    'ext5_option21',
    'ext5_option22',
    'ext5_option23',
    'ext5_option24',
    'ext5_option25',
    'ext5_option26',
    'ext5_option27',
    'ext5_option28',
    'ext5_option29',
    'ext5_option3',
    'ext5_option30',
    'ext5_option31',
    'ext5_option32',
    'ext5_option33',
    'ext5_option34',
    'ext5_option35',
    'ext5_option36',
    'ext5_option37',
    'ext5_option38',
    'ext5_option39',
    'ext5_option4',
    'ext5_option40',
    'ext5_option41',
    'ext5_option42',
    'ext5_option43',
    'ext5_option44',
    'ext5_option45',
    'ext5_option46',
    'ext5_option47',
    'ext5_option48',
    'ext5_option49',
    'ext5_option5',
    'ext5_option50',
    'ext5_option51',
    'ext5_option52',
    'ext5_option53',
    'ext5_option54',
    'ext5_option55',
    'ext5_option56',
    'ext5_option57',
    'ext5_option58',
    'ext5_option59',
    'ext5_option6',
    'ext5_option60',
    'ext5_option7',
    'ext5_option8',
    'ext5_option9'
    );

UPDATE `_sql_version` SET `revision`=17, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -17-2022-06-14-14:00------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev value: 0, DEV-13306' WHERE `setting_id` = 'summarySheet_regsWrongMissingHoverMessage' AND `valid` = 1;
UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev value: 0, DEV-13306' WHERE `setting_id` = 'summarySheet_regsTimeWrongMissingHoverMessage' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=18, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -18-2022-07-15-10:00------------------------------------------------

DELETE FROM `dictionary` WHERE `dict_id` IN ('page_title_payroll_evaluation', 'menu_item_payroll_evaluation') AND `lang` = 'hu';
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'page_title_payroll_evaluation', 'Bérszámfejtéshez táblázat kiértékelés', 1),
	('hu', 'ttwa-base', 'menu_item_payroll_evaluation', 'Bérszámfejtéshez táblázat kiértékelés', 1);

UPDATE `_sql_version` SET `revision`=19, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -19-2022-07-18-09:00------------------------------------------------

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('customers/eisberg/attendanceSheet', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2', 'SN-DEV-13389', NOW());

UPDATE `_sql_version` SET `revision`=20, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -20-2022-07-18-16:00------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev value: 0, DEV-13388' WHERE `setting_id` = 'workScheduleByGroupShortShowMonth' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=21, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -21-2022-07-18-16:15------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev value: 0, DEV-13358' WHERE `setting_id` = 'restday_balance_to_overtime' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=22, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -22-2022-08-08-16:15------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
    ('LOGA', 'FIGTA', 'state_type_id', NULL, 'absence_type_paid_dispbyemployer', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fiz. ig. távollét (közeli hozzát.)', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=23, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -23-2022-08-12-09:30------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev value: 0' WHERE `setting_id` = 'linkTypeORMode' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=24, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -24-2022-08-29-16:15------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev value: 0, DEV-13681' WHERE `setting_id` = 'summarySheet_regsRestDayMarking' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=25, `updated_on`=NOW() WHERE `module` = 'c_eisberg';

-- VERSION -25-2022-08-29-16:15------------------------------------------------

UPDATE `dictionary`
SET `dict_value` = 'Orvosi alkalmassági lejár'
WHERE `dict_id`  = 'ext2_option9'
  AND `lang`     = 'hu';

UPDATE `dictionary`
SET `dict_value` = 'Medical suitability expire'
WHERE `dict_id`  = 'ext2_option9'
  AND `lang`     = 'en';

UPDATE `dictionary`
SET `dict_value` = 'Tüdőszűrés érvényessége lejár'
WHERE `dict_id`  = 'ext2_option10'
  AND `lang`     = 'hu';

UPDATE `dictionary`
SET `dict_value` = 'Lung screening expire'
WHERE `dict_id`  = 'ext2_option10'
  AND `lang`     = 'en';

INSERT INTO `dictionary`
(`lang`, `module`, `dict_id`, `dict_value`, `valid`)
VALUES
('hu', 'ttwa-base', 'expired_medical_suitability_notice', 'A következő kollégák orvosi alkalmassági/tüdőszűrő vizsgálatainak érvényességi ideje a következő hónapban lejár:', 1),
('en', 'ttwa-base', 'expired_medical_suitability_notice', 'The validity period of the medical suitability/lung screening tests of the following colleagues will expire next month:', 1);

UPDATE
    `option_config`
SET
    `type`        = 'dPicker',
    `status`      = 2,
    `modified_by` = 'DEV-11850',
    `modified_on` = NOW()
WHERE
    `option_id`   = 'ext2_option9';

UPDATE
    `option_config`
SET
    `type`        = 'dPicker',
    `status`      = 2,
    `modified_by` = 'DEV-11850',
    `modified_on` = NOW()
WHERE
    `option_id`   = 'ext2_option10';


UPDATE `_sql_version` SET `revision`=26, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -26-2022-09-06-15:15-----DEV-11850a-----------------------------------------

INSERT INTO `dictionary`
(`lang`, `module`,  `dict_id`,                         `dict_value`,                `valid`)
VALUES
('hu', 'ttwa-base', 'type_of_suitability_examination', 'Alkalmassági vizsgálat típusa',   1),
('en', 'ttwa-base', 'type_of_suitability_examination', 'Type of suitability examination', 1),
('hu', 'ttwa-base', 'medical_suitability',             'Orvosi alkalmassági vizsgálat',   1),
('en', 'ttwa-base', 'medical_suitability',             'Medical suitability examination', 1),
('hu', 'ttwa-base', 'lung_screening',                  'Tüdőszűrés',                      1),
('en', 'ttwa-base', 'lung_screening',                  'Lung screening',                  1);

INSERT INTO `notification_email_config` (
    `process_id`,
    `sql`,
    `addresses`,
    `day_before_event`,
    `file_name`,
    `subject`,
    `message_text_dict_id`,
    `note`,
    `status`,
    `sendEmptyNotificationEmail`
) VALUES (
    'ExpiredMedicalSuitabilityNotice',
    '   SELECT
        `e`.`emp_id`,
        `ec`.`employee_id`,
        `comp`.`company_name`,
        {full_name} AS `fullname`,
        CASE
            WHEN `ext2`.`ext2_option9` IS NULL OR `ext2`.`ext2_option9` = '' THEN `ext2`.`ext2_option10`
            ELSE `ext2`.`ext2_option9`
        END AS `valid_to`,
        CASE
            WHEN `ext2`.`ext2_option9` IS NULL OR `ext2`.`ext2_option9` = '' THEN "lung_screening"
            ELSE "medical_suitability"
        END AS `dict_id_of_suitability_examination`
    FROM
        `employee_ext2` `ext2`
    INNER JOIN
        `employee` `e`
        ON `ext2`.`employee_id` = `e`.`employee_id`
        AND CURDATE() BETWEEN `e`.`valid_from` AND IFNULL(`e`.`valid_to`, "2038-01-01")
        AND `e`.`status` = 2
    INNER JOIN
        `company` `comp`
        ON `e`.`company_id` = `comp`.`company_id`
        AND CURDATE() BETWEEN `comp`.`valid_from` AND IFNULL(`comp`.`valid_to`, "2038-01-01")
        AND `comp`.`status` = 2
    INNER JOIN
        `employee_contract` `ec`
        ON  `ec`.`status` = 2
        AND `e`.`employee_id` = `ec`.`employee_id`
        AND CURDATE() BETWEEN `ec`.`valid_from` AND IFNULL(`ec`.`valid_to`, "2038-01-01")
        AND CURDATE() BETWEEN `ec`.`ec_valid_from` AND IFNULL(`ec`.`ec_valid_to`, "2038-01-01")
    WHERE
        `ext2`.`status` = 2
        AND (
            `ext2`.`ext2_option9` BETWEEN
                DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
                AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
            OR
            `ext2`.`ext2_option10`  BETWEEN
                DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
                AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
        )
    ORDER BY `valid_to`, `fullname`',
    '<EMAIL>',
    15,
    'expiredMedicalSuitabilityNotice',
    'Jövő hónapban lejáró orvosi alkalmasság',
    'expired_medical_suitability_notice',
    'Az orvosi alkalmassági/tüdőszűrő vizsgálatok következő hónapban lejáró érvényességi idejére figyelmeztet',
    2,
    1
);

UPDATE `_sql_version` SET `revision`=27, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -27-2022-09-07-13:15-----DEV-11850b-----------------------------------------

UPDATE `dictionary`
SET `dict_value` = 'Targonca alkalmassági lejár'
WHERE `dict_id`  = 'ext2_option11'
  AND `lang`     = 'hu';

UPDATE `dictionary`
SET `dict_value` = 'Forklift suitability expires'
WHERE `dict_id`  = 'ext2_option11'
  AND `lang`     = 'en';

INSERT INTO `dictionary`
(`lang`, `module`,  `dict_id`,                             `dict_value`,                                                                                                         `valid`)
VALUES
('hu', 'ttwa-base', 'expired_forklift_suitability_notice', 'A következő kollégák targoncavezetői orvosi alkalmassági vizsgálatának érvényességi ideje a következő hónapban lejár:',    1),
('en', 'ttwa-base', 'expired_forklift_suitability_notice', 'The validity period of the forklift driver medical suitability tests of the following colleagues will expire next month:', 1),
('hu', 'ttwa-base', 'forklift_suitability',                'Targoncavezetői alkalmassági vizsgálat',                                                                                   1),
('en', 'ttwa-base', 'forklift_suitability',                'Forklift driver suitability examination',                                                                                  1);

UPDATE `option_config`
SET `type`        = 'dPicker',
    `status`      = 2,
    `modified_by` = 'DEV-11851',
    `modified_on` = NOW()
WHERE `option_id` = 'ext2_option11';

INSERT INTO `notification_email_config` (
    `process_id`,
    `sql`,
    `addresses`,
    `day_before_event`,
    `file_name`,
    `subject`,
    `message_text_dict_id`,
    `note`,
    `status`,
    `sendEmptyNotificationEmail`
) VALUES (
    'ExpiredForkliftSuitabilityNotice',
    '   SELECT
        `e`.`emp_id`,
        `ec`.`employee_id`,
        `comp`.`company_name`,
        {full_name} AS `fullname`,
        `ext2`.`ext2_option11` AS `valid_to`
    FROM
        `employee_ext2` `ext2`
    INNER JOIN
        `employee` `e`
        ON `ext2`.`employee_id` = `e`.`employee_id`
        AND CURDATE() BETWEEN `e`.`valid_from` AND IFNULL(`e`.`valid_to`, "2038-01-01")
        AND `e`.`status` = 2
    INNER JOIN
        `company` `comp`
        ON `e`.`company_id` = `comp`.`company_id`
        AND CURDATE() BETWEEN `comp`.`valid_from` AND IFNULL(`comp`.`valid_to`, "2038-01-01")
        AND `comp`.`status` = 2
    INNER JOIN
        `employee_contract` `ec`
        ON  `ec`.`status` = 2
        AND `e`.`employee_id` = `ec`.`employee_id`
        AND CURDATE() BETWEEN `ec`.`valid_from` AND IFNULL(`ec`.`valid_to`, "2038-01-01")
        AND CURDATE() BETWEEN `ec`.`ec_valid_from` AND IFNULL(`ec`.`ec_valid_to`, "2038-01-01")
    WHERE
        `ext2`.`status` = 2
        AND `ext2`.`ext2_option11` BETWEEN
            DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
            AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
    ORDER BY `valid_to`, `fullname`',
    '<EMAIL>',
    15,
    'expiredForkliftSuitabilityNotice',
    'Jövő hónapban lejáró targoncavezetői orvosi alkalmasság',
    'expired_forklift_suitability_notice',
    'A targoncavezetői orvosi alkalmassági vizsgálat következő hónapban lejáró érvényességi idejére figyelmeztet (DEV-11851)',
    2,
    1
);

UPDATE `_sql_version` SET `revision`=28, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -28-2022-09-08-10:15-----DEV-11851 -----------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Kapcsolat' WHERE `dict_id` = 'tab_employeetabs_employeeext3' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Contact' WHERE `dict_id` = 'tab_employeetabs_employeeext3' AND `lang` = 'en';
UPDATE `option_config` SET `status` = 2, `modified_by` = 'SN-DEV-13769', `modified_on` = NOW() WHERE `option_id` IN ('ext3_option1', 'ext3_option2', 'ext3_option2', 'ext3_option3', 'ext3_option4', 'ext3_option5', 'ext3_option6', 'ext3_option7', 'ext3_option8');
UPDATE `dictionary` SET `dict_value` = 'Ország' WHERE `dict_id` = 'ext3_option1' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Country' WHERE `dict_id` = 'ext3_option1' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Irányítószám' WHERE `dict_id` = 'ext3_option2' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'ZIP code' WHERE `dict_id` = 'ext3_option2' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Város' WHERE `dict_id` = 'ext3_option3' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'City' WHERE `dict_id` = 'ext3_option3' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Utca' WHERE `dict_id` = 'ext3_option4' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Street' WHERE `dict_id` = 'ext3_option4' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Hozzátartozó neve' WHERE `dict_id` = 'ext3_option5' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Relative name' WHERE `dict_id` = 'ext3_option5' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kapcsolati jelleg' WHERE `dict_id` = 'ext3_option6' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Connection type' WHERE `dict_id` = 'ext3_option6' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Dolgozó telefonszáma' WHERE `dict_id` = 'ext3_option7' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Employees phone number' WHERE `dict_id` = 'ext3_option7' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Hozzátartozó telefonszáma' WHERE `dict_id` = 'ext3_option8' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Relatives phone number' WHERE `dict_id` = 'ext3_option8' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=29, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -29-2022-09-08-14:15--------------------------------------------------------

UPDATE `employee_contract` ec
JOIN `employee` e ON
        e.`status` = 2
    AND e.`employee_id` = ec.`employee_id`
    AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '2038-01-01')
JOIN `employee_ext2` ext2 ON
        ext2.`status` = 2
    AND ext2.`employee_id` = e.`employee_id`
    AND CURDATE() BETWEEN ext2.`valid_from` AND IFNULL(ext2.`valid_to`, '2038-01-01')
    AND ext2.`ext2_option7` IS NOT NULL
    AND ext2.`ext2_option7` <> ''
    AND ext2.`ext2_option7` <> ' '
SET
    ec.`note` = ec.`daily_worktime`,
    ec.`daily_worktime` = ext2.`ext2_option7`,
    ec.`modified_by` = 'SN-DEV-13766',
    ec.`modified_on` = NOW()
WHERE
        ec.`status` = 2
    AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '2038-01-01')
    AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '2038-01-01');

UPDATE `employee_ext2` ext2
JOIN `employee` e ON
        e.`status` = 2
    AND e.`employee_id` = ext2.`employee_id`
    AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '2038-01-01')
JOIN `employee_contract` ec ON
        ec.`status` = 2
    AND ec.`employee_id` = e.`employee_id`
    AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '2038-01-01')
    AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '2038-01-01')
    AND ec.`note` IS NOT NULL
    AND ec.`note` <> ''
    AND ec.`note` <> ' '
SET
    ext2.`ext2_option7` = ec.`note`,
    ext2.`modified_by` = 'SN-DEV-13766',
    ext2.`modified_on` = NOW()
WHERE
        ext2.`status` = 2
    AND CURDATE() BETWEEN ext2.`valid_from` AND IFNULL(ext2.`valid_to`, '2038-01-01');

UPDATE `employee_contract` ec
SET
    ec.`note` = NULL,
    ec.`modified_by` = 'SN-DEV-13766',
    ec.`modified_on` = NOW()
WHERE
        ec.`status` = 2
    AND ec.`modified_by` = 'SN-DEV-13766'
    AND ec.`note` IS NOT NULL
    AND ec.`note` <> ''
    AND ec.`note` <> ' '
    AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '2038-01-01')
    AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '2038-01-01');

UPDATE `dictionary` SET `dict_value` = 'Napi munkaidő' WHERE `dict_id` = 'ext2_option7' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Daily worktime' WHERE `dict_id` = 'ext2_option7' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Szerződés szerinti óra' WHERE `dict_id` = 'daily_worktime' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Contract worktime' WHERE `dict_id` = 'daily_worktime' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=30, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -30-2022-09-08-14:45--------------------------------------------------------

INSERT INTO `dictionary`
(`lang`, `module`,  `dict_id`,                      `dict_value`,                                                      `valid`)
VALUES
('hu', 'ttwa-base', 'employee_test_reqired_notice', 'Az alábbi kollégák dolgozói tesztje a mai nappal aktuálissá válik:',    1),
('en', 'ttwa-base', 'employee_test_reqired_notice', 'The employee test of the following colleagues becomes relevant today:', 1),
('hu', 'ttwa-base', '1m_test_reqired',              '1. havi teszt szükséges',                                               1),
('en', 'ttwa-base', '1m_test_reqired',              '1st monthly test required',                                             1),
('hu', 'ttwa-base', '3m_test_reqired',              '3. havi teszt szükséges',                                               1),
('en', 'ttwa-base', '3m_test_reqired',              '3rd monthly test required',                                             1),
('hu', 'ttwa-base', '6m_test_reqired',              '6. havi teszt szükséges',                                               1),
('en', 'ttwa-base', '6m_test_reqired',              '6th monthly test required',                                             1),
('hu', 'ttwa-base', '1y_test_reqired',              '12. havi teszt szükséges',                                              1),
('en', 'ttwa-base', '1y_test_reqired',              '12th monthly test required',                                            1);

INSERT INTO `notification_email_config` (
    `process_id`,
    `sql`,
    `addresses`,
    `day_before_event`,
    `file_name`,
    `subject`,
    `message_text_dict_id`,
    `note`,
    `status`,
    `sendEmptyNotificationEmail`
) VALUES (
    'EmployeeTestReqiredNotice',
    '   SELECT
        {full_name} AS `fullname`,
        `e`.`emp_id`,
        `comp`.`company_name`,
        `cc`.`cost_center_name`,
        `ec`.`ec_valid_from`,
        CASE
            WHEN
                DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 1 YEAR,  "%Y-%m-%d") = CURDATE()
            THEN "1y_test_reqired"
            WHEN
                DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 6 MONTH, "%Y-%m-%d") = CURDATE()
            THEN "6m_test_reqired"
            WHEN
                DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 3 MONTH, "%Y-%m-%d") = CURDATE()
            THEN "3m_test_reqired"
            WHEN
                DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 1 MONTH, "%Y-%m-%d") = CURDATE()
            THEN "1m_test_reqired"
        END AS `dict_id`
    FROM
        `employee_contract` `ec`
    INNER JOIN
        `employee` `e`
        ON `ec`.`employee_id` = `e`.`employee_id`
        AND CURDATE() BETWEEN `e`.`valid_from` AND IFNULL(`e`.`valid_to`, "2038-01-01")
        AND `e`.`status` = 2
    INNER JOIN
        `company` `comp`
        ON `e`.`company_id` = `comp`.`company_id`
        AND CURDATE() BETWEEN `comp`.`valid_from` AND IFNULL(`comp`.`valid_to`, "2038-01-01")
        AND `comp`.`status` = 2
    INNER JOIN
        `employee_cost` `eco`
        ON `ec`.`employee_contract_id` = `eco`.`employee_contract_id`
        AND CURDATE() BETWEEN `eco`.`valid_from` AND IFNULL(`eco`.`valid_to`, "2038-01-01")
        AND `eco`.`status` = 2
    INNER JOIN
        `cost_center` `cc`
        ON `cc`.`cost_center_id` = `eco`.`cost_center_id`
        AND CURDATE() BETWEEN `cc`.`valid_from` AND IFNULL(`cc`.`valid_to`, "2038-01-01")
        AND `cc`.`status` = 2
    WHERE
        `ec`.`status` = 2
        AND `ec`.`employee_id` = `e`.`employee_id`
        AND CURDATE() BETWEEN `ec`.`valid_from` AND IFNULL(`ec`.`valid_to`, "2038-01-01")
        AND CURDATE() BETWEEN `ec`.`ec_valid_from` AND IFNULL(`ec`.`ec_valid_to`, "2038-01-01")
        AND (
            DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 1 YEAR,  "%Y-%m-%d") = CURDATE()
            OR
            DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 6 MONTH, "%Y-%m-%d") = CURDATE()
            OR
            DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 3 MONTH, "%Y-%m-%d") = CURDATE()
            OR
            DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 1 MONTH, "%Y-%m-%d") = CURDATE()
        )
    ORDER BY `ec_valid_from` DESC, `fullname` ASC',
    '<EMAIL>;<EMAIL>;<EMAIL>',
    1,
    'employeeTestReqiredNotice',
    '1-3-6-12. havi teszt szükséges',
    'employee_test_reqired_notice',
    '1-3-6-12. havi dolgozói teszt esedékességére figyelmeztet (DEV-11849)',
    2,
    1
);

UPDATE `_sql_version` SET `revision`=31, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -31-2022-09-09-13:45---------DEV-11849--------------------------------------

UPDATE `app_settings` SET `setting_value` = 'KULCSSOFT', `note` = 'Prev value: LOGA , SN-DEV-14013' WHERE `setting_id` = 'ptr_default_mode';
UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev value: 0 , SN-DEV-14013' WHERE `setting_id` = 'ptr_loga_payroll';
UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev val: 0, SN-DEV-14013' WHERE `setting_id` = 'summarySheet_calculation_standby_sign';
UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev val: 0, SN-DEV-14013' WHERE `setting_id` = 'unusedRestdayToPaidot100';
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
    ('KULCSSOFT', 'JOV_ALLOT', 'inside_type_id', NULL, NULL, 0, 'data.`schedule_overtime_full_time` + data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`schedule_overtime_full_time` > 0) OR (data.`inside_type_id` LIKE \"ot%\")', 'Túlmunka alapdíj', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_OT', 'inside_type_id', NULL, NULL, 0, 'data.`schedule_overtime_full_time` + data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`schedule_overtime_full_time` > 0 AND data.`wsu_type_of_daytype` != \"RESTDAY\") OR (data.`inside_type_id` LIKE \"ot%\" AND data.`inside_type_id` NOT LIKE \"otw%\" AND data.`inside_type_id` NOT LIKE \"otstw%\")', 'Túlmunka pótlék 50%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_OTW', 'inside_type_id', NULL, NULL, 0, 'data.`schedule_overtime_full_time` + data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`schedule_overtime_full_time` > 0 AND data.`wsu_type_of_daytype` = \"RESTDAY\") OR (data.`inside_type_id` LIKE \"otw%\" OR data.`inside_type_id` LIKE \"otstw%\")', 'Túlmunka pótlék 100%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_PAIDOT', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` LIKE \"paidot%\"', 'Munkaidőkereten felüli túlmunka alapdíj', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_PAIDOT50', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"paidot_50\"', 'Munkaidőkereten felüli túlmunka 50%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_PAIDOT100', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` = \"paidot_100\"', 'Munkaidőkereten felüli túlmunka 100%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_STD', 'inside_type_id', NULL, NULL, 0, 'data.`schedule_standby_full_time` - data.`dt__overtime_st_sum` - data.`schedule_overtime_full_time`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`schedule_standby_full_time` > 0 AND ((data.`inside_type_id` NOT LIKE \"absredempt%\" AND data.`inside_type_id` NOT LIKE \"paidot%\") OR data.`inside_type_id` IS NULL) AND (data.`schedule_standby_full_time` - data.`dt__overtime_st_sum` - data.`schedule_overtime_full_time`) > 0', 'Készenlét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_DUTY', 'inside_type_id', NULL, NULL, 0, 'data.`schedule_duty_full_time` - data.`dt__base_overtime_sum` - data.`schedule_overtime_full_time`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`schedule_duty_full_time` > 0 AND ((data.`inside_type_id` NOT LIKE \"absredempt%\" AND data.`inside_type_id` NOT LIKE \"paidot%\") OR data.`inside_type_id` IS NULL) AND (data.`schedule_duty_full_time` - data.`dt__base_overtime_sum` - data.`schedule_overtime_full_time`) > 0', 'Ügyelet (40%)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_SUN', 'inside_type_id', NULL, NULL, 0, 'data.`schedule_overtime_full_time` + data.`dt__worktime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`date_day_of_week` = 1 AND data.`inside_type_id` NOT LIKE \"paidot%\"', 'Vasárnapi pótlék', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_MSZP', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`is_public_holiday` = 1 AND data.`inside_type_id` NOT LIKE \"paidot%\"', 'Mszüneti napi pótlék', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_DU2', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` IN (\"wtdu2\",\"otdu2\",\"otwdu2\",\"otstdu2\",\"otstwdu2\") AND IF(data.`mnth__total_schedule_sum` > 0, (data.`mnth__total_wtej_sum` + data.`mnth__total_wtdu1_sum` + data.`mnth__total_wtdu2_sum` + data.`mnth__total_absence_wtej_sum` + data.`mnth__total_absence_wtdu1_sum` + data.`mnth__total_absence_wtdu2_sum`) / data.`mnth__total_schedule_sum` >= 0.33, 0)', 'Műszakpótlék', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JOV_EJ', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` IN (\"wtej\",\"otej\",\"otwej\",\"otstej\",\"otstwej\") AND data.`dt__total_wtej_sum` + data.`dt__absence_wtej_sum` + data.`dt__total_otej_sum` + data.`dt__total_otwej_sum` >= 1 * 3600', 'Éjszakai pótlék', 2, 'ptc_sys', NULL, NULL, NULL, NULL),

    ('KULCSSOFT', 'MNKR1_P', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`is_restday` = 1', 'Pihenőnap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'MNKR1_X', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`dt_daily_worktime` > 0', 'Munkanap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'MNKR1_F', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`is_public_holiday` = 1', 'Ünnepnap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),

    ('KULCSSOFT', 'JETA1_M', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`dt_daily_worktime` > 0', 'Munkanap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_P', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`is_restday` = 1', 'Pihenőnap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_F', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`is_public_holiday` = 1', 'Ünnepnap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),

    ('KULCSSOFT', 'JETA1_Sz', 'state_type_id', NULL, 'a272f564576d443e7832587126b070aa', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fizetett szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_B', 'state_type_id', NULL, '3ad610cfce362896dbb4b11858dfae40', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Betegség', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_IF', 'state_type_id', NULL, 'f4f63ae4dd65cd97ee1f409d8b620806', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fizetett, igazolt', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_INF', 'state_type_id', NULL, '269b59b4efbbb4ef1d527492dc06cb60', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Igazolt, nem fiz', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_IGLAN', 'state_type_id', NULL, 'def32968390fb987c823da0cbf7d3bd8', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Igazolatlan', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_APA', 'state_type_id', NULL, 'ebf4ac30e7dc238fd2f4bc86332e0675', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Apanap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_FIZNÉLK', 'state_type_id', NULL, '628834878c5bc72f60283e37865678b6', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fizetésnélküli szabi', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_TANSZ', 'state_type_id', NULL, '5382dfe7cad1651991076b4e0af903ba', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Tanulmányi szabi', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_FELM', 'state_type_id', NULL, '90b9dd9dcbdc86ec8acaad81fd6add2b', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Felmentés', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'JETA1_HH', 'state_type_id', NULL, 'cffaabff749860f85840377da9f315cf', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Közeli hozzátartozó halála', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=32, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -32-2022-10-06-13:00--------------------------------------------------------

UPDATE
    `employee_ext2`
SET
    `valid_to`    = '2021-12-31',
    `modified_by` = 'DEV-14046',
    `modified_on` = NOW()
WHERE
    `status` = 2
    AND `valid_from` < '2022-01-01';

UPDATE
    `employee_ext2`
SET
    `status`      = 7,
    `modified_by` = 'DEV-14046',
    `modified_on` = NOW()
WHERE
    `status` = 2
    AND `valid_from` > '2022-01-01';

UPDATE `_sql_version` SET `revision`=33, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -33-2022-10-12-10:36-----------------------------------------------DEV-14046

UPDATE `dictionary` SET `dict_value` = 'Bell adatok' WHERE `dict_id` = 'tab_employeetabs_employeeext4' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Bell data' WHERE `dict_id` = 'tab_employeetabs_employeeext4' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Első belépés dátuma' WHERE `dict_id` = 'ext4_option1' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'First join date' WHERE `dict_id` = 'ext4_option1' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'PC felhasználó' WHERE `dict_id` = 'ext4_option2' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'PC user' WHERE `dict_id` = 'ext4_option2' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Management szint' WHERE `dict_id` = 'ext4_option3' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Management level' WHERE `dict_id` = 'ext4_option3' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Manager ID' WHERE `dict_id` = 'ext4_option4' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Manager ID' WHERE `dict_id` = 'ext4_option4' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'FTE' WHERE `dict_id` = 'ext4_option6' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'FTE' WHERE `dict_id` = 'ext4_option6' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Foglalkoztatási terület' WHERE `dict_id` = 'ext4_option7' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Employment area' WHERE `dict_id` = 'ext4_option7' AND `lang` = 'en';

UPDATE `option_config` SET `status` = 7, `modified_by` = 'SN-DEV-14109', `modified_on` = NOW() WHERE `option_id` NOT IN ('ext4_option1', 'ext4_option2', 'ext4_option3', 'ext4_option4', 'ext4_option6', 'ext4_option7') AND `option_id` LIKE 'ext4_option%';
UPDATE `option_config` SET `type` = 'combo', `status` = 2, `modified_by` = 'SN-DEV-14109', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option2', 'ext4_option3', 'ext4_option6', 'ext4_option7');
UPDATE `option_config` SET `type` = 'dPicker', `status` = 2, `modified_by` = 'SN-DEV-14109', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option1');
UPDATE `option_config` SET `type` = 'ed', `status` = 2, `modified_by` = 'SN-DEV-14109', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option4');
INSERT INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
    ('ext4_option2', 'IGEN', 0, 'yes', 1),
    ('ext4_option2', 'NEM', 1, 'no', 1),
    ('ext4_option3', '10', 1, 'ext4_option3_10', 1),
    ('ext4_option3', '20', 0, 'ext4_option3_20', 1),
    ('ext4_option3', '30', 0, 'ext4_option3_30', 1),
    ('ext4_option3', '40', 0, 'ext4_option3_40', 1),
    ('ext4_option3', '50', 0, 'ext4_option3_50', 1),
    ('ext4_option3', '60', 0, 'ext4_option3_60', 1),
    ('ext4_option3', '99', 0, 'ext4_option3_99', 1),
    ('ext4_option6', '100', 1, 'ext4_option6_100', 1),
    ('ext4_option6', '75', 0, 'ext4_option6_75', 1),
    ('ext4_option6', '50', 0, 'ext4_option6_50', 1),
    ('ext4_option7', 'prod', 1, 'ext4_option7_prod', 1),
    ('ext4_option7', 'prod-rel-func', 0, 'ext4_option7_prod-rel-func', 1),
    ('ext4_option7', 'it', 0, 'ext4_option7_it', 1),
    ('ext4_option7', 'procure-scm', 0, 'ext4_option7_procure-scm', 1),
    ('ext4_option7', 'sales-marketing', 0, 'ext4_option7_sales-marketing', 1),
    ('ext4_option7', 'mngmt-admin', 0, 'ext4_option7_mngmt-admin', 1);

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'ext4_option3_10', '10', 1),
	('en', 'ttwa-base', 'ext4_option3_10', '10', 1),
    ('hu', 'ttwa-base', 'ext4_option3_20', '20', 1),
	('en', 'ttwa-base', 'ext4_option3_20', '20', 1),
    ('hu', 'ttwa-base', 'ext4_option3_30', '30', 1),
	('en', 'ttwa-base', 'ext4_option3_30', '30', 1),
    ('hu', 'ttwa-base', 'ext4_option3_40', '40', 1),
	('en', 'ttwa-base', 'ext4_option3_40', '40', 1),
    ('hu', 'ttwa-base', 'ext4_option3_50', '50', 1),
	('en', 'ttwa-base', 'ext4_option3_50', '50', 1),
    ('hu', 'ttwa-base', 'ext4_option3_60', '60', 1),
	('en', 'ttwa-base', 'ext4_option3_60', '60', 1),
    ('hu', 'ttwa-base', 'ext4_option3_99', '99', 1),
	('en', 'ttwa-base', 'ext4_option3_99', '99', 1),
    ('hu', 'ttwa-base', 'ext4_option6_100', '100', 1),
	('en', 'ttwa-base', 'ext4_option6_100', '100', 1),
    ('hu', 'ttwa-base', 'ext4_option6_75', '75', 1),
	('en', 'ttwa-base', 'ext4_option6_75', '75', 1),
    ('hu', 'ttwa-base', 'ext4_option6_50', '50', 1),
	('en', 'ttwa-base', 'ext4_option6_50', '50', 1),
    ('hu', 'ttwa-base', 'ext4_option7_prod', 'Production', 1),
	('en', 'ttwa-base', 'ext4_option7_prod', 'Production', 1),
    ('hu', 'ttwa-base', 'ext4_option7_prod-rel-func', 'Production-related functions', 1),
	('en', 'ttwa-base', 'ext4_option7_prod-rel-func', 'Production-related functions', 1),
    ('hu', 'ttwa-base', 'ext4_option7_it', 'Information Technology/IT', 1),
	('en', 'ttwa-base', 'ext4_option7_it', 'Information Technology/IT', 1),
    ('hu', 'ttwa-base', 'ext4_option7_procure-scm', 'Procurement/SCM', 1),
	('en', 'ttwa-base', 'ext4_option7_procure-scm', 'Procurement/SCM', 1),
    ('hu', 'ttwa-base', 'ext4_option7_sales-marketing', 'Sales/Marketing', 1),
	('en', 'ttwa-base', 'ext4_option7_sales-marketing', 'Sales/Marketing', 1),
    ('hu', 'ttwa-base', 'ext4_option7_mngmt-admin', 'Management/Administration', 1),
	('en', 'ttwa-base', 'ext4_option7_mngmt-admin', 'Management/Administration', 1);

UPDATE `_sql_version` SET `revision`=34, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -34-2022-10-17-13:30--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'ALL', `note` = 'Prev value: NONE , DEV-14167' WHERE `setting_id` = 'workedBelowNeedException';

UPDATE `_sql_version` SET `revision`=35, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -35-2022-10-25-13:30--------------------------------------------------------

INSERT INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
	('customers/eisberg/payrollEvaluation', 'absence_name', NULL, 'ALL', '2', NULL, 'SN-DEV-13775', NOW());

UPDATE `_sql_version` SET `revision`=36, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -36-2022-10-27-15:00--------------------------------------------------------

UPDATE cost_center SET note = 'Purchasing' WHERE cost_center_id = '6';
UPDATE cost_center SET note = 'Packaging area' WHERE cost_center_id = '14';
UPDATE cost_center SET note = 'Trimming area' WHERE cost_center_id = '10';
UPDATE cost_center SET note = 'Picking area' WHERE cost_center_id = '15';
UPDATE cost_center SET note = 'EXT' WHERE cost_center_id = '9';
UPDATE cost_center SET note = 'Retail product development' WHERE cost_center_id = '8';
UPDATE cost_center SET note = 'IT' WHERE cost_center_id = '7';
UPDATE cost_center SET note = 'Commissioning area' WHERE cost_center_id = '18';
UPDATE cost_center SET note = 'Cardboard folding area' WHERE cost_center_id = '19';
UPDATE cost_center SET note = 'Managment' WHERE cost_center_id = '1';
UPDATE cost_center SET note = 'Quality' WHERE cost_center_id = '3';
UPDATE cost_center SET note = 'Raw material handler' WHERE cost_center_id = '11';
UPDATE cost_center SET note = 'Warehouse' WHERE cost_center_id = '20';
UPDATE cost_center SET note = '(Optical sorter) operator' WHERE cost_center_id = '12';
UPDATE cost_center SET note = 'Finance' WHERE cost_center_id = '2';
UPDATE cost_center SET note = 'Centrifuge operator' WHERE cost_center_id = '13';
UPDATE cost_center SET note = 'Sales&marketing' WHERE cost_center_id = '5';
UPDATE cost_center SET note = 'Social cleaner' WHERE cost_center_id = '21';
UPDATE cost_center SET note = 'Tray packaging area' WHERE cost_center_id = '16';
UPDATE cost_center SET note = 'Technica' WHERE cost_center_id = '23';
UPDATE cost_center SET note = 'Maintenance' WHERE cost_center_id = '24';
UPDATE cost_center SET note = 'Production management' WHERE cost_center_id = '17';
UPDATE cost_center SET note = 'Factory cleaner' WHERE cost_center_id = '22';
UPDATE cost_center SET note = 'Customer services' WHERE cost_center_id = '4';

UPDATE employee_position SET note = 'Back office&HR software kontroller' WHERE employee_position_id = '18';
UPDATE employee_position SET note = 'Back office&HR software coordinator' WHERE employee_position_id = '91';
UPDATE employee_position SET note = 'Internal quality controller' WHERE employee_position_id = '19';
UPDATE employee_position SET note = 'Payroll, - SS, - labor officer' WHERE employee_position_id = '20';
UPDATE employee_position SET note = 'Payroll contact' WHERE employee_position_id = '81';
UPDATE employee_position SET note = 'Deputy Purchasing manager' WHERE employee_position_id = '21';
UPDATE employee_position SET note = 'Deputy Managing Director /Purchasing manager' WHERE employee_position_id = '22';
UPDATE employee_position SET note = 'Purchaser' WHERE employee_position_id = '23';
UPDATE employee_position SET note = 'Technical manager Eisberg group' WHERE employee_position_id = '24';
UPDATE employee_position SET note = 'CSB consultant' WHERE employee_position_id = '25';
UPDATE employee_position SET note = 'Packer' WHERE employee_position_id = '26';
UPDATE employee_position SET note = 'Head of Packer Unit' WHERE employee_position_id = '27';
UPDATE employee_position SET note = 'Packer operator' WHERE employee_position_id = '82';
UPDATE employee_position SET note = 'Preparator' WHERE employee_position_id = '28';
UPDATE employee_position SET note = 'Head of Preparator Unit' WHERE employee_position_id = '29';
UPDATE employee_position SET note = 'Picker' WHERE employee_position_id = '30';
UPDATE employee_position SET note = 'Head of Picker Unit' WHERE employee_position_id = '31';
UPDATE employee_position SET note = 'Process development assistant' WHERE employee_position_id = '85';
UPDATE employee_position SET note = 'Process development leader' WHERE employee_position_id = '32';
UPDATE employee_position SET note = 'Process developer' WHERE employee_position_id = '33';
UPDATE employee_position SET note = 'HR manager' WHERE employee_position_id = '34';
UPDATE employee_position SET note = 'ERP Specialist' WHERE employee_position_id = '35';
UPDATE employee_position SET note = 'CSB Consulting and Application Manager' WHERE employee_position_id = '36';
UPDATE employee_position SET note = 'Maintenance ' WHERE employee_position_id = '37';
UPDATE employee_position SET note = 'Sales manager' WHERE employee_position_id = '38';
UPDATE employee_position SET note = 'Key Account Manager' WHERE employee_position_id = '39';
UPDATE employee_position SET note = 'Head of Comission Unit' WHERE employee_position_id = '40';
UPDATE employee_position SET note = 'Comission ' WHERE employee_position_id = '41';
UPDATE employee_position SET note = 'Bookkeeper' WHERE employee_position_id = '42';
UPDATE employee_position SET note = 'Laboratory assistent' WHERE employee_position_id = '43';
UPDATE employee_position SET note = 'Lab technician' WHERE employee_position_id = '44';
UPDATE employee_position SET note = 'Laboratory manager' WHERE employee_position_id = '45';
UPDATE employee_position SET note = 'Cleaning Equipment Operator' WHERE employee_position_id = '46';
UPDATE employee_position SET note = 'Head of Cleaning Equipment Unit' WHERE employee_position_id = '47';
UPDATE employee_position SET note = 'Marketing manager' WHERE employee_position_id = '86';
UPDATE employee_position SET note = 'Head of Quality Assurance and Hygiene' WHERE employee_position_id = '49';
UPDATE employee_position SET note = 'Internal quality controller team manager' WHERE employee_position_id = '90';
UPDATE employee_position SET note = 'Quality manager' WHERE employee_position_id = '50';
UPDATE employee_position SET note = 'Head of Cleaning Unit' WHERE employee_position_id = '0476aac35fd173f7e40c9fa56b14c113';
UPDATE employee_position SET note = 'Shiftleader' WHERE employee_position_id = '51';
UPDATE employee_position SET note = 'Shift manager, constructor' WHERE employee_position_id = 'b3b3e868de49c1c0d92a6db02f3f9306';
UPDATE employee_position SET note = 'Raw material administrator' WHERE employee_position_id = '52';
UPDATE employee_position SET note = 'RMQ Engineer' WHERE employee_position_id = '53';
UPDATE employee_position SET note = 'Raw material warehouse' WHERE employee_position_id = '54';
UPDATE employee_position SET note = 'Warehouseman' WHERE employee_position_id = '87';
UPDATE employee_position SET note = 'Head of IT Infrastructure Eisberg International' WHERE employee_position_id = '55';
UPDATE employee_position SET note = 'Operator' WHERE employee_position_id = '56';
UPDATE employee_position SET note = 'Financial manager' WHERE employee_position_id = '57';
UPDATE employee_position SET note = 'Warehousman' WHERE employee_position_id = '58';
UPDATE employee_position SET note = 'Warehouse manager' WHERE employee_position_id = '59';
UPDATE employee_position SET note = 'Warehouse assistant manager' WHERE employee_position_id = '60';
UPDATE employee_position SET note = 'Retail product development assistant' WHERE employee_position_id = '61';
UPDATE employee_position SET note = 'Retail product development leader' WHERE employee_position_id = '62';
UPDATE employee_position SET note = 'Saladmix packer-machinist' WHERE employee_position_id = '63';
UPDATE employee_position SET note = 'Head of Saladmix packer-machinist Unit' WHERE employee_position_id = '64';
UPDATE employee_position SET note = 'System gastronomy quality manager' WHERE employee_position_id = '65';
UPDATE employee_position SET note = 'Accounting manager' WHERE employee_position_id = '89';
UPDATE employee_position SET note = 'Social cleaner' WHERE employee_position_id = '66';
UPDATE employee_position SET note = 'Cleaning team leader' WHERE employee_position_id = '67';
UPDATE employee_position SET note = 'Tray packer' WHERE employee_position_id = '68';
UPDATE employee_position SET note = 'Head of Tray packer Unit' WHERE employee_position_id = '69';
UPDATE employee_position SET note = 'Technical manager' WHERE employee_position_id = '70';
UPDATE employee_position SET note = 'Maintenance ' WHERE employee_position_id = '71';
UPDATE employee_position SET note = 'Production assistant' WHERE employee_position_id = '72';
UPDATE employee_position SET note = 'Production manager' WHERE employee_position_id = '73';
UPDATE employee_position SET note = 'Shiftleader' WHERE employee_position_id = '74';
UPDATE employee_position SET note = 'Key account manager' WHERE employee_position_id = '75';
UPDATE employee_position SET note = 'Managing director' WHERE employee_position_id = '76';
UPDATE employee_position SET note = 'Field cleaner' WHERE employee_position_id = '77';
UPDATE employee_position SET note = 'Customer service assistant' WHERE employee_position_id = '78';
UPDATE employee_position SET note = 'Customer service leader' WHERE employee_position_id = '79';
UPDATE employee_position SET note = 'Vegetable processor - Preparator' WHERE employee_position_id = '83';
UPDATE employee_position SET note = 'Vegetable processor - Head of Preparator Unit' WHERE employee_position_id = '84';
UPDATE employee_position SET note = 'Vegetable processor operator' WHERE employee_position_id = '88';
UPDATE employee_position SET note = 'Chief accountant' WHERE employee_position_id = '100';

UPDATE `_sql_version` SET `revision`=37, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -37-2022-10-27-11:19--------------------------------------------------------

DELETE FROM `dictionary` WHERE `dict_id` IN ('downtime_ot_daily_balance', 'downtime_ot_balance');
INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-wfm', 'downtime_ot_daily_balance', 'Állásidő/túlóra göngyölt napi egyenleg', 1),
	('en', 'ttwa-wfm', 'downtime_ot_daily_balance', 'Downtime/overtime rolled daily balance', 1),
	('hu', 'ttwa-wfm', 'downtime_ot_balance', 'Állásidő/túlóra Göngyölt napi adat számítás', 1),
	('en', 'ttwa-wfm', 'downtime_ot_balance', 'Downtime/overtime rolled daily data calculation', 1);

INSERT INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
	('customers/eisberg/payrollEvaluation', 'cog1', NULL, 'ALL', '2', NULL, 'SN-DEV-13775', NOW());

UPDATE `_sql_version` SET `revision`=38, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -38-2022-10-28-12:45--------------------------------------------------------

UPDATE `option_config` SET `type` = 'dPicker', `status` = 2, `modified_by` = 'FR-DEV-14339', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option8');
UPDATE `dictionary` SET `dict_value` = 'Jelenlegi szerződés kezdete' WHERE `dict_id` = 'ext4_option8' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Start of current contract' WHERE `dict_id` = 'ext4_option8' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=39, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -39-2022-11-14-08:30--------------------------------------------------------

UPDATE `app_lookup` SET `valid` = '0' WHERE `lookup_value` = '2' AND `lookup_id` = 'ext4_option2';

INSERT INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
    ('ext4_option2', '0', 0, 'ext4_option2_0', 1),
    ('ext4_option3', '0', 0, 'ext4_option3_0', 1);

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'ext4_option2_0', '0', 1),
	('en', 'ttwa-base', 'ext4_option2_0', '0', 1),
    ('hu', 'ttwa-base', 'ext4_option3_0', '0', 1),
	('en', 'ttwa-base', 'ext4_option3_0', '0', 1);

UPDATE `_sql_version` SET `revision`=40, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -40-2022-11-18-14:00--------------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'eisberg_option7', '100% Túlóra pótlék', 1),
	('en', 'ttwa-base', 'eisberg_option7', '100% Overtime bonus', 1),
    ('hu', 'ttwa-base', 'total_hours_overtime_100_bonus', '100% Túlóra pótlék jogosult órái', 1),
	('en', 'ttwa-base', 'total_hours_overtime_100_bonus', 'Hours eligible for 100% overtime bonus', 1);

UPDATE `_sql_version` SET `revision`=41, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -41-2022-11-23-13:30--------------------------------------------------------

UPDATE `app_settings`
SET `setting_value` = '1'
WHERE `setting_id` = 'summarySheet_calculation_standby_bot_accept_and_change_to_ovetime';

UPDATE `_sql_version` SET `revision`=42, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -42-2022-12-02-14:30--------------------------------------------------------

UPDATE `state_type` SET `state_type_status` = '2', `modified_by` = 'DEV-14560', `modified_on` = NOW() WHERE `in_use` = 1 AND `status` = 2 AND `state_type_status` = '0';

UPDATE `_sql_version` SET `revision`=43, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -43-2022-12-13-08:30--------------------------------------------------------

UPDATE
    `app_settings`
SET
    `setting_value` = '1',
    `note`          = 'prev val: 0',
    `modified_by`   = 'DEV-14619',
    `modified_on`   = NOW()
WHERE  `setting_id` = 'summarySheetLockAbsences'
   AND `dict_id`    = 'summarySheetLockAbsences';

UPDATE `_sql_version` SET `revision`=44, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -44-2022-12-14-13:30-----------------------------------------------DEV-14619

UPDATE `dictionary` SET `dict_value` = 'Fizetett ünnep' WHERE `dict_id` = 'option8' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Paid national holiday' WHERE `dict_id` = 'option8' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'FEOR kód' WHERE `dict_id` = 'note' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'FEOR code' WHERE `dict_id` = 'note' AND `lang` = 'en' AND `module` = 'ttwa-base';

UPDATE `_sql_version` SET `revision`=45, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -45-2022-12-14-14:30--------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `target` = 'KULCSSOFT' AND `status` = 2;
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
    ('KULCSSOFT', 'BX', 'state_type_id', NULL, '3ad610cfce362896dbb4b11858dfae40', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Betegszabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'SZX', 'state_type_id', NULL, 'a272f564576d443e7832587126b070aa', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'KP', 'state_type_id', NULL, '6193a37b5816b', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Kiegyenlítő nap', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'ASZX', 'state_type_id', NULL, 'ebf4ac30e7dc238fd2f4bc86332e0675', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Apák munkaidő kedvezménye', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'HHTX', 'state_type_id', NULL, 'absence_type_paid_dispbyemployer', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fiz. ig. távollét (közeli hozzát.)', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'FNSZX', 'state_type_id', NULL, '628834878c5bc72f60283e37865678b6', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fizetés nélküli szabadság', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'FITX', 'state_type_id', NULL, 'f4f63ae4dd65cd97ee1f409d8b620806', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Fizetett igazolt távollét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'IGLANX', 'state_type_id', NULL, 'def32968390fb987c823da0cbf7d3bd8', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Igazolatlan távollét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'NFITX', 'state_type_id', NULL, '269b59b4efbbb4ef1d527492dc06cb60', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Nem fizetett igazolt távollét', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'BKX', 'state_type_id', NULL, 'absence_type_delegacy_in', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Belföldi kiküldetés', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'HOX', 'state_type_id', NULL, '17c82fbc41e2abf10dff3a857222ec14', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Home office', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'KKX', 'state_type_id', NULL, 'absence_type_delegacy_ext', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Külföldi kiküldetés', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'TX', 'state_type_id', NULL, 'aef6ebc4edde58fe4bf17402e6f16925', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Tréning - fiz.munkanap', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=46, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -46-2022-12-15-13:45--------------------------------------------------------

INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
    ('KULCSSOFT', 'VP', 'inside_type_id', NULL, NULL, 0, 'data.`dt__worktime_sum` - data.`dt__base_abstime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`dt__worktime_sum` - data.`dt__base_abstime_sum`) > 0 AND data.`date_day_of_week` = 1 AND data.`option4` = \"1\"', 'Vasárnapi pótlék', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'ÜP', 'inside_type_id', NULL, NULL, 0, 'data.`dt__worktime_sum` - data.`dt__base_abstime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`dt__worktime_sum` - data.`dt__base_abstime_sum`) > 0 AND data.`is_public_holiday` = 1 AND data.`option5` = \"1\"', 'Túlmunka pótlék munkaszüneti napra 50%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'T1', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'Túlmunka pótlék pihenőnapra', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'T1', 'inside_type_id', NULL, NULL, 0, 'data.`dt__base_overtime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`dt__base_overtime_sum` > 0 AND data.`is_restday` = 1', 'Túlmunka alapbére', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'CK', 'inside_type_id', NULL, NULL, 0, 'data.`schedule_standby_full_time` - data.`dt__overtime_st_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`schedule_standby_full_time` - data.`dt__overtime_st_sum`) > 0', 'Készenléti díj', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'ÜPEC', 'inside_type_id', NULL, NULL, 0, 'data.`ec_daily_worktime`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, 'data.`is_public_holiday` = 1 AND data.`date_day_of_week` NOT IN (\"7\", \"1\") AND data.`company_org_group1_name` = \"Termelés\"', 'Távolléti díj munkaszüneti napra', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'CM30', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, '(data.`inside_type_id` LIKE \"%du2%\" OR data.`inside_type_id` LIKE \"%ej%\") AND data.`option2` = \"1\"', 'Műszakpótlék 30%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'CM15', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, '(data.`inside_type_id` LIKE \"%du2%\" OR data.`inside_type_id` LIKE \"%ej%\") AND data.`option3` = \"1\"', 'Éjszakai pótlék', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=47, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -47-2022-12-15-14:00--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '0', `note` = 'Prev value: 1 , DEV-13973' WHERE `setting_id` = 'curlSslVerifyPeer';

UPDATE `_sql_version` SET `revision`=48, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -48-2022-12-15-23:00--------------------------------------------------------

UPDATE `legal_control` SET `display_type` = 'WARNING' WHERE `legal_control_id` = 'daily_max_worktime_hours';
UPDATE `legal_control` SET `display_type` = 'WARNING' WHERE `legal_control_id` = 'daily_min_worktime_hours';

UPDATE `_sql_version` SET `revision`=49, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -49-2023-01-09-09:30--------------------------------------------------------

INSERT INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
	('customers/eisberg/payrollEvaluation', 'standby_hours', NULL, 'ALL', '2', NULL, 'SN-DEV-14739', NOW());

UPDATE `_sql_version` SET `revision`=50, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -50-2023-01-16-09:30--------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Megjegyzés' WHERE `dict_id` = 'note' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Note' WHERE `dict_id` = 'note' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'FEOR kód' WHERE `dict_id` = 'ext2_option12' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'FEOR code' WHERE `dict_id` = 'ext2_option12' AND `lang` = 'en' AND `module` = 'ttwa-base';

UPDATE `option_config` SET `status` = 2, `modified_by` = 'DEV-14734', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option12');

UPDATE `_sql_version` SET `revision`=51, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -51-2023-01-18-17:00--------------------------------------------------------

UPDATE
    `notification_email_config`
SET
    `addresses` = '<EMAIL>'
WHERE
    `process_id` IN(
        'ExpiredMedicalSuitabilityNotice',
        'ExpiredForkliftSuitabilityNotice',
        'EmployeeTestReqiredNotice'
    )
    AND `status` = 2;

UPDATE `_sql_version` SET `revision`=52, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -52-2023-01-20-17:30-----------------------------------------------DEV-14843

UPDATE `dictionary` SET `dict_value` = 'Túlóra 50%' WHERE `dict_id` = 'paidot_50' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Túlóra 100%' WHERE `dict_id` = 'paidot_100' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Távollétek' WHERE `dict_id` = 'paid_absences' AND `lang` = 'hu';

UPDATE `_sql_version` SET `revision`=53, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -53-2023-01-26-13:10--------------------------------------------------------

UPDATE `column_rights` SET `column_id` = 'comment' WHERE `status` = 2 AND `column_id` = 'category_one' AND `controller_id` = 'customers/eisberg/attendanceEvaluation';

UPDATE `_sql_version` SET `revision`=54, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -54-2023-01-30-10:00--------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Ünnepnapi pótlék 100%' WHERE `dict_id` = 'option5' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Holiday bonus 100%' WHERE `dict_id` = 'option5' AND `lang` = 'en';

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('ÜP', 'T1', 'CK');
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
    ('KULCSSOFT', 'ÜP', 'inside_type_id', NULL, NULL, 0, 'data.`dt__worktime_sum` - data.`dt__base_abstime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`dt__worktime_sum` - data.`dt__base_abstime_sum`) > 0 AND data.`is_public_holiday` = 1 AND data.`option5` = \"1\"', 'Munkaszüneti pótlék', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'T1', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` LIKE \"paidot_base%\"', 'Túlmunka alapbére', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'T2', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` LIKE \"paidot_50%\" OR data.`inside_type_id` = \"paidot\"', 'Túlmunka pótlék 50%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'T3', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` LIKE \"paidot_100%\"', 'Túlmunka pótlék 100%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'CK', 'inside_type_id', NULL, NULL, 0, 'IF(data.`schedule_standby_end` LIKE \"%23:59:00\" OR data.`eeh_schedule_standby_end` = \"23:59\", data.`schedule_standby_full_time` - data.`dt__overtime_st_sum` + 60, data.`schedule_standby_full_time` - data.`dt__overtime_st_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`schedule_standby_full_time` - data.`dt__overtime_st_sum`) > 0', 'Készenléti díj', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=55, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -55-2023-01-31-15:30--------------------------------------------------------

INSERT INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
('customers/eisberg/attendanceSheetFlexible', 'AttendanceSheetFlexible', 'menu_item_attendanceSheetFlexible', 'DEV-14938', '2023-02-07 10:00:00');

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
('eisbergAttendanceSheetFlexible', 'customers/eisberg/attendanceSheetFlexible', 'view', '1', '1', 'DEV-14938', '2023-02-07 10:00:00');

INSERT INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
('eisbergAttendanceSheetFlexible', 'customers/eisberg/attendanceSheetFlexible --- view', '0', 'Jelenléti ív', 'DEV-14938', '2023-02-07 10:00:00');

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-wfm', 'menu_item_attendancesheetflexible_eisberg', 'Jelenléti ív', '1'),
('en', 'ttwa-wfm', 'menu_item_attendancesheetflexible_eisberg', 'Attendancesheet', '1');

INSERT INTO `menu_item_table` (`menu_item_id`, `menu_item_name`, `menu_modul`, `menu_label`, `menu_item_css_class`, `menu_url`, `menu_visible`, `menu_visible_operation`, `menu_item_parent_id`, `menu_order`) VALUES
('customers/eisberg/attendanceSheetFlexible', 'customers/eisberg/attendanceSheetFlexible', 'ttwa-wfm', 'menu_item_attendancesheetflexible_eisberg', 'sub', '/customers/eisberg/attendanceSheetFlexible/index', 'customers/eisberg/attendanceSheetFlexible', 'view', '82', '158');

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
('customers/eisberg/attendanceSheetFlexible', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2', 'DEV-14938', NOW()),
('customers/eisberg/attendanceSheetFlexible', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2', 'DEV-14938', NOW());

UPDATE `_sql_version` SET `revision`=56, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -56-2023-02-07-15:30--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '0', `note` = 'Prev value: 1 , SN-DEV-15047' WHERE `setting_id` = 'ptr_loga_payroll';

UPDATE `_sql_version` SET `revision`=57, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -57-2023-02-15-15:45--------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('ÜP', 'VP');
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
    ('KULCSSOFT', 'VP', 'inside_type_id', NULL, NULL, 0, 'data.`dt__worktime_sum` + data.`dt__base_overtime_sum` - data.`dt__base_abstime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`dt__worktime_sum` + data.`dt__base_overtime_sum` - data.`dt__base_abstime_sum`) > 0 AND data.`date_day_of_week` = 1 AND data.`option4` = \"1\"', 'Vasárnapi pótlék', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'ÜP', 'inside_type_id', NULL, NULL, 0, 'data.`dt__worktime_sum` + data.`dt__base_overtime_sum` - data.`dt__base_abstime_sum`', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`dt__worktime_sum` + data.`dt__base_overtime_sum` - data.`dt__base_abstime_sum`) > 0 AND data.`is_public_holiday` = 1 AND data.`option5` = \"1\"', 'Munkaszüneti pótlék', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=58, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -58-2023-02-15-16:00--------------------------------------------------------

UPDATE
    `notification_email_config`
SET
    `sql` = '
    SELECT
        `e`.`emp_id`,
        `ec`.`employee_id`,
        `comp`.`company_name`,
        {full_name} AS `fullname`,
        CASE
             WHEN
                 `ext2`.`ext2_option9` BETWEEN
                 DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
                 AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
                 THEN `ext2`.`ext2_option9`
             WHEN
                 `ext2`.`ext2_option10`  BETWEEN
                 DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
                 AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
                 THEN `ext2`.`ext2_option10`
             ELSE `ext2`.`valid_to`
        END AS `valid_to`,
        CASE
            WHEN `ext2`.`ext2_option9` IS NULL OR `ext2`.`ext2_option9` = "" THEN "lung_screening"
            ELSE "medical_suitability"
        END AS `dict_id_of_suitability_examination`
    FROM
        `employee_ext2` `ext2`
    INNER JOIN
        `employee` `e`
        ON `ext2`.`employee_id` = `e`.`employee_id`
        AND CURDATE() BETWEEN `e`.`valid_from` AND IFNULL(`e`.`valid_to`, "2038-01-01")
        AND `e`.`status` = 2
    INNER JOIN
        `company` `comp`
        ON `e`.`company_id` = `comp`.`company_id`
        AND CURDATE() BETWEEN `comp`.`valid_from` AND IFNULL(`comp`.`valid_to`, "2038-01-01")
        AND `comp`.`status` = 2
    INNER JOIN
        `employee_contract` `ec`
        ON  `ec`.`status` = 2
        AND `e`.`employee_id` = `ec`.`employee_id`
        AND CURDATE() BETWEEN `ec`.`valid_from` AND IFNULL(`ec`.`valid_to`, "2038-01-01")
        AND CURDATE() BETWEEN `ec`.`ec_valid_from` AND IFNULL(`ec`.`ec_valid_to`, "2038-01-01")
    WHERE
        `ext2`.`status` = 2
        AND (
            `ext2`.`ext2_option9` BETWEEN
                DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
                AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
            OR
            `ext2`.`ext2_option10`  BETWEEN
                DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
                AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
        )
    ORDER BY CASE
                 WHEN
                     `ext2`.`ext2_option9` BETWEEN
                     DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
                     AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
                     THEN `ext2`.`ext2_option9`
                 WHEN
                     `ext2`.`ext2_option10`  BETWEEN
                     DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
                     AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
                     THEN `ext2`.`ext2_option10`
             END DESC,
             `fullname` ASC',
    `addresses`  = '<EMAIL>'
WHERE
    `process_id` = 'ExpiredMedicalSuitabilityNotice'
    AND `status` = 2;


UPDATE
    `notification_email_config`
SET
    `sql` = '
    SELECT
        `e`.`emp_id`,
        `ec`.`employee_id`,
        `comp`.`company_name`,
        {full_name} AS `fullname`,
        `ext2`.`ext2_option11` AS `valid_to`
    FROM
        `employee_ext2` `ext2`
    INNER JOIN
        `employee` `e`
        ON `ext2`.`employee_id` = `e`.`employee_id`
        AND CURDATE() BETWEEN `e`.`valid_from` AND IFNULL(`e`.`valid_to`, "2038-01-01")
        AND `e`.`status` = 2
    INNER JOIN
        `company` `comp`
        ON `e`.`company_id` = `comp`.`company_id`
        AND CURDATE() BETWEEN `comp`.`valid_from` AND IFNULL(`comp`.`valid_to`, "2038-01-01")
        AND `comp`.`status` = 2
    INNER JOIN
        `employee_contract` `ec`
        ON  `ec`.`status` = 2
        AND `e`.`employee_id` = `ec`.`employee_id`
        AND CURDATE() BETWEEN `ec`.`valid_from` AND IFNULL(`ec`.`valid_to`, "2038-01-01")
        AND CURDATE() BETWEEN `ec`.`ec_valid_from` AND IFNULL(`ec`.`ec_valid_to`, "2038-01-01")
    WHERE
        `ext2`.`status` = 2
        AND `ext2`.`ext2_option11` BETWEEN
            DATE_FORMAT(CURDATE() + INTERVAL 1 MONTH, "%Y-%m-01")
            AND LAST_DAY(CURDATE() + INTERVAL 1 MONTH)
    ORDER BY `ext2`.`ext2_option11`, `fullname`',
    `addresses`  = '<EMAIL>'
WHERE
    `process_id` = 'ExpiredForkliftSuitabilityNotice'
    AND `status` = 2;


UPDATE
    `notification_email_config`
SET
    `sql` = '
    SELECT
        {full_name} AS `fullname`,
        `e`.`emp_id`,
        `comp`.`company_name`,
        `cc`.`cost_center_name`,
        `ec`.`ec_valid_from`,
        CASE
            WHEN
                DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 1 YEAR,  "%Y-%m-%d") = CURDATE()
            THEN "1y_test_reqired"
            WHEN
                DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 6 MONTH, "%Y-%m-%d") = CURDATE()
            THEN "6m_test_reqired"
            WHEN
                DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 3 MONTH, "%Y-%m-%d") = CURDATE()
            THEN "3m_test_reqired"
            WHEN
                DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 1 MONTH, "%Y-%m-%d") = CURDATE()
            THEN "1m_test_reqired"
        END AS `dict_id`
    FROM
        `employee_contract` `ec`
    INNER JOIN
        `employee` `e`
        ON `ec`.`employee_id` = `e`.`employee_id`
        AND CURDATE() BETWEEN `e`.`valid_from` AND IFNULL(`e`.`valid_to`, "2038-01-01")
        AND `e`.`status` = 2
    INNER JOIN
        `company` `comp`
        ON `e`.`company_id` = `comp`.`company_id`
        AND CURDATE() BETWEEN `comp`.`valid_from` AND IFNULL(`comp`.`valid_to`, "2038-01-01")
        AND `comp`.`status` = 2
    INNER JOIN
        `employee_cost` `eco`
        ON `ec`.`employee_contract_id` = `eco`.`employee_contract_id`
        AND CURDATE() BETWEEN `eco`.`valid_from` AND IFNULL(`eco`.`valid_to`, "2038-01-01")
        AND `eco`.`status` = 2
    INNER JOIN
        `cost_center` `cc`
        ON `cc`.`cost_center_id` = `eco`.`cost_center_id`
        AND CURDATE() BETWEEN `cc`.`valid_from` AND IFNULL(`cc`.`valid_to`, "2038-01-01")
        AND `cc`.`status` = 2
    WHERE
        `ec`.`status` = 2
        AND `ec`.`employee_id` = `e`.`employee_id`
        AND CURDATE() BETWEEN `ec`.`valid_from` AND IFNULL(`ec`.`valid_to`, "2038-01-01")
        AND CURDATE() BETWEEN `ec`.`ec_valid_from` AND IFNULL(`ec`.`ec_valid_to`, "2038-01-01")
        AND (
            DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 1 YEAR,  "%Y-%m-%d") = CURDATE()
            OR
            DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 6 MONTH, "%Y-%m-%d") = CURDATE()
            OR
            DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 3 MONTH, "%Y-%m-%d") = CURDATE()
            OR
            DATE_FORMAT(`ec`.`ec_valid_from` + INTERVAL 1 MONTH, "%Y-%m-%d") = CURDATE()
        )
    ORDER BY `ec`.`ec_valid_from` DESC, `fullname` ASC',
    `addresses`  = '<EMAIL>'
WHERE
    `process_id` = 'EmployeeTestReqiredNotice'
    AND `status` = 2;

UPDATE `_sql_version` SET `revision`=59, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -59-2023-02-22-11:30-----------------------------------------------DEV-14843

UPDATE `app_settings` SET `setting_value` = '1', `note` = '24 órás készenlét feature, Prev value: 0', `modified_by` = 'DEV-15099', `modified_on` = NOW()  WHERE `setting_id` = 'standbyUpgradedVersion';

UPDATE `_sql_version` SET `revision`=60, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -60-2023-02-23-11:00--------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('T1', 'T2', 'T3', 'CK');
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
    ('KULCSSOFT', 'T1', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` LIKE \"paidot_base%\" AND data.`option1` = 1', 'Túlmunka alapbére', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'T2', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, '(data.`inside_type_id` LIKE \"paidot_50%\" OR data.`inside_type_id` = \"paidot\") AND data.`option6` = 1', 'Túlmunka pótlék 50%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'T3', 'inside_type_id', NULL, NULL, 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, 'data.`inside_type_id` LIKE \"paidot_100%\" AND data.`option7` = 1', 'Túlmunka pótlék 100%', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'CK', 'inside_type_id', NULL, NULL, 0, 'IF(data.`schedule_standby_end` LIKE \"%23:59:00\" OR data.`eeh_schedule_standby_end` = \"23:59\", data.`schedule_standby_full_time` - data.`dt__overtime_st_sum` + 60, data.`schedule_standby_full_time` - data.`dt__overtime_st_sum`)', 'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL, '(data.`schedule_standby_full_time` - data.`dt__overtime_st_sum`) > 0 AND data.`option10` = 1', 'Készenléti díj', 2, 'ptc_sys', NULL, NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=61, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -61-2023-02-27-10:00--------------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'balance_downtime_ot_plan', 'Göngyölt napi egyenleg állásidő/túlóra tervezéshez', '1'),
    ('en', 'ttwa-base', 'balance_downtime_ot_plan', 'Wrapped daily balance for downtime/overtime planning', '1'),
    ('hu', 'ttwa-base', 'ot_during_standby', 'Készenlét alatti túlóra', '1'),
    ('en', 'ttwa-base', 'ot_during_standby', 'Overtime during standby', '1');

INSERT INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
	('customers/eisberg/payrollEvaluation', 'balance_downtime_ot_plan', NULL, 'ALL', '2', NULL, 'SN-DEV-15115', NOW()),
	('customers/eisberg/payrollEvaluation', 'standby_minus_ot', NULL, 'ALL', '2', NULL, 'SN-DEV-15115', NOW());

UPDATE `_sql_version` SET `revision`=62, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -62-2023-02-27-14:00--------------------------------------------------------

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'wrapped_data', 'göngyölt adat', '1'),
    ('en', 'ttwa-base', 'wrapped_data', 'wrapped data', '1');

INSERT INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'daily_data', 'napi adat', '1'),
    ('en', 'ttwa-base', 'daily_data', 'daily data ', '1');

INSERT INTO `column_rights` (`controller_id`, `column_id`, `model_name`, `rolegroup_id`, `status`, `roles`, `created_by`, `created_on`) VALUES
	('customers/eisberg/attendanceEvaluation', 'balance_downtime_ot_plan', NULL, 'ALL', '2', NULL, 'SN-DEV-15116', NOW());

UPDATE `_sql_version` SET `revision`=63, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -63-2023-02-27-15:00--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 1 WHERE `setting_id` = 'enableHolidayAsWorkday';

UPDATE `_sql_version` SET `revision`=64, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -64-2023-04-13-15:00--------------------------------------------------------

UPDATE `dictionary` SET `dict_id` = 'eisberg_daily_balance_in_frame' WHERE `dict_id` = 'eisberg_daily_balance';
UPDATE `dictionary` SET `dict_value` = 'Napi egyenleg keretben' WHERE `dict_id` = 'eisberg_daily_balance_in_frame' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Daily balance in frame' WHERE `dict_id` = 'eisberg_daily_balance_in_frame' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=65, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -65-2023-05-08-12:00--------------------------------------------------------

INSERT INTO `competency_group` (`competency_group_id`, `competency_group_name`, `status`, `created_by`, `created_on`) VALUES
('1', 'Alap',2,'DEV-15756',NOW()),
('2', 'Kiegészítő',2,'DEV-15756',NOW());

INSERT INTO `competency` (`competency_id`, `competency_name`, `competency_group_id`, `status`, `created_by`, `created_on`) VALUES
('1', 'Előkészítő', '1' ,2,'DEV-15756',NOW()),
('2', 'Tálcás csomagoló', '1' ,2,'DEV-15756',NOW()),
('3', 'Kommissió', '1' ,2,'DEV-15756',NOW()),
('4', 'Elszedő', '1' ,2,'DEV-15756',NOW()),
('5', 'Operátor', '1' ,2,'DEV-15756',NOW()),
('6', 'Ládamosó', '1' ,2,'DEV-15756',NOW()),
('7', 'Nyersanyag kezelő', '1' ,2,'DEV-15756',NOW()),
('8', 'Salátamix összekészítő-gépkezelő', '1' ,2,'DEV-15756',NOW()),
('9', 'Csomagoló', '1' ,2,'DEV-15756',NOW()),
('10', 'Fehér', '2' ,2,'DEV-15756',NOW()),
('11', 'Labor', '2' ,2,'DEV-15756',NOW()),
('12', 'Tiszta övezet', '2' ,2,'DEV-15756',NOW()),
('13', 'Fémdetektor', '2' ,2,'DEV-15756',NOW());

CREATE TABLE `temp_employee_competency_load` (
`row_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
`emp_id` varchar(32) NOT NULL,
`competency_group_name` varchar(128) NOT NULL,
`competency_name` varchar(128) NOT NULL,
`order` smallint(6),
PRIMARY KEY (`row_id`)) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

INSERT INTO `temp_employee_competency_load` (`emp_id`,`competency_group_name`, `competency_name`,`order`) VALUES
('S538','Alap','Előkészítő','1'),
('336','Alap','Tálcás csomagoló','1'),
('336','Alap','Előkészítő','2'),
('336','Kiegészítő','Fehér',NULL),
('356','Alap','Előkészítő','1'),
('356','Kiegészítő','Labor',NULL),
('406','Alap','Kommissió','1'),
('406','Alap','Elszedő','2'),
('406','Alap','Előkészítő','3'),
('406','Alap','Tálcás csomagoló','4'),
('406','Kiegészítő','Tiszta övezet',NULL),
('571','Alap','Operátor','1'),
('571','Alap','Előkészítő','2'),
('587','Alap','Tálcás csomagoló','1'),
('587','Alap','Előkészítő','2'),
('576','Alap','Ládamosó','1'),
('576','Alap','Elszedő','2'),
('357','Alap','Elszedő','1'),
('357','Alap','Előkészítő','2'),
('357','Alap','Tálcás csomagoló','3'),
('357','Kiegészítő','Fémdetektor',NULL),
('523','Alap','Ládamosó','1'),
('523','Alap','Elszedő','2'),
('227','Alap','Ládamosó','1'),
('227','Alap','Elszedő','2'),
('227','Kiegészítő','Fehér',NULL),
('434','Alap','Ládamosó','1'),
('389','Kiegészítő','Tiszta övezet',NULL),
('389','Kiegészítő','Fémdetektor',NULL),
('298','Alap','Operátor','1'),
('298','Alap','Előkészítő','2'),
('11','Alap','Elszedő','1'),
('11','Alap','Előkészítő','2'),
('11','Alap','Tálcás csomagoló','3'),
('11','Kiegészítő','Fémdetektor',NULL),
('320','Alap','Kommissió','1'),
('320','Alap','Elszedő','2'),
('320','Alap','Előkészítő','3'),
('320','Kiegészítő','Fehér',NULL),
('580','Alap','Kommissió','1'),
('391','Alap','Kommissió','1'),
('391','Alap','Előkészítő','2'),
('588','Alap','Elszedő','1'),
('588','Alap','Előkészítő','2'),
('588','Alap','Tálcás csomagoló','3'),
('588','Kiegészítő','Fémdetektor',NULL),
('343','Alap','Kommissió','1'),
('343','Alap','Előkészítő','2'),
('343','Alap','Ládamosó','3'),
('S536','Alap','Elszedő','1'),
('S536','Alap','Előkészítő','2'),
('S536','Kiegészítő','Fémdetektor',NULL),
('409','Alap','Nyersanyag kezelő','1'),
('409','Alap','Előkészítő','2'),
('495','Alap','Elszedő','1'),
('495','Alap','Előkészítő','2'),
('495','Alap','Tálcás csomagoló','3'),
('495','Kiegészítő','Fémdetektor',NULL),
('24','Alap','Előkészítő','1'),
('24','Alap','Tálcás csomagoló','2'),
('24','Kiegészítő','Labor',NULL),
('24','Kiegészítő','Tiszta övezet',NULL),
('591','Alap','Tálcás csomagoló','1'),
('591','Alap','Előkészítő','2'),
('167','Alap','Tálcás csomagoló','1'),
('167','Alap','Előkészítő','2'),
('167','Kiegészítő','Tiszta övezet',NULL),
('28','Alap','Tálcás csomagoló','1'),
('28','Alap','Operátor','2'),
('28','Alap','Elszedő','3'),
('28','Alap','Előkészítő','4'),
('28','Kiegészítő','Fehér',NULL),
('297','Alap','Előkészítő','1'),
('297','Alap','Tálcás csomagoló','2'),
('435','Alap','Salátamix összekészítő-gépkezelő','1'),
('435','Alap','Előkészítő','2'),
('435','Alap','Elszedő','3'),
('435','Kiegészítő','Tiszta övezet',NULL),
('480','Alap','Csomagoló','1'),
('480','Alap','Elszedő','2'),
('480','Kiegészítő','Tiszta övezet',NULL),
('480','Kiegészítő','Fémdetektor',NULL),
('34','Alap','Elszedő','1'),
('34','Alap','Előkészítő','2'),
('34','Alap','Tálcás csomagoló','3'),
('34','Kiegészítő','Fehér',NULL),
('34','Kiegészítő','Fémdetektor',NULL),
('403','Alap','Előkészítő','1'),
('403','Alap','Tálcás csomagoló','2'),
('S537','Alap','Elszedő','1'),
('S537','Alap','Előkészítő','2'),
('S537','Kiegészítő','Fémdetektor',NULL),
('344','Alap','Salátamix összekészítő-gépkezelő','1'),
('44','Alap','Tálcás csomagoló','1'),
('44','Alap','Előkészítő','2'),
('44','Kiegészítő','Tiszta övezet',NULL),
('225','Alap','Operátor','1'),
('225','Alap','Salátamix összekészítő-gépkezelő','2'),
('225','Alap','Előkészítő','3'),
('225','Alap','Nyersanyag kezelő','4'),
('225','Kiegészítő','Tiszta övezet',NULL),
('507','Alap','Csomagoló','1'),
('507','Alap','Elszedő','2'),
('507','Alap','Salátamix összekészítő-gépkezelő','3'),
('507','Kiegészítő','Fémdetektor',NULL),
('47','Alap','Nyersanyag kezelő','1'),
('47','Alap','Előkészítő','2'),
('257','Alap','Kommissió','1'),
('257','Alap','Elszedő','2'),
('257','Alap','Előkészítő','3'),
('257','Kiegészítő','Fehér',NULL),
('259','Alap','Előkészítő','1'),
('259','Alap','Tálcás csomagoló','2'),
('283','Alap','Elszedő','1'),
('283','Alap','Előkészítő','2'),
('283','Alap','Tálcás csomagoló','3'),
('283','Kiegészítő','Fehér',NULL),
('283','Kiegészítő','Fémdetektor',NULL),
('551','Alap','Csomagoló','1'),
('551','Alap','Elszedő','2'),
('551','Alap','Előkészítő','3'),
('551','Kiegészítő','Fémdetektor',NULL),
('358','Alap','Elszedő','1'),
('358','Alap','Előkészítő','2'),
('358','Alap','Tálcás csomagoló','3'),
('358','Kiegészítő','Fehér',NULL),
('358','Kiegészítő','Tiszta övezet',NULL),
('358','Kiegészítő','Fémdetektor',NULL),
('274','Alap','Elszedő','1'),
('274','Alap','Előkészítő','2'),
('274','Alap','Tálcás csomagoló','3'),
('274','Kiegészítő','Fehér',NULL),
('274','Kiegészítő','Fémdetektor',NULL),
('594','Alap','Nyersanyag kezelő','1'),
('519','Alap','Előkészítő','1'),
('519','Alap','Tálcás csomagoló','2'),
('286','Alap','Csomagoló','1'),
('286','Alap','Elszedő','2'),
('286','Alap','Előkészítő','3'),
('286','Kiegészítő','Tiszta övezet',NULL),
('286','Kiegészítő','Fémdetektor',NULL),
('386','Alap','Előkészítő','1'),
('386','Alap','Operátor','2'),
('386','Alap','Elszedő','3'),
('539','Alap','Tálcás csomagoló','1'),
('539','Alap','Elszedő','2'),
('539','Alap','Előkészítő','3'),
('554','Alap','Elszedő','1'),
('554','Alap','Előkészítő','2'),
('554','Alap','Tálcás csomagoló','3'),
('554','Kiegészítő','Fémdetektor',NULL),
('106','Alap','Ládamosó','1'),
('260','Alap','Kommissió','1'),
('260','Alap','Ládamosó','2'),
('260','Alap','Elszedő','3'),
('513','Alap','Előkészítő','1'),
('513','Alap','Csomagoló','2'),
('513','Alap','Elszedő','3'),
('513','Alap','Tálcás csomagoló','4'),
('513','Kiegészítő','Fehér',NULL),
('422','Alap','Előkészítő','1'),
('422','Alap','Elszedő','2'),
('422','Kiegészítő','Tiszta övezet',NULL),
('456','Alap','Kommissió','1'),
('456','Alap','Elszedő','2'),
('456','Alap','Előkészítő','3'),
('593','Alap','Előkészítő','1'),
('159','Alap','Csomagoló','1'),
('159','Alap','Salátamix összekészítő-gépkezelő','2'),
('159','Alap','Elszedő','3'),
('159','Kiegészítő','Fehér',NULL),
('159','Kiegészítő','Tiszta övezet',NULL),
('159','Kiegészítő','Fémdetektor',NULL),
('402','Alap','Tálcás csomagoló','1'),
('402','Alap','Ládamosó','2'),
('402','Alap','Előkészítő','3'),
('347','Alap','Kommissió','1'),
('347','Alap','Elszedő','2'),
('347','Alap','Csomagoló','3'),
('347','Alap','Előkészítő','4'),
('330','Alap','Ládamosó','1'),
('330','Alap','Elszedő','2'),
('93','Alap','Elszedő','1'),
('93','Alap','Előkészítő','2'),
('93','Alap','Tálcás csomagoló','3'),
('93','Kiegészítő','Fémdetektor',NULL),
('586','Alap','Kommissió','1'),
('578','Alap','Tálcás csomagoló','1'),
('578','Alap','Előkészítő','2'),
('579','Alap','Tálcás csomagoló','1'),
('579','Alap','Előkészítő','2'),
('247','Alap','Kommissió','1'),
('247','Alap','Elszedő','2'),
('247','Alap','Előkészítő','3'),
('247','Kiegészítő','Tiszta övezet',NULL),
('247','Kiegészítő','Fémdetektor',NULL),
('S494','Alap','Előkészítő','1'),
('S494','Alap','Nyersanyag kezelő','2'),
('S495','Alap','Előkészítő','1'),
('S495','Alap','Tálcás csomagoló','2'),
('501','Alap','Előkészítő','1'),
('501','Alap','Elszedő','2'),
('585','Alap','Csomagoló','1'),
('585','Alap','Salátamix összekészítő-gépkezelő','2'),
('585','Alap','Elszedő','3'),
('585','Kiegészítő','Fémdetektor',NULL),
('242','Alap','Előkészítő','1'),
('242','Alap','Tálcás csomagoló','2'),
('242','Kiegészítő','Labor',NULL),
('374','Alap','Csomagoló','1'),
('374','Alap','Elszedő','2'),
('374','Alap','Előkészítő','3'),
('374','Kiegészítő','Tiszta övezet',NULL),
('374','Kiegészítő','Fémdetektor',NULL),
('S533','Alap','Salátamix összekészítő-gépkezelő','1'),
('505','Alap','Csomagoló','1'),
('505','Alap','Elszedő','2'),
('505','Kiegészítő','Tiszta övezet',NULL),
('505','Kiegészítő','Fémdetektor',NULL),
('384','Alap','Tálcás csomagoló','1'),
('384','Alap','Elszedő','2'),
('384','Alap','Előkészítő','3'),
('384','Kiegészítő','Fehér',NULL),
('384','Kiegészítő','Fémdetektor',NULL),
('230','Kiegészítő','Tiszta övezet',NULL),
('230','Kiegészítő','Fémdetektor',NULL),
('332','Alap','Ládamosó','1'),
('332','Alap','Elszedő','2'),
('332','Kiegészítő','Tiszta övezet',NULL),
('332','Kiegészítő','Fémdetektor',NULL),
('465','Alap','Előkészítő','1'),
('465','Kiegészítő','Fehér',NULL),
('425','Alap','Ládamosó','1'),
('425','Alap','Elszedő','2'),
('385','Alap','Ládamosó','1'),
('385','Alap','Elszedő','2'),
('262','Alap','Tálcás csomagoló','1'),
('262','Alap','Salátamix összekészítő-gépkezelő','2'),
('262','Alap','Előkészítő','3'),
('262','Kiegészítő','Fehér',NULL),
('363','Alap','Előkészítő','1'),
('363','Alap','Elszedő','2'),
('363','Kiegészítő','Tiszta övezet',NULL),
('363','Kiegészítő','Fémdetektor',NULL),
('289','Alap','Előkészítő','1'),
('364','Alap','Kommissió','1'),
('364','Alap','Elszedő','2'),
('364','Alap','Előkészítő','3'),
('517','Alap','Kommissió','1'),
('566','Alap','Elszedő','1'),
('566','Alap','Előkészítő','2'),
('566','Alap','Tálcás csomagoló','3'),
('566','Kiegészítő','Fehér',NULL),
('566','Kiegészítő','Fémdetektor',NULL),
('S540','Alap','Tálcás csomagoló','1'),
('S540','Alap','Előkészítő','2'),
('387','Alap','Salátamix összekészítő-gépkezelő','1'),
('387','Alap','Előkészítő','2'),
('387','Kiegészítő','Tiszta övezet',NULL),
('290','Alap','Operátor','1'),
('290','Alap','Előkészítő','2'),
('23','Alap','Előkészítő','1'),
('23','Alap','Elszedő','2'),
('273','Alap','Csomagoló','1'),
('273','Alap','Salátamix összekészítő-gépkezelő','2'),
('273','Alap','Elszedő','3'),
('273','Kiegészítő','Fehér',NULL),
('273','Kiegészítő','Tiszta övezet',NULL),
('273','Kiegészítő','Fémdetektor',NULL),
('481','Alap','Csomagoló','1'),
('481','Alap','Salátamix összekészítő-gépkezelő','2'),
('481','Alap','Elszedő','3'),
('481','Kiegészítő','Fehér',NULL),
('481','Kiegészítő','Tiszta övezet',NULL),
('481','Kiegészítő','Fémdetektor',NULL),
('308','Alap','Elszedő','1'),
('308','Alap','Előkészítő','2'),
('308','Alap','Tálcás csomagoló','3'),
('308','Kiegészítő','Fehér',NULL),
('308','Kiegészítő','Fémdetektor',NULL),
('454','Alap','Előkészítő','1'),
('454','Alap','Elszedő','2'),
('424','Alap','Salátamix összekészítő-gépkezelő','1'),
('424','Alap','Előkészítő','2'),
('424','Kiegészítő','Tiszta övezet',NULL),
('301','Alap','Előkészítő','1'),
('301','Alap','Elszedő','2'),
('301','Alap','Tálcás csomagoló','3'),
('301','Kiegészítő','Labor',NULL),
('500','Alap','Kommissió','1'),
('500','Alap','Előkészítő','2'),
('567','Alap','Elszedő','1'),
('567','Alap','Előkészítő','2'),
('567','Alap','Tálcás csomagoló','3'),
('567','Kiegészítő','Fémdetektor',NULL),
('518','Alap','Tálcás csomagoló','1'),
('518','Alap','Előkészítő','2');

INSERT INTO `employee_competency` (`employee_contract_id`,`competency_group_id`,`competency_id`,`level_id`,`order`,`valid_from`,`valid_to`,`status`, `created_by`, `created_on`)
SELECT
    `employee_contract`.`employee_contract_id`,
    (SELECT `competency_group_id` FROM `competency_group` where `competency_group`.`competency_group_name` = tecl.`competency_group_name` AND `competency_group`.status = 2) AS competency_group_id,
    (SELECT `competency_id` FROM `competency` where `competency`.`competency_name` = tecl.`competency_name` AND `competency`.status = 2) AS competency_id,
    '1',
    tecl.order,
    '1915-01-01',
    '2038-01-01',
    2,
    'DEV-15826',
    NOW()
FROM
    `employee`
        JOIN
    `employee_contract`
    ON
                `employee`.`employee_id` = `employee_contract`.`employee_id`
            AND CURDATE() BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`,'2038-01-01')
            AND `employee_contract`.`status` = 2
        JOIN
    `temp_employee_competency_load` tecl
    ON
            tecl.`emp_id` = `employee`.`emp_id`
WHERE
    CURDATE() BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`,'2038-01-01')
  AND  `employee`.`status` = 2
  AND tecl.`competency_name` IN (SELECT competency_name FROM competency where `competency`.status = 2);

DROP TABLE `temp_employee_competency_load`;

UPDATE
    `app_settings`
SET
    `setting_value` = '{"show_order": 1, "max_order_number": 99, "first_order_changeable": 1, "order_number_unique": 1}',
    `note`          = CONCAT(`note`, ' Prev value: {"show_order": 0, "max_order_number": 99, "first_order_changeable": 1, "order_number_unique": 0}'),
    `modified_on`   = NOW(),
    `modified_by`   = 'DEV-15756'
WHERE
    `setting_id`    = 'showEmployeeCompetencyOrder';

UPDATE
    `app_settings`
SET
    `setting_value` = '1',
    `note`          = CONCAT(`note`, ' Prev value: 0'),
    `modified_on`   = NOW(),
    `modified_by`   = 'DEV-15756'
WHERE
        `setting_id`    = 'showCompetencyGroup';

UPDATE
    `dictionary`
SET
    `dict_value` = 'Kompetencia csoport'
WHERE
        `dict_id`    = 'competency_group_id'
  AND `lang` = 'hu';

UPDATE
    `dictionary`
SET
    `dict_value` = 'Competency group'
WHERE
        `dict_id`    = 'competency_group_id'
  AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=66, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -66-2023-06-06-12:00--------------------------------------------------------

UPDATE `dictionary` SET `dict_id` = 'eisberg_downtime_overtime_in_frame', `dict_value` = 'Állásidő/túlóra keretben' WHERE `dict_id` = 'eisberg_daily_balance_in_frame' AND `lang` = 'hu';

UPDATE `dictionary` SET `dict_value` = 'Downtime/overtime in frame' WHERE `dict_id` = 'eisberg_downtime_overtime_in_frame' AND `lang` = 'en';

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`) VALUES
    ('hu', 'ttwa-wfm', 'eisberg_balance_in_frame', 'Egyenleg keretben'),
    ('en', 'ttwa-wfm', 'eisberg_balance_in_frame', 'Balance in frame');

UPDATE `_sql_version` SET `revision`=67, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -67-2023-06-08-13:45--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '10800' WHERE `setting_id` = 'summarySheet_regsDaytypeMissingHoverMessage';

UPDATE `_sql_version` SET `revision`=68, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -68-2023-06-27-15:00--------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Bónusz' WHERE `dict_id` = 'tab_employeetabs_employeeext5' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Bonus' WHERE `dict_id` = 'tab_employeetabs_employeeext5' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Célhoz kötött bónusz' WHERE `dict_id` = 'ext5_option1' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Target bonus' WHERE `dict_id` = 'ext5_option1' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Szubjektív bónusz' WHERE `dict_id` = 'ext5_option2' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Subjective bonus' WHERE `dict_id` = 'ext5_option2' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jelenléti bónusz' WHERE `dict_id` = 'ext5_option3' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Attendance bonus' WHERE `dict_id` = 'ext5_option3' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Mosolypótlék' WHERE `dict_id` = 'ext5_option4' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Smile bonus' WHERE `dict_id` = 'ext5_option4' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kiemelt ter.bónusz' WHERE `dict_id` = 'ext5_option5' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Priority planned bonus' WHERE `dict_id` = 'ext5_option5' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Baleseti bónusz' WHERE `dict_id` = 'ext5_option6' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Accident bonus' WHERE `dict_id` = 'ext5_option6' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Baleseti bónusz éves' WHERE `dict_id` = 'ext5_option7' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Annual accident bonus' WHERE `dict_id` = 'ext5_option7' AND `lang` = 'en';

UPDATE `option_config` SET `status` = 2, `modified_by` = 'SZOF-1184', `modified_on` = NOW() WHERE `option_id` IN ('ext5_option1', 'ext5_option2', 'ext5_option3', 'ext5_option4', 'ext5_option5', 'ext5_option6', 'ext5_option7');

UPDATE `_sql_version` SET `revision`=69, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -69-2023-08-15-11:00--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev.val: 0, Default value, Amennyiben 1-esre állítjuk az egyéni munkarend szerkesztés felületen naptípus váltásnál kinullázza a készenlétet, túlórát, stb-t (mert metszheti az új munkaidőt)', `modified_by` = 'SZOF-886', `modified_on` = NOW() WHERE `setting_id` = 'wsbgDelExtraHoursOnDtChange';

UPDATE `app_settings` SET `setting_value` = '1', `modified_by` = 'SZOF-886', `modified_on` = NOW() WHERE `setting_id` = 'employee_extra_hours_incision';

UPDATE `_sql_version` SET `revision`=70, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -70-2023-09-28-13:00--------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Munkavédelmi bónusz' WHERE `dict_id` = 'ext5_option8' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Occupational safety bonus' WHERE `dict_id` = 'ext5_option8' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Öcsi bónusz' WHERE `dict_id` = 'ext5_option9' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Little brother bonus' WHERE `dict_id` = 'ext5_option9' AND `lang` = 'en';

UPDATE `option_config` SET `status` = 2, `modified_by` = 'SZOF-1763', `modified_on` = NOW() WHERE `option_id` IN ('ext5_option8', 'ext5_option9');

UPDATE `_sql_version` SET `revision`=71, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -71-2023-11-06-10:00--------------------------------------------------------

INSERT INTO `icon` (`icon_id`, `icon_text_name`, `color`, `dict_id`, `status`, `created_by`, `created_on`)
VALUES
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_group', null, 'sym_o_group','2', 'system', NOW()),
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_labs', 'brown', 'sym_o_labs','2', 'system', NOW()),
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_cleaning_services', 'green','sym_o_cleaning_services','2', 'system', NOW()),
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_radar', 'grey','sym_o_radar','2', 'system', NOW());
    
TRUNCATE TABLE `shift_daypart`;

INSERT INTO `shift_daypart` (`shift_daypart_id`, `dict_id`, `order`, `status`, `created_by`, `created_on`)  VALUES
    ('de', 'shift_daypart_de', 1, 2, 'system', NOW()),
    ('kt', 'shift_daypart_kt', 2, 2, 'system', NOW()),
    ('du', 'shift_daypart_du', 3, 2, 'system', NOW()),
    ('ej', 'shift_daypart_ej', 4, 2, 'system', NOW());

UPDATE 
    daytype dt
SET
     dt.earliest_arrival_time = CASE WHEN status = 2 AND dt.earliest_arrival_time = '' THEN dt.work_start ELSE dt.earliest_arrival_time END,
     dt.latest_depart_time = CASE WHEN status = 2 AND dt.latest_depart_time = '' THEN dt.work_end ELSE dt.latest_depart_time END,
     dt.ordinary_time_start = CASE WHEN status = 2 AND dt.ordinary_time_start = '' THEN '00:00' ELSE dt.ordinary_time_start END,
     dt.ordinary_time_end = CASE WHEN status = 2 AND dt.ordinary_time_end = '' THEN '00:00' ELSE dt.ordinary_time_end END
WHERE 
    status = 2;

UPDATE `_sql_version` SET `revision`=72, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -72-2023-11-24-10:00--------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '{\"employee_rows_limit\": 10, \"search_days_limit\": 10, \"search_days_default_range\": 7, \"competency_rows_limit\": 10, \"employee_summary_version\": 0, \"restday_request\": 1}' , 
    `note` = 'Prev value: {\"employee_rows_limit\": 10, \"search_days_limit\": 10, \"search_days_default_range\": 7, \"competency_rows_limit\": 10, \"employee_summary_version\": 0}' 
WHERE
    `setting_id` = 'shiftPageSettings' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=73, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -73-2023-01-10-10:00--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `modified_by` = 'SZOF-2515', `modified_on` = NOW() WHERE `setting_id` = 'competency_icon_field';

UPDATE `_sql_version` SET `revision`=74, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -74-2024-02-01-10:00--------------------------------------------------------

INSERT IGNORE INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
('customers/eisberg/reportKnives', 'reportKnives', 'menu_item_reportKnives', 'SZOF-2702', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
('eisbergReportKnives', 'customers/eisberg/reportKnives', 'view', '1', '1', 'SZOF-2702', NOW());

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
('eisbergReportKnives', 'customers/eisberg/reportKnives --- view', '0', 'Kések kimutatás', 'SZOF-2702', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-wfm', 'menu_item_reportKnives_eisberg', 'Kések kimutatás', '1'),
('en', 'ttwa-wfm', 'menu_item_reportKnives_eisberg', 'Report knives', '1'),
('hu', 'ttwa-wfm', 'page_title_reportKnives_eisberg', 'Kések kimutatás', '1'),
('en', 'ttwa-wfm', 'page_title_reportKnives_eisberg', 'Report knives', '1'),
('hu', 'ttwa-wfm', 'inspection_of_hand_knives', 'KÉZI KÉSEK ellenőrzése (darabszám, épség, tisztaság)', '1'),
('en', 'ttwa-wfm', 'inspection_of_hand_knives', 'Inspection of HAND KNIVES (number of pieces, integrity, cleanliness)', '1'),
('hu', 'ttwa-wfm', 'color_of_knives', 'Késszín (bekarikázandó): KÉK / PIROS', '1'),
('en', 'ttwa-wfm', 'color_of_knives', 'Color of knives (to be circled): BLUE / RED', '1'),
('hu', 'ttwa-wfm', 'number_of_little_knives', 'Kis kés szám', '1'),
('en', 'ttwa-wfm', 'number_of_little_knives', 'Number of little knives', '1'),
('hu', 'ttwa-wfm', 'number_of_big_knives', 'Nagy kés szám', '1'),
('en', 'ttwa-wfm', 'number_of_big_knives', 'Number of big knives', '1'),
('hu', 'ttwa-wfm', 'start_of_shift_intact_and_clean', 'Műszak kezdéskor – ép és tiszta', '1'),
('en', 'ttwa-wfm', 'start_of_shift_intact_and_clean', 'Start of shift - intact and clean', '1'),
('hu', 'ttwa-wfm', 'start_of_shift_not_intact_and_clean', 'Műszak kezdéskor – NEM ép és tiszta', '1'),
('en', 'ttwa-wfm', 'start_of_shift_not_intact_and_clean', 'Start of shift - NOT intact and clean', '1'),
('hu', 'ttwa-wfm', 'end_of_shift_intact', 'Műszak végén – ép', '1'),
('en', 'ttwa-wfm', 'end_of_shift_intact', 'End of shift - intact', '1'),
('hu', 'ttwa-wfm', 'end_of_shift_not_intact', 'Műszak végén – NEM ép', '1'),
('en', 'ttwa-wfm', 'end_of_shift_not_intact', 'End of shift - NOT intact', '1'),
('hu', 'ttwa-wfm', 'reportKnives_eisberg_inspection_of_knives_message', 'Kések ellenőrzése során tapasztalt eltérés rögzítése a megjegyzés rovatban (azonnali jelzés a műszakvezetőnek!)<br/>Sérült, csorba kést azonnal ki kell adni a TMK-nak élezésre, ami fertőtlenítést követően kerülhet vissza a termelésbe.', '1'),
('en', 'ttwa-wfm', 'reportKnives_eisberg_inspection_of_knives_message', 'Record any deviations observed during the inspection of knives in the comments section (immediate notification to the shift manager!)<br/>Damaged or broken knives must be immediately handed over to TMK for sharpening, which can be returned to production after disinfection.', '1'),
('hu', 'ttwa-wfm', 'signature_of_shift_leader', 'Műszakvezetői aláírás', '1'),
('en', 'ttwa-wfm', 'signature_of_shift_leader', 'Signature of shift leader', '1'),
('hu', 'ttwa-wfm', 'eisberg_hungary_kft', 'Eisberg Hungary Kft.', '1'),
('en', 'ttwa-wfm', 'eisberg_hungary_kft', 'Eisberg Hungary Kft.', '1'),
('hu', 'ttwa-wfm', 'reportKnives_eisberg_version', 'Verzió 2.3/FO 11-262 HU', '1'),
('en', 'ttwa-wfm', 'reportKnives_eisberg_version', 'Version 2.3/FO 11-262 HU', '1'),
('hu', 'ttwa-wfm', 'knives_total', 'Összesen (db kés)', '1'),
('en', 'ttwa-wfm', 'knives_total', 'Knives total', '1'),
('hu', 'ttwa-wfm', 'check_time', 'Ellenőrzés ideje', '1'),
('en', 'ttwa-wfm', 'check_time', 'Check time', '1'),
('hu', 'ttwa-wfm', 'signature_of_group_leader', 'Csoportvezető aláírása', '1'),
('en', 'ttwa-wfm', 'signature_of_group_leader', 'Signature of group leader', '1');

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`, `menu_item_name`, `menu_modul`, `menu_label`, `menu_item_css_class`, `menu_url`, `menu_visible`, `menu_visible_operation`, `menu_item_parent_id`, `menu_order`) VALUES
('customers/eisberg/reportKnives', 'customers/eisberg/reportKnives', 'ttwa-wfm', 'menu_item_reportKnives_eisberg', 'sub', '/customers/eisberg/reportKnives/index', 'customers/eisberg/reportKnives', 'view', '82', '160');

INSERT IGNORE INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
('customers/eisberg/reportKnives', 'date', 'EMPLOYEE_WITH_DATE', 'combo', '0', '2', 'SZOF-2702', NOW()),
('customers/eisberg/reportKnives', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2', 'SZOF-2702', NOW()),
('customers/eisberg/reportKnives', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2', 'SZOF-2702', NOW());

UPDATE `_sql_version` SET `revision`=75, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -75-2024-02-19-16:56--------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '{\"employee_rows_limit\": 10, \"search_days_limit\": 10, \"search_days_default_range\": 7, \"competency_rows_limit\": 10, \"employee_summary_version\": 0, \"restday_request\": 1, \"competency_group_ids_to_calculate\": [\"1\"] }' , 
    `note` = 'Prev value: {\"employee_rows_limit\": 10, \"search_days_limit\": 10, \"search_days_default_range\": 7, \"competency_rows_limit\": 10, \"employee_summary_version\": 0, \"restday_request\": 1}' 
WHERE
    `setting_id` = 'shiftPageSettings' AND `valid` = 1;
    
UPDATE `_sql_version` SET `revision`=76, `updated_on`=NOW() WHERE `module`='c_eisberg';    

-- VERSION -76-2024-02-26-11:30--------------------------------------------------------

INSERT INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES 
('customers/eisberg/payrollEvaluation', 'unit_name', 'ALL', '2', 'SZOF-2760', NOW());

UPDATE `_sql_version` SET `revision`=77, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -77-2024-02-27-10:42--------------------------------------------------------

INSERT IGNORE INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
('customers/eisberg/reportMorningShift', 'reportMorningShift', 'menu_item_reportMorningShift', 'SZOF-2705', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
('reportMorningShift', 'customers/eisberg/reportMorningShift', 'view', '1', '1', 'SZOF-2705', NOW());

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
('reportMorningShift', 'customers/eisberg/reportMorningShift --- view', '0', 'Reggeli beosztás lista kimutatás', 'SZOF-2705', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-wfm', 'menu_item_reportMorningShift_eisberg', 'Reggeli beosztás lista kimutatás', '1'),
('en', 'ttwa-wfm', 'menu_item_reportMorningShift_eisberg', 'Report morning shift', '1'),
('hu', 'ttwa-wfm', 'page_title_reportMorningShift_eisberg', 'Reggeli beosztás lista kimutatás', '1'),
('en', 'ttwa-wfm', 'page_title_reportMorningShift_eisberg', 'Report morning shift', '1'),
('hu', 'ttwa-wfm', 'start_of_work', 'Kezdés', '1'),
('en', 'ttwa-wfm', 'start_of_work', 'Start', '1'),
('hu', 'ttwa-wfm', 'end_of_work', 'Befejezés', '1'),
('en', 'ttwa-wfm', 'end_of_work', 'Finish', '1'),
('hu', 'ttwa-wfm', 'area_of_work', 'Terület', '1'),
('en', 'ttwa-wfm', 'area_of_work', 'Area', '1'),
('hu', 'ttwa-wfm', 'downTimePerOT', 'Állásidő / túlóra', '1'),
('en', 'ttwa-wfm', 'downTimePerOT', 'Downtime / overtime', '1');

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`, `menu_item_name`, `menu_modul`, `menu_label`, `menu_item_css_class`, `menu_url`, `menu_visible`, `menu_visible_operation`, `menu_item_parent_id`, `menu_order`) VALUES
('customers/eisberg/reportMorningShift', 'customers/eisberg/reportMorningShift', 'ttwa-wfm', 'menu_item_reportMorningShift_eisberg', 'sub', '/customers/eisberg/reportMorningShift/index', 'customers/eisberg/reportMorningShift', 'view', '82', '160');

INSERT IGNORE INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
('customers/eisberg/reportMorningShift', 'date', 'EMPLOYEE_WITH_DATE', 'combo', '0', '2', 'SZOF-2705', NOW()),
('customers/eisberg/reportMorningShift', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2', 'SZOF-2705', NOW()),
('customers/eisberg/reportMorningShift', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2', 'SZOF-2705', NOW());

UPDATE `_sql_version` SET `revision`=78, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -78-2024-03-01-16:30--------------------------------------------------------

INSERT IGNORE INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES 
('customers/eisberg/reportKnives', 'group', 'COMPANY_ORG_GROUP2', 'combo', '0', '2', 'SZOF-2702', NOW());

UPDATE `dictionary`
SET
	`dict_value` = 'Kések ellenőrzése során tapasztalt eltérés rögzítése a megjegyzés rovatban (azonnali jelzés a műszakvezetőnek!)'
WHERE
		`dict_id` = 'reportKnives_eisberg_inspection_of_knives_message'
	AND `lang` = 'hu';

UPDATE `dictionary`
SET
	`dict_value` = 'Record any deviations observed during the inspection of knives in the comments section (immediate notification to the shift manager!)'
WHERE
		`dict_id` = 'reportKnives_eisberg_inspection_of_knives_message'
	AND `lang` = 'en';

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-wfm', 'reportKnives_eisberg_inspection_of_knives_message2', 'Sérült, csorba kést azonnal ki kell adni a TMK-nak élezésre, ami fertőtlenítést követően kerülhet vissza a termelésbe.', '1'),
('en', 'ttwa-wfm', 'reportKnives_eisberg_inspection_of_knives_message2', 'Damaged or broken knives must be immediately handed over to TMK for sharpening, which can be returned to production after disinfection.', '1');

UPDATE `_sql_version` SET `revision`=79, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -79-2024-02-27-16:08--------------------------------------------------------

INSERT INTO `icon` (`icon_id`, `icon_text_name`, `color`, `dict_id`, `status`, `created_by`, `created_on`)
VALUES
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'custom_icon_10', null, 'custom_icon_10','2', 'system', NOW()),
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'custom_icon_11', null, 'custom_icon_11','2', 'system', NOW()),
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'custom_icon_12', null, 'custom_icon_12','2', 'system', NOW()),
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'custom_icon_13', null, 'custom_icon_13','2', 'system', NOW()),
	((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'custom_icon_14', null, 'custom_icon_14','2', 'system', NOW()),
	((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'custom_icon_15', null, 'custom_icon_15','2', 'system', NOW()),
	((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_subtitles', null, 'sym_o_subtitles','2', 'system', NOW()),
	((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_print', null, 'sym_o_print','2', 'system', NOW()),
	((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_school', null, 'sym_o_school','2', 'system', NOW()),
	((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_fact_check', null, 'sym_o_fact_check','2', 'system', NOW());
	
INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `component`, `valid`) 
VALUES
    ('hu', 'vue', 'custom_icon_10', '10-es', 'spa-shift', 1),
    ('hu', 'vue', 'custom_icon_11', '11-es', 'spa-shift', 1),
    ('hu', 'vue', 'custom_icon_12', '12-es', 'spa-shift', 1),
    ('hu', 'vue', 'custom_icon_13', '13-as', 'spa-shift', 1),
    ('hu', 'vue', 'custom_icon_14', '14-es', 'spa-shift', 1),
    ('hu', 'vue', 'custom_icon_15', '15-ös', 'spa-shift', 1),
    ('hu', 'vue', 'sym_o_subtitles', 'Címkéző', 'spa-shift', 1),
    ('hu', 'vue', 'sym_o_print', 'Száll. Nyom.', 'spa-shift', 1),
    ('hu', 'vue', 'sym_o_school', 'Betanul', 'spa-shift', 1),
    ('hu', 'vue', 'sym_o_fact_check', 'Összekészítő', 'spa-shift', 1),
    ('en', 'vue', 'custom_icon_10', '10-th', 'spa-shift', 1),
    ('en', 'vue', 'custom_icon_11', '11-th', 'spa-shift', 1),
    ('en', 'vue', 'custom_icon_12', '12-th', 'spa-shift', 1),
    ('en', 'vue', 'custom_icon_13', '13-th', 'spa-shift', 1),
    ('en', 'vue', 'custom_icon_14', '14-th', 'spa-shift', 1),
    ('en', 'vue', 'custom_icon_15', '15-th', 'spa-shift', 1),
    ('en', 'vue', 'sym_o_subtitles', 'Sticker', 'spa-shift', 1),
    ('en', 'vue', 'sym_o_print', 'Printer', 'spa-shift', 1),
    ('en', 'vue', 'sym_o_school', 'Learner', 'spa-shift', 1),
    ('en', 'vue', 'sym_o_fact_check', 'Fact checker', 'spa-shift', 1);
    
UPDATE `_sql_version` SET `revision`=80, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -80-2024-03-05-13:30--------------------------------------------------------

UPDATE `payroll_transfer_config` SET `status` = 7 WHERE `transfer_id` IN ('TBX', 'TPX', 'UTIÜBX', 'ÜBX');
INSERT INTO `payroll_transfer_config` (`target`, `transfer_id`, `content_type`, `content_category`, `content_value`, `null_value`, `select_value`, `select_as`, `total_sum`, `weekday`, `is_restday`, `is_holiday`, `is_public_holiday`, `sql_conditions`, `sql_conditions_2`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES 
    ('KULCSSOFT', 'TBX', 'state_type_id', NULL, '65dc77c3e5d3b', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Tervezett betegség', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'TPX', 'state_type_id', NULL, '7b3cf78f9fd2a404567fe572fcb0eaf9', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Táppénz', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'UTIÜBX', 'state_type_id', NULL, '879e1d423b540a25f8c152948bb5066a', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Úti baleseti táppénz', 2, 'ptc_sys', NULL, NULL, NULL, NULL),
    ('KULCSSOFT', 'ÜBX', 'state_type_id', NULL, '945e3618f487efc4325204521e49c5f3', 0, NULL, NULL, 0, NULL, 0, 0, 0, NULL, NULL, 'Munkahelyi baleset', 2, 'ptc_sys', NULL, NULL, NULL, NULL);
    
UPDATE `_sql_version` SET `revision`=81, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -81-2024-03-06-17:30--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `modified_by` = 'SZOF-2774', `modified_on` = NOW() WHERE `setting_id` = 'showShiftCompetenciesOnCPlatform';

UPDATE `_sql_version` SET `revision`=82, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -82-2024-03-08-11:00--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '0 month', `modified_by` = 'SZOF-2858', `modified_on` = NOW() WHERE `setting_id` = 'editWorkScheduleByEmployeeDefaultValidMonth';

UPDATE `_sql_version` SET `revision`=83, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -83-2024-03-18-09:15--------------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) 
VALUES
    ('hu', 'ttwa-wfm', 'morningShiftList', 'Reggeli beosztás lista', 1),
    ('en', 'ttwa-wfm', 'morningShiftList', 'Morning shift list', 1);

UPDATE `_sql_version` SET `revision`=84, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -84-2024-03-20-15:27--------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '{\"employee_rows_limit\": 10, \"search_days_limit\": 14, \"search_days_default_range\": 14, \"competency_rows_limit\": 10, \"employee_summary_version\": 1, \"restday_request\": 1, \"competency_group_ids_to_calculate\": [\"1\"] }' , 
    `note` = 'Prev value: {\"employee_rows_limit\": 10, \"search_days_limit\": 10, \"search_days_default_range\": 7, \"competency_rows_limit\": 10, \"employee_summary_version\": 1, \"restday_request\": 1, , \"competency_group_ids_to_calculate\": [\"1\"]}' 
WHERE
    `setting_id` = 'shiftPageSettings' AND `valid` = 1;
    
UPDATE `_sql_version` SET `revision`=85, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -85-2024-04-10-15:27--------------------------------------------------------    

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
	('hu', 'ttwa-base', 'page_title_report_daily_employee_absence', 'Napi dolgozói távollét kimutatás', 1),
	('en', 'ttwa-base', 'page_title_report_daily_employee_absence', 'Daily employee absence report', 1),
	('hu', 'ttwa-base', 'menu_item_report_daily_employee_absence', 'Napi dolgozói távollét kimutatás', 1),
	('en', 'ttwa-base', 'menu_item_report_daily_employee_absence', 'Daily employee absence report', 1);

INSERT INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
	('reportDailyEmployeeAbsence', 'customers/eisberg/reportDailyEmployeeAbsence --- view', '0', 'Napi dolgozói távollét kimutatás menüponthoz ad megtekinthetési jogot.', 'SZOF-3111', NOW());

INSERT INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
	('reportDailyEmployeeAbsence', 'customers/eisberg/reportDailyEmployeeAbsence', NULL, 'view', NULL, '1', '1', 'SZOF-3111', NOW());

INSERT INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,`menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`) VALUES
	('menu_item_report_daily_employee_absence', 'menu_item_report_daily_employee_absence', 'ttwa-base', 'menu_item_report_daily_employee_absence', 'sub', '/customers/eisberg/reportDailyEmployeeAbsence/index', 'customers/eisberg/reportDailyEmployeeAbsence', 'view', '82', 206);
 
INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('customers/eisberg/reportDailyEmployeeAbsence', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2', 'SZOF-3111', NOW()),
    ('customers/eisberg/reportDailyEmployeeAbsence', 'date', 'EMPLOYEE_WITH_FROM_TO', 'combo', '0', '2', 'SZOF-3111', NOW());

UPDATE `_sql_version` SET `revision`=86, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -86-2024-04-22-16:00--------------------------------------------------------  

UPDATE
    `app_settings`
SET `setting_value` = '1', 
    `note` = 'Prev value: 0' 
WHERE
    `setting_id` = 'showDialogAfterSaveAbsenceWithSuccess' AND `valid` = 1;
    
UPDATE `_sql_version` SET `revision`=87, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -87-2024-04-26-17:00--------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev. val: 0, Elutasított távollét esetén ne látszódjon a szabadság az Egyéni munkarend szerkesztésnél.', `modified_by` = 'SZOF-3222', `modified_on` = NOW() WHERE `setting_id` = 'ShowAbsenceRejectedInWorkScheduleByGroup';
UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev. val: 0, Lehessen akkor is kivenni szabit, ha arra a napra van egy elutasított.', `modified_by` = 'SZOF-3222', `modified_on` = NOW() WHERE `setting_id` = 'enableGetAbsIfIssetRejectedAbs';

UPDATE `_sql_version` SET `revision`=88, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -88-2024-05-07-15:30--------------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
    ('reportAfternoonShift', 'customers/eisberg/reportAfternoonShift --- view', '0', 'Délutáni beosztás lista kimutatás', 'SZOF-3224', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('d1b666a7b5cf9f07e47333306de45f1c', 'reportAfternoonShift', 'SZOF-3224', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
    ('reportAfternoonShift', 'customers/eisberg/reportAfternoonShift', 'view', '1', '1', 'SZOF-3224', NOW());

INSERT IGNORE INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
    ('customers/eisberg/reportAfternoonShift', 'reportAfternoonShift', 'menu_item_report_afternoon_shift', 'SZOF-3224', NOW());

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`, `menu_item_name`, `menu_modul`, `menu_label`, `menu_item_css_class`, `menu_url`, `menu_visible`, `menu_visible_operation`, `menu_item_parent_id`, `menu_order`) VALUES
    ('customers/eisberg/reportAfternoonShift', 'customers/eisberg/reportAfternoonShift', 'ttwa-wfm', 'menu_item_reportAfternoonShift_eisberg', 'sub', '/customers/eisberg/reportAfternoonShift/index', 'customers/eisberg/reportAfternoonShift', 'view', '82', '213');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-wfm', 'menu_item_reportAfternoonShift_eisberg', 'Délutáni beosztás lista kimutatás', '1'),
    ('en', 'ttwa-wfm', 'menu_item_reportAfternoonShift_eisberg', 'Report afternoon shift', '1'),
    ('hu', 'ttwa-wfm', 'page_title_reportAfternoonShift_eisberg', 'Délutáni beosztás lista kimutatás', '1'),
    ('en', 'ttwa-wfm', 'page_title_reportAfternoonShift_eisberg', 'Report afternoon shift', '1'),
    ('hu', 'ttwa-wfm', 'afternoonShiftList', 'Délutáni beosztás lista', 1),
    ('en', 'ttwa-wfm', 'afternoonShiftList', 'Afternoon shift list', 1);

INSERT IGNORE INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('customers/eisberg/reportAfternoonShift', 'date', 'EMPLOYEE_WITH_DATE', 'combo', '0', '2', 'SZOF-3224', NOW()),
    ('customers/eisberg/reportAfternoonShift', 'group', 'DEFAULT_GROUP_FILTER', 'combo', '0', '2', 'SZOF-3224', NOW()),
    ('customers/eisberg/reportAfternoonShift', 'group', 'COMPANY_ORG_GROUP1', 'combo', '0', '2', 'SZOF-3224', NOW());

UPDATE `_sql_version` SET `revision`=89, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -89-2024-05-14-15:00--------------------------------------------------------

UPDATE
    `notification_email_config` noc
SET
    noc.addresses = concat(noc.addresses, ';<EMAIL>')
WHERE
    noc.process_id IN (
                       'ExpiredMedicalSuitabilityNotice',
                       'ExpiredForkliftSuitabilityNotice'
    )
  AND status = 2;

UPDATE `_sql_version` SET `revision`=90, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -90-2024-06-03-15:30--------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '{\"employee_rows_limit\": 10, \"search_days_limit\": 31, \"search_days_default_range\": 31, \"competency_rows_limit\": 10, \"employee_summary_version\": 1, \"restday_request\": 1, \"competency_group_ids_to_calculate\": [\"1\"] }' ,
    `note` = 'Prev value: {\"employee_rows_limit\": 10, \"search_days_limit\": 14, \"search_days_default_range\": 14, \"competency_rows_limit\": 10, \"employee_summary_version\": 1, \"restday_request\": 1, , \"competency_group_ids_to_calculate\": [\"1\"]}'
WHERE
    `setting_id` = 'shiftPageSettings' AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=91, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -91-2024-04-10-15:27--------------------------------------------------------

UPDATE
    `app_settings`
SET
    `setting_value` = '1',
    `note` = 'Prev. val: 0, Elérhető az ügyfél számára a mobil layout.',
    `modified_by` = 'SZOF-3709',
    `modified_on` = NOW()
WHERE
    `setting_id` = 'hasMobileLayout'
  AND `valid` = 1;

UPDATE `_sql_version` SET `revision`=92, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -92-2024-07-29-09:30--------------------------------------------------------

UPDATE
    `app_settings`
SET
    `setting_value` = '1',
    `modified_by` = 'SZOF-3768',
    `modified_on` = NOW()
WHERE
    `setting_id` = 'useWorkScheduleLegalControl'
  AND `valid` = 1;

UPDATE
    `app_settings`
SET
    `setting_value` = '1',
    `modified_by` = 'SZOF-3768',
    `modified_on` = NOW()
WHERE
    `setting_id` = 'legalControlOnWorkScheduleByGroup'
  AND `valid` = 1;

UPDATE
    `legal_control`
SET
    `status` = 5,
    `modified_by` = 'SZOF-3768',
    `modified_on` = NOW()
WHERE
    `status` = 2;

UPDATE
    `legal_control`
SET
    `status` = 2,
    `modified_by` = 'SZOF-3768',
    `modified_on` = NOW()
WHERE
    `legal_control_id` IN ('normal_daily_breaktime','special_daily_breaktime','shift_daily_breaktime');

UPDATE `_sql_version` SET `revision`=93, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -93-2024-07-29-17:00--------------------------------------------------------

UPDATE
    `legal_control`
SET
    `status` = 2,
    `modified_by` = 'SZOF-3768',
    `modified_on` = NOW()
WHERE
        `legal_control_id` IN ('annual_max_overtime_hours', 'daily_max_worktime_hours', 'weekly_max_worktime_hours_not_framework', 'weekly_max_worktime_hours_framework', 'daily_min_worktime_hours', 'monthly_max_overtime_hours')
    AND `status` = 5;

UPDATE `_sql_version` SET `revision`=94, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -94-2024-08-07-08:50--------------------------------------------------------

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES
    ('customers/eisberg/attendanceEvaluation', 'shift_type_id', 'ALL', 2, 'SZOF-3864', NOW());

UPDATE `_sql_version` SET `revision`=95, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -95-2024-08-08-13:00--------------------------------------------------------

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES
    ('customers/eisberg/attendanceEvaluation', 'daytype_name', 'ALL', 2, 'SZOF-3927', NOW());

UPDATE `_sql_version` SET `revision`=96, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -96-2024-08-15-16:50--------------------------------------------------------

INSERT INTO `icon` (`icon_id`, `icon_text_name`, `color`, `dict_id`, `status`, `created_by`, `created_on`)
VALUES
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_local_shipping', null, 'sym_o_local_shipping','2', 'system', NOW()),
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_manufacturing', null, 'sym_o_manufacturing','2', 'system', NOW());
    
INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `component`, `valid`) 
VALUES
    ('hu', 'vue', 'sym_o_local_shipping', 'Árukiadás', 'spa-shift', 1),
    ('hu', 'vue', 'sym_o_manufacturing', 'Tálcás gépkezelő', 'spa-shift', 1),
    ('en', 'vue', 'sym_o_local_shipping', 'Goods shipping', 'spa-shift', 1),
    ('en', 'vue', 'sym_o_manufacturing', 'Tray machine operator', 'spa-shift', 1);    
    
UPDATE `_sql_version` SET `revision`=97, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -97-2024-08-16-10:15--------------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'cplatform_absence_sequence', 'Sorrend', '1'),
    ('en', 'ttwa-base', 'cplatform_absence_sequence', 'Abs. Seq.', '1');

UPDATE `cplatform_button_config`
SET
    `classes` = 'absSeqButton',
    `button_text_dict_id` = 'cplatform_absence_sequence',
    `navigation` = '1',
    `navigation_url` = '/ahp/sequenceOfDailyAbsences/index',
    `controller_id` = 'ahp/sequenceOfDailyAbsences',
    `status` = 2,
    `modified_by` = 'SZOF-3964',
    `modified_on` = NOW()
WHERE `button_id` = 'dashbutton16';

UPDATE `_sql_version` SET `revision`=98, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -98-2024-08-28-10:55--------------------------------------------------------

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`) VALUES
	('sequenceOfDailyAbsences_exception', 'ahp/sequenceOfDailyAbsences --- sees_only_himself_exception', 'sees_only_himself szerepkörcsoport ellenére lát mindenkit a beállított jogok alapján', 'SZOF-4020', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `column_name`, `operation_id`, `usergroup_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
	('sequenceOfDailyAbsences_exception', 'ahp/sequenceOfDailyAbsences', NULL, 'sees_only_himself_exception', NULL, 1, 1, 'SZOF-4020', NOW());

UPDATE `_sql_version` SET `revision`=99, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -99-2024-09-02-13:30--------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = 'en',
    `modified_by` = 'SZOF-4242',
    `modified_on` = NOW(),
    `note` = 'Prev value: '
WHERE `setting_id` = 'competencyTranslations';

UPDATE `_sql_version` SET `revision`=100, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -100-2024-10-30-12:30--------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '',
    `modified_by` = 'SZOF-4489',
    `modified_on` = NOW(),
    `note` = 'Prev value: a272f564576d443e7832587126b070aa'
WHERE `setting_id` = 'absenceCondition';

UPDATE `_sql_version` SET `revision`=101, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -101-2024-11-22-11:30--------------------------------------------------------    

UPDATE
    `app_settings`
SET `setting_value` = '1',
    `modified_by` = 'SZOF-SZOF-4487',
    `modified_on` = NOW(),
    `note` = 'Prev value: 0'
WHERE `setting_id` = 'noAbsenceOnScheduledHolidays';

UPDATE `_sql_version` SET `revision`=102, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -102-2024-11-25-11:30--------------------------------------------------------

INSERT INTO `icon` (`icon_id`, `icon_text_name`, `color`, `dict_id`, `status`, `created_by`, `created_on`)
VALUES
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_autorenew', null, 'sym_o_autorenew','2', 'system', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `component`, `valid`)
VALUES
    ('hu', 'vue', 'sym_o_autorenew', 'Váltó megnevezésű', 'spa-shift', 1),
	('en', 'vue', 'sym_o_autorenew', 'Switch name', 'spa-shift', 1);

UPDATE `_sql_version` SET `revision`=103, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -103-2024-11-25-11:30--------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = 'ebf4ac30e7dc238fd2f4bc86332e0675;628834878c5bc72f60283e37865678b6;f4f63ae4dd65cd97ee1f409d8b620806;absence_type_addiction;def32968390fb987c823da0cbf7d3bd8;fceedab36bb56f3a59665972fa0a7d54;17c82fbc41e2abf10dff3a857222ec14;8cb3945a0a8b1afa9d064d064584e5d4;3ad610cfce362896dbb4b11858dfae40;bfa221167d9944ce0ffcc8dffbbaf589;f691735315003fcaee9470c752d126f6;eb49097c829f3f5140e41e92eae91202;absence_type_baby_care_fee;269b59b4efbbb4ef1d527492dc06cb60;absence_type_paid_dispbyemployer;absence_type_delegacy_in;absence_type_delegacy_ext;6193a37b5816b;a272f564576d443e7832587126b070aa;945e3618f487efc4325204521e49c5f3;7b3cf78f9fd2a404567fe572fcb0eaf9;65dc77c3e5d3b;1ecfd16854c92211d6278a918038903d;667d7db52b2ba',
    `modified_by` = 'SZOF-5127',
    `modified_on` = NOW(),
    `note` = 'Prev value: null'
WHERE `setting_id` = 'legalControl_intermittorStateTypeIds';

UPDATE `_sql_version` SET `revision`=104, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -104-2025-03-10-09:45--------------------------------------------------------

INSERT INTO `icon` (`icon_id`, `icon_text_name`, `color`, `dict_id`, `status`, `created_by`, `created_on`)
VALUES
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_deployed_code_account', null, 'sym_o_deployed_code_account','2', 'system', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `component`, `valid`)
VALUES
    ('hu', 'vue', 'sym_o_deployed_code_account', 'Kiszolgáló', 'spa-shift', 1),
	('en', 'vue', 'sym_o_deployed_code_account', 'Counterman', 'spa-shift', 1);

UPDATE `_sql_version` SET `revision`=105, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -105-2025-03-12-11:00--------------------------------------------------------

INSERT INTO `icon` (`icon_id`, `icon_text_name`, `color`, `dict_id`, `status`, `created_by`, `created_on`)
VALUES
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_forklift', null, 'sym_o_forklift','2', 'system', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `component`, `valid`)
VALUES
    ('hu', 'vue', 'sym_o_forklift', 'Targoncás', 'spa-shift', 1),
	('en', 'vue', 'sym_o_forklift', 'Forklift', 'spa-shift', 1);

UPDATE `_sql_version` SET `revision`=106, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -106-2025-03-20-10:00--------------------------------------------------------

UPDATE
    dictionary
SET
    dict_value = '5'
WHERE
    dict_id = 'ext4_option2_20221117164724924';

UPDATE `_sql_version` SET `revision`=107, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -107-2025-04-24-10:00--------------------------------------------------------

UPDATE
    `option_config`
SET
    `status` = 2,
    `modified_by` = 'SZOF-5598',
    `settings` = '{"width": 600}',
    `modified_on` = NOW()
WHERE
    `option_id` = 'ext5_option10';

UPDATE
    `dictionary`
SET
    `dict_value` = 'Indoklás, megvonás esetén'
WHERE
    `dict_id` =  'ext5_option10'
  AND `valid` = 1
  AND `lang` = 'hu';

UPDATE
    `dictionary`
SET
    `dict_value` = 'Reasoning, in case of withdrawal'
WHERE
    `dict_id` =  'ext5_option10'
  AND `valid` = 1
  AND `lang` = 'en';

INSERT INTO `icon` (`icon_id`, `icon_text_name`, `color`, `dict_id`, `status`, `created_by`, `created_on`)
VALUES
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_fact_check', null, 'sym_o_fact_check','2', 'system', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `component`, `valid`)
VALUES
    ('hu', 'vue', 'sym_o_fact_check', 'Fólia bekészítés', 'spa-shift', 1),
	('en', 'vue', 'sym_o_fact_check', 'Foil preparation', 'spa-shift', 1);

UPDATE `_sql_version` SET `revision`=108, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -108-2025-05-29-10:00--------------------------------------------------------

INSERT INTO `icon` (`icon_id`, `icon_text_name`, `color`, `dict_id`, `status`, `created_by`, `created_on`)
VALUES
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_settings_accessibility', null, 'sym_o_settings_accessibility','2', 'system', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `component`, `valid`)
VALUES
    ('hu', 'vue', 'sym_o_settings_accessibility', 'Kihozatali felelős', 'spa-shift', 1),
	('en', 'vue', 'sym_o_settings_accessibility', 'Release manager', 'spa-shift', 1);

UPDATE `_sql_version` SET `revision`=109, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -109-2025-06-18-10:00--------------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`)
VALUES
    ('hu', 'ttwa-wfm', 'additional_area_of_work', 'Kiegészítő terület', 1),
	('en', 'ttwa-wfm', 'additional_area_of_work', 'Additional area', 1);

UPDATE `_sql_version` SET `revision`=110, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -110-2025-09-16-22:00--------------------------------------------------------

UPDATE
    dictionary
SET
    dict_value = 'További adatok III.'
WHERE
    dict_id = 'tab_employeetabs_employeeext4'
    AND lang = 'hu'
    AND module = 'ttwa-base'
    AND valid = 1;

UPDATE
    dictionary
SET
    dict_value = 'Personal data III.'
WHERE
    dict_id = 'tab_employeetabs_employeeext4'
    AND lang = 'en'
    AND module = 'ttwa-base'
    AND valid = 1;

INSERT INTO `icon` (`icon_id`, `icon_text_name`, `color`, `dict_id`, `status`, `created_by`, `created_on`)
VALUES
    ((md5(CONCAT('Icon', NOW(), RAND()*1000, `icon_id`))),'sym_o_shelves', null, 'sym_o_shelves','2', 'system', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `component`, `valid`)
VALUES
    ('hu', 'vue', 'sym_o_shelves', 'Fólia bekészítés', 'spa-shift', 1),
	('en', 'vue', 'sym_o_shelves', 'Shelves preparation', 'spa-shift', 1);

UPDATE `_sql_version` SET `revision`=111, `updated_on`=NOW() WHERE `module`='c_eisberg';

-- VERSION -111-2025-09-17-10:00--------------------------------------------------------
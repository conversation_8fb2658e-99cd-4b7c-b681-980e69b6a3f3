-- # RELEASE 1.0

INSERT INTO `_sql_version` (`id`, `module`, `major_minor`, `revision`, `updated_on`)
VALUES (NULL, 'c_havi', '1.0', '0', NOW()) ON DUPLICATE KEY UPDATE `module`=VALUES(`module`), `major_minor`=VALUES(`major_minor`), `revision`=VALUES(`revision`);

UPDATE `_sql_version` SET `revision`=1, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -1--2024-03-21-10:00---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Jogosítványok' WHERE `dict_id` = 'tab_employeetabs_employeeext2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Licenses' WHERE `dict_id` = 'tab_employeetabs_employeeext2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkocsivezető engedély' WHERE `dict_id` = 'tab_employeetabs_employeeext3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Driver permit' WHERE `dict_id` = 'tab_employeetabs_employeeext3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Targoncás jogosítvány' WHERE `dict_id` = 'tab_employeetabs_employeeext4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Forklift license' WHERE `dict_id` = 'tab_employeetabs_employeeext4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogosítvány száma' WHERE `dict_id` = 'ext2_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'License number' WHERE `dict_id` = 'ext2_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogosítvány lejárat' WHERE `dict_id` = 'ext2_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'License valid to' WHERE `dict_id` = 'ext2_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogosítvány száma' WHERE `dict_id` = 'ext2_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'License number' WHERE `dict_id` = 'ext2_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'GKI kártya sorszáma' WHERE `dict_id` = 'ext3_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Serial number of GKI card' WHERE `dict_id` = 'ext3_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'GKI kártya lejárat' WHERE `dict_id` = 'ext3_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'GKI card valid to' WHERE `dict_id` = 'ext3_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Digitális tach. kártya' WHERE `dict_id` = 'ext3_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Digital tach. card' WHERE `dict_id` = 'ext3_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Digitális tach. kártya lejárata' WHERE `dict_id` = 'ext3_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Digital tach. card valid to' WHERE `dict_id` = 'ext3_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Könnyűgépk. sorszáma' WHERE `dict_id` = 'ext4_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Forklift serial number' WHERE `dict_id` = 'ext4_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Könnyűgépk. sorszáma lejárat' WHERE `dict_id` = 'ext4_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Forklift serial number valid to' WHERE `dict_id` = 'ext4_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = 7, `modified_by` = 'SZOF-3206', `modified_on` = NOW() WHERE `option_id` LIKE 'ext2_option%' AND `option_id` NOT IN ('ext2_option1', 'ext2_option2') AND `status` = 2;
UPDATE `option_config` SET `status` = 7, `modified_by` = 'SZOF-3206', `modified_on` = NOW() WHERE `option_id` LIKE 'ext3_option%' AND `option_id` NOT IN ('ext3_option1', 'ext3_option2', 'ext3_option3', 'ext3_option4') AND `status` = 2;
UPDATE `option_config` SET `status` = 7, `modified_by` = 'SZOF-3206', `modified_on` = NOW() WHERE `option_id` LIKE 'ext4_option%' AND `option_id` NOT IN ('ext4_option1', 'ext4_option2') AND `status` = 2;

UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3206', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option2', 'ext3_option2', 'ext3_option4', 'ext4_option2') AND `status` = 2;

UPDATE `_sql_version` SET `revision`=2, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -2--2024-05-08-15:55---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Raktáros jogosítványok' WHERE `dict_id` = 'ext6_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Warehouse licenses' WHERE `dict_id` = 'ext6_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=3, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -3--2024-05-21-15:30---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Üzemorvosi adatok' WHERE `dict_id` = 'tab_employeetabs_employeeext6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Company medical data' WHERE `dict_id` = 'tab_employeetabs_employeeext6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Üzemorvosi lejárata' WHERE `dict_id` = 'ext6_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Expiry date of occupational medicine' WHERE `dict_id` = 'ext6_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=4, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -4--2024-05-27-16:00---------------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '1',
    `modified_by` = 'SZOF-3533',
    `modified_on` = NOW(),
    `note` = 'Prev value: 0'
WHERE `setting_id` = 'workScheduleByGroup_daytype_bgcolor';

UPDATE `_sql_version` SET `revision`=5, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -5--2024-06-17-16:00---------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'havi', `modified_by` = 'SZOF-3521', `modified_on` = NOW() WHERE `setting_id` = 'cplatform_client';
UPDATE `app_settings` SET `setting_value` = 'havi', `modified_by` = 'SZOF-3521', `modified_on` = NOW() WHERE `setting_id` = 'cplatform_skin';

UPDATE `cplatform_button_config` SET `classes` = NULL, `button_text_dict_id` = NULL, `navigation` = '0', `navigation_url` = NULL, `status` = '5';
UPDATE `cplatform_button_config` SET `classes` = 'entryExit entryButton', `button_text_dict_id` = 'cplatform_in', `navigation` = '0', `navigation_url` = NULL, `status` = '2', `modified_by` = 'SZOF-3521', `modified_on` = NOW() WHERE `button_id` = 'dashbutton1';
UPDATE `cplatform_button_config` SET `classes` = 'entryExit exitButton', `button_text_dict_id` = 'cplatform_out', navigation = '0', navigation_url = NULL, status = '2' , `modified_by` = 'SZOF-3521', `modified_on` = NOW() WHERE `button_id` = 'dashbutton2';

UPDATE `_sql_version` SET `revision`=6, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -6--2024-06-13-16:15---------------------------------------------------------------

UPDATE `option_config` SET `status` = 2, `modified_by` = 'SZOF-3539', `modified_on` = '2024-06-20' WHERE `option_id` IN ('ext5_option2', 'ext5_option3', 'ext5_option4', 'ext5_option5', 'ext5_option6');

UPDATE `dictionary` SET `dict_value` = 'Mozgóbér adatok' WHERE `dict_id` = 'tab_employeetabs_employeeext5' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Mobile wage data' WHERE `dict_id` = 'tab_employeetabs_employeeext5' AND `lang` = 'en';

UPDATE `dictionary` SET `dict_value` = 'Teljesítmény prémium (60%) összeg' WHERE `dict_id` = 'ext5_option1' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Performance premium (60%) amount' WHERE	`dict_id` = 'ext5_option1' AND `lang` = 'en';

UPDATE `dictionary` SET `dict_value` = 'Pontos munkavégzés prémium (25%) összeg' WHERE `dict_id` = 'ext5_option2' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Punctuality premium (25%) amount' WHERE	`dict_id` = 'ext5_option2' AND `lang` = 'en';

UPDATE `dictionary` SET `dict_value` = 'Karbantartás selejt (15%) összeg' WHERE	`dict_id` = 'ext5_option3' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Maintenance scrap (15%) amount' WHERE `dict_id` = 'ext5_option3' AND `lang` = 'en';

UPDATE `dictionary` SET `dict_value` = 'Input Teljesítmény prémium (60%)' WHERE	`dict_id` = 'ext5_option4' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Input Performance premium (60%)' WHERE	`dict_id` = 'ext5_option4' AND `lang` = 'en';

UPDATE `dictionary` SET `dict_value` = 'Input Pontos munkavégzés prémium (25%)' WHERE `dict_id` = 'ext5_option5' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Input Accurate work premium (25%)' WHERE `dict_id` = 'ext5_option5' AND `lang` = 'en';

UPDATE `dictionary` SET `dict_value` = 'Input Karbantartás selejt (15%)' WHERE `dict_id` = 'ext5_option6' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Input Maintenance Scrap (15%)' WHERE `dict_id` = 'ext5_option6' AND `lang` = 'en';

UPDATE `app_settings` SET `setting_value` = 'HAVI', `modified_by` = 'SZOF-3539', `modified_on` = NOW() WHERE `setting_id` = 'ptr_customer';

UPDATE `_sql_version` SET `revision`=7, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -7--2024-06-20-16:15---------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `modified_by` = 'SZOF-3614', `modified_on` = NOW() WHERE `setting_id` = 'hasMobileLayout';
UPDATE `app_settings` SET `setting_value` = '1', `modified_by` = 'SZOF-3614', `modified_on` = NOW() WHERE `setting_id` = 'approveGtcOnMobile';

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('fd4c638da5f85d025963f99fe90b1b1a', 'mobile_absence_approver', 'SZOF-3614', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('fd4c638da5f85d025963f99fe90b1b1a', 'mobile_absence_planner', 'SZOF-3614', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('fd4c638da5f85d025963f99fe90b1b1a', 'mobile_chat', 'SZOF-3614', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('fd4c638da5f85d025963f99fe90b1b1a', 'mobile_view_schedule', 'SZOF-3614', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('fd4c638da5f85d025963f99fe90b1b1a', 'worktimeManagement', 'SZOF-3614', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('d59cdeed7b7777138611c963ddfdb452', 'mobile_absence_approver', 'SZOF-3614', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('d59cdeed7b7777138611c963ddfdb452', 'mobile_absence_planner', 'SZOF-3614', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('d59cdeed7b7777138611c963ddfdb452', 'mobile_chat', 'SZOF-3614', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('d59cdeed7b7777138611c963ddfdb452', 'mobile_view_schedule', 'SZOF-3614', NOW());

INSERT IGNORE INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
    ('d59cdeed7b7777138611c963ddfdb452', 'worktimeManagement', 'SZOF-3614', NOW());


UPDATE `_sql_version` SET `revision`=8, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -8--2024-06-28-12:15---------------------------------------------------------------

UPDATE `dictionary`
SET
    `dict_value` = 'Már létezik azonos nevű dokumentum ebben az időszakban. Kérjük, válasszon másik nevet vagy időszakot!'
WHERE `dict_id` = 'templateUploadError_existingTemplateFilename' AND `lang` = 'hu';

UPDATE `dictionary` SET dict_value = 'A document with the same name already exists, the upload was unsuccessful!' WHERE `dict_id` = 'templateUploadError_existingTemplateFilename' AND `lang` = 'en';

UPDATE `dictionary`
SET
    `dict_value` = 'A fájl mérete túl nagy. Kérjük, válasszon 2 MB-nál kisebb méretű fájlt!'
WHERE `dict_id` = 'templateUploadError_maxFileSize' AND `lang` = 'hu';

UPDATE `dictionary` SET `dict_value` = 'Dokumentum feltöltés' WHERE `dict_id` IN ('menu_item_document_management','page_title_document_management') AND `lang` = 'hu';

UPDATE `_sql_version` SET `revision`=9, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -9--2024-09-23-09:30---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'SZABÁLYZATOK' WHERE `dict_id` IN ('menu_item_document_viewer', 'page_title_document_viewer') AND `lang` = 'hu';

UPDATE `_sql_version` SET `revision`=10, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -10--2024-10-01-11:10---------------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '0',
    `modified_by` = 'SZOF-4458',
    `modified_on` = NOW(),
    `note` = 'Prev value: 1'
WHERE `setting_id` = 'showMenuOnHover';

UPDATE `_sql_version` SET `revision`=11, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -11--2024-11-20-13:30---------------------------------------------------------------

UPDATE `search_filter` SET `status` = 7 WHERE `controller_id` = 'payrollTransfer' AND `status` = 2;

INSERT INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `note`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`, `pre_row_id`) VALUES
('payrollTransfer', 'date', 'EMPLOYEE_WITH_DATE', 'combo', 0, NULL, 2, 'system', '2024-11-25 12:00:00', NULL, NULL, NULL),
('payrollTransfer', 'group', 'COMPANY', 'combo', 0, NULL, 2, 'system', '2024-11-25 12:00:00', NULL, NULL, NULL),
('payrollTransfer', 'group', 'PAYROLL', 'combo', 0, NULL, 2, 'system', '2024-11-25 12:00:00', NULL, NULL, NULL),
('payrollTransfer', 'group', 'WORKGROUP', 'combo', 0, NULL, 2, 'system', '2024-11-25 12:00:00', NULL, NULL, NULL),
('payrollTransfer', 'group', 'COMPANY_ORG_GROUP2', 'combo', 0, NULL, 2, 'system', '2024-11-25 12:00:00', NULL, NULL, NULL),
('payrollTransfer', 'group', 'EMPLOYEECONTRACT', 'auto', 0, NULL, 2, 'system', '2024-11-25 12:00:00', NULL, NULL, NULL);

UPDATE `_sql_version` SET `revision`=12, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -12--2024-11-25-13:30---------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `modified_by` = 'SZOF-4437', `modified_on` = NOW(), `note` = 'Prev. val.: 0, További adatok betöltésénél, automatikusan betölti a nem megadott oszlopok bent lévő értékét is.' WHERE `setting_id` = 'automaticFieldsLoadInEmployeeUpload';

UPDATE `_sql_version` SET `revision`=13, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -13--2024-11-28-11:00---------------------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES 
('hu', 'ttwa-base', 'notification_occupational_health', 'Az üzemorvosi le fog járni 45 nap múlva.', 1),
('hu', 'ttwa-base', 'notification_drivers_license', 'A jogosítvány le fog járni 30 nap múlva.', 1),
('hu', 'ttwa-base', 'notification_forklift_license', 'A targoncás jogosítvány le fog járni 30 nap múlva.', 1),
('hu', 'ttwa-base', 'notification_gki_card', 'A GKI kártya le fog járni 30 nap múlva.', 1),
('hu', 'ttwa-base', 'notification_digital_tach_card', 'A Digitális tach. kártya le fog járni 30 nap múlva.', 1);

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('en', 'ttwa-base', 'notification_occupational_health', 'Your occupational health certificate will expire in 45 days.', 1),
('en', 'ttwa-base', 'notification_drivers_license', 'Your drivers license will expire in 30 days.', 1),
('en', 'ttwa-base', 'notification_forklift_license', 'Your forklift operators license will expire in 30 days.', 1),
('en', 'ttwa-base', 'notification_gki_card', 'Your Driver Qualification Card (GKI) will expire in 30 days.', 1),
('en', 'ttwa-base', 'notification_digital_tach_card', 'Your Digital Tachograph Card will expire in 30 days.', 1);

UPDATE `_sql_version` SET `revision`=14, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -14--2025-03-05-13:50---------------------------------------------------------------

INSERT IGNORE INTO `notification_email_config` (`process_id`,`sql`,`addresses`,`day_before_event`,`file_name`,`subject`,`message_text_dict_id`,`data_to_csv`,`rolegroup_ids`,`note`,`status`,`sendToEmployees`,`sendEmptyNotificationEmail`,`process_ids`) VALUES
('notificationExpiringOccupationalHealth',
       'SELECT
             CONCAT(e.`last_name`," ", e.`first_name`) AS fullname,
             ep.`employee_position_name` AS position_name,
             e.`emp_id` AS emp_id,
             ext6.`ext6_option1` AS field_value,
             d.`dict_value` AS field_name
         FROM `employee_ext6` ext6
         LEFT JOIN `employee` e ON
                 e.`employee_id` = ext6.`employee_id`
             AND e.`status` = 2
             AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, "2038-01-01")
        LEFT JOIN `employee_contract` ec ON
                 ec.`employee_id` = e.`employee_id`
             AND ec.`status` = 2
             AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, "2038-01-01")
 			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, "2038-01-01")
 		LEFT JOIN `employee_position` ep ON
 				ep.`employee_position_id` = ec.`employee_position_id`
 			AND ep.`status` = 2
             AND CURDATE() BETWEEN ep.`valid_from` AND IFNULL(ep.`valid_to`, "2038-01-01")
         LEFT JOIN `dictionary` d ON
          		d.`dict_id` = "ext6_option1"
          	AND d.`lang` = "hu"
 			AND d.`valid` = 1
         WHERE
                 ext6.`status` = "2"
             AND CURDATE() BETWEEN ext6.`valid_from` AND IFNULL(ext6.`valid_to`, "2038-01-01")
             AND DATE(ext6.`ext6_option1`) = CURDATE() + INTERVAL 45 DAY
             AND ext6.`row_id` IS NOT NULL
             AND e.`row_id` IS NOT NULL
             AND ec.`row_id` IS NOT NULL
             GROUP BY e.emp_id
             ORDER BY fullname',
       '',0,'notificationExpiringLicenses','Üzemorvosi lejárata','notification_occupational_health',0,'d59cdeed7b7777138611c963ddfdb452',NULL,2,0,0,NULL
   ),
('notificationExpiringDriversLicense',
    'SELECT
             CONCAT(e.`last_name`," ", e.`first_name`) AS fullname,
             ep.`employee_position_name` AS position_name,
             e.`emp_id` AS emp_id,
             ext2.`ext2_option2` AS field_value,
             d.`dict_value` AS field_name
         FROM `employee_ext2` ext2
         LEFT JOIN `employee` e ON
                 e.`employee_id` = ext2.`employee_id`
             AND e.`status` = 2
             AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, "2038-01-01")
        LEFT JOIN `employee_contract` ec ON
                 ec.`employee_id` = e.`employee_id`
             AND ec.`status` = 2
             AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, "2038-01-01")
 			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, "2038-01-01")
 		LEFT JOIN `employee_position` ep ON
 				ep.`employee_position_id` = ec.`employee_position_id`
 			AND ep.`status` = 2
             AND CURDATE() BETWEEN ep.`valid_from` AND IFNULL(ep.`valid_to`, "2038-01-01")
         LEFT JOIN `dictionary` d ON
          		d.`dict_id` = "ext2_option2"
          	AND d.`lang` = "hu"
 			AND d.`valid` = 1
         WHERE
                 ext2.`status` = "2"
             AND CURDATE() BETWEEN ext2.`valid_from` AND IFNULL(ext2.`valid_to`, "2038-01-01")
             AND DATE(ext2.`ext2_option2`) = CURDATE() + INTERVAL 30 DAY
             AND ext2.`row_id` IS NOT NULL
             AND e.`row_id` IS NOT NULL
             AND ec.`row_id` IS NOT NULL
             GROUP BY e.emp_id
             ORDER BY fullname',
    '',0,'notificationExpiringLicenses','Jogosítvány lejárata','notification_drivers_license',0,'d59cdeed7b7777138611c963ddfdb452',NULL,2,0,0,NULL
),
('notificationExpiringForkliftLicenseWarehouseWorker',
       'SELECT
             CONCAT(e.`last_name`," ", e.`first_name`) AS fullname,
             ep.`employee_position_name` AS position_name,
             e.`emp_id` AS emp_id,
             ext4.`ext4_option2` AS field_value,
             d.`dict_value` AS field_name
         FROM `employee_ext4` ext4
         LEFT JOIN `employee` e ON
                 e.`employee_id` = ext4.`employee_id`
             AND e.`status` = 2
             AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, "2038-01-01")
        LEFT JOIN `employee_contract` ec ON
                 ec.`employee_id` = e.`employee_id`
             AND ec.`status` = 2
             AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, "2038-01-01")
 			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, "2038-01-01")
 		LEFT JOIN `employee_position` ep ON
 				ep.`employee_position_id` = ec.`employee_position_id`
 			AND ep.`status` = 2
             AND CURDATE() BETWEEN ep.`valid_from` AND IFNULL(ep.`valid_to`, "2038-01-01")
         LEFT JOIN `dictionary` d ON
          		d.`dict_id` = "ext4_option2"
          	AND d.`lang` = "hu"
 			AND d.`valid` = 1
         WHERE
                 ext4.`status` = "2"
             AND CURDATE() BETWEEN ext4.`valid_from` AND IFNULL(ext4.`valid_to`, "2038-01-01")
             AND DATE(ext4.`ext4_option2`) = CURDATE() + INTERVAL 30 DAY
             AND ep.`employee_position_id` = "67"
             AND ext4.`row_id` IS NOT NULL
             AND e.`row_id` IS NOT NULL
             AND ec.`row_id` IS NOT NULL
             GROUP BY e.emp_id
             ORDER BY fullname',
   '',0,'notificationExpiringLicenses','Targoncás jogosítvány lejárata','notification_forklift_license',0,'d59cdeed7b7777138611c963ddfdb452',NULL,2,0,0,NULL
),
('notificationExpiringForkliftLicenseDriver',
       'SELECT
             CONCAT(e.`last_name`," ", e.`first_name`) AS fullname,
             ep.`employee_position_name` AS position_name,
             e.`emp_id` AS emp_id,
             ext4.`ext4_option2` AS field_value,
             d.`dict_value` AS field_name
         FROM `employee_ext4` ext4
         LEFT JOIN `employee` e ON
                 e.`employee_id` = ext4.`employee_id`
             AND e.`status` = 2
             AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, "2038-01-01")
        LEFT JOIN `employee_contract` ec ON
                 ec.`employee_id` = e.`employee_id`
             AND ec.`status` = 2
             AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, "2038-01-01")
 			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, "2038-01-01")
 		LEFT JOIN `employee_position` ep ON
 				ep.`employee_position_id` = ec.`employee_position_id`
 			AND ep.`status` = 2
             AND CURDATE() BETWEEN ep.`valid_from` AND IFNULL(ep.`valid_to`, "2038-01-01")
         LEFT JOIN `dictionary` d ON
          		d.`dict_id` = "ext4_option2"
          	AND d.`lang` = "hu"
 			AND d.`valid` = 1
         WHERE
                 ext4.`status` = "2"
             AND CURDATE() BETWEEN ext4.`valid_from` AND IFNULL(ext4.`valid_to`, "2038-01-01")
             AND DATE(ext4.`ext4_option2`) = CURDATE() + INTERVAL 30 DAY
             AND ep.`employee_position_id` = "62"
             AND ext4.`row_id` IS NOT NULL
             AND e.`row_id` IS NOT NULL
             AND ec.`row_id` IS NOT NULL
             GROUP BY e.emp_id
             ORDER BY fullname',
   '',0,'notificationExpiringLicenses','Targoncás jogosítvány lejárata','notification_forklift_license',0,'d59cdeed7b7777138611c963ddfdb452',NULL,2,0,0,NULL
),
('notificationExpiringGkiCard',
       'SELECT
             CONCAT(e.`last_name`," ", e.`first_name`) AS fullname,
             ep.`employee_position_name` AS position_name,
             e.`emp_id` AS emp_id,
			ext3.`ext3_option2` AS field_value,
             d.`dict_value` AS field_name
         FROM `employee_ext3` ext3
         LEFT JOIN `employee` e ON
                 e.`employee_id` = ext3.`employee_id`
             AND e.`status` = 2
             AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, "2038-01-01")
        LEFT JOIN `employee_contract` ec ON
                 ec.`employee_id` = e.`employee_id`
             AND ec.`status` = 2
             AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, "2038-01-01")
 			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, "2038-01-01")
 		LEFT JOIN `employee_position` ep ON
 				ep.`employee_position_id` = ec.`employee_position_id`
 			AND ep.`status` = 2
             AND CURDATE() BETWEEN ep.`valid_from` AND IFNULL(ep.`valid_to`, "2038-01-01")
         LEFT JOIN `dictionary` d ON
          		d.`dict_id` = "ext3_option2"
          	AND d.`lang` = "hu"
 			AND d.`valid` = 1
         WHERE
                 ext3.`status` = "2"
             AND CURDATE() BETWEEN ext3.`valid_from` AND IFNULL(ext3.`valid_to`, "2038-01-01")
             AND DATE(ext3.`ext3_option2`) = CURDATE() + INTERVAL 30 DAY
             AND ext3.`row_id` IS NOT NULL
             AND e.`row_id` IS NOT NULL
             AND ec.`row_id` IS NOT NULL
             GROUP BY e.emp_id
             ORDER BY fullname',
   '',0,'notificationExpiringLicenses','GKI kártya lejárata','notification_gki_card',0,'d59cdeed7b7777138611c963ddfdb452',NULL,2,0,0,NULL
),
('notificationExpiringDigitalTachCard',
       'SELECT
             CONCAT(e.`last_name`," ", e.`first_name`) AS fullname,
             ep.`employee_position_name` AS position_name,
             e.`emp_id` AS emp_id,
			ext3.`ext3_option4` AS field_value,
             d.`dict_value` AS field_name
         FROM `employee_ext3` ext3
         LEFT JOIN `employee` e ON
                 e.`employee_id` = ext3.`employee_id`
             AND e.`status` = 2
             AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, "2038-01-01")
        LEFT JOIN `employee_contract` ec ON
                 ec.`employee_id` = e.`employee_id`
             AND ec.`status` = 2
             AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, "2038-01-01")
 			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, "2038-01-01")
 		LEFT JOIN `employee_position` ep ON
 				ep.`employee_position_id` = ec.`employee_position_id`
 			AND ep.`status` = 2
             AND CURDATE() BETWEEN ep.`valid_from` AND IFNULL(ep.`valid_to`, "2038-01-01")
         LEFT JOIN `dictionary` d ON
          		d.`dict_id` = "ext3_option4"
          	AND d.`lang` = "hu"
 			AND d.`valid` = 1
         WHERE
                 ext3.`status` = "2"
             AND CURDATE() BETWEEN ext3.`valid_from` AND IFNULL(ext3.`valid_to`, "2038-01-01")
             AND DATE(ext3.`ext3_option4`) = CURDATE() + INTERVAL 30 DAY
             AND ext3.`row_id` IS NOT NULL
             AND e.`row_id` IS NOT NULL
             AND ec.`row_id` IS NOT NULL
             GROUP BY e.emp_id
             ORDER BY fullname',
   '',0,'notificationExpiringLicenses','Digitális tach. kártya lejárata','notification_digital_tach_card',0,'d59cdeed7b7777138611c963ddfdb452',NULL,2,0,0,NULL
);

UPDATE `_sql_version` SET `revision`=15, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -15--2025-03-05-14:15---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Pótlék átalány' WHERE `dict_id` = 'es_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Flat rate supplement' WHERE `dict_id` = 'es_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=16, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -16--2025-03-28-11:00---------------------------------------------------------------
INSERT INTO payroll_transfer_config (row_id, target, transfer_id, content_type, content_category, content_value, null_value, select_value, select_as, total_sum, weekday, is_restday, is_holiday, is_public_holiday, sql_conditions, sql_conditions_2, note, status, created_by, created_on, modified_by, modified_on, pre_row_id)
VALUES (null, 'NEXON', '34', 'state_type_id', null, 'absence_type_ml', 0, null, null, 0, null, 0, 0, 0, null, null, 'Felmentés mv.alól', 2, 'ptc_sys', null, null, null, null);

UPDATE `_sql_version` SET `revision`=17, `updated_on`=NOW() WHERE `module` = 'c_havi';
-- VERSION -17--2025-05-07-16:45---------------------------------------------------------------

UPDATE payroll_transfer_config
SET status = 5
WHERE transfer_id IN ('CM', 'CE', 'T2');

UPDATE payroll_transfer_config
SET sql_conditions_2 = '(data.`inside_type_id` LIKE "paidot%" AND data.`wage_type` = "MoLo")'
WHERE transfer_id = 'T1';

UPDATE payroll_transfer_config
SET sql_conditions_2 = '(data.`inside_type_id` LIKE "paidot%" AND data.`wage_type` = "MoLo")'
WHERE transfer_id = 'TA';

INSERT INTO payroll_transfer_config
(target, transfer_id, content_type, content_category, content_value, null_value, select_value, select_as,
 total_sum, weekday, is_restday, is_holiday, is_public_holiday, sql_conditions, sql_conditions_2, note, status,
 created_by, created_on, modified_by, modified_on, pre_row_id)
VALUES
    ('NEXON', 'AIP', 'inside_type_id', NULL, NULL, '0',
     NULL, NULL, '0', NULL, '0', '0', '0', NULL,
     '(data.`inside_type_id` LIKE "paidot%"
         AND data.`value` < 0
         AND data.`wage_type` = "OB"
     )',
     'Állásidő', '2', 'ptc_sys', NULL, NULL, NULL, NULL),
    ('NEXON', 'T8', 'inside_type_id', NULL, NULL, '0',
     NULL, NULL, '0', NULL, '0', '0', '0', NULL,
     '(data.`inside_type_id` LIKE "paidot%"
         AND data.`value` > 0
         AND data.`wage_type` = "OB"
     )',
     'MIK túlóra 50% raktár', '2', 'ptc_sys', NULL, NULL, NULL, NULL),

    ('NEXON', 'T3', 'inside_type_id', NULL, NULL, '0',
     NULL, NULL, '0', NULL, '0', '0', '0', NULL,
     '(data.`inside_type_id` LIKE "ot%" AND data.`wage_type` = "OB")',
     'Túlóra 50% raktár', '2', 'ptc_sys', NULL, NULL, NULL, NULL),

    ('NEXON', 'ÁI', 'inside_type_id', NULL, NULL, '0',
     NULL, NULL, '0', NULL, '0', '0', '0', NULL,
     '(data.`inside_type_id` LIKE "paidot%"
         AND data.`value` < 0
         AND data.`wage_type` = "OB"
     )',
     'Állásidő', '2', 'ptc_sys', NULL, NULL, NULL, NULL)
;

UPDATE `_sql_version` SET `revision`=18, `updated_on`=NOW() WHERE `module` = 'c_havi';
-- VERSION -18--2025-06-24-13:00---------------------------------------------------------------

UPDATE payroll_transfer_config
SET sql_conditions_2 = '(data.`inside_type_id` LIKE "paidot%" AND data.`wage_type` = "MoLo")
OR (data.`inside_type_id` LIKE "ot%" AND data.`wage_type` = "OB")'
WHERE row_id = 9;

UPDATE `_sql_version` SET `revision`=19, `updated_on`=NOW() WHERE `module` = 'c_havi';
-- VERSION -19--2025-09-01-15:30---------------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = 'CONTRACT',
    `modified_by` = 'SZOF-6005',
    `modified_on` = NOW(),
    `note` = 'Prev value: WORKSCHEDULE, Napi munkaóráinak helye (CONTRACT, WORKGROUP, WORKSCHEDULE) CONTRACT, WORKGROUP, WORKSCHEDULE, prev val: CONTRACT, DEV-7877'
WHERE `setting_id` = 'dailyWorktimePlace';

UPDATE `_sql_version` SET `revision`=20, `updated_on`=NOW() WHERE `module` = 'c_havi';

-- VERSION -20--2025-09-11-14:00---------------------------------------------------------------
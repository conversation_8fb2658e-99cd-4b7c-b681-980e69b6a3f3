-- # RELEASE 1.0

INSERT INTO `_sql_version` (`id`, `module`, `major_minor`, `revision`, `updated_on`)
VALUES (NULL, 'c_hopi', '1.0', '0', NOW()) ON DUPLICATE KEY UPDATE `module`=VALUES(`module`), `major_minor`=VALUES(`major_minor`), `revision`=VALUES(`revision`);

UPDATE `_sql_version` SET `revision`=1, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -1--2024-02-15-10:00---------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `modified_by` = 'SZOF-2545', `modified_on` = NOW() WHERE `setting_id` = 'useModulesFromSqlVersionTable';

UPDATE `_sql_version` SET `revision`=2, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -2--2024-02-15-16:15---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés célja' WHERE `dict_id` = 'option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract goal' WHERE `dict_id` = 'option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés dátuma' WHERE `dict_id` = 'option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract date' WHERE `dict_id` = 'option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés érvényesség dátuma (lejárat)' WHERE `dict_id` = 'option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract validity date' WHERE `dict_id` = 'option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés összege' WHERE `dict_id` = 'option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract amount' WHERE `dict_id` = 'option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés megjegyzés' WHERE `dict_id` = 'option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract comment' WHERE `dict_id` = 'option6' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3372', `modified_on` = NOW() WHERE `option_id` = 'option2' AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3372', `modified_on` = NOW() WHERE `option_id` IN ('option3', 'option4') AND `status` = '2';
UPDATE `option_config` SET `status` = '7', `modified_by` = 'SZOF-3372', `modified_on` = NOW() WHERE `option_id` IN ('option7', 'option8', 'option9', 'option10') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=3, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -3--2024-05-28-09:00---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Születési ország' WHERE `dict_id` = 'ext2_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Country of birth' WHERE `dict_id` = 'ext2_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Nexon törzsszám' WHERE `dict_id` = 'ext2_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Nexon employee number' WHERE `dict_id` = 'ext2_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állampolgárság 1' WHERE `dict_id` = 'ext2_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Nationality 1' WHERE `dict_id` = 'ext2_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állampolgárság 2' WHERE `dict_id` = 'ext2_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Nationality 2' WHERE `dict_id` = 'ext2_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Másodlagos e-mail cím' WHERE `dict_id` = 'ext2_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Alternate email' WHERE `dict_id` = 'ext2_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Privát telefonszám' WHERE `dict_id` = 'ext2_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Private phone number' WHERE `dict_id` = 'ext2_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Vészhelyzeti kontakt megnevezése' WHERE `dict_id` = 'ext2_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Emergency contact' WHERE `dict_id` = 'ext2_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Vészhelyzeti kontakt elérhetősége' WHERE `dict_id` = 'ext2_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Emergency contact availability' WHERE `dict_id` = 'ext2_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fogl. Eü. Alkalmasság dátuma' WHERE `dict_id` = 'ext2_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Occupational Health Check date' WHERE `dict_id` = 'ext2_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fogl. Eü. Alkalmasság eredménye' WHERE `dict_id` = 'ext2_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Occupational Health Check result' WHERE `dict_id` = 'ext2_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fogl. Eü. Korlátozás' WHERE `dict_id` = 'ext2_option11' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Occupational Health Check limitation' WHERE `dict_id` = 'ext2_option11' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fogl. Eü. alkalmasság érvényesség dátuma (lejárat)' WHERE `dict_id` = 'ext2_option12' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Occupational Health Check  validity date' WHERE `dict_id` = 'ext2_option12' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Erkölcsi bizonyítványt bemutatta' WHERE `dict_id` = 'ext2_option13' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Criminal record document presented' WHERE `dict_id` = 'ext2_option13' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Legmagasabb iskolai végzettség szintje' WHERE `dict_id` = 'ext2_option14' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Highest education degree level' WHERE `dict_id` = 'ext2_option14' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Legmagasabb iskolai végzettség neve' WHERE `dict_id` = 'ext2_option15' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Highest education degree name' WHERE `dict_id` = 'ext2_option15' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Legmagasabb iskolai végzettség száma' WHERE `dict_id` = 'ext2_option16' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Highest education degree number' WHERE `dict_id` = 'ext2_option16' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Legmagasabb iskolai végzettség dátuma' WHERE `dict_id` = 'ext2_option17' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Highest education degree date' WHERE `dict_id` = 'ext2_option17' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb végzettség 1' WHERE `dict_id` = 'ext2_option18' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other Education degree' WHERE `dict_id` = 'ext2_option18' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb végzettség 2' WHERE `dict_id` = 'ext2_option19' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other Education degree' WHERE `dict_id` = 'ext2_option19' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Angol nyelvtudás szintje' WHERE `dict_id` = 'ext2_option20' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'English language level' WHERE `dict_id` = 'ext2_option20' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb nyelvtudás' WHERE `dict_id` = 'ext2_option21' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other language' WHERE `dict_id` = 'ext2_option21' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb nyelvtudás szint' WHERE `dict_id` = 'ext2_option22' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other language level' WHERE `dict_id` = 'ext2_option22' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3367', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option1', 'ext2_option3', 'ext2_option4', 'ext2_option10', 'ext2_option13', 'ext2_option14', 'ext2_option20', 'ext2_option22') AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3367', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option9', 'ext2_option12', 'ext2_option17') AND `status` = '2';
UPDATE `option_config` SET `status` = '7', `modified_by` = 'SZOF-3367', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option23', 'ext2_option24', 'ext2_option25', 'ext2_option26', 'ext2_option27', 'ext2_option28', 'ext2_option29', 'ext2_option30', 'ext2_option31', 'ext2_option32', 'ext2_option33', 'ext2_option34', 'ext2_option35', 'ext2_option36', 'ext2_option37', 'ext2_option38', 'ext2_option39', 'ext2_option40') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=4, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -4--2024-05-28-09:45---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Telephely' WHERE `dict_id` = 'ext3_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Workplace location' WHERE `dict_id` = 'ext3_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkakör (magyarul)' WHERE `dict_id` = 'ext3_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Job position (HU)' WHERE `dict_id` = 'ext3_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkakör (angolul)' WHERE `dict_id` = 'ext3_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Job position (Eng)' WHERE `dict_id` = 'ext3_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Besorolási kategória megnevezése' WHERE `dict_id` = 'ext3_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'HR Booklet category name' WHERE `dict_id` = 'ext3_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Besorolási kategória kódja' WHERE `dict_id` = 'ext3_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'HR Booklet category code' WHERE `dict_id` = 'ext3_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fizikai, szellemi besorolás' WHERE `dict_id` = 'ext3_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Direct, indirect category' WHERE `dict_id` = 'ext3_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Könyvelési kategória' WHERE `dict_id` = 'ext3_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Accounting category' WHERE `dict_id` = 'ext3_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kornferry grade' WHERE `dict_id` = 'ext3_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Kornferry grade' WHERE `dict_id` = 'ext3_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kornferry munkakör kód' WHERE `dict_id` = 'ext3_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Kornferry jobcode' WHERE `dict_id` = 'ext3_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Szervezeti egység (magyarul)' WHERE `dict_id` = 'ext3_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Department name (HU)' WHERE `dict_id` = 'ext3_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Szervezeti egység (angolul)' WHERE `dict_id` = 'ext3_option11' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Department name (ENG)' WHERE `dict_id` = 'ext3_option11' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'FEOR kód' WHERE `dict_id` = 'ext3_option12' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'FEOR code' WHERE `dict_id` = 'ext3_option12' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'FEOR megnevezése' WHERE `dict_id` = 'ext3_option13' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'FEOR name' WHERE `dict_id` = 'ext3_option13' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Képzettségi szint kód' WHERE `dict_id` = 'ext3_option14' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Qualification level code' WHERE `dict_id` = 'ext3_option14' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Képzettségi szint típus' WHERE `dict_id` = 'ext3_option15' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Qualification level type' WHERE `dict_id` = 'ext3_option15' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Képzettségi szint név' WHERE `dict_id` = 'ext3_option16' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Qualification level name' WHERE `dict_id` = 'ext3_option16' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Költséghely neve' WHERE `dict_id` = 'ext3_option17' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Cost center name' WHERE `dict_id` = 'ext3_option17' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Költséghely kódja' WHERE `dict_id` = 'ext3_option18' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Cost center code' WHERE `dict_id` = 'ext3_option18' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3368', `modified_on` = NOW() WHERE `option_id` IN ('ext3_option1', 'ext3_option2', 'ext3_option3', 'ext3_option4', 'ext3_option5', 'ext3_option6', 'ext3_option7', 'ext3_option8', 'ext3_option9', 'ext3_option10', 'ext3_option11', 'ext3_option12', 'ext3_option14', 'ext3_option15', 'ext3_option16', 'ext3_option17', 'ext3_option18') AND `status` = '2';
UPDATE `option_config` SET `status` = '7', `modified_by` = 'SZOF-3368', `modified_on` = NOW() WHERE `option_id` IN ('ext3_option19', 'ext3_option20', 'ext3_option21', 'ext3_option22', 'ext3_option23', 'ext3_option24', 'ext3_option25', 'ext3_option26', 'ext3_option27', 'ext3_option28', 'ext3_option29', 'ext3_option30', 'ext3_option31', 'ext3_option32', 'ext3_option33', 'ext3_option34', 'ext3_option35', 'ext3_option36', 'ext3_option37', 'ext3_option38', 'ext3_option39', 'ext3_option40') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=5, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -5--2024-05-28-10:15---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 1 fajtája' WHERE `dict_id` = 'ext4_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice type' WHERE `dict_id` = 'ext4_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 1 dátuma' WHERE `dict_id` = 'ext4_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice date' WHERE `dict_id` = 'ext4_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 2 fajtája' WHERE `dict_id` = 'ext4_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice type' WHERE `dict_id` = 'ext4_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 2 dátuma' WHERE `dict_id` = 'ext4_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice date' WHERE `dict_id` = 'ext4_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 3 fajtája' WHERE `dict_id` = 'ext4_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice type' WHERE `dict_id` = 'ext4_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 3 dátuma' WHERE `dict_id` = 'ext4_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice date' WHERE `dict_id` = 'ext4_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 4 fajtája' WHERE `dict_id` = 'ext4_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice type' WHERE `dict_id` = 'ext4_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 4 dátuma' WHERE `dict_id` = 'ext4_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice date' WHERE `dict_id` = 'ext4_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Károkozás 1 oka' WHERE `dict_id` = 'ext4_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Damage caused type' WHERE `dict_id` = 'ext4_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Károkozás 1 dátuma' WHERE `dict_id` = 'ext4_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Damage caused date' WHERE `dict_id` = 'ext4_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Károkozás 1 következménye' WHERE `dict_id` = 'ext4_option11' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Damage caused consequence' WHERE `dict_id` = 'ext4_option11' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3369', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option1', 'ext4_option3', 'ext4_option5', 'ext4_option7', 'ext4_option9') AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3369', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option2', 'ext4_option4', 'ext4_option6', 'ext4_option8', 'ext4_option10') AND `status` = '2';
UPDATE `option_config` SET `status` = '7', `modified_by` = 'SZOF-3369', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option12', 'ext4_option13', 'ext4_option14', 'ext4_option15', 'ext4_option16', 'ext4_option17', 'ext4_option18', 'ext4_option19', 'ext4_option20', 'ext4_option21', 'ext4_option22', 'ext4_option23', 'ext4_option24', 'ext4_option25', 'ext4_option26', 'ext4_option27', 'ext4_option28', 'ext4_option29', 'ext4_option30', 'ext4_option31', 'ext4_option32', 'ext4_option33', 'ext4_option34', 'ext4_option35', 'ext4_option36', 'ext4_option37', 'ext4_option38', 'ext4_option39', 'ext4_option40') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=6, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -6--2024-05-28-10:45---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 4361 híddaru' WHERE `dict_id` = 'ext5_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 4361' WHERE `dict_id` = 'ext5_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 4191 Szintkülönbség kiegyenlítő' WHERE `dict_id` = 'ext5_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 4191' WHERE `dict_id` = 'ext5_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 3312 Gyalogkiséretű targonca' WHERE `dict_id` = 'ext5_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 3312' WHERE `dict_id` = 'ext5_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 3313 Vezetőállásos targonca' WHERE `dict_id` = 'ext5_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 3313' WHERE `dict_id` = 'ext5_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 3324 Vezetőüléses targonca' WHERE `dict_id` = 'ext5_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 3324' WHERE `dict_id` = 'ext5_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 4211 Ollós emelőállvány kezelő' WHERE `dict_id` = 'ext5_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 4211' WHERE `dict_id` = 'ext5_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelő jogosítvány OKJ' WHERE `dict_id` = 'ext5_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence OKJ' WHERE `dict_id` = 'ext5_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány orvosi érvényesség' WHERE `dict_id` = 'ext5_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence health validity' WHERE `dict_id` = 'ext5_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Elsősegélynyújtó képzettség' WHERE `dict_id` = 'ext5_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'First aid training' WHERE `dict_id` = 'ext5_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Elsősegélynyújtó képzettség dátuma' WHERE `dict_id` = 'ext5_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'First aid training  date' WHERE `dict_id` = 'ext5_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = '2', `modified_by` = 'SZOF-3370', `modified_on` = NOW() WHERE `option_id` IN ('ext5_option2', 'ext5_option3', 'ext5_option4', 'ext5_option5', 'ext5_option6', 'ext5_option7', 'ext5_option8', 'ext5_option9', 'ext5_option10') AND `status` = '7';
UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3370', `modified_on` = NOW() WHERE `option_id` IN ('ext5_option1', 'ext5_option2', 'ext5_option3', 'ext5_option4', 'ext5_option5', 'ext5_option6', 'ext5_option7', 'ext5_option9') AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3370', `modified_on` = NOW() WHERE `option_id` IN ('ext5_option8', 'ext5_option10') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=7, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -7--2024-05-28-13:45---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 4361 híddaru' WHERE `dict_id` = 'ext6_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 4361' WHERE `dict_id` = 'ext6_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 4191 Szintkülönbség kiegyenlítő' WHERE `dict_id` = 'ext6_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 4191' WHERE `dict_id` = 'ext6_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 3312 Gyalogkiséretű targonca' WHERE `dict_id` = 'ext6_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 3312' WHERE `dict_id` = 'ext6_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 3313 Vezetőállásos targonca' WHERE `dict_id` = 'ext6_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 3313' WHERE `dict_id` = 'ext6_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 3324 Vezetőüléses targonca' WHERE `dict_id` = 'ext6_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 3324' WHERE `dict_id` = 'ext6_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 4211 Ollós emelőállvány kezelő' WHERE `dict_id` = 'ext6_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 4211' WHERE `dict_id` = 'ext6_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelő jogosítvány OKJ' WHERE `dict_id` = 'ext6_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence OKJ' WHERE `dict_id` = 'ext6_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány orvosi érvényesség' WHERE `dict_id` = 'ext6_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence health validity' WHERE `dict_id` = 'ext6_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Elsősegélynyújtó képzettség' WHERE `dict_id` = 'ext6_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'First aid training' WHERE `dict_id` = 'ext6_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Elsősegélynyújtó képzettség dátuma' WHERE `dict_id` = 'ext6_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'First aid training  date' WHERE `dict_id` = 'ext6_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = '2', `modified_by` = 'SZOF-3371', `modified_on` = NOW() WHERE `option_id` IN ('ext6_option2', 'ext6_option3', 'ext6_option4', 'ext6_option5', 'ext6_option6', 'ext6_option7', 'ext6_option8', 'ext6_option9', 'ext6_option10') AND `status` = '7';
UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3371', `modified_on` = NOW() WHERE `option_id` IN ('ext6_option1', 'ext6_option2', 'ext6_option3', 'ext6_option4', 'ext6_option5', 'ext6_option6', 'ext6_option7', 'ext6_option9') AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3371', `modified_on` = NOW() WHERE `option_id` IN ('ext6_option8', 'ext6_option10') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=8, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -8--2024-05-28-14:30---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Édesanya neve' WHERE `dict_id` = 'mothers_name' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Útlevél szám' WHERE `dict_id` = 'passport_number' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Személyi igazolvány szám' WHERE `dict_id` = 'personal_id_card_number' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'ID card number' WHERE `dict_id` = 'personal_id_card_number' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Nexon törzsszám' WHERE `dict_id` = 'option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Nexon employee number' WHERE `dict_id` = 'option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Születési ország' WHERE `dict_id` = 'option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Country of birth' WHERE `dict_id` = 'option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állampolgárság 1' WHERE `dict_id` = 'option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Nationality 1' WHERE `dict_id` = 'option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állampolgárság 2' WHERE `dict_id` = 'option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Nationality 2' WHERE `dict_id` = 'option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Adószám' WHERE `dict_id` = 'option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Tax number' WHERE `dict_id` = 'option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Másodlagos e-mail cím' WHERE `dict_id` = 'option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Alternate email' WHERE `dict_id` = 'option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Privát telefonszám' WHERE `dict_id` = 'option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Private phone number' WHERE `dict_id` = 'option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Telephely' WHERE `dict_id` = 'option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Workplace location' WHERE `dict_id` = 'option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Foglalkoztatás specialitása' WHERE `dict_id` = 'option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Special employement' WHERE `dict_id` = 'option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Próbaidő vége' WHERE `dict_id` = 'option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'End of trial period' WHERE `dict_id` = 'option10' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Felmondási idő napban' WHERE `dict_id` = 'option11' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Termination period (days)' WHERE `dict_id` = 'option11' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Versenytilalmi kikötés' WHERE `dict_id` = 'option12' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Non-competition clause' WHERE `dict_id` = 'option12' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkakör (magyarul)' WHERE `dict_id` = 'option13' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Job position (HU)' WHERE `dict_id` = 'option13' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkakör (angolul)' WHERE `dict_id` = 'option14' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Job position (Eng)' WHERE `dict_id` = 'option14' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Besorolási kategória megnevezése' WHERE `dict_id` = 'option15' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'HR Booklet category name' WHERE `dict_id` = 'option15' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Besorolási kategória kódja' WHERE `dict_id` = 'option16' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'HR Booklet category code' WHERE `dict_id` = 'option16' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fizikai, szellemi besorolás' WHERE `dict_id` = 'option17' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Direct, indirect category' WHERE `dict_id` = 'option17' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Könyvelési kategória' WHERE `dict_id` = 'option18' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Accounting category' WHERE `dict_id` = 'option18' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Képzettségi szint kód' WHERE `dict_id` = 'option19' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Qualification level code' WHERE `dict_id` = 'option19' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Képzettségi szint típus' WHERE `dict_id` = 'option20' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Qualification level type' WHERE `dict_id` = 'option20' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Képzettségi szint név' WHERE `dict_id` = 'option21' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Qualification level name' WHERE `dict_id` = 'option21' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Költséghely neve' WHERE `dict_id` = 'option22' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Cost center name' WHERE `dict_id` = 'option22' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Költséghely kódja' WHERE `dict_id` = 'option23' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Cost center code' WHERE `dict_id` = 'option23' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Szervezeti egység (magyarul)' WHERE `dict_id` = 'option24' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Department name (HU)' WHERE `dict_id` = 'option24' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Szervezeti egység (angolul)' WHERE `dict_id` = 'option25' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Department name (ENG)' WHERE `dict_id` = 'option25' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kornferry grade' WHERE `dict_id` = 'option26' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Kornferry grade' WHERE `dict_id` = 'option26' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kornferry munkakör kód' WHERE `dict_id` = 'option27' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Kornferry job code' WHERE `dict_id` = 'option27' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'FEOR kód' WHERE `dict_id` = 'option28' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'FEOR code' WHERE `dict_id` = 'option28' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'FEOR megnevezése' WHERE `dict_id` = 'option29' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'FEOR name' WHERE `dict_id` = 'option29' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkaviszony megszünés kezdeményezője' WHERE `dict_id` = 'option30' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Initiator of contract termination' WHERE `dict_id` = 'option30' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkaviszony megszünés típusa' WHERE `dict_id` = 'option31' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Type of contract termination' WHERE `dict_id` = 'option31' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkaviszony megszünés oka' WHERE `dict_id` = 'option32' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Reason of contract termination' WHERE `dict_id` = 'option32' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kilépő interjú készült' WHERE `dict_id` = 'option33' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Exit interview prepared' WHERE `dict_id` = 'option33' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kilépő interjú dátuma' WHERE `dict_id` = 'option34' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Exit interview date' WHERE `dict_id` = 'option34' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = '2', `modified_by` = 'SZOF-3601', `modified_on` = NOW() WHERE `option_id` IN ('option7', 'option8', 'option9', 'option10', 'option11', 'option12', 'option13', 'option14', 'option15', 'option16', 'option17', 'option18', 'option19', 'option20', 'option21', 'option22', 'option23', 'option24', 'option25', 'option26', 'option27', 'option28', 'option29', 'option30', 'option31', 'option32', 'option33', 'option34') AND `status` = '7';
UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3601', `modified_on` = NOW() WHERE `option_id` IN ('option2', 'option3', 'option4', 'option8', 'option9', 'option13', 'option14', 'option15', 'option16', 'option17', 'option18', 'option19', 'option20', 'option21', 'option22', 'option23', 'option24', 'option25', 'option26', 'option27', 'option29', 'option30', 'option31', 'option32', 'option33') AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3601', `modified_on` = NOW() WHERE `option_id` IN ('option10', 'option34') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=9, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -9--2024-07-08-13:45---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely címe irányítószám' WHERE `dict_id` = 'ext2_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address ZIP code' WHERE `dict_id` = 'ext2_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely címe ország' WHERE `dict_id` = 'ext2_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address country' WHERE `dict_id` = 'ext2_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely címe város' WHERE `dict_id` = 'ext2_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address city' WHERE `dict_id` = 'ext2_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely címe kerület' WHERE `dict_id` = 'ext2_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address district' WHERE `dict_id` = 'ext2_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely címe közterület neve' WHERE `dict_id` = 'ext2_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address address' WHERE `dict_id` = 'ext2_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely címe közterület típusa' WHERE `dict_id` = 'ext2_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address address type' WHERE `dict_id` = 'ext2_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely címe házszám' WHERE `dict_id` = 'ext2_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address house number' WHERE `dict_id` = 'ext2_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely címe emelet' WHERE `dict_id` = 'ext2_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address floor' WHERE `dict_id` = 'ext2_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely címe ajtó' WHERE `dict_id` = 'ext2_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address door' WHERE `dict_id` = 'ext2_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Állandó lakóhely megjegyzés mező' WHERE `dict_id` = 'ext2_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Permanent address comment' WHERE `dict_id` = 'ext2_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely címe irányítószám' WHERE `dict_id` = 'ext2_option11' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address ZIP code' WHERE `dict_id` = 'ext2_option11' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely címe ország' WHERE `dict_id` = 'ext2_option12' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address country' WHERE `dict_id` = 'ext2_option12' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely címe város' WHERE `dict_id` = 'ext2_option13' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address city' WHERE `dict_id` = 'ext2_option13' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely címe kerület' WHERE `dict_id` = 'ext2_option14' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address district' WHERE `dict_id` = 'ext2_option14' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely címe közterület neve' WHERE `dict_id` = 'ext2_option15' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address address' WHERE `dict_id` = 'ext2_option15' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely címe közterület típusa' WHERE `dict_id` = 'ext2_option16' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address address type' WHERE `dict_id` = 'ext2_option16' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely címe házszám' WHERE `dict_id` = 'ext2_option17' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address house number' WHERE `dict_id` = 'ext2_option17' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely címe emelet' WHERE `dict_id` = 'ext2_option18' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address floor' WHERE `dict_id` = 'ext2_option18' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely címe ajtó' WHERE `dict_id` = 'ext2_option19' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address door' WHERE `dict_id` = 'ext2_option19' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tartózkodási hely megjegyzés mező' WHERE `dict_id` = 'ext2_option20' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Interim address comment' WHERE `dict_id` = 'ext2_option20' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás címe irányítószám' WHERE `dict_id` = 'ext2_option21' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting ZIP code' WHERE `dict_id` = 'ext2_option21' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás címe ország' WHERE `dict_id` = 'ext2_option22' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting country' WHERE `dict_id` = 'ext2_option22' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás címe város' WHERE `dict_id` = 'ext2_option23' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting city' WHERE `dict_id` = 'ext2_option23' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Bérlet elszámolás igénylés (tömegközl.)' WHERE `dict_id` = 'ext2_option24' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Request of commuting ticket' WHERE `dict_id` = 'ext2_option24' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás címe kerület' WHERE `dict_id` = 'ext2_option25' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting district' WHERE `dict_id` = 'ext2_option25' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás címe közterület neve' WHERE `dict_id` = 'ext2_option26' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting address' WHERE `dict_id` = 'ext2_option26' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás címe közterület típusa' WHERE `dict_id` = 'ext2_option27' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting address type' WHERE `dict_id` = 'ext2_option27' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás címe házszám' WHERE `dict_id` = 'ext2_option28' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting house number' WHERE `dict_id` = 'ext2_option28' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás címe emelet' WHERE `dict_id` = 'ext2_option29' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting floor' WHERE `dict_id` = 'ext2_option29' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás címe ajtó' WHERE `dict_id` = 'ext2_option30' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting door' WHERE `dict_id` = 'ext2_option30' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás távolság (km)' WHERE `dict_id` = 'ext2_option31' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Commuting distance (km)' WHERE `dict_id` = 'ext2_option31' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkábajárás megjegyzés mező' WHERE `dict_id` = 'ext2_option32' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Address for commuting comment' WHERE `dict_id` = 'ext2_option32' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Bérlet elszámolás % (tömegközl.)' WHERE `dict_id` = 'ext2_option33' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Commuting ticket support (%)' WHERE `dict_id` = 'ext2_option33' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Céges busz bérlet igénylés' WHERE `dict_id` = 'ext2_option34' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Request of Co. Bus ticket' WHERE `dict_id` = 'ext2_option34' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Megjegyzés' WHERE `dict_id` = 'ext2_option35' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other comment' WHERE `dict_id` = 'ext2_option35' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkaviszony megszünés kezdeményezője' WHERE `dict_id` = 'ext2_option36' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Initiator of contract termination' WHERE `dict_id` = 'ext2_option36' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkaviszony megszünés típusa' WHERE `dict_id` = 'ext2_option37' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Type of contract termination' WHERE `dict_id` = 'ext2_option37' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkaviszony megszünés oka' WHERE `dict_id` = 'ext2_option38' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Reason of contract termination' WHERE `dict_id` = 'ext2_option38' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kilépő interjú készült' WHERE `dict_id` = 'ext2_option39' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Exit interview prepared' WHERE `dict_id` = 'ext2_option39' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Kilépő interjú dátuma' WHERE `dict_id` = 'ext2_option40' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Exit interview date' WHERE `dict_id` = 'ext2_option40' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Megjegyzés' WHERE `dict_id` = 'ext2_option41' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Comment' WHERE `dict_id` = 'ext2_option41' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = '2', `modified_by` = 'SZOF-3206', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option23', 'ext2_option24', 'ext2_option25', 'ext2_option26', 'ext2_option27', 'ext2_option28', 'ext2_option29', 'ext2_option30', 'ext2_option31', 'ext2_option32', 'ext2_option33', 'ext2_option34', 'ext2_option35', 'ext2_option36', 'ext2_option37', 'ext2_option38', 'ext2_option39', 'ext2_option40', 'ext2_option41') AND `status` = '7';
UPDATE `option_config` SET `type` = 'ed', `modified_by` = 'SZOF-3206', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option1', 'ext2_option3', 'ext2_option4', 'ext2_option9', 'ext2_option10', 'ext2_option13', 'ext2_option14', 'ext2_option17', 'ext2_option20') AND `status` = '2';
UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3206', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option2', 'ext2_option12', 'ext2_option22', 'ext2_option36', 'ext2_option37', 'ext2_option38', 'ext2_option39') AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3206', `modified_on` = NOW() WHERE `option_id` = 'ext2_option17' AND `status` = '2';

UPDATE `_sql_version` SET `revision`=10, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -10--2024-07-09-17:00---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 4361 híddaru' WHERE `dict_id` = 'ext3_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 4361' WHERE `dict_id` = 'ext3_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 4191 Szintkülönbség kiegyenlítő' WHERE `dict_id` = 'ext3_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 4191' WHERE `dict_id` = 'ext3_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 3312 Gyalogkiséretű targonca' WHERE `dict_id` = 'ext3_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 3312' WHERE `dict_id` = 'ext3_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 3313 Vezetőállásos targonca' WHERE `dict_id` = 'ext3_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 3313' WHERE `dict_id` = 'ext3_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 3324 Vezetőüléses targonca' WHERE `dict_id` = 'ext3_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 3324' WHERE `dict_id` = 'ext3_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány 4211 Ollós emelőállvány kezelő' WHERE `dict_id` = 'ext3_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence 4211' WHERE `dict_id` = 'ext3_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelő jogosítvány OKJ' WHERE `dict_id` = 'ext3_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence OKJ' WHERE `dict_id` = 'ext3_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépkezelői jogosítvány orvosi érvényesség' WHERE `dict_id` = 'ext3_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Machinery licence health validity' WHERE `dict_id` = 'ext3_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb gépkezelői végzettség' WHERE `dict_id` = 'ext3_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other licence' WHERE `dict_id` = 'ext3_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Elsősegélynyújtó képzettség' WHERE `dict_id` = 'ext3_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'First aid training' WHERE `dict_id` = 'ext3_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Elsősegélynyújtó képzettség dátuma' WHERE `dict_id` = 'ext3_option11' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'First aid training date' WHERE `dict_id` = 'ext3_option11' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogosítvány CE kategória száma' WHERE `dict_id` = 'ext3_option12' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Driving licence CE category number' WHERE `dict_id` = 'ext3_option12' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogosítvány CE kategória érvényessége' WHERE `dict_id` = 'ext3_option13' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Driving licence CE category validity date' WHERE `dict_id` = 'ext3_option13' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'GKI száma' WHERE `dict_id` = 'ext3_option14' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'GKI licence number' WHERE `dict_id` = 'ext3_option14' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'GKI érvényessége' WHERE `dict_id` = 'ext3_option15' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'GKI licence validity date' WHERE `dict_id` = 'ext3_option15' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépjárművezetői kártya száma' WHERE `dict_id` = 'ext3_option16' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Driver card number' WHERE `dict_id` = 'ext3_option16' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Gépjárművezetői kártya érvényessége' WHERE `dict_id` = 'ext3_option17' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Driver card validity date' WHERE `dict_id` = 'ext3_option17' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'ADR kártya száma' WHERE `dict_id` = 'ext3_option18' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'ADR card number' WHERE `dict_id` = 'ext3_option18' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'ADR kártya érvényessége' WHERE `dict_id` = 'ext3_option19' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'ADR card validity date' WHERE `dict_id` = 'ext3_option19' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Megjegyzés' WHERE `dict_id` = 'ext3_option20' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Comment' WHERE `dict_id` = 'ext3_option20' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = '2', `modified_by` = 'SZOF-3603', `modified_on` = NOW() WHERE `option_id` IN ('ext3_option19', 'ext3_option20') AND `status` = '7';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3603', `modified_on` = NOW() WHERE `option_id` IN ('ext3_option8', 'ext3_option11', 'ext3_option13', 'ext3_option15', 'ext3_option17', 'ext3_option19') AND `status` = '2';
UPDATE `option_config` SET `type` = 'ed', `modified_by` = 'SZOF-3603', `modified_on` = NOW() WHERE `option_id` IN ('ext3_option9', 'ext3_option12', 'ext3_option14', 'ext3_option16', 'ext3_option18') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=11, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -11--2024-07-10-15:30---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Fogl. Eü. Alkalmasság dátuma' WHERE `dict_id` = 'ext4_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Occupational Health Check date' WHERE `dict_id` = 'ext4_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fogl. Eü. Alkalmasság eredménye' WHERE `dict_id` = 'ext4_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Occupational Health Check result' WHERE `dict_id` = 'ext4_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fogl. Eü. Korlátozás' WHERE `dict_id` = 'ext4_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Occupational Health Check limitation' WHERE `dict_id` = 'ext4_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fogl. Eü. alkalmasság érvényesség dátuma (lejárat)' WHERE `dict_id` = 'ext4_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Occupational Health Check validity date' WHERE `dict_id` = 'ext4_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tüdőszűrő lejárat dátuma' WHERE `dict_id` = 'ext4_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Pulmonary filter expiration date' WHERE `dict_id` = 'ext4_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Erkölcsi bizonyítványt bemutatta' WHERE `dict_id` = 'ext4_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Criminal record document presented' WHERE `dict_id` = 'ext4_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Legmagasabb iskolai végzettség szintje' WHERE `dict_id` = 'ext4_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Highest education degree level' WHERE `dict_id` = 'ext4_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Legmagasabb iskolai végzettség neve' WHERE `dict_id` = 'ext4_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Highest education degree name' WHERE `dict_id` = 'ext4_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Legmagasabb iskolai végzettség száma' WHERE `dict_id` = 'ext4_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Highest education degree number' WHERE `dict_id` = 'ext4_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Legmagasabb iskolai végzettség dátuma' WHERE `dict_id` = 'ext4_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Highest education degree date' WHERE `dict_id` = 'ext4_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkakörhöz előírt végzettség szintje' WHERE `dict_id` = 'ext4_option11' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Degree required for position level' WHERE `dict_id` = 'ext4_option11' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkakörhöz előírt végzettség neve' WHERE `dict_id` = 'ext4_option12' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Degree required for position name' WHERE `dict_id` = 'ext4_option12' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkakörhöz előírt végzettség száma' WHERE `dict_id` = 'ext4_option13' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Degree required for position number' WHERE `dict_id` = 'ext4_option13' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Munkakörhöz előírt végzettség dátuma' WHERE `dict_id` = 'ext4_option14' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Degree required for position date' WHERE `dict_id` = 'ext4_option14' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb végzettség 1' WHERE `dict_id` = 'ext4_option15' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other Education degree' WHERE `dict_id` = 'ext4_option15' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb végzettség 2' WHERE `dict_id` = 'ext4_option16' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other Education degree' WHERE `dict_id` = 'ext4_option16' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Angol nyelvtudás szintje' WHERE `dict_id` = 'ext4_option17' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'English language level' WHERE `dict_id` = 'ext4_option17' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb nyelvtudás' WHERE `dict_id` = 'ext4_option18' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other language' WHERE `dict_id` = 'ext4_option18' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb nyelvtudás szint' WHERE `dict_id` = 'ext4_option19' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other language level' WHERE `dict_id` = 'ext4_option19' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb nyelvtudás 2' WHERE `dict_id` = 'ext4_option20' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other language 2' WHERE `dict_id` = 'ext4_option20' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb nyelvtudás szint 2' WHERE `dict_id` = 'ext4_option21' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other language level 2' WHERE `dict_id` = 'ext4_option21' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb nyelvtudás 3' WHERE `dict_id` = 'ext4_option22' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other language 3' WHERE `dict_id` = 'ext4_option22' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb nyelvtudás szint 3' WHERE `dict_id` = 'ext4_option23' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other language level 3' WHERE `dict_id` = 'ext4_option23' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Megjegyzés' WHERE `dict_id` = 'ext4_option24' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Comment' WHERE `dict_id` = 'ext4_option24' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = '2', `modified_by` = 'SZOF-3604', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option12', 'ext4_option13', 'ext4_option14', 'ext4_option15', 'ext4_option16', 'ext4_option17', 'ext4_option18', 'ext4_option19', 'ext4_option20', 'ext4_option21', 'ext4_option22', 'ext4_option23', 'ext4_option24') AND `status` = '7';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3604', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option1', 'ext4_option4', 'ext4_option5', 'ext4_option10', 'ext4_option14') AND `status` = '2';
UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3604', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option2', 'ext4_option6', 'ext4_option17', 'ext4_option19', 'ext4_option21', 'ext4_option23') AND `status` = '2';
UPDATE `option_config` SET `type` = 'ed', `modified_by` = 'SZOF-3604', `modified_on` = NOW() WHERE `option_id` IN ('ext4_option3', 'ext4_option8', 'ext4_option9') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=12, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -12--2024-07-10-16:00---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Bankszámla kedvezményezett neve' WHERE `dict_id` = 'ext5_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Bank account owner name' WHERE `dict_id` = 'ext5_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Bankszámla szám' WHERE `dict_id` = 'ext5_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Bank account number' WHERE `dict_id` = 'ext5_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Bankszámla kedvezményezett neve 2.' WHERE `dict_id` = 'ext5_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Bank account owner name 2.' WHERE `dict_id` = 'ext5_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Bankszámla szám 2.' WHERE `dict_id` = 'ext5_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Bank account number 2.' WHERE `dict_id` = 'ext5_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Több számlaszám esetén %-os vagy forintos bontás' WHERE `dict_id` = 'ext5_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Ratio between bank accounts' WHERE `dict_id` = 'ext5_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'SZÉP kártya kedvezményezett neve' WHERE `dict_id` = 'ext5_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'SZÉP card owner name' WHERE `dict_id` = 'ext5_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'SZÉP kártya száma' WHERE `dict_id` = 'ext5_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'SZÉP card number' WHERE `dict_id` = 'ext5_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'SZÉP kártya kibocsátó' WHERE `dict_id` = 'ext5_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'SZÉP card issuer' WHERE `dict_id` = 'ext5_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'SZÉP kártya összege' WHERE `dict_id` = 'ext5_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'SZÉP card amount' WHERE `dict_id` = 'ext5_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'SZÉP kártya összege (Ft)' WHERE `dict_id` = 'ext5_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'SZÉP card amount (Ft)' WHERE `dict_id` = 'ext5_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cégautó rendszáma' WHERE `dict_id` = 'ext5_option11' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Company car licence plate' WHERE `dict_id` = 'ext5_option11' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cégautó típusa' WHERE `dict_id` = 'ext5_option12' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Company car type' WHERE `dict_id` = 'ext5_option12' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cégautó kiadás dátuma' WHERE `dict_id` = 'ext5_option13' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Company car issue date' WHERE `dict_id` = 'ext5_option13' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'OMV tankolókártya száma' WHERE `dict_id` = 'ext5_option14' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'OMV petrol card number' WHERE `dict_id` = 'ext5_option14' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogosítvány B kategória száma' WHERE `dict_id` = 'ext5_option15' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Driving licence B category number' WHERE `dict_id` = 'ext5_option15' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogosítvány B kategória érvényessége' WHERE `dict_id` = 'ext5_option16' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Driving licence B category validity date' WHERE `dict_id` = 'ext5_option16' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fizetési előleg összege' WHERE `dict_id` = 'ext5_option17' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Salary advance request amount' WHERE `dict_id` = 'ext5_option17' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fizetési előleg igénylés dátuma' WHERE `dict_id` = 'ext5_option18' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Salary advance request date' WHERE `dict_id` = 'ext5_option18' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fizetési előleg levonás részletek száma' WHERE `dict_id` = 'ext5_option19' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Salary advance instalment (month)' WHERE `dict_id` = 'ext5_option19' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fizetési előleg levonás kezdete (hónap)' WHERE `dict_id` = 'ext5_option20' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Salary deduction starting month' WHERE `dict_id` = 'ext5_option20' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Fizetési előleg levonás vége (hónap)' WHERE `dict_id` = 'ext5_option21' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Salary deduction last month' WHERE `dict_id` = 'ext5_option21' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 1 fajtája' WHERE `dict_id` = 'ext5_option22' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice type' WHERE `dict_id` = 'ext5_option22' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 1 dátuma' WHERE `dict_id` = 'ext5_option23' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice date' WHERE `dict_id` = 'ext5_option23' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 2 fajtája' WHERE `dict_id` = 'ext5_option24' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice type 2' WHERE `dict_id` = 'ext5_option24' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 2 dátuma' WHERE `dict_id` = 'ext5_option25' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice date 2' WHERE `dict_id` = 'ext5_option25' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 3 fajtája' WHERE `dict_id` = 'ext5_option26' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice type 3' WHERE `dict_id` = 'ext5_option26' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 3 dátuma' WHERE `dict_id` = 'ext5_option27' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice date 3' WHERE `dict_id` = 'ext5_option27' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 4 fajtája' WHERE `dict_id` = 'ext5_option28' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice type 4' WHERE `dict_id` = 'ext5_option28' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Figyelmeztetés 4 dátuma' WHERE `dict_id` = 'ext5_option29' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Notice date 4' WHERE `dict_id` = 'ext5_option29' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Károkozás 1 oka' WHERE `dict_id` = 'ext5_option30' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Damage caused type' WHERE `dict_id` = 'ext5_option30' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Károkozás 1 dátuma' WHERE `dict_id` = 'ext5_option31' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Damage caused date' WHERE `dict_id` = 'ext5_option31' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Károkozás 1 következménye' WHERE `dict_id` = 'ext5_option32' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Damage caused consequence' WHERE `dict_id` = 'ext5_option32' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés célja' WHERE `dict_id` = 'ext5_option33' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract goal' WHERE `dict_id` = 'ext5_option33' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés dátuma' WHERE `dict_id` = 'ext5_option34' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract date' WHERE `dict_id` = 'ext5_option34' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés érvényesség dátuma (lejárat)' WHERE `dict_id` = 'ext5_option35' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract validity date' WHERE `dict_id` = 'ext5_option35' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés összege' WHERE `dict_id` = 'ext5_option36' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract amount' WHERE `dict_id` = 'ext5_option36' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés megjegyzés' WHERE `dict_id` = 'ext5_option37' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract comment' WHERE `dict_id` = 'ext5_option37' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés dátuma 2' WHERE `dict_id` = 'ext5_option38' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract goal' WHERE `dict_id` = 'ext5_option38' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés érvényesség dátuma (lejárat) 2' WHERE `dict_id` = 'ext5_option39' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract date' WHERE `dict_id` = 'ext5_option39' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés összege 2' WHERE `dict_id` = 'ext5_option40' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract validity date' WHERE `dict_id` = 'ext5_option40' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Tanulmányi szerződés megjegyzés 2' WHERE `dict_id` = 'ext5_option41' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Educational contract amount' WHERE `dict_id` = 'ext5_option41' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Megjegyzés' WHERE `dict_id` = 'ext5_option42' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Comment' WHERE `dict_id` = 'ext5_option42' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = '2', `modified_by` = 'SZOF-3605', `modified_on` = NOW() WHERE `option_id` IN ('ext5_option11', 'ext5_option12', 'ext5_option13', 'ext5_option14', 'ext5_option15', 'ext5_option16', 'ext5_option17', 'ext5_option18', 'ext5_option19', 'ext5_option20', 'ext5_option21', 'ext5_option22', 'ext5_option23', 'ext5_option24', 'ext5_option25', 'ext5_option26', 'ext5_option27', 'ext5_option28', 'ext5_option29', 'ext5_option30', 'ext5_option31', 'ext5_option32', 'ext5_option33', 'ext5_option34', 'ext5_option35', 'ext5_option36', 'ext5_option37', 'ext5_option38', 'ext5_option39', 'ext5_option40', 'ext5_option41', 'ext5_option42') AND `status` = '7';
UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3605', `modified_on` = NOW() WHERE `option_id` IN ('ext5_option12', 'ext5_option22', 'ext5_option24', 'ext5_option26', 'ext5_option28', 'ext5_option30', 'ext5_option33') AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3605', `modified_on` = NOW() WHERE `option_id` IN ('ext5_option13', 'ext5_option16', 'ext5_option18', 'ext5_option23', 'ext5_option25', 'ext5_option27', 'ext5_option29', 'ext5_option31', 'ext5_option34', 'ext5_option35', 'ext5_option38', 'ext5_option39') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=13, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -13--2024-07-10-16:30---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Személyi alapbér (havibér Ft)' WHERE `dict_id` = 'personal_month_salary' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Basic salary (Ft)' WHERE `dict_id` = 'personal_month_salary' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Személyi alapbér (havibér Eur)' WHERE `dict_id` = 'personal_hour_salary' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Basic salary (Eur)' WHERE `dict_id` = 'personal_hour_salary' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Képzettségi bónusz összeg' WHERE `dict_id` = 'es_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Qualification bonus' WHERE `dict_id` = 'es_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Csapatbónusz összeg' WHERE `dict_id` = 'es_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Team bonus' WHERE `dict_id` = 'es_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Teljesítmény bónusz összeg' WHERE `dict_id` = 'es_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Performance bonus' WHERE `dict_id` = 'es_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Éves bónusz (%)' WHERE `dict_id` = 'es_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Annual bonus (%)' WHERE `dict_id` = 'es_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Hűség bónusz összeg' WHERE `dict_id` = 'es_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Loyalty bonus' WHERE `dict_id` = 'es_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéb bónusz összeg' WHERE `dict_id` = 'es_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Other bonus' WHERE `dict_id` = 'es_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cafeteria összege' WHERE `dict_id` = 'es_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Cafeteria amount' WHERE `dict_id` = 'es_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Cafeteria összege (Ft)' WHERE `dict_id` = 'es_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Cafeteria amount (Ft)' WHERE `dict_id` = 'es_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = '7', `modified_by` = 'SZOF-3600', `modified_on` = NOW() WHERE `option_id` IN ('es_option9', 'es_option10') AND `status` = '2';
UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3600', `modified_on` = NOW() WHERE `option_id` = 'es_option7' AND `status` = '2';

UPDATE `_sql_version` SET `revision`=14, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -14--2024-07-11-15:30---------------------------------------------------------------

INSERT IGNORE INTO `employee_position` (`employee_position_id`, `employee_position_name`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES
    ('85', 'Kontroling Vezető', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW()),
    ('86', 'Csomagoló Áruátvevő', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW()),
    ('87', 'Csomagoló Vezető', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW()),
    ('88', 'Minőségbiztosítási specialista', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW()),
    ('89', 'Értékesítési specialista', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW()),
    ('90', 'Operációs Igazgató', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW()),
    ('91', 'Raklap és Göngyöleggazdálkodási Vezető', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW()),
    ('92', 'Készletgazdálkodási Csoportvezető', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW()),
    ('92', 'Reklamációkezelő', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW()),
    ('93', 'Üzletfejlesztési Vezető', '1915-01-01', '2038-01-01', '2', 'SZOF-3735', NOW());

UPDATE `dictionary` SET `dict_value` = 'Egyéb' WHERE `dict_id` = 'other' AND `module` = 'ttwa-csm' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'havibér' WHERE `dict_id` = 'wage_type_molo' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'határozatlan idejű' WHERE `dict_id` = 'employee_contract_type_indefinite' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Unlimited' WHERE `dict_id` = 'employee_contract_type_indefinite' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'határozott idejű' WHERE `dict_id` = 'employee_contract_type_firm' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Limited' WHERE `dict_id` = 'employee_contract_type_firm' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'megbízási' WHERE `dict_id` = 'employee_contract_type_OKJ' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'assignment' WHERE `dict_id` = 'employee_contract_type_OKJ' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `app_lookup` SET `valid` = '0' WHERE `lookup_id` = 'employee_contract_type' AND `dict_id` IN ('employee_contract_type_trainee', 'employee_contract_type_learner') AND `valid` = '1';
UPDATE `app_lookup` SET `valid` = '0' WHERE `lookup_id` = 'wage_type' AND `dict_id` IN ('wage_type_ob', 'wage_type_sove') AND `valid` = '1';

UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-3735', `modified_on` = NOW() WHERE `option_id` IN ('option12', 'ext2_option24', 'option28') AND `status` = '2';
UPDATE `option_config` SET `type` = 'ed', `modified_by` = 'SZOF-3735', `modified_on` = NOW() WHERE `option_id` IN ('ext5_option1', 'ext5_option2', 'ext5_option3', 'ext5_option4', 'ext5_option5', 'ext5_option6', 'ext5_option7', 'ext5_option8', 'ext5_option10') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=15, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -15--2024-07-17-11:30---------------------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
  ('hu', 'ttwa-base', 'fix', 'Fix', 1),
  ('en', 'ttwa-base', 'fix', 'Fix', 1),
  ('hu', 'ttwa-base', 'variable', 'Változó', 1),
  ('en', 'ttwa-base', 'variable', 'Variable', 1),
  ('hu', 'ttwa-base', 'wage_type_commission', 'megbízási díj', 1),
  ('en', 'ttwa-base', 'wage_type_commission', 'Commission fee', 1),
  ('hu', 'ttwa-base', 'gyal_1', 'Gyál1', 1),
  ('en', 'ttwa-base', 'gyal_1', 'Gyál1', 1),
  ('hu', 'ttwa-base', 'gyal_2', 'Gyál2', 1),
  ('en', 'ttwa-base', 'gyal_2', 'Gyál2', 1),
  ('hu', 'ttwa-base', 'vecses', 'Vecsés', 1),
  ('en', 'ttwa-base', 'vecses', 'Vecsés', 1),
  ('hu', 'ttwa-base', 'szigetszentmiklos', 'Szigetszentmiklós', 1),
  ('en', 'ttwa-base', 'szigetszentmiklos', 'Szigetszentmiklós', 1),
  ('hu', 'ttwa-base', 'retired', 'nyugdíjas', 1),
  ('en', 'ttwa-base', 'retired', 'Retired', 1),
  ('hu', 'ttwa-base', 'next_to_childcarefee', 'GYED melletti', 1),
  ('en', 'ttwa-base', 'next_to_childcarefee', 'next to GYED', 1),
  ('hu', 'ttwa-base', 'employer', 'Munkáltató', 1),
  ('en', 'ttwa-base', 'employer', 'Employer', 1),
  ('hu', 'ttwa-base', 'term_probation_period_employee', 'Próbaidő alatti felmondás, Munkavállaló által', 1),
  ('en', 'ttwa-base', 'term_probation_period_employee', 'Termination during the probationary period, by the Employee', 1),
  ('hu', 'ttwa-base', 'term_probation_period_employer', 'Próbaidő alatti felmondás, Munkáltató által', 1),
  ('en', 'ttwa-base', 'term_probation_period_employer', 'Termination during the probationary period, by the Employer', 1),
  ('hu', 'ttwa-base', 'certain_period_time_expired', 'Határozott időtartam lejárta', 1),
  ('en', 'ttwa-base', 'certain_period_time_expired', 'Certain period of time has expired', 1),
  ('hu', 'ttwa-base', 'restrictions_affecting_work', 'Munkaköri alkalmasságot érintő korlátozás (eü. ok)', 1),
  ('en', 'ttwa-base', 'restrictions_affecting_work', 'Restrictions affecting work suitability (reasons for health)', 1),
  ('hu', 'ttwa-base', 'security_issues', 'Biztonsági problémák (lopás, károkozás, alkohol)', 1),
  ('en', 'ttwa-base', 'security_issues', 'Security issues (theft, vandalism, alcohol)', 1),
  ('hu', 'ttwa-base', 'behavioral_problems', 'Magatartási gondok (igazolatlan távollét, elfogadhatatlan viselkedés)', 1),
  ('en', 'ttwa-base', 'behavioral_problems', 'Behavioral problems (unauthorized absence, unacceptable behavior)', 1),
  ('hu', 'ttwa-base', 'quality_replacement', 'Minőségi csere', 1),
  ('en', 'ttwa-base', 'quality_replacement', 'Quality replacement', 1),
  ('hu', 'ttwa-base', 'reorganization', 'Átszervezés', 1),
  ('en', 'ttwa-base', 'reorganization', 'Reorganization', 1),
  ('hu', 'ttwa-base', 'group_downsizing', 'Csoportos létszámcsökkentés', 1),
  ('en', 'ttwa-base', 'group_downsizing', 'Group downsizing', 1),
  ('hu', 'ttwa-base', 'loss_trust', 'Bizalom vesztés', 1),
  ('en', 'ttwa-base', 'loss_trust', 'Loss of trust', 1),
  ('hu', 'ttwa-base', 'working_conditions_tools', 'Munkakörülmények, eszközök', 1),
  ('en', 'ttwa-base', 'working_conditions_tools', 'Working conditions, tools', 1),
  ('hu', 'ttwa-base', 'job_content', 'Munkakör tartalma', 1),
  ('en', 'ttwa-base', 'job_content', 'Job content', 1),
  ('hu', 'ttwa-base', 'commuting_distance', 'Ingázás (távolság)', 1),
  ('en', 'ttwa-base', 'commuting_distance', 'Commuting (distance)', 1),
  ('hu', 'ttwa-base', 'workplace_relations_team', 'Munkahelyi kapcsolatok/csapat', 1),
  ('en', 'ttwa-base', 'workplace_relations_team', 'Workplace relations/team', 1),
  ('hu', 'ttwa-base', 'relationship_with_manager', 'Vezetővel való viszony', 1),
  ('en', 'ttwa-base', 'relationship_with_manager', 'Relationship with manager', 1),
  ('hu', 'ttwa-base', 'salary_benefits', 'Bérjellegű juttatások', 1),
  ('en', 'ttwa-base', 'salary_benefits', 'Salary benefits', 1),
  ('hu', 'ttwa-base', 'health_reasons', 'Egészségügyi okok', 1),
  ('en', 'ttwa-base', 'health_reasons', 'Health reasons', 1),
  ('hu', 'ttwa-base', 'family_issues', 'Családi okok', 1),
  ('en', 'ttwa-base', 'family_issues', 'Family issues', 1),
  ('hu', 'ttwa-base', 'moving', 'Költözés', 1),
  ('en', 'ttwa-base', 'moving', 'Moving', 1),
  ('hu', 'ttwa-base', 'lack_advancement_opportunities', 'Továbblépési lehetőségek hiánya', 1),
  ('en', 'ttwa-base', 'lack_advancement_opportunities', 'Lack of advancement opportunities', 1),
  ('hu', 'ttwa-base', 'lack_further_training', 'Továbbképzés hiánya', 1),
  ('en', 'ttwa-base', 'lack_further_training', 'Lack of further training', 1),
  ('hu', 'ttwa-base', 'do_not_comment', 'Nem kívánok nyilatkozni', 1),
  ('en', 'ttwa-base', 'do_not_comment', 'I do not want to comment', 1),
  ('hu', 'ttwa-base', 'controlling_manager_hu', 'Kontroling Vezető', 1),
  ('hu', 'ttwa-base', 'controller_hu', 'Kontroller', 1),
  ('hu', 'ttwa-base', 'accountant_hu', 'Könyvelő', 1),
  ('hu', 'ttwa-base', 'finance_it_director_hu', 'Pénzügyi és IT igazgató', 1),
  ('hu', 'ttwa-base', 'finance_accounting_manager_hu', 'Pénzügyi és Számviteli Vezető', 1),
  ('hu', 'ttwa-base', 'financial_officer_hu', 'Pénzügyi Ügyintéző', 1),
  ('hu', 'ttwa-base', 'packer_goods_receiver_hu', 'Csomagoló Áruátvevő', 1),
  ('hu', 'ttwa-base', 'packing_manager_hu', 'Csomagoló Vezető', 1),
  ('hu', 'ttwa-base', 'packer_hu', 'Csomagoló', 1),
  ('hu', 'ttwa-base', 'administ_group_leader_hu', 'Adminisztrációs Csoportvezető', 1),
  ('hu', 'ttwa-base', 'administ_complaints_leader_hu', 'Adminisztrációs és Reklamációs Csoportvezető', 1),
  ('hu', 'ttwa-base', 'administ_manager_hu', 'Adminisztrációs Vezető', 1),
  ('hu', 'ttwa-base', 'administrator_hu', 'Adminisztrátor', 1),
  ('hu', 'ttwa-base', 'goods_receiver_hu', 'Áruátvevő', 1),
  ('hu', 'ttwa-base', 'logistics_manager_hu', 'Logisztikai Menedzser', 1),
  ('hu', 'ttwa-base', 'high_lift_driver_hu', 'Magasemelésű Targoncavezető', 1),
  ('hu', 'ttwa-base', 'operations_shift_manager_hu', 'Operációs Műszakvezető', 1),
  ('hu', 'ttwa-base', 'warehouse_manager_hu', 'Raktárvezető', 1),
  ('hu', 'ttwa-base', 'forklift_goods_receiver_hu', 'Targoncás Áruátvevő', 1),
  ('hu', 'ttwa-base', 'wms_operator_hu', 'WMS Operátor', 1),
  ('hu', 'ttwa-base', 'quality_specialist_hu', 'Minőségbiztosítási specialista', 1),
  ('hu', 'ttwa-base', 'quality_manager_hu', 'Minőségbiztosítási Vezető', 1),
  ('hu', 'ttwa-base', 'sales_manager_hu', 'Értékesítési Vezető', 1),
  ('hu', 'ttwa-base', 'sales_specialist_hu', 'Értékesítési Specialista', 1),
  ('hu', 'ttwa-base', 'billing_coordinator_hu', 'Számlázási Koordinátor', 1),
  ('hu', 'ttwa-base', 'it_sap_key_user_hu', 'IT & SAP key user', 1),
  ('hu', 'ttwa-base', 'it_group_manager_hu', 'IT Csoportvezető', 1),
  ('hu', 'ttwa-base', 'hr_administrator_hu', 'HR Adminisztrátor', 1),
  ('hu', 'ttwa-base', 'hr_business_partner_hu', 'HR Business Partner', 1),
  ('hu', 'ttwa-base', 'hr_specialist_hu', 'HR Specialista', 1),
  ('hu', 'ttwa-base', 'hr_manager_hu', 'HR Vezető', 1),
  ('hu', 'ttwa-base', 'operations_director_hu', 'Operációs Igazgató', 1),
  ('hu', 'ttwa-base', 'warehouse_trainer_hu', 'Raktári tréner', 1),
  ('hu', 'ttwa-base', 'gongyoleg_admin_leader_hu', 'Göngyöleg Adminisztrációs Csoportvezető', 1),
  ('hu', 'ttwa-base', 'gongyoleg_warehouse_driver_hu', 'Göngyölegraktáros Targoncavezető', 1),
  ('hu', 'ttwa-base', 'pallet_roll_manager_hu', 'Raklap és Göngyöleggazdálkodási Vezető', 1),
  ('hu', 'ttwa-base', 'battery_charging_coordi_hu', 'Akkumulátortöltő terület Koordinátor', 1),
  ('hu', 'ttwa-base', 'safety_maintenance_coordi_hu', 'Biztonsági és Karbantartási Koordinátor', 1),
  ('hu', 'ttwa-base', 'safety_operations_manager_hu', 'Biztonsági és Üzemeltetési Vezető', 1),
  ('hu', 'ttwa-base', 'cctv_operator_hu', 'CCTV Operátor', 1),
  ('hu', 'ttwa-base', 'maintenance_hu', 'Karbantartó', 1),
  ('hu', 'ttwa-base', 'operations_manager_hu', 'Üzemeltetési Vezető', 1),
  ('hu', 'ttwa-base', 'inventory_controller_man_hu', 'Készletellenőr - készletgazda', 1),
  ('hu', 'ttwa-base', 'inventory_manager_hu', 'Készletgazda', 1),
  ('hu', 'ttwa-base', 'inven_management_leader_hu', 'Készletgazdálkodási Csoportvezető', 1),
  ('hu', 'ttwa-base', 'logistic_support_manager_hu', 'Logisztikai Támogató Menedzser', 1),
  ('hu', 'ttwa-base', 'project_coordinator_hu', 'Projekt Koordinátor', 1),
  ('hu', 'ttwa-base', 'claims_manager_hu', 'Reklamációkezelő', 1),
  ('hu', 'ttwa-base', 'complaints_group_leader_hu', 'Reklamációs Csoportvezető', 1),
  ('hu', 'ttwa-base', 'dispatch_service_manager_hu', 'Diszpécser szolgálati Vezető', 1),
  ('hu', 'ttwa-base', 'dispatch_vehi_technican_hu', 'Diszpécser-Gépjármű üzemeltetési Technikus', 1),
  ('hu', 'ttwa-base', 'dispatcher_driver_hu', 'Diszpécser-Gépjárművezető', 1),
  ('hu', 'ttwa-base', 'transport_dispatcher_hu', 'Fuvarszervező Diszpécser', 1),
  ('hu', 'ttwa-base', 'driver_hu', 'Gépjárművezető', 1),
  ('hu', 'ttwa-base', 'delivery_coordinator_hu', 'Kiszállítási Koordinátor', 1),
  ('hu', 'ttwa-base', 'operational_dispatcher_hu', 'Operatív Diszpécser', 1),
  ('hu', 'ttwa-base', 'ramp_coordinator_hu', 'Rámpa Koordinátor', 1),
  ('hu', 'ttwa-base', 'senior_dispatcher_hu', 'Senior Diszpécser', 1),
  ('hu', 'ttwa-base', 'transport_group_leader_hu', 'Szállítási Adminisztrációs Csoportvezető', 1),
  ('hu', 'ttwa-base', 'shipping_director_hu', 'Szállítási Igazgató', 1),
  ('hu', 'ttwa-base', 'delivery_project_coordi_hu', 'Szállítási Projekt Kordinátor', 1),
  ('hu', 'ttwa-base', 'transport_project_manager_hu', 'Szállítási Projektvezető', 1),
  ('hu', 'ttwa-base', 'executive_director_hu', 'Ügyvezető Igazgató', 1),
  ('hu', 'ttwa-base', 'business_dev_manager_hu', 'Üzletfejlesztési Vezető', 1),
  ('hu', 'ttwa-base', 'receptionist_hu', 'Recepciós Ügyintéző', 1),
  ('hu', 'ttwa-base', 'controlling_manager_en', 'Controlling Manager', 1),
  ('hu', 'ttwa-base', 'controller_en', 'Controller', 1),
  ('hu', 'ttwa-base', 'accountant_en', 'Accountant', 1),
  ('hu', 'ttwa-base', 'finance_it_director_en', 'Finance and IT director', 1),
  ('hu', 'ttwa-base', 'finance_accounting_manager_en', 'Finance and Accounting Manager', 1),
  ('hu', 'ttwa-base', 'financial_officer_en', 'Finance Administrator', 1),
  ('hu', 'ttwa-base', 'packer_goods_receiver_en', 'Co-pack Checker', 1),
  ('hu', 'ttwa-base', 'packing_manager_en', 'Co-Pack Leader', 1),
  ('hu', 'ttwa-base', 'packer_en', 'Co-Pack Worker', 1),
  ('hu', 'ttwa-base', 'administ_group_leader_en', 'Administration Team leader', 1),
  ('hu', 'ttwa-base', 'administ_complaints_leader_en', 'Administration and Claim Team leader', 1),
  ('hu', 'ttwa-base', 'administ_manager_en', 'Administration Manager', 1),
  ('hu', 'ttwa-base', 'administrator_en', 'Administrator', 1),
  ('hu', 'ttwa-base', 'goods_receiver_en', 'Checker', 1),
  ('hu', 'ttwa-base', 'logistics_manager_en', 'Logistics Manager', 1),
  ('hu', 'ttwa-base', 'high_lift_driver_en', 'RT driver', 1),
  ('hu', 'ttwa-base', 'operations_shift_manager_en', 'Shift Leader', 1),
  ('hu', 'ttwa-base', 'warehouse_manager_en', 'Warehouse Manager', 1),
  ('hu', 'ttwa-base', 'forklift_goods_receiver_en', 'FT Driver Checker', 1),
  ('hu', 'ttwa-base', 'wms_operator_en', 'WMS Operator', 1),
  ('hu', 'ttwa-base', 'quality_specialist_en', 'Quality Specialist', 1),
  ('hu', 'ttwa-base', 'quality_manager_en', 'Quality Manager', 1),
  ('hu', 'ttwa-base', 'sales_manager_en', 'Sales Manager', 1),
  ('hu', 'ttwa-base', 'sales_specialist_en', 'Sales Specialist', 1),
  ('hu', 'ttwa-base', 'billing_coordinator_en', 'Invoicing Coordinator', 1),
  ('hu', 'ttwa-base', 'it_sap_key_user_en', 'IT & SAP key user', 1),
  ('hu', 'ttwa-base', 'it_group_manager_en', 'IT Team leader', 1),
  ('hu', 'ttwa-base', 'hr_administrator_en', 'HR Administrator', 1),
  ('hu', 'ttwa-base', 'hr_business_partner_en', 'HR Business Partner', 1),
  ('hu', 'ttwa-base', 'hr_specialist_en', 'HR Specialist', 1),
  ('hu', 'ttwa-base', 'hr_manager_en', 'HR Manager', 1),
  ('hu', 'ttwa-base', 'operations_director_en', 'Operation Director', 1),
  ('hu', 'ttwa-base', 'warehouse_trainer_en', 'Warehouse Trainer', 1),
  ('hu', 'ttwa-base', 'gongyoleg_admin_leader_en', 'CSO Team leader', 1),
  ('hu', 'ttwa-base', 'gongyoleg_warehouse_driver_en', 'Pallet Forklift driver', 1),
  ('hu', 'ttwa-base', 'pallet_roll_manager_en', 'CSO and Pallet Manager', 1),
  ('hu', 'ttwa-base', 'battery_charging_coordi_en', 'Battery Charging Area Coordinator', 1),
  ('hu', 'ttwa-base', 'safety_maintenance_coordi_en', 'Security and Facility Coordinator', 1),
  ('hu', 'ttwa-base', 'safety_operations_manager_en', 'Security and Facility Manager', 1),
  ('hu', 'ttwa-base', 'cctv_operator_en', 'CCTV Operator', 1),
  ('hu', 'ttwa-base', 'maintenance_en', 'Maintainer', 1),
  ('hu', 'ttwa-base', 'operations_manager_en', 'Facility Manager', 1),
  ('hu', 'ttwa-base', 'inventory_controller_man_en', 'Inventory controller', 1),
  ('hu', 'ttwa-base', 'inventory_manager_en', 'Inventory controller', 1),
  ('hu', 'ttwa-base', 'inven_management_leader_en', 'Inventory team leader', 1),
  ('hu', 'ttwa-base', 'logistic_support_manager_en', 'Logistics Support Manager', 1),
  ('hu', 'ttwa-base', 'project_coordinator_en', 'Project Coordinator', 1),
  ('hu', 'ttwa-base', 'claims_manager_en', 'Claim Administrator', 1),
  ('hu', 'ttwa-base', 'complaints_group_leader_en', 'Claims Team leader', 1),
  ('hu', 'ttwa-base', 'dispatch_service_manager_en', 'Dispatcher Team leader', 1),
  ('hu', 'ttwa-base', 'dispatch_vehi_technican_en', 'Dispatcher-truck Technician', 1),
  ('hu', 'ttwa-base', 'dispatcher_driver_en', 'Dispatcher-Driver', 1),
  ('hu', 'ttwa-base', 'transport_dispatcher_en', 'Dispatcher', 1),
  ('hu', 'ttwa-base', 'driver_en', 'Driver', 1),
  ('hu', 'ttwa-base', 'delivery_coordinator_en', 'Delivery Coordinator', 1),
  ('hu', 'ttwa-base', 'operational_dispatcher_en', 'Operative Dispatcher', 1),
  ('hu', 'ttwa-base', 'ramp_coordinator_en', 'Delivery Coordinator', 1),
  ('hu', 'ttwa-base', 'senior_dispatcher_en', 'Senior Dispatcher', 1),
  ('hu', 'ttwa-base', 'transport_group_leader_en', 'Transport Administration Team leader', 1),
  ('hu', 'ttwa-base', 'shipping_director_en', 'Transport Director', 1),
  ('hu', 'ttwa-base', 'delivery_project_coordi_en', 'Transport Project Coordinator', 1),
  ('hu', 'ttwa-base', 'transport_project_manager_en', 'Transport Project Coordinator', 1),
  ('hu', 'ttwa-base', 'executive_director_en', 'CEO', 1),
  ('hu', 'ttwa-base', 'business_dev_manager_en', 'Business Development Manager', 1),
  ('hu', 'ttwa-base', 'receptionist_en', 'Receptionist', 1),
  ('hu', 'ttwa-base', 'suitable', 'alkalmas', 1),
  ('en', 'ttwa-base', 'suitable', 'suitable', 1),
  ('hu', 'ttwa-base', 'not_suitable', 'nem alkalmas', 1),
  ('en', 'ttwa-base', 'not_suitable', 'not suitable', 1),
  ('hu', 'ttwa-base', 'temporarily_suitable', 'ideiglenesen alkalmas', 1),
  ('en', 'ttwa-base', 'temporarily_suitable', 'temporarily suitable', 1),
  ('hu', 'ttwa-base', 'eight_primary_schools', '8 általános iskola', 1),
  ('en', 'ttwa-base', 'eight_primary_schools', '8 primary schools', 1),
  ('hu', 'ttwa-base', 'vocational_school', 'szakmunkás iskola', 1),
  ('en', 'ttwa-base', 'vocational_school', 'vocational school', 1),
  ('hu', 'ttwa-base', 'secondary_school_graduation', 'szakközép iskolai érettségi', 1),
  ('en', 'ttwa-base', 'secondary_school_graduation', 'secondary schoo graduationl', 1),
  ('hu', 'ttwa-base', 'high_school_graduation', 'gimnáziumi érettségi', 1),
  ('en', 'ttwa-base', 'high_school_graduation', 'high school graduation', 1),
  ('hu', 'ttwa-base', 'technical_school', 'technikum', 1),
  ('en', 'ttwa-base', 'technical_school', 'technical school', 1),
  ('hu', 'ttwa-base', 'bsc_degree', 'főiskolai diploma (BSC)', 1),
  ('en', 'ttwa-base', 'bsc_degree', 'Bachelors Degree (BSC)', 1),
  ('hu', 'ttwa-base', 'msc_degree', 'egyetemi diploma (MSC)', 1),
  ('en', 'ttwa-base', 'msc_degree', 'Masters Degree (MSC)', 1),
  ('hu', 'ttwa-base', 'high_education_training', 'felsőoktatási szakképzés (FOSZK)', 1),
  ('en', 'ttwa-base', 'high_education_training', 'higher education vocational training (FOSZK)', 1),
  ('hu', 'ttwa-base', 'mba', 'MBA', 1),
  ('en', 'ttwa-base', 'mba', 'MBA', 1),
  ('hu', 'ttwa-base', 'phd', 'PHD', 1),
  ('en', 'ttwa-base', 'phd', 'PHD', 1),
  ('hu', 'ttwa-base', 'okj', 'OKJ', 1),
  ('en', 'ttwa-base', 'okj', 'OKJ', 1),
  ('hu', 'ttwa-base', 'a_one_level', 'A1', 1),
  ('en', 'ttwa-base', 'a_one_level', 'A1', 1),
  ('hu', 'ttwa-base', 'a_two_level', 'A2', 1),
  ('en', 'ttwa-base', 'a_two_level', 'A2', 1),
  ('hu', 'ttwa-base', 'b_one_level', 'B1', 1),
  ('en', 'ttwa-base', 'b_one_level', 'B1', 1),
  ('hu', 'ttwa-base', 'b_two_level', 'B2', 1),
  ('en', 'ttwa-base', 'b_two_level', 'B2', 1),
  ('hu', 'ttwa-base', 'c_one_level', 'C1', 1),
  ('en', 'ttwa-base', 'c_one_level', 'C1', 1),
  ('hu', 'ttwa-base', 'c_two_level', 'C2', 1),
  ('en', 'ttwa-base', 'c_two_level', 'C2', 1),
  ('hu', 'ttwa-base', 'professional', 'szakmai', 1),
  ('en', 'ttwa-base', 'professional', 'professional', 1),
  ('hu', 'ttwa-base', 'verbal_warning_in_writing', 'Szóbeli figyelmeztetés írásban kiadva', 1),
  ('en', 'ttwa-base', 'verbal_warning_in_writing', 'Verbal warning issued in writing', 1),
  ('hu', 'ttwa-base', 'written_warning', 'Írásbeli figyelmeztetés', 1),
  ('en', 'ttwa-base', 'written_warning', 'Written warning', 1),
  ('hu', 'ttwa-base', 'physical_damage', 'fizikai rongálás', 1),
  ('en', 'ttwa-base', 'physical_damage', 'physical damage', 1),
  ('hu', 'ttwa-base', 'traffic_violation', 'közlekedési szabályszegés', 1),
  ('en', 'ttwa-base', 'traffic_violation', 'traffic violation', 1),
  ('hu', 'ttwa-base', 'forklift_license', 'Targoncás jogosítvány', 1),
  ('en', 'ttwa-base', 'forklift_license', 'Forklift license', 1),
  ('hu', 'ttwa-base', 'ce_license', 'CE jogosítvány', 1),
  ('en', 'ttwa-base', 'ce_license', 'CE license', 1),
  ('hu', 'ttwa-base', 'other_course', 'egyéb tanfolyam', 1),
  ('en', 'ttwa-base', 'other_course', 'other course', 1),
  ('hu', 'ttwa-base', 'car_audi', 'Audi', 1),
  ('en', 'ttwa-base', 'car_audi', 'Audi', 1),
  ('hu', 'ttwa-base', 'car_volkwagen', 'Volkwagen', 1),
  ('en', 'ttwa-base', 'car_volkwagen', 'Volkwagen', 1),
  ('hu', 'ttwa-base', 'car_skoda_superb', 'Skoda Superb', 1),
  ('en', 'ttwa-base', 'car_skoda_superb', 'Skoda Superb', 1),
  ('hu', 'ttwa-base', 'car_skoda_octavia', 'Skoda Octavia', 1),
  ('en', 'ttwa-base', 'car_skoda_octavia', 'Skoda Octavia', 1),
  ('hu', 'ttwa-base', 'car_skoda_fabia', 'Skoda Fabia', 1),
  ('en', 'ttwa-base', 'car_skoda_fabia', 'Skoda Fabia', 1),
  ('hu', 'ttwa-base', 'rented', 'bérelt', 1),
  ('en', 'ttwa-base', 'rented', 'rented', 1),
  ('hu', 'ttwa-base', 'executive', 'Executive', 1),
  ('en', 'ttwa-base', 'executive', 'Executive', 1),
  ('hu', 'ttwa-base', 'specialists', 'Specialists', 1),
  ('en', 'ttwa-base', 'specialists', 'Specialists', 1),
  ('hu', 'ttwa-base', 'admin_staff', 'Admin staff', 1),
  ('en', 'ttwa-base', 'admin_staff', 'Admin staff', 1),
  ('hu', 'ttwa-base', 'drivers', 'Drivers', 1),
  ('en', 'ttwa-base', 'drivers', 'Drivers', 1),
  ('hu', 'ttwa-base', 'warehouse_Staff', 'Warehouse Staff', 1),
  ('en', 'ttwa-base', 'warehouse_Staff', 'Warehouse Staff', 1),
  ('hu', 'ttwa-base', 'others', 'Others', 1),
  ('en', 'ttwa-base', 'others', 'Others', 1),
  ('hu', 'ttwa-base', 'letter_e', 'E', 1),
  ('en', 'ttwa-base', 'letter_e', 'E', 1),
  ('hu', 'ttwa-base', 'letter_s', 'S', 1),
  ('en', 'ttwa-base', 'letter_s', 'S', 1),
  ('hu', 'ttwa-base', 'letter_a', 'A', 1),
  ('en', 'ttwa-base', 'letter_a', 'A', 1),
  ('hu', 'ttwa-base', 'letter_d', 'D', 1),
  ('en', 'ttwa-base', 'letter_d', 'D', 1),
  ('hu', 'ttwa-base', 'letter_w', 'W', 1),
  ('en', 'ttwa-base', 'letter_w', 'W', 1),
  ('hu', 'ttwa-base', 'letter_o', 'O', 1),
  ('en', 'ttwa-base', 'letter_o', 'O', 1),
  ('hu', 'ttwa-base', 'indirect', 'INDIRECT', 1),
  ('en', 'ttwa-base', 'indirect', 'INDIRECT', 1),
  ('hu', 'ttwa-base', 'direct', 'DIRECT', 1),
  ('en', 'ttwa-base', 'direct', 'DIRECT', 1),
  ('hu', 'ttwa-base', 'number_three', '3', 1),
  ('en', 'ttwa-base', 'number_three', '3', 1),
  ('hu', 'ttwa-base', 'number_four', '4', 1),
  ('en', 'ttwa-base', 'number_four', '4', 1),
  ('hu', 'ttwa-base', 'number_five', '5', 1),
  ('en', 'ttwa-base', 'number_five', '5', 1),
  ('hu', 'ttwa-base', 'number_six', '6', 1),
  ('en', 'ttwa-base', 'number_six', '6', 1),
  ('hu', 'ttwa-base', 'number_seven', '7', 1),
  ('en', 'ttwa-base', 'number_seven', '7', 1),
  ('hu', 'ttwa-base', 'number_eight', '8', 1),
  ('en', 'ttwa-base', 'number_eight', '8', 1),
  ('hu', 'ttwa-base', 'number_nine', '9', 1),
  ('en', 'ttwa-base', 'number_nine', '9', 1),
  ('hu', 'ttwa-base', 'number_ten', '10', 1),
  ('en', 'ttwa-base', 'number_ten', '10', 1),
  ('hu', 'ttwa-base', 'number_eleven', '11', 1),
  ('en', 'ttwa-base', 'number_eleven', '11', 1),
  ('hu', 'ttwa-base', 'number_twelve', '12', 1),
  ('en', 'ttwa-base', 'number_twelve', '12', 1),
  ('hu', 'ttwa-base', 'number_thirteen', '13', 1),
  ('en', 'ttwa-base', 'number_thirteen', '13', 1),
  ('hu', 'ttwa-base', 'number_fourteen', '14', 1),
  ('en', 'ttwa-base', 'number_fourteen', '14', 1),
  ('hu', 'ttwa-base', 'number_fifteen', '15', 1),
  ('en', 'ttwa-base', 'number_fifteen', '15', 1),
  ('hu', 'ttwa-base', 'number_sixteen', '16', 1),
  ('en', 'ttwa-base', 'number_sixteen', '16', 1),
  ('hu', 'ttwa-base', 'number_seventeen', '17', 1),
  ('en', 'ttwa-base', 'number_seventeen', '17', 1),
  ('hu', 'ttwa-base', 'number_eighteen', '18', 1),
  ('en', 'ttwa-base', 'number_eighteen', '18', 1),
  ('hu', 'ttwa-base', 'number_nineteen', '19', 1),
  ('en', 'ttwa-base', 'number_nineteen', '19', 1),
  ('hu', 'ttwa-base', 'number_twenty', '20', 1),
  ('en', 'ttwa-base', 'number_twenty', '20', 1),
  ('hu', 'ttwa-base', 'code_fah', 'FAH', 1),
  ('en', 'ttwa-base', 'code_fah', 'FAH', 1),
  ('hu', 'ttwa-base', 'code_faa', 'FAA', 1),
  ('en', 'ttwa-base', 'code_faa', 'FAA', 1),
  ('hu', 'ttwa-base', 'code_faz', 'FAZ', 1),
  ('en', 'ttwa-base', 'code_faz', 'FAZ', 1),
  ('hu', 'ttwa-base', 'code_lsb', 'LSB', 1),
  ('en', 'ttwa-base', 'code_lsb', 'LSB', 1),
  ('hu', 'ttwa-base', 'code_pra', 'PRA', 1),
  ('en', 'ttwa-base', 'code_pra', 'PRA', 1),
  ('hu', 'ttwa-base', 'code_asa', 'ASA', 1),
  ('en', 'ttwa-base', 'code_asa', 'ASA', 1),
  ('hu', 'ttwa-base', 'code_csb', 'CSB', 1),
  ('en', 'ttwa-base', 'code_csb', 'CSB', 1),
  ('hu', 'ttwa-base', 'code_qaa', 'QAA', 1),
  ('en', 'ttwa-base', 'code_qaa', 'QAA', 1),
  ('hu', 'ttwa-base', 'code_slz', 'SLZ', 1),
  ('en', 'ttwa-base', 'code_slz', 'SLZ', 1),
  ('hu', 'ttwa-base', 'code_sla', 'SLA', 1),
  ('en', 'ttwa-base', 'code_sla', 'SLA', 1),
  ('hu', 'ttwa-base', 'code_itc', 'ITC', 1),
  ('en', 'ttwa-base', 'code_itc', 'ITC', 1),
  ('hu', 'ttwa-base', 'code_hra', 'HRA', 1),
  ('en', 'ttwa-base', 'code_hra', 'HRA', 1),
  ('hu', 'ttwa-base', 'code_hrz', 'HRZ', 1),
  ('en', 'ttwa-base', 'code_hrz', 'HRZ', 1),
  ('hu', 'ttwa-base', 'code_emc', 'EMC', 1),
  ('en', 'ttwa-base', 'code_emc', 'EMC', 1),
  ('hu', 'ttwa-base', 'code_ptb', 'PTB', 1),
  ('en', 'ttwa-base', 'code_ptb', 'PTB', 1),
  ('hu', 'ttwa-base', 'code_prb', 'PRB', 1),
  ('en', 'ttwa-base', 'code_prb', 'PRB', 1),
  ('hu', 'ttwa-base', 'code_lsz', 'LSZ', 1),
  ('en', 'ttwa-base', 'code_lsz', 'LSZ', 1),
  ('hu', 'ttwa-base', 'code_lsc', 'LSC', 1),
  ('en', 'ttwa-base', 'code_lsc', 'LSC', 1),
  ('hu', 'ttwa-base', 'feor_code_1312', '1312', 1),
  ('hu', 'ttwa-base', 'feor_code_1321', '1321', 1),
  ('hu', 'ttwa-base', 'feor_code_1322', '1322', 1),
  ('hu', 'ttwa-base', 'feor_code_1411', '1411', 1),
  ('hu', 'ttwa-base', 'feor_code_1412', '1412', 1),
  ('hu', 'ttwa-base', 'feor_code_1415', '1415', 1),
  ('hu', 'ttwa-base', 'feor_code_1419', '1419', 1),
  ('hu', 'ttwa-base', 'feor_code_2137', '2137', 1),
  ('hu', 'ttwa-base', 'feor_code_2159', '2159', 1),
  ('hu', 'ttwa-base', 'feor_code_2514', '2514', 1),
  ('hu', 'ttwa-base', 'feor_code_2910', '2910', 1),
  ('hu', 'ttwa-base', 'feor_code_3135', '3135', 1),
  ('hu', 'ttwa-base', 'feor_code_3139', '3139', 1),
  ('hu', 'ttwa-base', 'feor_code_3143', '3143', 1),
  ('hu', 'ttwa-base', 'feor_code_3161', '3161', 1),
  ('hu', 'ttwa-base', 'feor_code_3190', '3190', 1),
  ('hu', 'ttwa-base', 'feor_code_3221', '3221', 1),
  ('hu', 'ttwa-base', 'feor_code_3622', '3622', 1),
  ('hu', 'ttwa-base', 'feor_code_3910', '3910', 1),
  ('hu', 'ttwa-base', 'feor_code_4112', '4112', 1),
  ('hu', 'ttwa-base', 'feor_code_4121', '4121', 1),
  ('hu', 'ttwa-base', 'feor_code_4131', '4131', 1),
  ('hu', 'ttwa-base', 'feor_code_4132', '4132', 1),
  ('hu', 'ttwa-base', 'feor_code_4134', '4134', 1),
  ('hu', 'ttwa-base', 'feor_code_4190', '4190', 1),
  ('hu', 'ttwa-base', 'feor_code_7341', '7341', 1),
  ('hu', 'ttwa-base', 'feor_code_7342', '7342', 1),
  ('hu', 'ttwa-base', 'feor_code_8416', '8416', 1),
  ('hu', 'ttwa-base', 'feor_code_8417', '8417', 1),
  ('hu', 'ttwa-base', 'feor_code_8425', '8425', 1),
  ('hu', 'ttwa-base', 'feor_code_9223', '9223', 1),
  ('hu', 'ttwa-base', 'feor_code_9225', '9225', 1),
  ('en', 'ttwa-base', 'feor_code_1312', '1312', 1),
  ('en', 'ttwa-base', 'feor_code_1321', '1321', 1),
  ('en', 'ttwa-base', 'feor_code_1322', '1322', 1),
  ('en', 'ttwa-base', 'feor_code_1411', '1411', 1),
  ('en', 'ttwa-base', 'feor_code_1412', '1412', 1),
  ('en', 'ttwa-base', 'feor_code_1415', '1415', 1),
  ('en', 'ttwa-base', 'feor_code_1419', '1419', 1),
  ('en', 'ttwa-base', 'feor_code_2137', '2137', 1),
  ('en', 'ttwa-base', 'feor_code_2159', '2159', 1),
  ('en', 'ttwa-base', 'feor_code_2514', '2514', 1),
  ('en', 'ttwa-base', 'feor_code_2910', '2910', 1),
  ('en', 'ttwa-base', 'feor_code_3135', '3135', 1),
  ('en', 'ttwa-base', 'feor_code_3139', '3139', 1),
  ('en', 'ttwa-base', 'feor_code_3143', '3143', 1),
  ('en', 'ttwa-base', 'feor_code_3161', '3161', 1),
  ('en', 'ttwa-base', 'feor_code_3190', '3190', 1),
  ('en', 'ttwa-base', 'feor_code_3221', '3221', 1),
  ('en', 'ttwa-base', 'feor_code_3622', '3622', 1),
  ('en', 'ttwa-base', 'feor_code_3910', '3910', 1),
  ('en', 'ttwa-base', 'feor_code_4112', '4112', 1),
  ('en', 'ttwa-base', 'feor_code_4121', '4121', 1),
  ('en', 'ttwa-base', 'feor_code_4131', '4131', 1),
  ('en', 'ttwa-base', 'feor_code_4132', '4132', 1),
  ('en', 'ttwa-base', 'feor_code_4134', '4134', 1),
  ('en', 'ttwa-base', 'feor_code_4190', '4190', 1),
  ('en', 'ttwa-base', 'feor_code_7341', '7341', 1),
  ('en', 'ttwa-base', 'feor_code_7342', '7342', 1),
  ('en', 'ttwa-base', 'feor_code_8416', '8416', 1),
  ('en', 'ttwa-base', 'feor_code_8417', '8417', 1),
  ('en', 'ttwa-base', 'feor_code_8425', '8425', 1),
  ('en', 'ttwa-base', 'feor_code_9223', '9223', 1),
  ('en', 'ttwa-base', 'feor_code_9225', '9225', 1),
  ('hu', 'ttwa-base', 'head_of_industrial_activity', 'Ipari tevékenységet folytató egység vezetője', 1),
  ('en', 'ttwa-base', 'head_of_industrial_activity', 'Head of a unit engaged in industrial activity', 1),
  ('hu', 'ttwa-base', 'trans_logis_ware_unit_leader', 'Szállítási, logiszt, rakt-i tev. folyt. egység vez', 1),
  ('en', 'ttwa-base', 'trans_logis_ware_unit_leader', 'Transport, logistics, warehousing. was going on. unit leader', 1),
  ('hu', 'ttwa-base', 'info_telecomm_unit_leader', 'Inf-i és telekommunikációs tev.folyt. egység vez', 1),
  ('en', 'ttwa-base', 'info_telecomm_unit_leader', 'Information and telecommunication activities cont. unit leader', 1),
  ('hu', 'ttwa-base', 'acc_financial_unit_manager', 'Számviteli és pénzügyi tev. folytató egység vez.', 1),
  ('en', 'ttwa-base', 'acc_financial_unit_manager', 'Accounting and financial activities. continuing unit manager', 1),
  ('hu', 'ttwa-base', 'head_of_person_policy_unit', 'Személyzeti vez, humánpolitikai egység vezetője', 1),
  ('en', 'ttwa-base', 'head_of_person_policy_unit', 'Head of personnel, head of human policy unit', 1),
  ('hu', 'ttwa-base', 'sales_marketing_co', 'Értékesítési és marketingtev. folyt.egys.vezetője', 1),
  ('en', 'ttwa-base', 'sales_marketing_co', 'Sales and marketing activities. co-head of', 1),
  ('hu', 'ttwa-base', 'head_of_unit_supporting', 'Egyéb gazdasági tevékenységet segítő egység vez.', 1),
  ('en', 'ttwa-base', 'head_of_unit_supporting', 'Head of unit supporting other economic activities.', 1),
  ('hu', 'ttwa-base', 'quality_assurance_engineer', 'Minőségbiztosítási mérnök', 1),
  ('en', 'ttwa-base', 'quality_assurance_engineer', 'Quality assurance engineer', 1),
  ('hu', 'ttwa-base', 'other_database_analyst', 'Egyéb adatbázis- és hálózati elemző, üzemeltető', 1),
  ('en', 'ttwa-base', 'other_database_analyst', 'Other database and network analyst, operator', 1),
  ('hu', 'ttwa-base', 'feor_controller', 'Kontroller', 1),
  ('en', 'ttwa-base', 'feor_controller', 'Controller', 1),
  ('hu', 'ttwa-base', 'other_qualified_clerk', 'Egyéb magasan képzett ügyintéző', 1),
  ('en', 'ttwa-base', 'other_qualified_clerk', 'Other highly qualified clerk', 1),
  ('hu', 'ttwa-base', 'quality_assurance_techni', 'Minőségbiztosítási technikus', 1),
  ('en', 'ttwa-base', 'quality_assurance_techni', 'Quality assurance technician', 1),
  ('hu', 'ttwa-base', 'other_technician_not_class', 'Egyéb, máshova nem sorolható technikus', 1),
  ('en', 'ttwa-base', 'other_technician_not_class', 'Other technician not classified elsewhere', 1),
  ('hu', 'ttwa-base', 'computer_network_techni', 'Számítógéphálózat- és rendszertechnikus', 1),
  ('en', 'ttwa-base', 'computer_network_techni', 'Computer network and system technician', 1),
  ('hu', 'ttwa-base', 'work_production_organizer', 'Munka- és termelésszervező', 1),
  ('en', 'ttwa-base', 'work_production_organizer', 'Work and production organizer', 1),
  ('hu', 'ttwa-base', 'other_technical_occupation', 'Egyéb műszaki foglalkozású', 1),
  ('en', 'ttwa-base', 'other_technical_occupation', 'Other technical occupation', 1),
  ('hu', 'ttwa-base', 'office_profess_manager', 'Irodai szakmai irányító, felügyelő', 1),
  ('en', 'ttwa-base', 'office_profess_manager', 'Office professional manager, supervisor', 1),
  ('hu', 'ttwa-base', 'sales_manager', 'Kereskedelmi ügyintéző', 1),
  ('en', 'ttwa-base', 'sales_manager', 'Sales manager', 1),
  ('hu', 'ttwa-base', 'other_clerk', 'Egyéb ügyintéző', 1),
  ('en', 'ttwa-base', 'other_clerk', 'Other clerk', 1),
  ('hu', 'ttwa-base', 'general_office_admin', 'Általános irodai adminisztrátor', 1),
  ('en', 'ttwa-base', 'general_office_admin', 'General office administrator', 1),
  ('hu', 'ttwa-base', 'accountant_analyst', 'Könyvelő (analitikus)', 1),
  ('en', 'ttwa-base', 'accountant_analyst', 'Accountant (analyst)', 1),
  ('hu', 'ttwa-base', 'inventory_mat_register', 'Készlet- és anyagnyilvántartó', 1),
  ('en', 'ttwa-base', 'inventory_mat_register', 'Inventory and material register', 1),
  ('hu', 'ttwa-base', 'shipping_forward_register', 'Szállítási, szállítmányozási nyilvántartó', 1),
  ('en', 'ttwa-base', 'shipping_forward_register', 'Shipping and forwarding register', 1),
  ('hu', 'ttwa-base', 'human_policy_admin', 'Humánpolitikai adminisztrátor', 1),
  ('en', 'ttwa-base', 'human_policy_admin', 'Human policy administrator', 1),
  ('hu', 'ttwa-base', 'other_not_class_admin_occupat', 'Egyéb, máshova nem sorolható irodai, ügyv.foglalk', 1),
  ('en', 'ttwa-base', 'other_not_class_admin_occupat', 'Other, not classifiable elsewhere, office, administrative occupations', 1),
  ('hu', 'ttwa-base', 'technic_electr_devices', 'Villamos gépek és készülékek műszerésze, javítója', 1),
  ('en', 'ttwa-base', 'technic_electr_devices', 'Technician and repairer of electrical machines and devices', 1),
  ('hu', 'ttwa-base', 'inf_telecom_furn_repairer', 'Inf-i és telekomm. berend. műszerésze, javítója', 1),
  ('en', 'ttwa-base', 'inf_telecom_furn_repairer', 'Inf. and telecom. furniture. engineer, repairer', 1),
  ('hu', 'ttwa-base', 'car_driver', 'Személygépkocsi-vezető', 1),
  ('en', 'ttwa-base', 'car_driver', 'Car driver', 1),
  ('hu', 'ttwa-base', 'lorry_driver', 'Tehergépkocsi-vezető, kamionsofőr', 1),
  ('en', 'ttwa-base', 'lorry_driver', 'Lorry driver', 1),
  ('hu', 'ttwa-base', 'forklift_driver', 'Targoncavezető', 1),
  ('en', 'ttwa-base', 'forklift_driver', 'Forklift driver', 1),
  ('hu', 'ttwa-base', 'loading_worker', 'Rakodómunkás', 1),
  ('en', 'ttwa-base', 'loading_worker', 'Loading worker', 1),
  ('hu', 'ttwa-base', 'hand_packer', 'Kézi csomagoló', 1),
  ('en', 'ttwa-base', 'hand_packer', 'Hand packer', 1),
  ('hu', 'ttwa-base', 'quali_level_ML1', 'ML1', 1),
  ('hu', 'ttwa-base', 'quali_level_ML2', 'ML2', 1),
  ('hu', 'ttwa-base', 'quali_level_ML3', 'ML3', 1),
  ('hu', 'ttwa-base', 'quali_level_ML4', 'ML4', 1),
  ('hu', 'ttwa-base', 'quali_level_KL1', 'KL1', 1),
  ('hu', 'ttwa-base', 'quali_level_KL2', 'KL2', 1),
  ('hu', 'ttwa-base', 'quali_level_KL3', 'KL3', 1),
  ('hu', 'ttwa-base', 'quali_level_KL4', 'KL4', 1),
  ('hu', 'ttwa-base', 'quali_level_AL1', 'AL1', 1),
  ('hu', 'ttwa-base', 'quali_level_AL2', 'AL2', 1),
  ('hu', 'ttwa-base', 'quali_level_AL3', 'AL3', 1),
  ('hu', 'ttwa-base', 'quali_level_AL4', 'AL4', 1),
  ('hu', 'ttwa-base', 'quali_level_WL1', 'WL1', 1),
  ('hu', 'ttwa-base', 'quali_level_WL2', 'WL2', 1),
  ('hu', 'ttwa-base', 'quali_level_CPL1', 'CPL1', 1),
  ('hu', 'ttwa-base', 'quali_level_CPL2', 'CPL2', 1),
  ('hu', 'ttwa-base', 'quali_level_CPL3', 'CPL3', 1),
  ('hu', 'ttwa-base', 'quali_level_PL1', 'PL1', 1),
  ('hu', 'ttwa-base', 'quali_level_PL2', 'PL2', 1),
  ('hu', 'ttwa-base', 'quali_level_PL3', 'PL3', 1),
  ('hu', 'ttwa-base', 'quali_level_BAL1', 'BAL1', 1),
  ('hu', 'ttwa-base', 'quali_level_BAL2', 'BAL2', 1),
  ('hu', 'ttwa-base', 'quali_level_BAL3', 'BAL3', 1),
  ('hu', 'ttwa-base', 'quali_level_HAL1', 'HAL1', 1),
  ('hu', 'ttwa-base', 'quali_level_HAL2', 'HAL2', 1),
  ('hu', 'ttwa-base', 'quali_level_HAL3', 'HAL3', 1),
  ('hu', 'ttwa-base', 'quali_level_ITL1', 'ITL1', 1),
  ('hu', 'ttwa-base', 'quali_level_ITL2', 'ITL2', 1),
  ('hu', 'ttwa-base', 'quali_level_ITL3', 'ITL3', 1),
  ('hu', 'ttwa-base', 'quali_level_SLL1', 'SLL1', 1),
  ('hu', 'ttwa-base', 'quali_level_SLL2', 'SLL2', 1),
  ('hu', 'ttwa-base', 'quali_level_SLL3', 'SLL3', 1),
  ('en', 'ttwa-base', 'quali_level_ML1', 'ML1', 1),
  ('en', 'ttwa-base', 'quali_level_ML2', 'ML2', 1),
  ('en', 'ttwa-base', 'quali_level_ML3', 'ML3', 1),
  ('en', 'ttwa-base', 'quali_level_ML4', 'ML4', 1),
  ('en', 'ttwa-base', 'quali_level_KL1', 'KL1', 1),
  ('en', 'ttwa-base', 'quali_level_KL2', 'KL2', 1),
  ('en', 'ttwa-base', 'quali_level_KL3', 'KL3', 1),
  ('en', 'ttwa-base', 'quali_level_KL4', 'KL4', 1),
  ('en', 'ttwa-base', 'quali_level_AL1', 'AL1', 1),
  ('en', 'ttwa-base', 'quali_level_AL2', 'AL2', 1),
  ('en', 'ttwa-base', 'quali_level_AL3', 'AL3', 1),
  ('en', 'ttwa-base', 'quali_level_AL4', 'AL4', 1),
  ('en', 'ttwa-base', 'quali_level_WL1', 'WL1', 1),
  ('en', 'ttwa-base', 'quali_level_WL2', 'WL2', 1),
  ('en', 'ttwa-base', 'quali_level_CPL1', 'CPL1', 1),
  ('en', 'ttwa-base', 'quali_level_CPL2', 'CPL2', 1),
  ('en', 'ttwa-base', 'quali_level_CPL3', 'CPL3', 1),
  ('en', 'ttwa-base', 'quali_level_PL1', 'PL1', 1),
  ('en', 'ttwa-base', 'quali_level_PL2', 'PL2', 1),
  ('en', 'ttwa-base', 'quali_level_PL3', 'PL3', 1),
  ('en', 'ttwa-base', 'quali_level_BAL1', 'BAL1', 1),
  ('en', 'ttwa-base', 'quali_level_BAL2', 'BAL2', 1),
  ('en', 'ttwa-base', 'quali_level_BAL3', 'BAL3', 1),
  ('en', 'ttwa-base', 'quali_level_HAL1', 'HAL1', 1),
  ('en', 'ttwa-base', 'quali_level_HAL2', 'HAL2', 1),
  ('en', 'ttwa-base', 'quali_level_HAL3', 'HAL3', 1),
  ('en', 'ttwa-base', 'quali_level_ITL1', 'ITL1', 1),
  ('en', 'ttwa-base', 'quali_level_ITL2', 'ITL2', 1),
  ('en', 'ttwa-base', 'quali_level_ITL3', 'ITL3', 1),
  ('en', 'ttwa-base', 'quali_level_SLL1', 'SLL1', 1),
  ('en', 'ttwa-base', 'quali_level_SLL2', 'SLL2', 1),
  ('en', 'ttwa-base', 'quali_level_SLL3', 'SLL3', 1),
  ('hu', 'ttwa-base', 'blue', 'Blue', 1),
  ('en', 'ttwa-base', 'blue', 'Blue', 1),
  ('hu', 'ttwa-base', 'admin', 'Admin', 1),
  ('en', 'ttwa-base', 'admin', 'Admin', 1),
  ('hu', 'ttwa-base', 'rt_driver', 'Magasemelésű targoncavezető', 1),
  ('en', 'ttwa-base', 'rt_driver', 'RT driver', 1),
  ('hu', 'ttwa-base', 'picker', 'Komissiózó', 1),
  ('en', 'ttwa-base', 'picker', 'Picker', 1),
  ('hu', 'ttwa-base', 'checker', 'Áruátvevő', 1),
  ('en', 'ttwa-base', 'checker', 'Checker', 1),
  ('hu', 'ttwa-base', 'wh_support', 'WH support', 1),
  ('en', 'ttwa-base', 'wh_support', 'WH support', 1),
  ('hu', 'ttwa-base', 'co_pack', 'Csomagoló', 1),
  ('en', 'ttwa-base', 'co_pack', 'Co-Pack', 1),
  ('hu', 'ttwa-base', 'pallet_forklift_driver', 'Paletta targoncás', 1),
  ('en', 'ttwa-base', 'pallet_forklift_driver', 'Pallet forklift driver', 1),
  ('hu', 'ttwa-base', 'basic_admin', 'Basic admin', 1),
  ('en', 'ttwa-base', 'basic_admin', 'Basic admin', 1),
  ('hu', 'ttwa-base', 'higher_admin', 'Higher admin', 1),
  ('en', 'ttwa-base', 'higher_admin', 'Higher admin', 1),
  ('hu', 'ttwa-base', 'it_system_sap_support', 'IT System/SAP support', 1),
  ('en', 'ttwa-base', 'it_system_sap_support', 'IT System/SAP support', 1),
  ('hu', 'ttwa-base', 'shift_leader', 'Műszakvezető', 1),
  ('en', 'ttwa-base', 'shift_leader', 'Shift leader', 1),
  ('hu', 'ttwa-base', 'admin_overhead_holding_local', 'Administration_OverHead HOLDING/LOCAL', 1),
  ('en', 'ttwa-base', 'admin_overhead_holding_local', 'Administration_OverHead HOLDING/LOCAL', 1),
  ('hu', 'ttwa-base', 'company_head_office', 'Company Head Office', 1),
  ('en', 'ttwa-base', 'company_head_office', 'Company Head Office', 1),
  ('hu', 'ttwa-base', 'unused_tech_sources', 'Unused technical sources', 1),
  ('en', 'ttwa-base', 'unused_tech_sources', 'Unused technical sources', 1),
  ('hu', 'ttwa-base', 'info_technology', 'Information Technology', 1),
  ('en', 'ttwa-base', 'info_technology', 'Information Technology', 1),
  ('hu', 'ttwa-base', 'financial_costs', 'Financial costs', 1),
  ('en', 'ttwa-base', 'financial_costs', 'Financial costs', 1),
  ('hu', 'ttwa-base', 'non_calculable_costs', 'Non-calculable costs', 1),
  ('en', 'ttwa-base', 'non_calculable_costs', 'Non-calculable costs', 1),
  ('hu', 'ttwa-base', 'logistics_head_office', 'Logistics Head Office', 1),
  ('en', 'ttwa-base', 'logistics_head_office', 'Logistics Head Office', 1),
  ('hu', 'ttwa-base', 'common_costs', 'Common costs', 1),
  ('en', 'ttwa-base', 'common_costs', 'Common costs', 1),
  ('hu', 'ttwa-base', 'operation_admin', 'Operation Admininstration', 1),
  ('en', 'ttwa-base', 'operation_admin', 'Operation Admininstration', 1),
  ('hu', 'ttwa-base', 'racks', 'Racks', 1),
  ('en', 'ttwa-base', 'racks', 'Racks', 1),
  ('hu', 'ttwa-base', 'pallet_warehouse_mgmt', 'Pallet warehouse / management', 1),
  ('en', 'ttwa-base', 'pallet_warehouse_mgmt', 'Pallet warehouse / management', 1),
  ('hu', 'ttwa-base', 'wlan', 'WLAN', 1),
  ('en', 'ttwa-base', 'wlan', 'WLAN', 1),
  ('hu', 'ttwa-base', 'intl_transport', 'International transport', 1),
  ('en', 'ttwa-base', 'intl_transport', 'International transport', 1),
  ('hu', 'ttwa-base', 'hh_office_intl_transport', 'HH Head Office - International transport', 1),
  ('en', 'ttwa-base', 'hh_office_intl_transport', 'HH Head Office - International transport', 1),
  ('hu', 'ttwa-base', 'ocean_freight', 'Ocean Freight', 1),
  ('en', 'ttwa-base', 'ocean_freight', 'Ocean Freight', 1),
  ('hu', 'ttwa-base', 'transport_mgmt_hhu', 'Transport Management HHU', 1),
  ('en', 'ttwa-base', 'transport_mgmt_hhu', 'Transport Management HHU', 1),
  ('hu', 'ttwa-base', 'oper_fulfillment_gyal', 'Operation Fulfillment Center Gyál', 1),
  ('en', 'ttwa-base', 'oper_fulfillment_gyal', 'Operation Fulfillment Center Gyál', 1),
  ('hu', 'ttwa-base', 'wh_cw1_dry_gyal1', 'Warehouse CW 1 Dry Gyál 1', 1),
  ('en', 'ttwa-base', 'wh_cw1_dry_gyal1', 'Warehouse CW 1 Dry Gyál 1', 1),
  ('hu', 'ttwa-base', 'wh_xd_dry_gyal1', 'Warehouse XD Dry Gyál 1', 1),
  ('en', 'ttwa-base', 'wh_xd_dry_gyal1', 'Warehouse XD Dry Gyál 1', 1),
  ('hu', 'ttwa-base', 'wh_cw2_dry_gyal1', 'Warehouse CW 2 Dry Gyál 1', 1),
  ('en', 'ttwa-base', 'wh_cw2_dry_gyal1', 'Warehouse CW 2 Dry Gyál 1', 1),
  ('hu', 'ttwa-base', 'wh_cw_xd_frozen_gyal1', 'Warehouse CW/XD Frozen Gyál 1', 1),
  ('en', 'ttwa-base', 'wh_cw_xd_frozen_gyal1', 'Warehouse CW/XD Frozen Gyál 1', 1),
  ('hu', 'ttwa-base', 'wh_cw_xd_dry_gyal2_glp', 'Warehouse CW/XD Dry Gyál 2 (GLP)', 1),
  ('en', 'ttwa-base', 'wh_cw_xd_dry_gyal2_glp', 'Warehouse CW/XD Dry Gyál 2 (GLP)', 1),
  ('hu', 'ttwa-base', 'site_gyal1', 'Site Gyál 1', 1),
  ('en', 'ttwa-base', 'site_gyal1', 'Site Gyál 1', 1),
  ('hu', 'ttwa-base', 'building_gyal1', 'Building Gyál 1', 1),
  ('en', 'ttwa-base', 'building_gyal1', 'Building Gyál 1', 1),
  ('hu', 'ttwa-base', 'cooling_tech_gyal1', 'Cooling technology Gyál 1', 1),
  ('en', 'ttwa-base', 'cooling_tech_gyal1', 'Cooling technology Gyál 1', 1),
  ('hu', 'ttwa-base', 'freezing_tech_gyal1', 'Freezing technology Gyál 1', 1),
  ('en', 'ttwa-base', 'freezing_tech_gyal1', 'Freezing technology Gyál 1', 1),
  ('hu', 'ttwa-base', 'building_gyal2_glp', 'Building Gyál 2 (GLP)', 1),
  ('en', 'ttwa-base', 'building_gyal2_glp', 'Building Gyál 2 (GLP)', 1),
  ('hu', 'ttwa-base', 'dc_support_gyal1', 'DC Support Gyál 1', 1),
  ('en', 'ttwa-base', 'dc_support_gyal1', 'DC Support Gyál 1', 1),
  ('hu', 'ttwa-base', 'dc_shared_gyal1', 'DC Shared Gyál 1', 1),
  ('en', 'ttwa-base', 'dc_shared_gyal1', 'DC Shared Gyál 1', 1),
  ('hu', 'ttwa-base', 'dc_support_gyal2_glp', 'DC Support Gyál 2 (GLP)', 1),
  ('en', 'ttwa-base', 'dc_support_gyal2_glp', 'DC Support Gyál 2 (GLP)', 1),
  ('hu', 'ttwa-base', 'dc_shared_gyal2_glp', 'DC Shared Gyál 2 (GLP)', 1),
  ('en', 'ttwa-base', 'dc_shared_gyal2_glp', 'DC Shared Gyál 2 (GLP)', 1),
  ('hu', 'ttwa-base', 'depot_mgmt_hhu_gyal', 'Depot Management HHU Gyál', 1),
  ('en', 'ttwa-base', 'depot_mgmt_hhu_gyal', 'Depot Management HHU Gyál', 1),
  ('hu', 'ttwa-base', 'oper_dispatch_hhu_gyal', 'Operative Dispatching HHU Gyál', 1),
  ('en', 'ttwa-base', 'oper_dispatch_hhu_gyal', 'Operative Dispatching HHU Gyál', 1),
  ('hu', 'ttwa-base', 'direct_dispatch_hhu_gyal', 'Direct Plan.dispatching HHU Gyál', 1),
  ('en', 'ttwa-base', 'direct_dispatch_hhu_gyal', 'Direct Plan.dispatching HHU Gyál', 1),
  ('hu', 'ttwa-base', 'network_dispatch_hhu_gyal', 'Network Plan.dispatching HHU Gyál', 1),
  ('en', 'ttwa-base', 'network_dispatch_hhu_gyal', 'Network Plan.dispatching HHU Gyál', 1),
  ('hu', 'ttwa-base', 'smalldis_dispatch_hhu_gyal', 'SmallDis Plan.dispatching HHU Gyál', 1),
  ('en', 'ttwa-base', 'smalldis_dispatch_hhu_gyal', 'SmallDis Plan.dispatching HHU Gyál', 1),
  ('hu', 'ttwa-base', 'total_dispatch_hhu_gyal', 'Total Dispatching HHU Gyál', 1),
  ('en', 'ttwa-base', 'total_dispatch_hhu_gyal', 'Total Dispatching HHU Gyál', 1),
  ('hu', 'ttwa-base', 'external_transport_hhu_gyal', 'External Transport HHU Gyál', 1),
  ('en', 'ttwa-base', 'external_transport_hhu_gyal', 'External Transport HHU Gyál', 1),
  ('hu', 'ttwa-base', 'fleet_mgmt_hhu_gyal', 'Fleet Management HHU Gyál', 1),
  ('en', 'ttwa-base', 'fleet_mgmt_hhu_gyal', 'Fleet Management HHU Gyál', 1),
  ('hu', 'ttwa-base', 'rigid_01_03_hhu_gyal', 'Rigid 01-03 HHU Gyál', 1),
  ('en', 'ttwa-base', 'rigid_01_03_hhu_gyal', 'Rigid 01-03 HHU Gyál', 1),
  ('hu', 'ttwa-base', 'rigid_04_08_hhu_gyal', 'Rigid 04-08 HHU Gyál', 1),
  ('en', 'ttwa-base', 'rigid_04_08_hhu_gyal', 'Rigid 04-08 HHU Gyál', 1),
  ('hu', 'ttwa-base', 'rigid_09_14_hhu_gyal', 'Rigid 09-14 HHU Gyál', 1),
  ('en', 'ttwa-base', 'rigid_09_14_hhu_gyal', 'Rigid 09-14 HHU Gyál', 1),
  ('hu', 'ttwa-base', 'rigid_15_19_hhu_gyal', 'Rigid 15-19 HHU Gyál', 1),
  ('en', 'ttwa-base', 'rigid_15_19_hhu_gyal', 'Rigid 15-19 HHU Gyál', 1),
  ('hu', 'ttwa-base', 'rigid_20_24_hhu_gyal', 'Rigid 20-24 HHU Gyál', 1),
  ('en', 'ttwa-base', 'rigid_20_24_hhu_gyal', 'Rigid 20-24 HHU Gyál', 1),
  ('hu', 'ttwa-base', 'semitrailer_33_72_hhu_gyal', 'SemiTrailer/Trailer 33-72 HHU Gyál', 1),
  ('en', 'ttwa-base', 'semitrailer_33_72_hhu_gyal', 'SemiTrailer/Trailer 33-72 HHU Gyál', 1),
  ('hu', 'ttwa-base', 'tractor_hhu_gyal', 'Tractor HHU Gyál', 1),
  ('en', 'ttwa-base', 'tractor_hhu_gyal', 'Tractor HHU Gyál', 1),
  ('hu', 'ttwa-base', 'roadtrain_53_96_hhu_gyal', 'RoadTrain 53-96 HHU Gyál', 1),
  ('en', 'ttwa-base', 'roadtrain_53_96_hhu_gyal', 'RoadTrain 53-96 HHU Gyál', 1),
  ('hu', 'ttwa-base', 'drivers_hhu_gyal', 'Drivers HHU Gyál', 1),
  ('en', 'ttwa-base', 'drivers_hhu_gyal', 'Drivers HHU Gyál', 1),
  ('hu', 'ttwa-base', 'loaders_cross_dock_hhu_gyal', 'Loaders Cross-Dock HHU Gyál', 1),
  ('en', 'ttwa-base', 'loaders_cross_dock_hhu_gyal', 'Loaders Cross-Dock HHU Gyál', 1),
  ('hu', 'ttwa-base', 'rigid_01_03_hhu_gyal_int', 'Rigid 01-03 HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'rigid_01_03_hhu_gyal_int', 'Rigid 01-03 HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'rigid_04_08_hhu_gyal_int', 'Rigid 04-08 HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'rigid_04_08_hhu_gyal_int', 'Rigid 04-08 HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'rigid_09_14_hhu_gyal_int', 'Rigid 09-14 HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'rigid_09_14_hhu_gyal_int', 'Rigid 09-14 HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'rigid_15_19_hhu_gyal_int', 'Rigid 15-19 HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'rigid_15_19_hhu_gyal_int', 'Rigid 15-19 HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'rigid_20_24_hhu_gyal_int', 'Rigid 20-24 HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'rigid_20_24_hhu_gyal_int', 'Rigid 20-24 HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'semitrailer_33_72_hhu_gyal_int', 'SemiTr/Trailer 33-72 HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'semitrailer_33_72_hhu_gyal_int', 'SemiTr/Trailer 33-72 HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'tractor_hhu_gyal_int', 'Tractor HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'tractor_hhu_gyal_int', 'Tractor HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'roadtrain_53_96_hhu_gyal_int', 'RoadTrain 53-96 HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'roadtrain_53_96_hhu_gyal_int', 'RoadTrain 53-96 HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'dry_chill_xd_hhu_gyal_int', 'Dry/Chill XD Cross-Dock HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'dry_chill_xd_hhu_gyal_int', 'Dry/Chill XD Cross-Dock HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'frozen_xd_hhu_gyal_int', 'Frozen XD Cross-Dock HHU Gyál (INT)', 1),
  ('en', 'ttwa-base', 'frozen_xd_hhu_gyal_int', 'Frozen XD Cross-Dock HHU Gyál (INT)', 1),
  ('hu', 'ttwa-base', 'operations_vas_gyal', 'Operations VAS Gyál', 1),
  ('en', 'ttwa-base', 'operations_vas_gyal', 'Operations VAS Gyál', 1),
  ('hu', 'ttwa-base', 'wh_cw_xd_chilled_vecses', 'Warehouse CW/XD Chilled Vecsés', 1),
  ('en', 'ttwa-base', 'wh_cw_xd_chilled_vecses', 'Warehouse CW/XD Chilled Vecsés', 1),
  ('hu', 'ttwa-base', 'site_vecses_no_use', 'Site Vecsés NO USE', 1),
  ('en', 'ttwa-base', 'site_vecses_no_use', 'Site Vecsés NO USE', 1),
  ('hu', 'ttwa-base', 'dc_support_vecses', 'DC Support Vecsés', 1),
  ('en', 'ttwa-base', 'dc_support_vecses', 'DC Support Vecsés', 1),
  ('hu', 'ttwa-base', 'dc_shared_vecses', 'DC Shared Vecsés', 1),
  ('en', 'ttwa-base', 'dc_shared_vecses', 'DC Shared Vecsés', 1),
  ('hu', 'ttwa-base', 'dry_chill_xd_hhu_vecses_int', 'Dry/Chill XD Cross-Dock HHU Vecsés (INT)', 1),
  ('en', 'ttwa-base', 'dry_chill_xd_hhu_vecses_int', 'Dry/Chill XD Cross-Dock HHU Vecsés (INT)', 1),
  ('hu', 'ttwa-base', 'no_use_xd_hhu_ve_int', 'NO_USE Dry/Chill XD C HHU VE (INT)', 1),
  ('en', 'ttwa-base', 'no_use_xd_hhu_ve_int', 'NO_USE Dry/Chill XD C HHU VE (INT)', 1),
  ('hu', 'ttwa-base', 'fulfill_center_szig_2', 'Oper. Fulfill Center Szigetszentmiklós 2', 1),
  ('en', 'ttwa-base', 'fulfill_center_szig_2', 'Oper. Fulfill Center Szigetszentmiklós 2', 1),
  ('hu', 'ttwa-base', 'common_costs_szig_1', 'Common costs Szigetszentmiklós 1', 1),
  ('en', 'ttwa-base', 'common_costs_szig_1', 'Common costs Szigetszentmiklós 1', 1),
  ('hu', 'ttwa-base', 'common_costs_szig_2', 'Common costs Szigetszentmiklós 2', 1),
  ('en', 'ttwa-base', 'common_costs_szig_2', 'Common costs Szigetszentmiklós 2', 1),
  ('hu', 'ttwa-base', 'common_cost_oper_szig', 'Common Cost Operation Szigetszentmiklós', 1),
  ('en', 'ttwa-base', 'common_cost_oper_szig', 'Common Cost Operation Szigetszentmiklós', 1),
  ('hu', 'ttwa-base', 'wh_cw_dry_szig_1', 'Warehouse CW Dry Szigetszentmiklós 1', 1),
  ('en', 'ttwa-base', 'wh_cw_dry_szig_1', 'Warehouse CW Dry Szigetszentmiklós 1', 1),
  ('hu', 'ttwa-base', 'building_szig_1', 'Building Szigetszentmiklós 1', 1),
  ('en', 'ttwa-base', 'building_szig_1', 'Building Szigetszentmiklós 1', 1),
  ('hu', 'ttwa-base', 'building_szig_2', 'Building Szigetszentmiklós 2', 1),
  ('en', 'ttwa-base', 'building_szig_2', 'Building Szigetszentmiklós 2', 1),
  ('hu', 'ttwa-base', 'dc_support_szig', 'DC Support Szigetszentmiklós', 1),
  ('en', 'ttwa-base', 'dc_support_szig', 'DC Support Szigetszentmiklós', 1),
  ('hu', 'ttwa-base', 'dc_shared_szig', 'DC Shared Szigetszentmiklós', 1),
  ('en', 'ttwa-base', 'dc_shared_szig', 'DC Shared Szigetszentmiklós', 1),
  ('hu', 'ttwa-base', 'operations_vas_szig_1', 'Operations VAS Szigetszentmiklós 1', 1),
  ('en', 'ttwa-base', 'operations_vas_szig_1', 'Operations VAS Szigetszentmiklós 1', 1),
  ('hu', 'ttwa-base', 'operations_vas_szig_2', 'Operations VAS Szigetszentmiklós 2', 1),
  ('en', 'ttwa-base', 'operations_vas_szig_2', 'Operations VAS Szigetszentmiklós 2', 1),
  ('hu', 'ttwa-base', 'hh_global_solutions', 'HH Global Solutions Management&Others', 1),
  ('en', 'ttwa-base', 'hh_global_solutions', 'HH Global Solutions Management&Others', 1),
  ('hu', 'ttwa-base', 'ocean_road_office_global_hu_gy', 'Ocean&Road Office Global HU GY', 1),
  ('en', 'ttwa-base', 'ocean_road_office_global_hu_gy', 'Ocean&Road Office Global HU GY', 1),
  ('hu', 'ttwa-base', 'road_office_global_hu_gy', 'Road Office Global Solutions HU GY', 1),
  ('en', 'ttwa-base', 'road_office_global_hu_gy', 'Road Office Global Solutions HU GY', 1),
  ('hu', 'ttwa-base', 'transport_admin_hhu', 'Transport Administration HHU', 1),
  ('en', 'ttwa-base', 'transport_admin_hhu', 'Transport Administration HHU', 1),
  ('hu', 'ttwa-base', 'cost_code_4110AO00', '4110AO00', 1),
  ('hu', 'ttwa-base', 'cost_code_4110AO01', '4110AO01', 1),
  ('hu', 'ttwa-base', 'cost_code_4110AO05', '4110AO05', 1),
  ('hu', 'ttwa-base', 'cost_code_4110AO06', '4110AO06', 1),
  ('hu', 'ttwa-base', 'cost_code_4110AO07', '4110AO07', 1),
  ('hu', 'ttwa-base', 'cost_code_4110AO08', '4110AO08', 1),
  ('hu', 'ttwa-base', 'cost_code_4110AO12', '4110AO12', 1),
  ('hu', 'ttwa-base', 'cost_code_4110AO99', '4110AO99', 1),
  ('hu', 'ttwa-base', 'cost_code_4110LA11', '4110LA11', 1),
  ('hu', 'ttwa-base', 'cost_code_4110LE01', '4110LE01', 1),
  ('hu', 'ttwa-base', 'cost_code_4110LW10', '4110LW10', 1),
  ('hu', 'ttwa-base', 'cost_code_4110SO65', '4110SO65', 1),
  ('hu', 'ttwa-base', 'cost_code_4110TA20', '4110TA20', 1),
  ('hu', 'ttwa-base', 'cost_code_4110TA21', '4110TA21', 1),
  ('hu', 'ttwa-base', 'cost_code_4110TA30', '4110TA30', 1),
  ('hu', 'ttwa-base', 'cost_code_4110TA31', '4110TA31', 1),
  ('hu', 'ttwa-base', 'cost_code_4110TA32', '4110TA32', 1),
  ('hu', 'ttwa-base', 'cost_code_4112FO01', '4112FO01', 1),
  ('hu', 'ttwa-base', 'cost_code_4112LW01', '4112LW01', 1),
  ('hu', 'ttwa-base', 'cost_code_4112LW02', '4112LW02', 1),
  ('hu', 'ttwa-base', 'cost_code_4112LW07', '4112LW07', 1),
  ('hu', 'ttwa-base', 'cost_code_4112LW12', '4112LW12', 1),
  ('hu', 'ttwa-base', 'cost_code_4112LW13', '4112LW13', 1),
  ('hu', 'ttwa-base', 'cost_code_4112SO01', '4112SO01', 1),
  ('hu', 'ttwa-base', 'cost_code_4112SO02', '4112SO02', 1),
  ('hu', 'ttwa-base', 'cost_code_4112SO03', '4112SO03', 1),
  ('hu', 'ttwa-base', 'cost_code_4112SO04', '4112SO04', 1),
  ('hu', 'ttwa-base', 'cost_code_4112SO06', '4112SO06', 1),
  ('hu', 'ttwa-base', 'cost_code_4112SO61', '4112SO61', 1),
  ('hu', 'ttwa-base', 'cost_code_4112SO62', '4112SO62', 1),
  ('hu', 'ttwa-base', 'cost_code_4112SO64', '4112SO64', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TA01', '4112TA01', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TD00', '4112TD00', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TD02', '4112TD02', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TD03', '4112TD03', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TD04', '4112TD04', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TD05', '4112TD05', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TE01', '4112TE01', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF01', '4112TF01', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF11', '4112TF11', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF12', '4112TF12', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF13', '4112TF13', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF14', '4112TF14', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF15', '4112TF15', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF16', '4112TF16', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF17', '4112TF17', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF18', '4112TF18', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF21', '4112TF21', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF22', '4112TF22', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF31', '4112TF31', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF32', '4112TF32', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF33', '4112TF33', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF34', '4112TF34', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF35', '4112TF35', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF36', '4112TF36', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF37', '4112TF37', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TF38', '4112TF38', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TX11', '4112TX11', 1),
  ('hu', 'ttwa-base', 'cost_code_4112TX12', '4112TX12', 1),
  ('hu', 'ttwa-base', 'cost_code_4112VC01', '4112VC01', 1),
  ('hu', 'ttwa-base', 'cost_code_4113LW02', '4113LW02', 1),
  ('hu', 'ttwa-base', 'cost_code_4113SO01', '4113SO01', 1),
  ('hu', 'ttwa-base', 'cost_code_4113SO61', '4113SO61', 1),
  ('hu', 'ttwa-base', 'cost_code_4113SO62', '4113SO62', 1),
  ('hu', 'ttwa-base', 'cost_code_4113TX01', '4113TX01', 1),
  ('hu', 'ttwa-base', 'cost_code_4113TX11', '4113TX11', 1),
  ('hu', 'ttwa-base', 'cost_code_4117FO01', '4117FO01', 1),
  ('hu', 'ttwa-base', 'cost_code_4117LO01', '4117LO01', 1),
  ('hu', 'ttwa-base', 'cost_code_4117LO02', '4117LO02', 1),
  ('hu', 'ttwa-base', 'cost_code_4117LO03', '4117LO03', 1),
  ('hu', 'ttwa-base', 'cost_code_4117LW01', '4117LW01', 1),
  ('hu', 'ttwa-base', 'cost_code_4117SO02', '4117SO02', 1),
  ('hu', 'ttwa-base', 'cost_code_4117SO03', '4117SO03', 1),
  ('hu', 'ttwa-base', 'cost_code_4117SO61', '4117SO61', 1),
  ('hu', 'ttwa-base', 'cost_code_4117SO62', '4117SO62', 1),
  ('hu', 'ttwa-base', 'cost_code_4117VC01', '4117VC01', 1),
  ('hu', 'ttwa-base', 'cost_code_4117VC02', '4117VC02', 1),
  ('hu', 'ttwa-base', 'cost_code_4210HA30', '4210HA30', 1),
  ('hu', 'ttwa-base', 'cost_code_4212GO10', '4212GO10', 1),
  ('hu', 'ttwa-base', 'cost_code_4212GR10', '4212GR10', 1),
  ('en', 'ttwa-base', 'cost_code_4110AO00', '4110AO00', 1),
  ('en', 'ttwa-base', 'cost_code_4110AO01', '4110AO01', 1),
  ('en', 'ttwa-base', 'cost_code_4110AO05', '4110AO05', 1),
  ('en', 'ttwa-base', 'cost_code_4110AO06', '4110AO06', 1),
  ('en', 'ttwa-base', 'cost_code_4110AO07', '4110AO07', 1),
  ('en', 'ttwa-base', 'cost_code_4110AO08', '4110AO08', 1),
  ('en', 'ttwa-base', 'cost_code_4110AO12', '4110AO12', 1),
  ('en', 'ttwa-base', 'cost_code_4110AO99', '4110AO99', 1),
  ('en', 'ttwa-base', 'cost_code_4110LA11', '4110LA11', 1),
  ('en', 'ttwa-base', 'cost_code_4110LE01', '4110LE01', 1),
  ('en', 'ttwa-base', 'cost_code_4110LW10', '4110LW10', 1),
  ('en', 'ttwa-base', 'cost_code_4110SO65', '4110SO65', 1),
  ('en', 'ttwa-base', 'cost_code_4110TA20', '4110TA20', 1),
  ('en', 'ttwa-base', 'cost_code_4110TA21', '4110TA21', 1),
  ('en', 'ttwa-base', 'cost_code_4110TA30', '4110TA30', 1),
  ('en', 'ttwa-base', 'cost_code_4110TA31', '4110TA31', 1),
  ('en', 'ttwa-base', 'cost_code_4110TA32', '4110TA32', 1),
  ('en', 'ttwa-base', 'cost_code_4112FO01', '4112FO01', 1),
  ('en', 'ttwa-base', 'cost_code_4112LW01', '4112LW01', 1),
  ('en', 'ttwa-base', 'cost_code_4112LW02', '4112LW02', 1),
  ('en', 'ttwa-base', 'cost_code_4112LW07', '4112LW07', 1),
  ('en', 'ttwa-base', 'cost_code_4112LW12', '4112LW12', 1),
  ('en', 'ttwa-base', 'cost_code_4112LW13', '4112LW13', 1),
  ('en', 'ttwa-base', 'cost_code_4112SO01', '4112SO01', 1),
  ('en', 'ttwa-base', 'cost_code_4112SO02', '4112SO02', 1),
  ('en', 'ttwa-base', 'cost_code_4112SO03', '4112SO03', 1),
  ('en', 'ttwa-base', 'cost_code_4112SO04', '4112SO04', 1),
  ('en', 'ttwa-base', 'cost_code_4112SO06', '4112SO06', 1),
  ('en', 'ttwa-base', 'cost_code_4112SO61', '4112SO61', 1),
  ('en', 'ttwa-base', 'cost_code_4112SO62', '4112SO62', 1),
  ('en', 'ttwa-base', 'cost_code_4112SO64', '4112SO64', 1),
  ('en', 'ttwa-base', 'cost_code_4112TA01', '4112TA01', 1),
  ('en', 'ttwa-base', 'cost_code_4112TD00', '4112TD00', 1),
  ('en', 'ttwa-base', 'cost_code_4112TD02', '4112TD02', 1),
  ('en', 'ttwa-base', 'cost_code_4112TD03', '4112TD03', 1),
  ('en', 'ttwa-base', 'cost_code_4112TD04', '4112TD04', 1),
  ('en', 'ttwa-base', 'cost_code_4112TD05', '4112TD05', 1),
  ('en', 'ttwa-base', 'cost_code_4112TE01', '4112TE01', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF01', '4112TF01', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF11', '4112TF11', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF12', '4112TF12', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF13', '4112TF13', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF14', '4112TF14', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF15', '4112TF15', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF16', '4112TF16', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF17', '4112TF17', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF18', '4112TF18', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF21', '4112TF21', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF22', '4112TF22', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF31', '4112TF31', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF32', '4112TF32', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF33', '4112TF33', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF34', '4112TF34', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF35', '4112TF35', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF36', '4112TF36', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF37', '4112TF37', 1),
  ('en', 'ttwa-base', 'cost_code_4112TF38', '4112TF38', 1),
  ('en', 'ttwa-base', 'cost_code_4112TX11', '4112TX11', 1),
  ('en', 'ttwa-base', 'cost_code_4112TX12', '4112TX12', 1),
  ('en', 'ttwa-base', 'cost_code_4112VC01', '4112VC01', 1),
  ('en', 'ttwa-base', 'cost_code_4113LW02', '4113LW02', 1),
  ('en', 'ttwa-base', 'cost_code_4113SO01', '4113SO01', 1),
  ('en', 'ttwa-base', 'cost_code_4113SO61', '4113SO61', 1),
  ('en', 'ttwa-base', 'cost_code_4113SO62', '4113SO62', 1),
  ('en', 'ttwa-base', 'cost_code_4113TX01', '4113TX01', 1),
  ('en', 'ttwa-base', 'cost_code_4113TX11', '4113TX11', 1),
  ('en', 'ttwa-base', 'cost_code_4117FO01', '4117FO01', 1),
  ('en', 'ttwa-base', 'cost_code_4117LO01', '4117LO01', 1),
  ('en', 'ttwa-base', 'cost_code_4117LO02', '4117LO02', 1),
  ('en', 'ttwa-base', 'cost_code_4117LO03', '4117LO03', 1),
  ('en', 'ttwa-base', 'cost_code_4117LW01', '4117LW01', 1),
  ('en', 'ttwa-base', 'cost_code_4117SO02', '4117SO02', 1),
  ('en', 'ttwa-base', 'cost_code_4117SO03', '4117SO03', 1),
  ('en', 'ttwa-base', 'cost_code_4117SO61', '4117SO61', 1),
  ('en', 'ttwa-base', 'cost_code_4117SO62', '4117SO62', 1),
  ('en', 'ttwa-base', 'cost_code_4117VC01', '4117VC01', 1),
  ('en', 'ttwa-base', 'cost_code_4117VC02', '4117VC02', 1),
  ('en', 'ttwa-base', 'cost_code_4210HA30', '4210HA30', 1),
  ('en', 'ttwa-base', 'cost_code_4212GO10', '4212GO10', 1),
  ('en', 'ttwa-base', 'cost_code_4212GR10', '4212GR10', 1);

UPDATE `_sql_version` SET `revision`=16, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -16--2024-07-17-11:30---------------------------------------------------------------

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
    ('es_option7', '1', '0', 'fix', '1'),
    ('es_option7', '2', '0', 'variable', '1'),
    ('wage_type', 'CoM', '0', 'wage_type_commission', '1'),
    ('option8', '1', '0', 'gyal_1', '1'),
    ('option8', '2', '0', 'gyal_2', '1'),
    ('option8', '3', '0', 'vecses', '1'),
    ('option8', '4', '0', 'szigetszentmiklos', '1'),
    ('option9', '1', '0', 'retired', '1'),
    ('option9', '2', '0', 'next_to_childcarefee', '1'),
    ('option12', '1', '0', 'yes', '1'),
    ('option12', '2', '0', 'no', '1'),
    ('option30', '1', '0', 'employer', '1'),
    ('option30', '2', '0', 'atr_employee', '1'),
    ('option31', '1', '0', 'employee_termination', '1'),
    ('option31', '2', '0', 'employer_termination', '1'),
    ('option31', '3', '0', 'common_understanding_employee', '1'),
    ('option31', '4', '0', 'joint_agreement_employer', '1'),
    ('option31', '5', '0', 'term_probation_period_employee', '1'),
    ('option31', '6', '0', 'term_probation_period_employer', '1'),
    ('option31', '7', '0', 'immediate_termination_employee', '1'),
    ('option31', '8', '0', 'immediate_termination_employer', '1'),
    ('option31', '9', '0', 'certain_period_time_expired', '1'),
    ('option31', '10', '0', 'retirement', '1'),
    ('option31', '11', '0', 'restrictions_affecting_work', '1'),
    ('option31', '12', '0', 'death', '1'),
    ('option31', '13', '0', 'other', '1'),
    ('option32', '1', '0', 'security_issues', '1'),
    ('option32', '2', '0', 'behavioral_problems', '1'),
    ('option32', '3', '0', 'quality_replacement', '1'),
    ('option32', '4', '0', 'reorganization', '1'),
    ('option32', '5', '0', 'group_downsizing', '1'),
    ('option32', '6', '0', 'loss_trust', '1'),
    ('option32', '7', '0', 'working_conditions_tools', '1'),
    ('option32', '8', '0', 'worktime', '1'),
    ('option32', '9', '0', 'job_content', '1'),
    ('option32', '10', '0', 'commuting_distance', '1'),
    ('option32', '11', '0', 'workplace_relations_team', '1'),
    ('option32', '12', '0', 'relationship_with_manager', '1'),
    ('option32', '13', '0', 'salary_benefits', '1'),
    ('option32', '14', '0', 'health_reasons', '1'),
    ('option32', '15', '0', 'family_issues', '1'),
    ('option32', '16', '0', 'moving', '1'),
    ('option32', '17', '0', 'education', '1'),
    ('option32', '18', '0', 'lack_advancement_opportunities', '1'),
    ('option32', '19', '0', 'lack_further_training', '1'),
    ('option32', '20', '0', 'do_not_comment', '1'),
    ('option32', '21', '0', 'other', '1'),
    ('option33', '1', '0', 'yes', '1'),
    ('option33', '2', '0', 'no', '1'),
    ('option13', '1', '0', 'controlling_manager_hu', '1'),
    ('option13', '2', '0', 'controller_hu', '1'),
    ('option13', '3', '0', 'accountant_hu', '1'),
    ('option13', '4', '0', 'finance_it_director_hu', '1'),
    ('option13', '5', '0', 'finance_accounting_manager_hu', '1'),
    ('option13', '6', '0', 'financial_officer_hu', '1'),
    ('option13', '7', '0', 'packer_goods_receiver_hu', '1'),
    ('option13', '8', '0', 'packing_manager_hu', '1'),
    ('option13', '9', '0', 'packer_hu', '1'),
    ('option13', '10', '0', 'administ_group_leader_hu', '1'),
    ('option13', '11', '0', 'administ_complaints_leader_hu', '1'),
    ('option13', '12', '0', 'administ_manager_hu', '1'),
    ('option13', '13', '0', 'administrator_hu', '1'),
    ('option13', '14', '0', 'goods_receiver_hu', '1'),
    ('option13', '15', '0', 'logistics_manager_hu', '1'),
    ('option13', '16', '0', 'high_lift_driver_hu', '1'),
    ('option13', '17', '0', 'operations_manager_hu', '1'),
    ('option13', '18', '0', 'warehouse_manager_hu', '1'),
    ('option13', '19', '0', 'forklift_goods_receiver_hu', '1'),
    ('option13', '20', '0', 'wms_operator_hu', '1'),
    ('option13', '21', '0', 'quality_specialist_hu', '1'),
    ('option13', '22', '0', 'quality_manager_hu', '1'),
    ('option13', '23', '0', 'sales_manager_hu', '1'),
    ('option13', '24', '0', 'sales_specialist_hu', '1'),
    ('option13', '25', '0', 'billing_coordinator_hu', '1'),
    ('option13', '26', '0', 'it_sap_key_user_hu', '1'),
    ('option13', '27', '0', 'it_group_manager_hu', '1'),
    ('option13', '28', '0', 'hr_administrator_hu', '1'),
    ('option13', '29', '0', 'hr_business_partner_hu', '1'),
    ('option13', '30', '0', 'hr_specialist_hu', '1'),
    ('option13', '31', '0', 'hr_manager_hu', '1'),
    ('option13', '32', '0', 'operations_director_hu', '1'),
    ('option13', '33', '0', 'warehouse_trainer_hu', '1'),
    ('option13', '34', '0', 'receptionist_hu', '1'),
    ('option13', '35', '0', 'gongyoleg_admin_leader_hu', '1'),
    ('option13', '36', '0', 'gongyoleg_warehouse_driver_hu', '1'),
    ('option13', '37', '0', 'pallet_roll_manager_hu', '1'),
    ('option13', '38', '0', 'battery_charging_coordi_hu', '1'),
    ('option13', '39', '0', 'safety_maintenance_coordi_hu', '1'),
    ('option13', '40', '0', 'safety_operations_manager_hu', '1'),
    ('option13', '41', '0', 'cctv_operator_hu', '1'),
    ('option13', '42', '0', 'maintenance_hu', '1'),
    ('option13', '43', '0', 'operations_manager_hu', '1'),
    ('option13', '44', '0', 'inventory_controller_man_hu', '1'),
    ('option13', '45', '0', 'inventory_manager_hu', '1'),
    ('option13', '46', '0', 'inven_management_leader_hu', '1'),
    ('option13', '47', '0', 'logistic_support_manager_hu', '1'),
    ('option13', '48', '0', 'project_coordinator_hu', '1'),
    ('option13', '49', '0', 'claims_manager_hu', '1'),
    ('option13', '50', '0', 'complaints_group_leader_hu', '1'),
    ('option13', '51', '0', 'dispatch_service_manager_hu', '1'),
    ('option13', '52', '0', 'dispatch_vehi_technican_hu', '1'),
    ('option13', '53', '0', 'dispatcher_driver_hu', '1'),
    ('option13', '54', '0', 'transport_dispatcher_hu', '1'),
    ('option13', '55', '0', 'driver_hu', '1'),
    ('option13', '56', '0', 'delivery_coordinator_hu', '1'),
    ('option13', '57', '0', 'operational_dispatcher_hu', '1'),
    ('option13', '58', '0', 'ramp_coordinator_hu', '1'),
    ('option13', '59', '0', 'senior_dispatcher_hu', '1'),
    ('option13', '60', '0', 'transport_group_leader_hu', '1'),
    ('option13', '61', '0', 'shipping_director_hu', '1'),
    ('option13', '62', '0', 'delivery_project_coordi_hu', '1'),
    ('option13', '63', '0', 'transport_project_manager_hu', '1'),
    ('option13', '64', '0', 'executive_director_hu', '1'),
    ('option13', '65', '0', 'business_dev_manager_hu', '1'),
    ('option13', '66', '0', 'receptionist_hu', '1'),
    ('option14', '1', '0', 'controlling_manager_en', '1'),
    ('option14', '2', '0', 'controller_en', '1'),
    ('option14', '3', '0', 'accountant_en', '1'),
    ('option14', '4', '0', 'finance_it_director_en', '1'),
    ('option14', '5', '0', 'finance_accounting_manager_en', '1'),
    ('option14', '6', '0', 'financial_officer_en', '1'),
    ('option14', '7', '0', 'packer_goods_receiver_en', '1'),
    ('option14', '8', '0', 'packing_manager_en', '1'),
    ('option14', '9', '0', 'packer_en', '1'),
    ('option14', '10', '0', 'administ_group_leader_en', '1'),
    ('option14', '11', '0', 'administ_complaints_leader_en', '1'),
    ('option14', '12', '0', 'administ_manager_en', '1'),
    ('option14', '13', '0', 'administrator_en', '1'),
    ('option14', '14', '0', 'goods_receiver_en', '1'),
    ('option14', '15', '0', 'logistics_manager_en', '1'),
    ('option14', '16', '0', 'high_lift_driver_en', '1'),
    ('option14', '17', '0', 'operations_manager_en', '1'),
    ('option14', '18', '0', 'warehouse_manager_en', '1'),
    ('option14', '19', '0', 'forklift_goods_receiver_en', '1'),
    ('option14', '20', '0', 'wms_operator_en', '1'),
    ('option14', '21', '0', 'quality_specialist_en', '1'),
    ('option14', '22', '0', 'quality_manager_en', '1'),
    ('option14', '23', '0', 'sales_manager_en', '1'),
    ('option14', '24', '0', 'sales_specialist_en', '1'),
    ('option14', '25', '0', 'billing_coordinator_en', '1'),
    ('option14', '26', '0', 'it_sap_key_user_en', '1'),
    ('option14', '27', '0', 'it_group_manager_en', '1'),
    ('option14', '28', '0', 'hr_administrator_en', '1'),
    ('option14', '29', '0', 'hr_business_partner_en', '1'),
    ('option14', '30', '0', 'hr_specialist_en', '1'),
    ('option14', '31', '0', 'hr_manager_en', '1'),
    ('option14', '32', '0', 'operations_director_en', '1'),
    ('option14', '33', '0', 'warehouse_trainer_en', '1'),
    ('option14', '34', '0', 'receptionist_en', '1'),
    ('option14', '35', '0', 'gongyoleg_admin_leader_en', '1'),
    ('option14', '36', '0', 'gongyoleg_warehouse_driver_en', '1'),
    ('option14', '37', '0', 'pallet_roll_manager_en', '1'),
    ('option14', '38', '0', 'battery_charging_coordi_en', '1'),
    ('option14', '39', '0', 'safety_maintenance_coordi_en', '1'),
    ('option14', '40', '0', 'safety_operations_manager_en', '1'),
    ('option14', '41', '0', 'cctv_operator_en', '1'),
    ('option14', '42', '0', 'maintenance_en', '1'),
    ('option14', '43', '0', 'operations_manager_en', '1'),
    ('option14', '44', '0', 'inventory_controller_man_en', '1'),
    ('option14', '45', '0', 'inventory_manager_en', '1'),
    ('option14', '46', '0', 'inven_management_leader_en', '1'),
    ('option14', '47', '0', 'logistic_support_manager_en', '1'),
    ('option14', '48', '0', 'project_coordinator_en', '1'),
    ('option14', '49', '0', 'claims_manager_en', '1'),
    ('option14', '50', '0', 'complaints_group_leader_en', '1'),
    ('option14', '51', '0', 'dispatch_service_manager_en', '1'),
    ('option14', '52', '0', 'dispatch_vehi_technican_en', '1'),
    ('option14', '53', '0', 'dispatcher_driver_en', '1'),
    ('option14', '54', '0', 'transport_dispatcher_en', '1'),
    ('option14', '55', '0', 'driver_en', '1'),
    ('option14', '56', '0', 'delivery_coordinator_en', '1'),
    ('option14', '57', '0', 'operational_dispatcher_en', '1'),
    ('option14', '58', '0', 'ramp_coordinator_en', '1'),
    ('option14', '59', '0', 'senior_dispatcher_en', '1'),
    ('option14', '60', '0', 'transport_group_leader_en', '1'),
    ('option14', '61', '0', 'shipping_director_en', '1'),
    ('option14', '62', '0', 'delivery_project_coordi_en', '1'),
    ('option14', '63', '0', 'transport_project_manager_en', '1'),
    ('option14', '64', '0', 'executive_director_en', '1'),
    ('option14', '65', '0', 'business_dev_manager_en', '1'),
    ('option14', '66', '0', 'receptionist_en', '1'),
    ('ext4_option6', '1', '0', 'yes', '1'),
    ('ext4_option6', '2', '0', 'no', '1'),
    ('ext4_option2', '1', '0', 'suitable', '1'),
    ('ext4_option2', '2', '0', 'not_suitable', '1'),
    ('ext4_option2', '3', '0', 'temporarily_suitable', '1'),
    ('ext4_option7', '1', '0', 'eight_primary_schools', '1'),
    ('ext4_option7', '2', '0', 'vocational_school', '1'),
    ('ext4_option7', '3', '0', 'secondary_school_graduation', '1'),
    ('ext4_option7', '4', '0', 'high_school_graduation', '1'),
    ('ext4_option7', '5', '0', 'technical_school', '1'),
    ('ext4_option7', '6', '0', 'bsc_degree', '1'),
    ('ext4_option7', '7', '0', 'msc_degree', '1'),
    ('ext4_option7', '8', '0', 'high_education_training', '1'),
    ('ext4_option7', '9', '0', 'mba', '1'),
    ('ext4_option7', '10', '0', 'phd', '1'),
    ('ext4_option7', '11', '0', 'okj', '1'),
    ('ext4_option17', '1', '0', 'a_one_level', '1'),
    ('ext4_option17', '2', '0', 'a_two_level', '1'),
    ('ext4_option17', '3', '0', 'b_one_level', '1'),
    ('ext4_option17', '4', '0', 'b_two_level', '1'),
    ('ext4_option17', '5', '0', 'c_one_level', '1'),
    ('ext4_option17', '6', '0', 'c_two_level', '1'),
    ('ext4_option17', '7', '0', 'professional', '1'),
    ('ext4_option19', '1', '0', 'a_one_level', '1'),
    ('ext4_option19', '2', '0', 'a_two_level', '1'),
    ('ext4_option19', '3', '0', 'b_one_level', '1'),
    ('ext4_option19', '4', '0', 'b_two_level', '1'),
    ('ext4_option19', '5', '0', 'c_one_level', '1'),
    ('ext4_option19', '6', '0', 'c_two_level', '1'),
    ('ext4_option19', '7', '0', 'professional', '1'),
    ('ext4_option21', '1', '0', 'a_one_level', '1'),
    ('ext4_option21', '2', '0', 'a_two_level', '1'),
    ('ext4_option21', '3', '0', 'b_one_level', '1'),
    ('ext4_option21', '4', '0', 'b_two_level', '1'),
    ('ext4_option21', '5', '0', 'c_one_level', '1'),
    ('ext4_option21', '6', '0', 'c_two_level', '1'),
    ('ext4_option21', '7', '0', 'professional', '1'),
    ('ext4_option23', '1', '0', 'a_one_level', '1'),
    ('ext4_option23', '2', '0', 'a_two_level', '1'),
    ('ext4_option23', '3', '0', 'b_one_level', '1'),
    ('ext4_option23', '4', '0', 'b_two_level', '1'),
    ('ext4_option23', '5', '0', 'c_one_level', '1'),
    ('ext4_option23', '6', '0', 'c_two_level', '1'),
    ('ext4_option23', '7', '0', 'professional', '1'),
    ('ext5_option22', '1', '0', 'verbal_warning_in_writing', '1'),
    ('ext5_option22', '2', '0', 'written_warning', '1'),
    ('ext5_option24', '1', '0', 'verbal_warning_in_writing', '1'),
    ('ext5_option24', '2', '0', 'written_warning', '1'),
    ('ext5_option26', '1', '0', 'verbal_warning_in_writing', '1'),
    ('ext5_option26', '2', '0', 'written_warning', '1'),
    ('ext5_option28', '1', '0', 'verbal_warning_in_writing', '1'),
    ('ext5_option28', '2', '0', 'written_warning', '1'),
    ('ext5_option30', '1', '0', 'physical_damage', '1'),
    ('ext5_option30', '2', '0', 'traffic_violation', '1'),
    ('ext5_option30', '3', '0', 'other', '1'),
    ('ext5_option33', '1', '0', 'forklift_license', '1'),
    ('ext5_option33', '2', '0', 'ce_license', '1'),
    ('ext5_option33', '3', '0', 'other_course', '1'),
    ('ext5_option12', '1', '0', 'car_audi', '1'),
    ('ext5_option12', '2', '0', 'car_volkwagen', '1'),
    ('ext5_option12', '3', '0', 'car_skoda_superb', '1'),
    ('ext5_option12', '4', '0', 'car_skoda_octavia', '1'),
    ('ext5_option12', '5', '0', 'car_skoda_fabia', '1'),
    ('ext5_option12', '6', '0', 'rented', '1'),
    ('ext5_option9', '1', '0', 'fix', '1'),
    ('ext5_option9', '2', '0', 'variable', '1'),
    ('ext3_option1', '1', '0', 'yes', '1'),
    ('ext3_option1', '2', '0', 'no', '1'),
    ('ext3_option2', '1', '0', 'yes', '1'),
    ('ext3_option2', '2', '0', 'no', '1'),
    ('ext3_option3', '1', '0', 'yes', '1'),
    ('ext3_option3', '2', '0', 'no', '1'),
    ('ext3_option4', '1', '0', 'yes', '1'),
    ('ext3_option4', '2', '0', 'no', '1'),
    ('ext3_option5', '1', '0', 'yes', '1'),
    ('ext3_option5', '2', '0', 'no', '1'),
    ('ext3_option6', '1', '0', 'yes', '1'),
    ('ext3_option6', '2', '0', 'no', '1'),
    ('ext3_option7', '1', '0', 'yes', '1'),
    ('ext3_option7', '2', '0', 'no', '1'),
    ('ext3_option10', '1', '0', 'yes', '1'),
    ('ext3_option10', '2', '0', 'no', '1'),
    ('ext2_option34', '1', '0', 'yes', '1'),
    ('ext2_option34', '2', '0', 'no', '1'),
    ('ext2_option24', '1', '0', 'yes', '1'),
    ('ext2_option24', '2', '0', 'no', '1'),
    ('option15', '1', '0', 'executive', '1'),
    ('option15', '2', '0', 'specialists', '1'),
    ('option15', '3', '0', 'admin_staff', '1'),
    ('option15', '4', '0', 'drivers', '1'),
    ('option15', '5', '0', 'warehouse_Staff', '1'),
    ('option15', '6', '0', 'others', '1'),
    ('option16', '1', '0', 'letter_e', '1'),
    ('option16', '2', '0', 'letter_s', '1'),
    ('option16', '3', '0', 'letter_a', '1'),
    ('option16', '4', '0', 'letter_d', '1'),
    ('option16', '5', '0', 'letter_w', '1'),
    ('option16', '6', '0', 'letter_o', '1'),
    ('option17', '1', '0', 'indirect', '1'),
    ('option17', '2', '0', 'direct', '1'),
    ('option18', '1', '0', 'fix', '1'),
    ('option18', '2', '0', 'variable', '1'),
    ('option26', '1', '0', 'number_three', '1'),
    ('option26', '2', '0', 'number_four', '1'),
    ('option26', '3', '0', 'number_five', '1'),
    ('option26', '4', '0', 'number_six', '1'),
    ('option26', '5', '0', 'number_seven', '1'),
    ('option26', '6', '0', 'number_eight', '1'),
    ('option26', '7', '0', 'number_nine', '1'),
    ('option26', '8', '0', 'number_ten', '1'),
    ('option26', '9', '0', 'number_eleven', '1'),
    ('option26', '10', '0', 'number_twelve', '1'),
    ('option26', '11', '0', 'number_thirteen', '1'),
    ('option26', '12', '0', 'number_fourteen', '1'),
    ('option26', '13', '0', 'number_fifteen', '1'),
    ('option26', '14', '0', 'number_sixteen', '1'),
    ('option26', '15', '0', 'number_seventeen', '1'),
    ('option26', '16', '0', 'number_eighteen', '1'),
    ('option26', '17', '0', 'number_nineteen', '1'),
    ('option26', '18', '0', 'number_twenty', '1'),
    ('option27', '1', '0', 'code_fah', '1'),
    ('option27', '2', '0', 'code_faa', '1'),
    ('option27', '3', '0', 'code_faz', '1'),
    ('option27', '4', '0', 'code_lsb', '1'),
    ('option27', '5', '0', 'code_pra', '1'),
    ('option27', '6', '0', 'code_asa', '1'),
    ('option27', '7', '0', 'code_csb', '1'),
    ('option27', '8', '0', 'code_qaa', '1'),
    ('option27', '9', '0', 'code_slz', '1'),
    ('option27', '10', '0', 'code_sla', '1'),
    ('option27', '11', '0', 'code_itc', '1'),
    ('option27', '12', '0', 'code_hra', '1'),
    ('option27', '13', '0', 'code_hrz', '1'),
    ('option27', '14', '0', 'code_emc', '1'),
    ('option27', '15', '0', 'code_ptb', '1'),
    ('option27', '16', '0', 'code_prb', '1'),
    ('option27', '17', '0', 'code_lsz', '1'),
    ('option27', '18', '0', 'code_lsc', '1'),
    ('option28', '1', '0', 'feor_code_1312', '1'),
    ('option28', '2', '0', 'feor_code_1321', '1'),
    ('option28', '3', '0', 'feor_code_1322', '1'),
    ('option28', '4', '0', 'feor_code_1411', '1'),
    ('option28', '5', '0', 'feor_code_1412', '1'),
    ('option28', '6', '0', 'feor_code_1415', '1'),
    ('option28', '7', '0', 'feor_code_1419', '1'),
    ('option28', '8', '0', 'feor_code_2137', '1'),
    ('option28', '9', '0', 'feor_code_2159', '1'),
    ('option28', '10', '0', 'feor_code_2514', '1'),
    ('option28', '11', '0', 'feor_code_2910', '1'),
    ('option28', '12', '0', 'feor_code_3135', '1'),
    ('option28', '13', '0', 'feor_code_3139', '1'),
    ('option28', '14', '0', 'feor_code_3143', '1'),
    ('option28', '15', '0', 'feor_code_3161', '1'),
    ('option28', '16', '0', 'feor_code_3190', '1'),
    ('option28', '17', '0', 'feor_code_3221', '1'),
    ('option28', '18', '0', 'feor_code_3622', '1'),
    ('option28', '19', '0', 'feor_code_3910', '1'),
    ('option28', '20', '0', 'feor_code_4112', '1'),
    ('option28', '21', '0', 'feor_code_4121', '1'),
    ('option28', '22', '0', 'feor_code_4131', '1'),
    ('option28', '23', '0', 'feor_code_4132', '1'),
    ('option28', '24', '0', 'feor_code_4134', '1'),
    ('option28', '25', '0', 'feor_code_4190', '1'),
    ('option28', '26', '0', 'feor_code_7341', '1'),
    ('option28', '27', '0', 'feor_code_7342', '1'),
    ('option28', '28', '0', 'feor_code_8416', '1'),
    ('option28', '29', '0', 'feor_code_8417', '1'),
    ('option28', '30', '0', 'feor_code_8425', '1'),
    ('option28', '31', '0', 'feor_code_9223', '1'),
    ('option28', '32', '0', 'feor_code_9225', '1'),
    ('option29', '1', '0', 'head_of_industrial_activity', '1'),
    ('option29', '2', '0', 'trans_logis_ware_unit_leader', '1'),
    ('option29', '3', '0', 'info_telecomm_unit_leader', '1'),
    ('option29', '4', '0', 'acc_financial_unit_manager', '1'),
    ('option29', '5', '0', 'head_of_person_policy_unit', '1'),
    ('option29', '6', '0', 'sales_marketing_co', '1'),
    ('option29', '7', '0', 'head_of_unit_supporting', '1'),
    ('option29', '8', '0', 'quality_assurance_engineer', '1'),
    ('option29', '9', '0', 'other_database_analyst', '1'),
    ('option29', '10', '0', 'feor_controller', '1'),
    ('option29', '11', '0', 'other_qualified_clerk', '1'),
    ('option29', '12', '0', 'quality_assurance_techni', '1'),
    ('option29', '13', '0', 'other_technician_not_class', '1'),
    ('option29', '14', '0', 'computer_network_techni', '1'),
    ('option29', '15', '0', 'work_production_organizer', '1'),
    ('option29', '16', '0', 'other_technical_occupation', '1'),
    ('option29', '17', '0', 'office_profess_manager', '1'),
    ('option29', '18', '0', 'sales_manager', '1'),
    ('option29', '19', '0', 'other_clerk', '1'),
    ('option29', '20', '0', 'general_office_admin', '1'),
    ('option29', '21', '0', 'accountant_analyst', '1'),
    ('option29', '22', '0', 'inventory_mat_register', '1'),
    ('option29', '23', '0', 'shipping_forward_register', '1'),
    ('option29', '24', '0', 'human_policy_admin', '1'),
    ('option29', '25', '0', 'other_not_class_admin_occupat', '1'),
    ('option29', '26', '0', 'technic_electr_devices', '1'),
    ('option29', '27', '0', 'inf_telecom_furn_repairer', '1'),
    ('option29', '28', '0', 'car_driver', '1'),
    ('option29', '29', '0', 'lorry_driver', '1'),
    ('option29', '30', '0', 'forklift_driver', '1'),
    ('option29', '31', '0', 'loading_worker', '1'),
    ('option29', '32', '0', 'hand_packer', '1'),
    ('option19', '1', '0', 'quali_level_ML1', '1'),
    ('option19', '2', '0', 'quali_level_ML2', '1'),
    ('option19', '3', '0', 'quali_level_ML3', '1'),
    ('option19', '4', '0', 'quali_level_ML4', '1'),
    ('option19', '5', '0', 'quali_level_KL1', '1'),
    ('option19', '6', '0', 'quali_level_KL2', '1'),
    ('option19', '7', '0', 'quali_level_KL3', '1'),
    ('option19', '8', '0', 'quali_level_KL4', '1'),
    ('option19', '9', '0', 'quali_level_AL1', '1'),
    ('option19', '10', '0', 'quali_level_AL2', '1'),
    ('option19', '11', '0', 'quali_level_AL3', '1'),
    ('option19', '12', '0', 'quali_level_AL4', '1'),
    ('option19', '13', '0', 'quali_level_WL1', '1'),
    ('option19', '14', '0', 'quali_level_WL2', '1'),
    ('option19', '15', '0', 'quali_level_CPL1', '1'),
    ('option19', '16', '0', 'quali_level_CPL2', '1'),
    ('option19', '17', '0', 'quali_level_CPL3', '1'),
    ('option19', '18', '0', 'quali_level_PL1', '1'),
    ('option19', '19', '0', 'quali_level_PL2', '1'),
    ('option19', '20', '0', 'quali_level_PL3', '1'),
    ('option19', '21', '0', 'quali_level_BAL1', '1'),
    ('option19', '22', '0', 'quali_level_BAL2', '1'),
    ('option19', '23', '0', 'quali_level_BAL3', '1'),
    ('option19', '24', '0', 'quali_level_HAL1', '1'),
    ('option19', '25', '0', 'quali_level_HAL2', '1'),
    ('option19', '26', '0', 'quali_level_HAL3', '1'),
    ('option19', '27', '0', 'quali_level_ITL1', '1'),
    ('option19', '28', '0', 'quali_level_ITL2', '1'),
    ('option19', '29', '0', 'quali_level_ITL3', '1'),
    ('option19', '30', '0', 'quali_level_SLL1', '1'),
    ('option19', '31', '0', 'quali_level_SLL2', '1'),
    ('option19', '32', '0', 'quali_level_SLL3', '1'),
    ('option20', '1', '0', 'blue', '1'),
    ('option20', '2', '0', 'admin', '1'),
    ('option21', '1', '0', 'rt_driver', '1'),
    ('option21', '2', '0', 'picker', '1'),
    ('option21', '3', '0', 'checker', '1'),
    ('option21', '4', '0', 'wh_support', '1'),
    ('option21', '5', '0', 'co_pack', '1'),
    ('option21', '6', '0', 'pallet_forklift_driver', '1'),
    ('option21', '7', '0', 'basic_admin', '1'),
    ('option21', '8', '0', 'higher_admin', '1'),
    ('option21', '9', '0', 'it_system_sap_support', '1'),
    ('option21', '10', '0', 'shift_leader', '1'),
    ('option22', '1', '0', 'admin_overhead_holding_local', '1'),
    ('option22', '2', '0', 'company_head_office', '1'),
    ('option22', '3', '0', 'unused_technical_sources', '1'),
    ('option22', '4', '0', 'info_technology', '1'),
    ('option22', '5', '0', 'financial_costs', '1'),
    ('option22', '6', '0', 'non_calculable_costs', '1'),
    ('option22', '7', '0', 'logistics_head_office', '1'),
    ('option22', '8', '0', 'common_costs', '1'),
    ('option22', '9', '0', 'operation_admin', '1'),
    ('option22', '10', '0', 'racks', '1'),
    ('option22', '11', '0', 'pallet_warehouse_mgmt', '1'),
    ('option22', '12', '0', 'wlan', '1'),
    ('option22', '13', '0', 'intl_transport', '1'),
    ('option22', '14', '0', 'hh_office_intl_transport', '1'),
    ('option22', '15', '0', 'ocean_freight', '1'),
    ('option22', '16', '0', 'transport_mgmt_hhu', '1'),
    ('option22', '17', '0', 'oper_fulfillment_gyal', '1'),
    ('option22', '18', '0', 'wh_cw1_dry_gyal1', '1'),
    ('option22', '19', '0', 'wh_xd_dry_gyal1', '1'),
    ('option22', '20', '0', 'wh_cw2_dry_gyal1', '1'),
    ('option22', '21', '0', 'wh_cw_xd_frozen_gyal1', '1'),
    ('option22', '22', '0', 'wh_cw_xd_dry_gyal2_glp', '1'),
    ('option22', '23', '0', 'site_gyal1', '1'),
    ('option22', '24', '0', 'building_gyal1', '1'),
    ('option22', '25', '0', 'cooling_tech_gyal1', '1'),
    ('option22', '26', '0', 'freezing_tech_gyal1', '1'),
    ('option22', '27', '0', 'building_gyal2_glp', '1'),
    ('option22', '28', '0', 'dc_support_gyal1', '1'),
    ('option22', '29', '0', 'dc_shared_gyal1', '1'),
    ('option22', '30', '0', 'dc_support_gyal2_glp', '1'),
    ('option22', '31', '0', 'dc_shared_gyal2_glp', '1'),
    ('option22', '32', '0', 'depot_mgmt_hhu_gyal', '1'),
    ('option22', '33', '0', 'oper_dispatch_hhu_gyal', '1'),
    ('option22', '34', '0', 'direct_dispatch_hhu_gyal', '1'),
    ('option22', '35', '0', 'network_dispatch_hhu_gyal', '1'),
    ('option22', '36', '0', 'smalldis_dispatch_hhu_gyal', '1'),
    ('option22', '37', '0', 'total_dispatch_hhu_gyal', '1'),
    ('option22', '38', '0', 'external_transport_hhu_gyal', '1'),
    ('option22', '39', '0', 'fleet_mgmt_hhu_gyal', '1'),
    ('option22', '40', '0', 'rigid_01_03_hhu_gyal', '1'),
    ('option22', '41', '0', 'rigid_04_08_hhu_gyal', '1'),
    ('option22', '42', '0', 'rigid_09_14_hhu_gyal', '1'),
    ('option22', '43', '0', 'rigid_15_19_hhu_gyal', '1'),
    ('option22', '44', '0', 'rigid_20_24_hhu_gyal', '1'),
    ('option22', '45', '0', 'semitrailer_33_72_hhu_gyal', '1'),
    ('option22', '46', '0', 'tractor_hhu_gyal', '1'),
    ('option22', '47', '0', 'roadtrain_53_96_hhu_gyal', '1'),
    ('option22', '48', '0', 'drivers_hhu_gyal', '1'),
    ('option22', '49', '0', 'loaders_cross_dock_hhu_gyal', '1'),
    ('option22', '50', '0', 'rigid_01_03_hhu_gyal_int', '1'),
    ('option22', '51', '0', 'rigid_04_08_hhu_gyal_int', '1'),
    ('option22', '52', '0', 'rigid_09_14_hhu_gyal_int', '1'),
    ('option22', '53', '0', 'rigid_15_19_hhu_gyal_int', '1'),
    ('option22', '54', '0', 'rigid_20_24_hhu_gyal_int', '1'),
    ('option22', '55', '0', 'semitrailer_33_72_hhu_gyal_int', '1'),
    ('option22', '56', '0', 'tractor_hhu_gyal_int', '1'),
    ('option22', '57', '0', 'roadtrain_53_96_hhu_gyal_int', '1'),
    ('option22', '58', '0', 'dry_chill_xd_hhu_gyal_int', '1'),
    ('option22', '59', '0', 'frozen_xd_hhu_gyal_int', '1'),
    ('option22', '60', '0', 'operations_vas_gyal', '1'),
    ('option22', '61', '0', 'wh_cw_xd_chilled_vecses', '1'),
    ('option22', '62', '0', 'site_vecses_no_use', '1'),
    ('option22', '63', '0', 'dc_support_vecses', '1'),
    ('option22', '64', '0', 'dc_shared_vecses', '1'),
    ('option22', '65', '0', 'dry_chill_xd_hhu_vecses_int', '1'),
    ('option22', '66', '0', 'no_use_xd_hhu_ve_int', '1'),
    ('option22', '67', '0', 'fulfill_center_szig_2', '1'),
    ('option22', '68', '0', 'common_costs_szig_1', '1'),
    ('option22', '69', '0', 'common_costs_szig_2', '1'),
    ('option22', '70', '0', 'common_cost_oper_szig', '1'),
    ('option22', '71', '0', 'wh_cw_dry_szig_1', '1'),
    ('option22', '72', '0', 'building_szig_1', '1'),
    ('option22', '73', '0', 'building_szig_2', '1'),
    ('option22', '74', '0', 'dc_support_szig', '1'),
    ('option22', '75', '0', 'dc_shared_szig', '1'),
    ('option22', '76', '0', 'operations_vas_szig_1', '1'),
    ('option22', '77', '0', 'operations_vas_szig_2', '1'),
    ('option22', '78', '0', 'hh_global_solutions', '1'),
    ('option22', '79', '0', 'ocean_road_office_global_hu_gy', '1'),
    ('option22', '80', '0', 'road_office_global_hu_gy', '1'),
    ('option22', '81', '0', 'transport_admin_hhu', '1'),
    ('option23', '1', '0', 'cost_code_4110AO00', '1'),
    ('option23', '2', '0', 'cost_code_4110AO01', '1'),
    ('option23', '3', '0', 'cost_code_4110AO05', '1'),
    ('option23', '4', '0', 'cost_code_4110AO06', '1'),
    ('option23', '5', '0', 'cost_code_4110AO07', '1'),
    ('option23', '6', '0', 'cost_code_4110AO08', '1'),
    ('option23', '7', '0', 'cost_code_4110AO12', '1'),
    ('option23', '8', '0', 'cost_code_4110AO99', '1'),
    ('option23', '9', '0', 'cost_code_4110LA11', '1'),
    ('option23', '10', '0', 'cost_code_4110LE01', '1'),
    ('option23', '11', '0', 'cost_code_4110LW10', '1'),
    ('option23', '12', '0', 'cost_code_4110SO65', '1'),
    ('option23', '13', '0', 'cost_code_4110TA20', '1'),
    ('option23', '14', '0', 'cost_code_4110TA21', '1'),
    ('option23', '15', '0', 'cost_code_4110TA30', '1'),
    ('option23', '16', '0', 'cost_code_4110TA31', '1'),
    ('option23', '17', '0', 'cost_code_4110TA32', '1'),
    ('option23', '18', '0', 'cost_code_4112FO01', '1'),
    ('option23', '19', '0', 'cost_code_4112LW01', '1'),
    ('option23', '20', '0', 'cost_code_4112LW02', '1'),
    ('option23', '21', '0', 'cost_code_4112LW07', '1'),
    ('option23', '22', '0', 'cost_code_4112LW12', '1'),
    ('option23', '23', '0', 'cost_code_4112LW13', '1'),
    ('option23', '24', '0', 'cost_code_4112SO01', '1'),
    ('option23', '25', '0', 'cost_code_4112SO02', '1'),
    ('option23', '26', '0', 'cost_code_4112SO03', '1'),
    ('option23', '27', '0', 'cost_code_4112SO04', '1'),
    ('option23', '28', '0', 'cost_code_4112SO06', '1'),
    ('option23', '29', '0', 'cost_code_4112SO61', '1'),
    ('option23', '30', '0', 'cost_code_4112SO62', '1'),
    ('option23', '31', '0', 'cost_code_4112SO63', '1'),
    ('option23', '32', '0', 'cost_code_4112SO64', '1'),
    ('option23', '33', '0', 'cost_code_4112TA01', '1'),
    ('option23', '34', '0', 'cost_code_4112TD00', '1'),
    ('option23', '35', '0', 'cost_code_4112TD02', '1'),
    ('option23', '36', '0', 'cost_code_4112TD03', '1'),
    ('option23', '37', '0', 'cost_code_4112TD04', '1'),
    ('option23', '38', '0', 'cost_code_4112TD05', '1'),
    ('option23', '39', '0', 'cost_code_4112TE01', '1'),
    ('option23', '40', '0', 'cost_code_4112TF01', '1'),
    ('option23', '41', '0', 'cost_code_4112TF11', '1'),
    ('option23', '42', '0', 'cost_code_4112TF12', '1'),
    ('option23', '43', '0', 'cost_code_4112TF13', '1'),
    ('option23', '44', '0', 'cost_code_4112TF14', '1'),
    ('option23', '45', '0', 'cost_code_4112TF15', '1'),
    ('option23', '46', '0', 'cost_code_4112TF16', '1'),
    ('option23', '47', '0', 'cost_code_4112TF17', '1'),
    ('option23', '48', '0', 'cost_code_4112TF18', '1'),
    ('option23', '49', '0', 'cost_code_4112TF21', '1'),
    ('option23', '50', '0', 'cost_code_4112TF22', '1'),
    ('option23', '51', '0', 'cost_code_4112TF31', '1'),
    ('option23', '52', '0', 'cost_code_4112TF32', '1'),
    ('option23', '53', '0', 'cost_code_4112TF33', '1'),
    ('option23', '54', '0', 'cost_code_4112TF34', '1'),
    ('option23', '55', '0', 'cost_code_4112TF35', '1'),
    ('option23', '56', '0', 'cost_code_4112TF36', '1'),
    ('option23', '57', '0', 'cost_code_4112TF37', '1'),
    ('option23', '58', '0', 'cost_code_4112TF38', '1'),
    ('option23', '59', '0', 'cost_code_4112TX11', '1'),
    ('option23', '60', '0', 'cost_code_4112TX12', '1'),
    ('option23', '61', '0', 'cost_code_4112VC01', '1'),
    ('option23', '62', '0', 'cost_code_4113LW02', '1'),
    ('option23', '63', '0', 'cost_code_4113SO01', '1'),
    ('option23', '64', '0', 'cost_code_4113SO61', '1'),
    ('option23', '65', '0', 'cost_code_4113SO62', '1'),
    ('option23', '66', '0', 'cost_code_4113TX01', '1'),
    ('option23', '67', '0', 'cost_code_4113TX11', '1'),
    ('option23', '68', '0', 'cost_code_4117FO01', '1'),
    ('option23', '69', '0', 'cost_code_4117LO01', '1'),
    ('option23', '70', '0', 'cost_code_4117LO02', '1'),
    ('option23', '71', '0', 'cost_code_4117LO03', '1'),
    ('option23', '72', '0', 'cost_code_4117LW01', '1'),
    ('option23', '73', '0', 'cost_code_4117SO02', '1'),
    ('option23', '74', '0', 'cost_code_4117SO03', '1'),
    ('option23', '75', '0', 'cost_code_4117SO61', '1'),
    ('option23', '76', '0', 'cost_code_4117SO62', '1'),
    ('option23', '77', '0', 'cost_code_4117VC01', '1'),
    ('option23', '78', '0', 'cost_code_4117VC02', '1'),
    ('option23', '79', '0', 'cost_code_4210HA30', '1'),
    ('option23', '80', '0', 'cost_code_4212GO10', '1'),
    ('option23', '81', '0', 'cost_code_4212GR10', '1');

UPDATE `_sql_version` SET `revision`=17, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -17--2024-07-17-11:30---------------------------------------------------------------

UPDATE `option_config` SET `status` = '7', `modified_by` = 'bySanyi', `modified_on` = NOW() WHERE `option_id` = 'option5' AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'bySanyi', `modified_on` = NOW() WHERE `option_id` = 'ext2_option40' AND `status` = '2';
UPDATE `option_config` SET `status` = '2', `modified_by` = 'bySanyi', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option42', 'ext2_option43', 'ext2_option44', 'ext2_option45')  AND `status` = '7';

UPDATE `dictionary` SET `dict_value` = 'Vészhelyzeti kontakt megnevezése' WHERE `dict_id` = 'ext2_option42' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Emergency contact' WHERE `dict_id` = 'ext2_option42' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Vészhelyzeti kontakt elérhetősége' WHERE `dict_id` = 'ext2_option43' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Emergency contact availability' WHERE `dict_id` = 'ext2_option43' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Születési vezetéknév' WHERE `dict_id` = 'ext2_option44' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Birth family name' WHERE `dict_id` = 'ext2_option44' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Születési keresztnév' WHERE `dict_id` = 'ext2_option45' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Birth first name' WHERE `dict_id` = 'ext2_option45' AND `module` = 'ttwa-base' AND `lang` = 'en';

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
    ('ext2_option36', '1', '0', 'employer', '1'),
    ('ext2_option36', '2', '0', 'atr_employee', '1'),
    ('ext2_option37', '1', '0', 'employee_termination', '1'),
    ('ext2_option37', '2', '0', 'employer_termination', '1'),
    ('ext2_option37', '3', '0', 'common_understanding_employee', '1'),
    ('ext2_option37', '4', '0', 'joint_agreement_employer', '1'),
    ('ext2_option37', '5', '0', 'term_probation_period_employee', '1'),
    ('ext2_option37', '6', '0', 'term_probation_period_employer', '1'),
    ('ext2_option37', '7', '0', 'immediate_termination_employee', '1'),
    ('ext2_option37', '8', '0', 'immediate_termination_employer', '1'),
    ('ext2_option37', '9', '0', 'certain_period_time_expired', '1'),
    ('ext2_option37', '10', '0', 'retirement', '1'),
    ('ext2_option37', '11', '0', 'restrictions_affecting_work', '1'),
    ('ext2_option37', '12', '0', 'death', '1'),
    ('ext2_option37', '13', '0', 'other', '1'),
    ('ext2_option38', '1', '0', 'security_issues', '1'),
    ('ext2_option38', '2', '0', 'behavioral_problems', '1'),
    ('ext2_option38', '3', '0', 'quality_replacement', '1'),
    ('ext2_option38', '4', '0', 'reorganization', '1'),
    ('ext2_option38', '5', '0', 'group_downsizing', '1'),
    ('ext2_option38', '6', '0', 'loss_trust', '1'),
    ('ext2_option38', '7', '0', 'working_conditions_tools', '1'),
    ('ext2_option38', '8', '0', 'worktime', '1'),
    ('ext2_option38', '9', '0', 'job_content', '1'),
    ('ext2_option38', '10', '0', 'commuting_distance', '1'),
    ('ext2_option38', '11', '0', 'workplace_relations_team', '1'),
    ('ext2_option38', '12', '0', 'relationship_with_manager', '1'),
    ('ext2_option38', '13', '0', 'salary_benefits', '1'),
    ('ext2_option38', '14', '0', 'health_reasons', '1'),
    ('ext2_option38', '15', '0', 'family_issues', '1'),
    ('ext2_option38', '16', '0', 'moving', '1'),
    ('ext2_option38', '17', '0', 'education', '1'),
    ('ext2_option38', '18', '0', 'lack_advancement_opportunities', '1'),
    ('ext2_option38', '19', '0', 'lack_further_training', '1'),
    ('ext2_option38', '20', '0', 'do_not_comment', '1'),
    ('ext2_option38', '21', '0', 'other', '1'),
    ('ext2_option39', '1', '0', 'yes', '1'),
    ('ext2_option39', '2', '0', 'no', '1');

UPDATE `_sql_version` SET `revision`=18, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -18--2024-07-31-13:45---------------------------------------------------------------

UPDATE `option_config` SET `status` = '7', `modified_by` = 'SZOF-3908', `modified_on` = NOW() WHERE `option_id` IN ('ext2_option36', 'ext2_option37', 'ext2_option38', 'ext2_option39', 'ext2_option40', 'ext2_option41', 'ext6_option9', 'ext6_option10') AND `status` = '2';
UPDATE `option_config` SET `type` = 'dPicker', `modified_by` = 'SZOF-3908', `modified_on` = NOW() WHERE `option_id` IN ('ext6_option1', 'ext6_option3', 'ext6_option6', 'ext6_option8') AND `status` = '2';
UPDATE `option_config` SET `type` = 'ed', `modified_by` = 'SZOF-3908', `modified_on` = NOW() WHERE `option_id` IN ('ext6_option2', 'ext6_option4', 'ext6_option5', 'ext6_option7') AND `status` = '2';

UPDATE `dictionary` SET `dict_value` = 'Jogfolytonossági idő kezdete' WHERE `dict_id` = 'ext6_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Beginning of legal continuity period' WHERE `dict_id` = 'ext6_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogelőd cég neve' WHERE `dict_id` = 'ext6_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'The name of your company' WHERE `dict_id` = 'ext6_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Ellátás Kezdete' WHERE `dict_id` = 'ext6_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Start of Care' WHERE `dict_id` = 'ext6_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Nyugdíj törzsszám' WHERE `dict_id` = 'ext6_option4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Pension identification number' WHERE `dict_id` = 'ext6_option4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jövedelem típus (ellátás típusa)' WHERE `dict_id` = 'ext6_option5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Income type (type of benefit)' WHERE `dict_id` = 'ext6_option5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Érvényesség kezdete' WHERE `dict_id` = 'ext6_option6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Beginning of validity' WHERE `dict_id` = 'ext6_option6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Mérték' WHERE `dict_id` = 'ext6_option7' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Measure' WHERE `dict_id` = 'ext6_option7' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Érvényesség kezdete' WHERE `dict_id` = 'ext6_option8' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Beginning of validity' WHERE `dict_id` = 'ext6_option8' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=19, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -19--2024-08-12-09:30---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Fehérorosz' WHERE `dict_id` = 'nat_by' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Belga' WHERE `dict_id` = 'nat_b' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Hercegovina - bosnyák' WHERE `dict_id` = 'nat_ba' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Macedónia - Észak-macedón' WHERE `dict_id` = 'nat_mk' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Macedonia - North Macedonia' WHERE `dict_id` = 'nat_mk' AND `module` = 'ttwa-base' AND `lang` = 'en';

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
    ('option3', '1', '0', 'nat_al', '1'),
    ('option3', '2', '0', 'nat_and', '1'),
    ('option3', '3', '0', 'nat_at', '1'),
    ('option3', '4', '0', 'nat_by', '1'),
    ('option3', '5', '0', 'nat_b', '1'),
    ('option3', '6', '0', 'nat_ba', '1'),
    ('option3', '7', '0', 'nat_bg', '1'),
    ('option3', '8', '0', 'nat_hr', '1'),
    ('option3', '9', '0', 'nat_cy', '1'),
    ('option3', '10', '0', 'nat_cz', '1'),
    ('option3', '11', '0', 'nat_dk', '1'),
    ('option3', '12', '0', 'nat_ee', '1'),
    ('option3', '13', '0', 'nat_fin', '1'),
    ('option3', '14', '0', 'nat_fr', '1'),
    ('option3', '15', '0', 'nat_deu', '1'),
    ('option3', '16', '0', 'nat_gr', '1'),
    ('option3', '17', '0', 'nat_hun', '1'),
    ('option3', '18', '0', 'nat_is', '1'),
    ('option3', '19', '0', 'nat_ie', '1'),
    ('option3', '20', '0', 'nat_it', '1'),
    ('option3', '21', '0', 'nat_lv', '1'),
    ('option3', '22', '0', 'nat_li', '1'),
    ('option3', '23', '0', 'nat_lt', '1'),
    ('option3', '24', '0', 'nat_lu', '1'),
    ('option3', '25', '0', 'nat_mt', '1'),
    ('option3', '26', '0', 'nat_mol', '1'),
    ('option3', '27', '0', 'nat_mc', '1'),
    ('option3', '28', '0', 'nat_me', '1'),
    ('option3', '29', '0', 'nat_mk', '1'),
    ('option3', '30', '0', 'nat_no', '1'),
    ('option3', '31', '0', 'nat_pl', '1'),
    ('option3', '32', '0', 'nat_pt', '1'),
    ('option3', '33', '0', 'nat_ro', '1'),
    ('option3', '34', '0', 'nat_ru', '1'),
    ('option3', '35', '0', 'nat_sm', '1'),
    ('option3', '36', '0', 'nat_srb', '1'),
    ('option3', '37', '0', 'nat_sk', '1'),
    ('option3', '38', '0', 'nat_si', '1'),
    ('option3', '39', '0', 'nat_es', '1'),
    ('option3', '40', '0', 'nat_se', '1'),
    ('option3', '41', '0', 'nat_ch', '1'),
    ('option3', '42', '0', 'nat_ua', '1'),
    ('option3', '43', '0', 'nat_gbr', '1'),
    ('option3', '44', '0', 'nat_vat', '1'),
    ('option4', '1', '0', 'nat_al', '1'),
    ('option4', '2', '0', 'nat_and', '1'),
    ('option4', '3', '0', 'nat_at', '1'),
    ('option4', '4', '0', 'nat_by', '1'),
    ('option4', '5', '0', 'nat_b', '1'),
    ('option4', '6', '0', 'nat_ba', '1'),
    ('option4', '7', '0', 'nat_bg', '1'),
    ('option4', '8', '0', 'nat_hr', '1'),
    ('option4', '9', '0', 'nat_cy', '1'),
    ('option4', '10', '0', 'nat_cz', '1'),
    ('option4', '11', '0', 'nat_dk', '1'),
    ('option4', '12', '0', 'nat_ee', '1'),
    ('option4', '13', '0', 'nat_fin', '1'),
    ('option4', '14', '0', 'nat_fr', '1'),
    ('option4', '15', '0', 'nat_deu', '1'),
    ('option4', '16', '0', 'nat_gr', '1'),
    ('option4', '17', '0', 'nat_hun', '1'),
    ('option4', '18', '0', 'nat_is', '1'),
    ('option4', '19', '0', 'nat_ie', '1'),
    ('option4', '20', '0', 'nat_it', '1'),
    ('option4', '21', '0', 'nat_lv', '1'),
    ('option4', '22', '0', 'nat_li', '1'),
    ('option4', '23', '0', 'nat_lt', '1'),
    ('option4', '24', '0', 'nat_lu', '1'),
    ('option4', '25', '0', 'nat_mt', '1'),
    ('option4', '26', '0', 'nat_mol', '1'),
    ('option4', '27', '0', 'nat_mc', '1'),
    ('option4', '28', '0', 'nat_me', '1'),
    ('option4', '29', '0', 'nat_mk', '1'),
    ('option4', '30', '0', 'nat_no', '1'),
    ('option4', '31', '0', 'nat_pl', '1'),
    ('option4', '32', '0', 'nat_pt', '1'),
    ('option4', '33', '0', 'nat_ro', '1'),
    ('option4', '34', '0', 'nat_ru', '1'),
    ('option4', '35', '0', 'nat_sm', '1'),
    ('option4', '36', '0', 'nat_srb', '1'),
    ('option4', '37', '0', 'nat_sk', '1'),
    ('option4', '38', '0', 'nat_si', '1'),
    ('option4', '39', '0', 'nat_es', '1'),
    ('option4', '40', '0', 'nat_se', '1'),
    ('option4', '41', '0', 'nat_ch', '1'),
    ('option4', '42', '0', 'nat_ua', '1'),
    ('option4', '43', '0', 'nat_gbr', '1'),
    ('option4', '44', '0', 'nat_vat', '1'),
    ('option2', '1', '0', 'country_al', '1'),
    ('option2', '2', '0', 'country_and', '1'),
    ('option2', '3', '0', 'country_at', '1'),
    ('option2', '4', '0', 'country_by', '1'),
    ('option2', '5', '0', 'country_b', '1'),
    ('option2', '6', '0', 'country_ba', '1'),
    ('option2', '7', '0', 'country_bg', '1'),
    ('option2', '8', '0', 'country_hr', '1'),
    ('option2', '9', '0', 'country_cy', '1'),
    ('option2', '10', '0', 'country_cz', '1'),
    ('option2', '11', '0', 'country_dk', '1'),
    ('option2', '12', '0', 'country_ee', '1'),
    ('option2', '13', '0', 'country_fin', '1'),
    ('option2', '14', '0', 'country_fr', '1'),
    ('option2', '15', '0', 'country_deu', '1'),
    ('option2', '16', '0', 'country_gr', '1'),
    ('option2', '17', '0', 'country_hun', '1'),
    ('option2', '18', '0', 'country_is', '1'),
    ('option2', '19', '0', 'country_ie', '1'),
    ('option2', '20', '0', 'country_it', '1'),
    ('option2', '21', '0', 'country_lv', '1'),
    ('option2', '22', '0', 'country_li', '1'),
    ('option2', '23', '0', 'country_lt', '1'),
    ('option2', '24', '0', 'country_lu', '1'),
    ('option2', '25', '0', 'country_mt', '1'),
    ('option2', '26', '0', 'country_mol', '1'),
    ('option2', '27', '0', 'country_mc', '1'),
    ('option2', '28', '0', 'country_me', '1'),
    ('option2', '29', '0', 'country_mk', '1'),
    ('option2', '30', '0', 'country_no', '1'),
    ('option2', '31', '0', 'country_pl', '1'),
    ('option2', '32', '0', 'country_pt', '1'),
    ('option2', '33', '0', 'country_ro', '1'),
    ('option2', '34', '0', 'country_ru', '1'),
    ('option2', '35', '0', 'country_sm', '1'),
    ('option2', '36', '0', 'country_srb', '1'),
    ('option2', '37', '0', 'country_sk', '1'),
    ('option2', '38', '0', 'country_si', '1'),
    ('option2', '39', '0', 'country_es', '1'),
    ('option2', '40', '0', 'country_se', '1'),
    ('option2', '41', '0', 'country_ch', '1'),
    ('option2', '42', '0', 'country_ua', '1'),
    ('option2', '43', '0', 'country_gbr', '1'),
    ('option2', '44', '0', 'country_vat', '1'),
    ('ext2_option22', '1', '0', 'country_al', '1'),
    ('ext2_option22', '2', '0', 'country_and', '1'),
    ('ext2_option22', '3', '0', 'country_at', '1'),
    ('ext2_option22', '4', '0', 'country_by', '1'),
    ('ext2_option22', '5', '0', 'country_b', '1'),
    ('ext2_option22', '6', '0', 'country_ba', '1'),
    ('ext2_option22', '7', '0', 'country_bg', '1'),
    ('ext2_option22', '8', '0', 'country_hr', '1'),
    ('ext2_option22', '9', '0', 'country_cy', '1'),
    ('ext2_option22', '10', '0', 'country_cz', '1'),
    ('ext2_option22', '11', '0', 'country_dk', '1'),
    ('ext2_option22', '12', '0', 'country_ee', '1'),
    ('ext2_option22', '13', '0', 'country_fin', '1'),
    ('ext2_option22', '14', '0', 'country_fr', '1'),
    ('ext2_option22', '15', '0', 'country_deu', '1'),
    ('ext2_option22', '16', '0', 'country_gr', '1'),
    ('ext2_option22', '17', '0', 'country_hun', '1'),
    ('ext2_option22', '18', '0', 'country_is', '1'),
    ('ext2_option22', '19', '0', 'country_ie', '1'),
    ('ext2_option22', '20', '0', 'country_it', '1'),
    ('ext2_option22', '21', '0', 'country_lv', '1'),
    ('ext2_option22', '22', '0', 'country_li', '1'),
    ('ext2_option22', '23', '0', 'country_lt', '1'),
    ('ext2_option22', '24', '0', 'country_lu', '1'),
    ('ext2_option22', '25', '0', 'country_mt', '1'),
    ('ext2_option22', '26', '0', 'country_mol', '1'),
    ('ext2_option22', '27', '0', 'country_mc', '1'),
    ('ext2_option22', '28', '0', 'country_me', '1'),
    ('ext2_option22', '29', '0', 'country_mk', '1'),
    ('ext2_option22', '30', '0', 'country_no', '1'),
    ('ext2_option22', '31', '0', 'country_pl', '1'),
    ('ext2_option22', '32', '0', 'country_pt', '1'),
    ('ext2_option22', '33', '0', 'country_ro', '1'),
    ('ext2_option22', '34', '0', 'country_ru', '1'),
    ('ext2_option22', '35', '0', 'country_sm', '1'),
    ('ext2_option22', '36', '0', 'country_srb', '1'),
    ('ext2_option22', '37', '0', 'country_sk', '1'),
    ('ext2_option22', '38', '0', 'country_si', '1'),
    ('ext2_option22', '39', '0', 'country_es', '1'),
    ('ext2_option22', '40', '0', 'country_se', '1'),
    ('ext2_option22', '41', '0', 'country_ch', '1'),
    ('ext2_option22', '42', '0', 'country_ua', '1'),
    ('ext2_option22', '43', '0', 'country_gbr', '1'),
    ('ext2_option22', '44', '0', 'country_vat', '1');

UPDATE `_sql_version` SET `revision`=20, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -20--2024-08-12-13:30---------------------------------------------------------------

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
    ('ext2_option2', '1', '0', 'country_al', '1'),
    ('ext2_option2', '2', '0', 'country_and', '1'),
    ('ext2_option2', '3', '0', 'country_at', '1'),
    ('ext2_option2', '4', '0', 'country_by', '1'),
    ('ext2_option2', '5', '0', 'country_b', '1'),
    ('ext2_option2', '6', '0', 'country_ba', '1'),
    ('ext2_option2', '7', '0', 'country_bg', '1'),
    ('ext2_option2', '8', '0', 'country_hr', '1'),
    ('ext2_option2', '9', '0', 'country_cy', '1'),
    ('ext2_option2', '10', '0', 'country_cz', '1'),
    ('ext2_option2', '11', '0', 'country_dk', '1'),
    ('ext2_option2', '12', '0', 'country_ee', '1'),
    ('ext2_option2', '13', '0', 'country_fin', '1'),
    ('ext2_option2', '14', '0', 'country_fr', '1'),
    ('ext2_option2', '15', '0', 'country_deu', '1'),
    ('ext2_option2', '16', '0', 'country_gr', '1'),
    ('ext2_option2', '17', '0', 'country_hun', '1'),
    ('ext2_option2', '18', '0', 'country_is', '1'),
    ('ext2_option2', '19', '0', 'country_ie', '1'),
    ('ext2_option2', '20', '0', 'country_it', '1'),
    ('ext2_option2', '21', '0', 'country_lv', '1'),
    ('ext2_option2', '22', '0', 'country_li', '1'),
    ('ext2_option2', '23', '0', 'country_lt', '1'),
    ('ext2_option2', '24', '0', 'country_lu', '1'),
    ('ext2_option2', '25', '0', 'country_mt', '1'),
    ('ext2_option2', '26', '0', 'country_mol', '1'),
    ('ext2_option2', '27', '0', 'country_mc', '1'),
    ('ext2_option2', '28', '0', 'country_me', '1'),
    ('ext2_option2', '29', '0', 'country_mk', '1'),
    ('ext2_option2', '30', '0', 'country_no', '1'),
    ('ext2_option2', '31', '0', 'country_pl', '1'),
    ('ext2_option2', '32', '0', 'country_pt', '1'),
    ('ext2_option2', '33', '0', 'country_ro', '1'),
    ('ext2_option2', '34', '0', 'country_ru', '1'),
    ('ext2_option2', '35', '0', 'country_sm', '1'),
    ('ext2_option2', '36', '0', 'country_srb', '1'),
    ('ext2_option2', '37', '0', 'country_sk', '1'),
    ('ext2_option2', '38', '0', 'country_si', '1'),
    ('ext2_option2', '39', '0', 'country_es', '1'),
    ('ext2_option2', '40', '0', 'country_se', '1'),
    ('ext2_option2', '41', '0', 'country_ch', '1'),
    ('ext2_option2', '42', '0', 'country_ua', '1'),
    ('ext2_option2', '43', '0', 'country_gbr', '1'),
    ('ext2_option2', '44', '0', 'country_vat', '1'),
    ('ext2_option12', '1', '0', 'country_al', '1'),
    ('ext2_option12', '2', '0', 'country_and', '1'),
    ('ext2_option12', '3', '0', 'country_at', '1'),
    ('ext2_option12', '4', '0', 'country_by', '1'),
    ('ext2_option12', '5', '0', 'country_b', '1'),
    ('ext2_option12', '6', '0', 'country_ba', '1'),
    ('ext2_option12', '7', '0', 'country_bg', '1'),
    ('ext2_option12', '8', '0', 'country_hr', '1'),
    ('ext2_option12', '9', '0', 'country_cy', '1'),
    ('ext2_option12', '10', '0', 'country_cz', '1'),
    ('ext2_option12', '11', '0', 'country_dk', '1'),
    ('ext2_option12', '12', '0', 'country_ee', '1'),
    ('ext2_option12', '13', '0', 'country_fin', '1'),
    ('ext2_option12', '14', '0', 'country_fr', '1'),
    ('ext2_option12', '15', '0', 'country_deu', '1'),
    ('ext2_option12', '16', '0', 'country_gr', '1'),
    ('ext2_option12', '17', '0', 'country_hun', '1'),
    ('ext2_option12', '18', '0', 'country_is', '1'),
    ('ext2_option12', '19', '0', 'country_ie', '1'),
    ('ext2_option12', '20', '0', 'country_it', '1'),
    ('ext2_option12', '21', '0', 'country_lv', '1'),
    ('ext2_option12', '22', '0', 'country_li', '1'),
    ('ext2_option12', '23', '0', 'country_lt', '1'),
    ('ext2_option12', '24', '0', 'country_lu', '1'),
    ('ext2_option12', '25', '0', 'country_mt', '1'),
    ('ext2_option12', '26', '0', 'country_mol', '1'),
    ('ext2_option12', '27', '0', 'country_mc', '1'),
    ('ext2_option12', '28', '0', 'country_me', '1'),
    ('ext2_option12', '29', '0', 'country_mk', '1'),
    ('ext2_option12', '30', '0', 'country_no', '1'),
    ('ext2_option12', '31', '0', 'country_pl', '1'),
    ('ext2_option12', '32', '0', 'country_pt', '1'),
    ('ext2_option12', '33', '0', 'country_ro', '1'),
    ('ext2_option12', '34', '0', 'country_ru', '1'),
    ('ext2_option12', '35', '0', 'country_sm', '1'),
    ('ext2_option12', '36', '0', 'country_srb', '1'),
    ('ext2_option12', '37', '0', 'country_sk', '1'),
    ('ext2_option12', '38', '0', 'country_si', '1'),
    ('ext2_option12', '39', '0', 'country_es', '1'),
    ('ext2_option12', '40', '0', 'country_se', '1'),
    ('ext2_option12', '41', '0', 'country_ch', '1'),
    ('ext2_option12', '42', '0', 'country_ua', '1'),
    ('ext2_option12', '43', '0', 'country_gbr', '1'),
    ('ext2_option12', '44', '0', 'country_vat', '1');

UPDATE `_sql_version` SET `revision`=21, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -21--2024-08-13-14:30---------------------------------------------------------------

UPDATE `option_config` SET `type` = 'ed', `modified_by` = 'SZOF-4001', `modified_on` = NOW() WHERE `option_id` IN ('option22', 'option23') AND `status` = '2';

UPDATE `_sql_version` SET `revision`=22, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -22--2024-09-02-11:00---------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `hr_booklet_category_details` (
   `row_id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
   `hr_booklet_category_code_id` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Besorolási kategória kód id',
   `hr_booklet_category_name_id` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Besorolási kategória megnevezés id',
   `direct_indirect_category_id` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Fizikai, szellemi besorolás id',
   `accounting_category_id` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Könyveléshez besorolás id',
   `status` TINYINT NOT NULL COMMENT 'status',
   `created_by` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'created_by',
   `created_on` DATETIME NOT NULL COMMENT 'created_on',
   `modified_by` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'modified_by',
   `modified_on` DATETIME COMMENT 'modified_on'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Besorolási kategóriához kapcsolt mezők';

CREATE TABLE IF NOT EXISTS `qualification_level_details` (
    `row_id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    `qualification_level_code_id` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Képzettségi szint kód id',
    `qualification_level_name_id` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Képzettségi szint megnevezés id',
    `qualification_level_type_id` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Képzettségi szint típus id',
    `status` TINYINT NOT NULL COMMENT 'status',
    `created_by` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'created_by',
    `created_on` DATETIME NOT NULL COMMENT 'created_on',
    `modified_by` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'modified_by',
    `modified_on` DATETIME COMMENT 'modified_on'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Képzettségi szinthez kapcsolt mezők';

INSERT INTO `hr_booklet_category_details` (`hr_booklet_category_code_id`, `hr_booklet_category_name_id`, `direct_indirect_category_id`, `accounting_category_id`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`) VALUES
    ('1','1','1','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('2','2','1','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('3','3','1','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('4','4','2','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('5','5','2','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('6','6','2','1','2','SZOF-3958',NOW(),NULL,NULL);

INSERT INTO `qualification_level_details` (`qualification_level_code_id`, `qualification_level_name_id`, `qualification_level_type_id`, `status`, `created_by`, `created_on`, `modified_by`, `modified_on`) VALUES
    ('1','1','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('2','1','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('3','1','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('4','1','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('5','2','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('6','2','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('7','2','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('8','2','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('9','3','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('10','3','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('11','3','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('12','3','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('13','4','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('14','4','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('15','5','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('16','5','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('17','5','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('18','6','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('19','6','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('20','6','1','2','SZOF-3958',NOW(),NULL,NULL),
    ('21','7','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('22','7','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('23','7','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('24','8','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('25','8','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('26','8','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('27','9','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('28','9','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('29','9','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('30','10','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('31','10','2','2','SZOF-3958',NOW(),NULL,NULL),
    ('32','10','2','2','SZOF-3958',NOW(),NULL,NULL);

UPDATE `app_settings` SET `setting_value` = '1', `note` = CONCAT(`note`, 'prev:0, SZOF-3958') WHERE `setting_id` = 'automaticLoadExtFromFeorCode';
UPDATE `app_settings` SET `setting_value` = '1', `note` = CONCAT(`note`, 'prev:0, SZOF-3958') WHERE `setting_id` = 'automaticLoadExtFromHrBookletCategoryCode';
UPDATE `app_settings` SET `setting_value` = '1', `note` = CONCAT(`note`, 'prev:0, SZOF-3958') WHERE `setting_id` = 'automaticLoadExtFromQualificationLevelCode';

UPDATE `_sql_version` SET `revision`=23, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -23--2024-09-17-11:00---------------------------------------------------------------

DELETE FROM `app_lookup` WHERE `lookup_id` IN ('option3', 'option4');
UPDATE `dictionary` SET `dict_value` = 'Makedóniai' WHERE `dict_id` = 'nat_mk' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Macedonian' WHERE `dict_id` = 'nat_mk' AND `lang` = 'en' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Makedónia' WHERE `dict_id` = 'country_mk' AND `lang` = 'hu' AND `module` = 'ttwa-base';
UPDATE `dictionary` SET `dict_value` = 'Macedonia' WHERE `dict_id` = 'country_mk' AND `lang` = 'en' AND `module` = 'ttwa-base';

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
    ('option2', '45', '0', 'country_hu', '1'),
    ('ext2_option2', '45', '0', 'country_hu', '1'),
    ('ext2_option12', '45', '0', 'country_hu', '1'),
    ('ext2_option22', '45', '0', 'country_hu', '1'),
    ('option3', '1', '0', 'nat_hun', '1'),
    ('option3', '2', '0', 'nat_ua', '1'),
    ('option3', '3', '0', 'nat_afg', '1'),
    ('option3', '4', '0', 'nat_al', '1'),
    ('option3', '5', '0', 'nat_alg', '1'),
    ('option3', '6', '0', 'nat_us', '1'),
    ('option3', '7', '0', 'nat_and', '1'),
    ('option3', '8', '0', 'nat_ago', '1'),
    ('option3', '9', '0', 'nat_atg', '1'),
    ('option3', '10', '0', 'nat_arg', '1'),
    ('option3', '11', '0', 'nat_abw', '1'),
    ('option3', '12', '0', 'nat_aus', '1'),
    ('option3', '13', '0', 'nat_aze', '1'),
    ('option3', '14', '0', 'nat_bhs', '1'),
    ('option3', '15', '0', 'nat_bh', '1'),
    ('option3', '16', '0', 'nat_bgd', '1'),
    ('option3', '17', '0', 'nat_brb', '1'),
    ('option3', '18', '0', 'nat_b', '1'),
    ('option3', '19', '0', 'nat_bz', '1'),
    ('option3', '20', '0', 'nat_by', '1'),
    ('option3', '21', '0', 'nat_bj', '1'),
    ('option3', '22', '0', 'nat_ber', '1'),
    ('option3', '23', '0', 'nat_bt', '1'),
    ('option3', '24', '0', 'nat_bg', '1'),
    ('option3', '25', '0', 'nat_bo', '1'),
    ('option3', '26', '0', 'nat_ba', '1'),
    ('option3', '27', '0', 'nat_bw', '1'),
    ('option3', '28', '0', 'nat_bra', '1'),
    ('option3', '29', '0', 'nat_gbr', '1'),
    ('option3', '30', '0', 'nat_bvi', '1'),
    ('option3', '31', '0', 'nat_bf', '1'),
    ('option3', '32', '0', 'nat_bur', '1'),
    ('option3', '33', '0', 'nat_bi', '1'),
    ('option3', '34', '0', 'nat_cv', '1'),
    ('option3', '35', '0', 'nat_cl', '1'),
    ('option3', '36', '0', 'nat_cy', '1'),
    ('option3', '37', '0', 'nat_cr', '1'),
    ('option3', '38', '0', 'nat_td', '1'),
    ('option3', '39', '0', 'nat_cz', '1'),
    ('option3', '40', '0', 'nat_dk', '1'),
    ('option3', '41', '0', 'nat_za', '1'),
    ('option3', '42', '0', 'nat_kr', '1'),
    ('option3', '43', '0', 'nat_do', '1'),
    ('option3', '44', '0', 'nat_dubai', '1'),
    ('option3', '45', '0', 'nat_dj', '1'),
    ('option3', '46', '0', 'nat_ec', '1'),
    ('option3', '47', '0', 'nat_gq', '1'),
    ('option3', '48', '0', 'nat_ae', '1'),
    ('option3', '49', '0', 'nat_eg', '1'),
    ('option3', '50', '0', 'nat_ci', '1'),
    ('option3', '51', '0', 'nat_er', '1'),
    ('option3', '52', '0', 'nat_nk', '1'),
    ('option3', '53', '0', 'nat_ee', '1'),
    ('option3', '54', '0', 'nat_et', '1'),
    ('option3', '55', '0', 'nat_gbfi', '1'),
    ('option3', '56', '0', 'nat_fro', '1'),
    ('option3', '57', '0', 'nat_fj', '1'),
    ('option3', '58', '0', 'nat_fin', '1'),
    ('option3', '59', '0', 'nat_fr', '1'),
    ('option3', '60', '0', 'nat_frgy', '1'),
    ('option3', '61', '0', 'nat_pf', '1'),
    ('option3', '62', '0', 'nat_ph', '1'),
    ('option3', '63', '0', 'nat_ga', '1'),
    ('option3', '64', '0', 'nat_gm', '1'),
    ('option3', '65', '0', 'nat_gh', '1'),
    ('option3', '66', '0', 'nat_gbz', '1'),
    ('option3', '67', '0', 'nat_gr', '1'),
    ('option3', '68', '0', 'nat_gd', '1'),
    ('option3', '69', '0', 'nat_gl', '1'),
    ('option3', '70', '0', 'nat_ge', '1'),
    ('option3', '71', '0', 'nat_gt', '1'),
    ('option3', '72', '0', 'nat_gw', '1'),
    ('option3', '73', '0', 'nat_gn', '1'),
    ('option3', '74', '0', 'nat_gy', '1'),
    ('option3', '75', '0', 'nat_ht', '1'),
    ('option3', '76', '0', 'nat_heart', '1'),
    ('option3', '77', '0', 'nat_nl', '1'),
    ('option3', '78', '0', 'nat_nlant', '1'),
    ('option3', '79', '0', 'nat_hn', '1'),
    ('option3', '80', '0', 'nat_hk', '1'),
    ('option3', '81', '0', 'nat_hr', '1'),
    ('option3', '82', '0', 'nat_in', '1'),
    ('option3', '83', '0', 'nat_id', '1'),
    ('option3', '84', '0', 'nat_ie', '1'),
    ('option3', '85', '0', 'nat_iq', '1'),
    ('option3', '86', '0', 'nat_ir', '1'),
    ('option3', '87', '0', 'nat_is', '1'),
    ('option3', '88', '0', 'nat_il', '1'),
    ('option3', '89', '0', 'nat_jm', '1'),
    ('option3', '90', '0', 'nat_jp', '1'),
    ('option3', '91', '0', 'nat_ye', '1'),
    ('option3', '92', '0', 'nat_jor', '1'),
    ('option3', '93', '0', 'nat_ky', '1'),
    ('option3', '94', '0', 'nat_kh', '1'),
    ('option3', '95', '0', 'nat_cm', '1'),
    ('option3', '96', '0', 'nat_can', '1'),
    ('option3', '97', '0', 'nat_auch', '1'),
    ('option3', '98', '0', 'nat_qa', '1'),
    ('option3', '99', '0', 'nat_kz', '1'),
    ('option3', '100', '0', 'nat_ke', '1'),
    ('option3', '101', '0', 'nat_chn', '1'),
    ('option3', '102', '0', 'nat_kg', '1'),
    ('option3', '103', '0', 'nat_aucc', '1'),
    ('option3', '104', '0', 'nat_co', '1'),
    ('option3', '105', '0', 'nat_cog', '1'),
    ('option3', '106', '0', 'nat_xk', '1'),
    ('option3', '107', '0', 'nat_cf', '1'),
    ('option3', '108', '0', 'nat_cu', '1'),
    ('option3', '109', '0', 'nat_kw', '1'),
    ('option3', '110', '0', 'nat_la', '1'),
    ('option3', '111', '0', 'nat_pl', '1'),
    ('option3', '112', '0', 'nat_ls', '1'),
    ('option3', '113', '0', 'nat_lv', '1'),
    ('option3', '114', '0', 'nat_lb', '1'),
    ('option3', '115', '0', 'nat_lr', '1'),
    ('option3', '116', '0', 'nat_ly', '1'),
    ('option3', '117', '0', 'nat_li', '1'),
    ('option3', '118', '0', 'nat_lt', '1'),
    ('option3', '119', '0', 'nat_lu', '1'),
    ('option3', '120', '0', 'nat_mg', '1'),
    ('option3', '121', '0', 'nat_mo', '1'),
    ('option3', '122', '0', 'nat_mk', '1'),
    ('option3', '123', '0', 'nat_mw', '1'),
    ('option3', '124', '0', 'nat_mys', '1'),
    ('option3', '125', '0', 'nat_mv', '1'),
    ('option3', '126', '0', 'nat_ml', '1'),
    ('option3', '127', '0', 'nat_mt', '1'),
    ('option3', '128', '0', 'nat_ma', '1'),
    ('option3', '129', '0', 'nat_mr', '1'),
    ('option3', '130', '0', 'nat_mu', '1'),
    ('option3', '131', '0', 'nat_mx', '1'),
    ('option3', '132', '0', 'nat_mol', '1'),
    ('option3', '133', '0', 'nat_mc', '1'),
    ('option3', '134', '0', 'nat_mn', '1'),
    ('option3', '135', '0', 'nat_me', '1'),
    ('option3', '136', '0', 'nat_mz', '1'),
    ('option3', '137', '0', 'nat_mm', '1'),
    ('option3', '138', '0', 'nat_na', '1'),
    ('option3', '139', '0', 'nat_deu', '1'),
    ('option3', '140', '0', 'nat_np', '1'),
    ('option3', '141', '0', 'nat_ni', '1'),
    ('option3', '142', '0', 'nat_ng', '1'),
    ('option3', '143', '0', 'nat_nf', '1'),
    ('option3', '144', '0', 'nat_no', '1'),
    ('option3', '145', '0', 'nat_wf', '1'),
    ('option3', '146', '0', 'nat_ws', '1'),
    ('option3', '147', '0', 'nat_it', '1'),
    ('option3', '148', '0', 'nat_om', '1'),
    ('option3', '149', '0', 'nat_ru', '1'),
    ('option3', '150', '0', 'nat_at', '1'),
    ('option3', '151', '0', 'nat_am', '1'),
    ('option3', '152', '0', 'nat_pk', '1'),
    ('option3', '153', '0', 'nat_pa', '1'),
    ('option3', '154', '0', 'nat_pg', '1'),
    ('option3', '155', '0', 'nat_py', '1'),
    ('option3', '156', '0', 'nat_pe', '1'),
    ('option3', '157', '0', 'nat_pt', '1'),
    ('option3', '158', '0', 'nat_pr', '1'),
    ('option3', '159', '0', 'nat_ro', '1'),
    ('option3', '160', '0', 'nat_rw', '1'),
    ('option3', '161', '0', 'nat_lc', '1'),
    ('option3', '162', '0', 'nat_svin', '1'),
    ('option3', '163', '0', 'nat_sb', '1'),
    ('option3', '164', '0', 'nat_sv', '1'),
    ('option3', '165', '0', 'nat_sm', '1'),
    ('option3', '166', '0', 'nat_st', '1'),
    ('option3', '167', '0', 'nat_sc', '1'),
    ('option3', '168', '0', 'nat_sl', '1'),
    ('option3', '169', '0', 'nat_es', '1'),
    ('option3', '170', '0', 'nat_lk', '1'),
    ('option3', '171', '0', 'nat_sr', '1'),
    ('option3', '172', '0', 'nat_ch', '1'),
    ('option3', '173', '0', 'nat_se', '1'),
    ('option3', '174', '0', 'nat_sa', '1'),
    ('option3', '175', '0', 'nat_sn', '1'),
    ('option3', '176', '0', 'nat_srb', '1'),
    ('option3', '177', '0', 'nat_sg', '1'),
    ('option3', '178', '0', 'nat_sy', '1'),
    ('option3', '179', '0', 'nat_sk', '1'),
    ('option3', '180', '0', 'nat_si', '1'),
    ('option3', '181', '0', 'nat_so', '1'),
    ('option3', '182', '0', 'nat_sd', '1'),
    ('option3', '183', '0', 'nat_tj', '1'),
    ('option3', '184', '0', 'nat_tw', '1'),
    ('option3', '185', '0', 'nat_tz', '1'),
    ('option3', '186', '0', 'nat_th', '1'),
    ('option3', '187', '0', 'nat_tg', '1'),
    ('option3', '188', '0', 'nat_to', '1'),
    ('option3', '189', '0', 'nat_tr', '1'),
    ('option3', '190', '0', 'nat_tt', '1'),
    ('option3', '191', '0', 'nat_tn', '1'),
    ('option3', '192', '0', 'nat_tv', '1'),
    ('option3', '193', '0', 'nat_tm', '1'),
    ('option3', '194', '0', 'nat_ug', '1'),
    ('option3', '195', '0', 'nat_nh', '1'),
    ('option3', '196', '0', 'nat_nz', '1'),
    ('option3', '197', '0', 'nat_uy', '1'),
    ('option3', '198', '0', 'nat_uz', '1'),
    ('option3', '199', '0', 'nat_ve', '1'),
    ('option3', '200', '0', 'nat_vn', '1'),
    ('option3', '201', '0', 'nat_zr', '1'),
    ('option3', '202', '0', 'nat_zm', '1'),
    ('option3', '203', '0', 'nat_zw', '1'),
    ('option3', '204', '0', 'nat_ot', '1'),
    ('option3', '205', '0', 'nat_vat', '1'),
    ('option3', '206', '0', 'nat_ai', '1'),
    ('option3', '207', '0', 'nat_aq', '1'),
    ('option3', '208', '0', 'nat_bv', '1'),
    ('option3', '209', '0', 'nat_coi', '1'),
    ('option3', '210', '0', 'nat_cvi', '1'),
    ('option3', '211', '0', 'nat_io', '1'),
    ('option3', '212', '0', 'nat_ki', '1'),
    ('option3', '213', '0', 'nat_mca', '1'),
    ('option3', '214', '0', 'nat_mh', '1'),
    ('option3', '215', '0', 'nat_mq', '1'),
    ('option3', '216', '0', 'nat_nam', '1'),
    ('option3', '217', '0', 'nat_nru', '1'),
    ('option3', '218', '0', 'nat_nu', '1'),
    ('option3', '219', '0', 'nat_pm', '1'),
    ('option3', '220', '0', 'nat_pn', '1'),
    ('option3', '221', '0', 'nat_pw', '1'),
    ('option3', '222', '0', 'nat_re', '1'),
    ('option3', '223', '0', 'nat_sh', '1'),
    ('option3', '224', '0', 'nat_sj', '1'),
    ('option3', '225', '0', 'nat_swa', '1'),
    ('option3', '226', '0', 'nat_tc', '1'),
    ('option3', '227', '0', 'nat_tk', '1'),
    ('option3', '228', '0', 'nat_um', '1'),
    ('option3', '229', '0', 'nat_un', '1'),
    ('option3', '230', '0', 'nat_vu', '1'),
    ('option3', '231', '0', 'nat_wafu', '1'),
    ('option3', '232', '0', 'nat_wsh', '1'),
    ('option3', '233', '0', 'nat_yt', '1'),
	('option4', '1', '0', 'nat_hun', '1'),
    ('option4', '2', '0', 'nat_ua', '1'),
    ('option4', '3', '0', 'nat_afg', '1'),
    ('option4', '4', '0', 'nat_al', '1'),
    ('option4', '5', '0', 'nat_alg', '1'),
    ('option4', '6', '0', 'nat_us', '1'),
    ('option4', '7', '0', 'nat_and', '1'),
    ('option4', '8', '0', 'nat_ago', '1'),
    ('option4', '9', '0', 'nat_atg', '1'),
    ('option4', '10', '0', 'nat_arg', '1'),
    ('option4', '11', '0', 'nat_abw', '1'),
    ('option4', '12', '0', 'nat_aus', '1'),
    ('option4', '13', '0', 'nat_aze', '1'),
    ('option4', '14', '0', 'nat_bhs', '1'),
    ('option4', '15', '0', 'nat_bh', '1'),
    ('option4', '16', '0', 'nat_bgd', '1'),
    ('option4', '17', '0', 'nat_brb', '1'),
    ('option4', '18', '0', 'nat_b', '1'),
    ('option4', '19', '0', 'nat_bz', '1'),
    ('option4', '20', '0', 'nat_by', '1'),
    ('option4', '21', '0', 'nat_bj', '1'),
    ('option4', '22', '0', 'nat_ber', '1'),
    ('option4', '23', '0', 'nat_bt', '1'),
    ('option4', '24', '0', 'nat_bg', '1'),
    ('option4', '25', '0', 'nat_bo', '1'),
    ('option4', '26', '0', 'nat_ba', '1'),
    ('option4', '27', '0', 'nat_bw', '1'),
    ('option4', '28', '0', 'nat_bra', '1'),
    ('option4', '29', '0', 'nat_gbr', '1'),
    ('option4', '30', '0', 'nat_bvi', '1'),
    ('option4', '31', '0', 'nat_bf', '1'),
    ('option4', '32', '0', 'nat_bur', '1'),
    ('option4', '33', '0', 'nat_bi', '1'),
    ('option4', '34', '0', 'nat_cv', '1'),
    ('option4', '35', '0', 'nat_cl', '1'),
    ('option4', '36', '0', 'nat_cy', '1'),
    ('option4', '37', '0', 'nat_cr', '1'),
    ('option4', '38', '0', 'nat_td', '1'),
    ('option4', '39', '0', 'nat_cz', '1'),
    ('option4', '40', '0', 'nat_dk', '1'),
    ('option4', '41', '0', 'nat_za', '1'),
    ('option4', '42', '0', 'nat_kr', '1'),
    ('option4', '43', '0', 'nat_do', '1'),
    ('option4', '44', '0', 'nat_dubai', '1'),
    ('option4', '45', '0', 'nat_dj', '1'),
    ('option4', '46', '0', 'nat_ec', '1'),
    ('option4', '47', '0', 'nat_gq', '1'),
    ('option4', '48', '0', 'nat_ae', '1'),
    ('option4', '49', '0', 'nat_eg', '1'),
    ('option4', '50', '0', 'nat_ci', '1'),
    ('option4', '51', '0', 'nat_er', '1'),
    ('option4', '52', '0', 'nat_nk', '1'),
    ('option4', '53', '0', 'nat_ee', '1'),
    ('option4', '54', '0', 'nat_et', '1'),
    ('option4', '55', '0', 'nat_gbfi', '1'),
    ('option4', '56', '0', 'nat_fro', '1'),
    ('option4', '57', '0', 'nat_fj', '1'),
    ('option4', '58', '0', 'nat_fin', '1'),
    ('option4', '59', '0', 'nat_fr', '1'),
    ('option4', '60', '0', 'nat_frgy', '1'),
    ('option4', '61', '0', 'nat_pf', '1'),
    ('option4', '62', '0', 'nat_ph', '1'),
    ('option4', '63', '0', 'nat_ga', '1'),
    ('option4', '64', '0', 'nat_gm', '1'),
    ('option4', '65', '0', 'nat_gh', '1'),
    ('option4', '66', '0', 'nat_gbz', '1'),
    ('option4', '67', '0', 'nat_gr', '1'),
    ('option4', '68', '0', 'nat_gd', '1'),
    ('option4', '69', '0', 'nat_gl', '1'),
    ('option4', '70', '0', 'nat_ge', '1'),
    ('option4', '71', '0', 'nat_gt', '1'),
    ('option4', '72', '0', 'nat_gw', '1'),
    ('option4', '73', '0', 'nat_gn', '1'),
    ('option4', '74', '0', 'nat_gy', '1'),
    ('option4', '75', '0', 'nat_ht', '1'),
    ('option4', '76', '0', 'nat_heart', '1'),
    ('option4', '77', '0', 'nat_nl', '1'),
    ('option4', '78', '0', 'nat_nlant', '1'),
    ('option4', '79', '0', 'nat_hn', '1'),
    ('option4', '80', '0', 'nat_hk', '1'),
    ('option4', '81', '0', 'nat_hr', '1'),
    ('option4', '82', '0', 'nat_in', '1'),
    ('option4', '83', '0', 'nat_id', '1'),
    ('option4', '84', '0', 'nat_ie', '1'),
    ('option4', '85', '0', 'nat_iq', '1'),
    ('option4', '86', '0', 'nat_ir', '1'),
    ('option4', '87', '0', 'nat_is', '1'),
    ('option4', '88', '0', 'nat_il', '1'),
    ('option4', '89', '0', 'nat_jm', '1'),
    ('option4', '90', '0', 'nat_jp', '1'),
    ('option4', '91', '0', 'nat_ye', '1'),
    ('option4', '92', '0', 'nat_jor', '1'),
    ('option4', '93', '0', 'nat_ky', '1'),
    ('option4', '94', '0', 'nat_kh', '1'),
    ('option4', '95', '0', 'nat_cm', '1'),
    ('option4', '96', '0', 'nat_can', '1'),
    ('option4', '97', '0', 'nat_auch', '1'),
    ('option4', '98', '0', 'nat_qa', '1'),
    ('option4', '99', '0', 'nat_kz', '1'),
    ('option4', '100', '0', 'nat_ke', '1'),
    ('option4', '101', '0', 'nat_chn', '1'),
    ('option4', '102', '0', 'nat_kg', '1'),
    ('option4', '103', '0', 'nat_aucc', '1'),
    ('option4', '104', '0', 'nat_co', '1'),
    ('option4', '105', '0', 'nat_cog', '1'),
    ('option4', '106', '0', 'nat_xk', '1'),
    ('option4', '107', '0', 'nat_cf', '1'),
    ('option4', '108', '0', 'nat_cu', '1'),
    ('option4', '109', '0', 'nat_kw', '1'),
    ('option4', '110', '0', 'nat_la', '1'),
    ('option4', '111', '0', 'nat_pl', '1'),
    ('option4', '112', '0', 'nat_ls', '1'),
    ('option4', '113', '0', 'nat_lv', '1'),
    ('option4', '114', '0', 'nat_lb', '1'),
    ('option4', '115', '0', 'nat_lr', '1'),
    ('option4', '116', '0', 'nat_ly', '1'),
    ('option4', '117', '0', 'nat_li', '1'),
    ('option4', '118', '0', 'nat_lt', '1'),
    ('option4', '119', '0', 'nat_lu', '1'),
    ('option4', '120', '0', 'nat_mg', '1'),
    ('option4', '121', '0', 'nat_mo', '1'),
    ('option4', '122', '0', 'nat_mk', '1'),
    ('option4', '123', '0', 'nat_mw', '1'),
    ('option4', '124', '0', 'nat_mys', '1'),
    ('option4', '125', '0', 'nat_mv', '1'),
    ('option4', '126', '0', 'nat_ml', '1'),
    ('option4', '127', '0', 'nat_mt', '1'),
    ('option4', '128', '0', 'nat_ma', '1'),
    ('option4', '129', '0', 'nat_mr', '1'),
    ('option4', '130', '0', 'nat_mu', '1'),
    ('option4', '131', '0', 'nat_mx', '1'),
    ('option4', '132', '0', 'nat_mol', '1'),
    ('option4', '133', '0', 'nat_mc', '1'),
    ('option4', '134', '0', 'nat_mn', '1'),
    ('option4', '135', '0', 'nat_me', '1'),
    ('option4', '136', '0', 'nat_mz', '1'),
    ('option4', '137', '0', 'nat_mm', '1'),
    ('option4', '138', '0', 'nat_na', '1'),
    ('option4', '139', '0', 'nat_deu', '1'),
    ('option4', '140', '0', 'nat_np', '1'),
    ('option4', '141', '0', 'nat_ni', '1'),
    ('option4', '142', '0', 'nat_ng', '1'),
    ('option4', '143', '0', 'nat_nf', '1'),
    ('option4', '144', '0', 'nat_no', '1'),
    ('option4', '145', '0', 'nat_wf', '1'),
    ('option4', '146', '0', 'nat_ws', '1'),
    ('option4', '147', '0', 'nat_it', '1'),
    ('option4', '148', '0', 'nat_om', '1'),
    ('option4', '149', '0', 'nat_ru', '1'),
    ('option4', '150', '0', 'nat_at', '1'),
    ('option4', '151', '0', 'nat_am', '1'),
    ('option4', '152', '0', 'nat_pk', '1'),
    ('option4', '153', '0', 'nat_pa', '1'),
    ('option4', '154', '0', 'nat_pg', '1'),
    ('option4', '155', '0', 'nat_py', '1'),
    ('option4', '156', '0', 'nat_pe', '1'),
    ('option4', '157', '0', 'nat_pt', '1'),
    ('option4', '158', '0', 'nat_pr', '1'),
    ('option4', '159', '0', 'nat_ro', '1'),
    ('option4', '160', '0', 'nat_rw', '1'),
    ('option4', '161', '0', 'nat_lc', '1'),
    ('option4', '162', '0', 'nat_svin', '1'),
    ('option4', '163', '0', 'nat_sb', '1'),
    ('option4', '164', '0', 'nat_sv', '1'),
    ('option4', '165', '0', 'nat_sm', '1'),
    ('option4', '166', '0', 'nat_st', '1'),
    ('option4', '167', '0', 'nat_sc', '1'),
    ('option4', '168', '0', 'nat_sl', '1'),
    ('option4', '169', '0', 'nat_es', '1'),
    ('option4', '170', '0', 'nat_lk', '1'),
    ('option4', '171', '0', 'nat_sr', '1'),
    ('option4', '172', '0', 'nat_ch', '1'),
    ('option4', '173', '0', 'nat_se', '1'),
    ('option4', '174', '0', 'nat_sa', '1'),
    ('option4', '175', '0', 'nat_sn', '1'),
    ('option4', '176', '0', 'nat_srb', '1'),
    ('option4', '177', '0', 'nat_sg', '1'),
    ('option4', '178', '0', 'nat_sy', '1'),
    ('option4', '179', '0', 'nat_sk', '1'),
    ('option4', '180', '0', 'nat_si', '1'),
    ('option4', '181', '0', 'nat_so', '1'),
    ('option4', '182', '0', 'nat_sd', '1'),
    ('option4', '183', '0', 'nat_tj', '1'),
    ('option4', '184', '0', 'nat_tw', '1'),
    ('option4', '185', '0', 'nat_tz', '1'),
    ('option4', '186', '0', 'nat_th', '1'),
    ('option4', '187', '0', 'nat_tg', '1'),
    ('option4', '188', '0', 'nat_to', '1'),
    ('option4', '189', '0', 'nat_tr', '1'),
    ('option4', '190', '0', 'nat_tt', '1'),
    ('option4', '191', '0', 'nat_tn', '1'),
    ('option4', '192', '0', 'nat_tv', '1'),
    ('option4', '193', '0', 'nat_tm', '1'),
    ('option4', '194', '0', 'nat_ug', '1'),
    ('option4', '195', '0', 'nat_nh', '1'),
    ('option4', '196', '0', 'nat_nz', '1'),
    ('option4', '197', '0', 'nat_uy', '1'),
    ('option4', '198', '0', 'nat_uz', '1'),
    ('option4', '199', '0', 'nat_ve', '1'),
    ('option4', '200', '0', 'nat_vn', '1'),
    ('option4', '201', '0', 'nat_zr', '1'),
    ('option4', '202', '0', 'nat_zm', '1'),
    ('option4', '203', '0', 'nat_zw', '1'),
    ('option4', '204', '0', 'nat_ot', '1'),
    ('option4', '205', '0', 'nat_vat', '1'),
    ('option4', '206', '0', 'nat_ai', '1'),
    ('option4', '207', '0', 'nat_aq', '1'),
    ('option4', '208', '0', 'nat_bv', '1'),
    ('option4', '209', '0', 'nat_coi', '1'),
    ('option4', '210', '0', 'nat_cvi', '1'),
    ('option4', '211', '0', 'nat_io', '1'),
    ('option4', '212', '0', 'nat_ki', '1'),
    ('option4', '213', '0', 'nat_mca', '1'),
    ('option4', '214', '0', 'nat_mh', '1'),
    ('option4', '215', '0', 'nat_mq', '1'),
    ('option4', '216', '0', 'nat_nam', '1'),
    ('option4', '217', '0', 'nat_nru', '1'),
    ('option4', '218', '0', 'nat_nu', '1'),
    ('option4', '219', '0', 'nat_pm', '1'),
    ('option4', '220', '0', 'nat_pn', '1'),
    ('option4', '221', '0', 'nat_pw', '1'),
    ('option4', '222', '0', 'nat_re', '1'),
    ('option4', '223', '0', 'nat_sh', '1'),
    ('option4', '224', '0', 'nat_sj', '1'),
    ('option4', '225', '0', 'nat_swa', '1'),
    ('option4', '226', '0', 'nat_tc', '1'),
    ('option4', '227', '0', 'nat_tk', '1'),
    ('option4', '228', '0', 'nat_um', '1'),
    ('option4', '229', '0', 'nat_un', '1'),
    ('option4', '230', '0', 'nat_vu', '1'),
    ('option4', '231', '0', 'nat_wafu', '1'),
    ('option4', '232', '0', 'nat_wsh', '1'),
    ('option4', '233', '0', 'nat_yt', '1');

UPDATE `_sql_version` SET `revision`=24, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -24--2024-10-04-11:00---------------------------------------------------------------

DELETE FROM `app_lookup` WHERE `lookup_id` IN ('option2', 'ext2_option2', 'ext2_option12', 'ext2_option22');

UPDATE `option_config` SET `type` = 'ed', `modified_by` = 'SZOF-4264', `modified_on` = NOW() WHERE `option_id` = 'ext2_option17' AND `status` = '2';

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
    ('option2', '1', '0', 'country_al', '1'),
    ('option2', '2', '0', 'country_and', '1'),
    ('option2', '3', '0', 'country_at', '1'),
    ('option2', '4', '0', 'country_by', '1'),
    ('option2', '5', '0', 'country_b', '1'),
    ('option2', '6', '0', 'country_ba', '1'),
    ('option2', '7', '0', 'country_bg', '1'),
    ('option2', '8', '0', 'country_hr', '1'),
    ('option2', '9', '0', 'country_cy', '1'),
    ('option2', '10', '0', 'country_cz', '1'),
    ('option2', '11', '0', 'country_dk', '1'),
    ('option2', '12', '0', 'country_ee', '1'),
    ('option2', '13', '0', 'country_fi', '1'),
    ('option2', '14', '0', 'country_fr', '1'),
    ('option2', '15', '0', 'country_de', '1'),
    ('option2', '16', '0', 'country_gr', '1'),
    ('option2', '17', '0', 'country_hu', '1'),
    ('option2', '18', '0', 'country_is', '1'),
    ('option2', '19', '0', 'country_ie', '1'),
    ('option2', '20', '0', 'country_it', '1'),
    ('option2', '21', '0', 'country_lv', '1'),
    ('option2', '22', '0', 'country_li', '1'),
    ('option2', '23', '0', 'country_lt', '1'),
    ('option2', '24', '0', 'country_lu', '1'),
    ('option2', '25', '0', 'country_mt', '1'),
    ('option2', '26', '0', 'country_mol', '1'),
    ('option2', '27', '0', 'country_mc', '1'),
    ('option2', '28', '0', 'country_me', '1'),
    ('option2', '29', '0', 'country_mk', '1'),
    ('option2', '30', '0', 'country_no', '1'),
    ('option2', '31', '0', 'country_pl', '1'),
    ('option2', '32', '0', 'country_pt', '1'),
    ('option2', '33', '0', 'country_ro', '1'),
    ('option2', '34', '0', 'country_ru', '1'),
    ('option2', '35', '0', 'country_sm', '1'),
    ('option2', '36', '0', 'country_srb', '1'),
    ('option2', '37', '0', 'country_sk', '1'),
    ('option2', '38', '0', 'country_si', '1'),
    ('option2', '39', '0', 'country_es', '1'),
    ('option2', '40', '0', 'country_se', '1'),
    ('option2', '41', '0', 'country_ch', '1'),
    ('option2', '42', '0', 'country_ua', '1'),
    ('option2', '43', '0', 'country_gbr', '1'),
    ('option2', '44', '0', 'country_vat', '1'),
    ('option2', '45', '0', 'country_ai', '1'),
    ('option2', '46', '0', 'country_aq', '1'),
    ('option2', '47', '0', 'country_bv', '1'),
    ('option2', '48', '0', 'country_km', '1'),
    ('option2', '49', '0', 'country_cv', '1'),
    ('option2', '50', '0', 'country_io', '1'),
    ('option2', '51', '0', 'country_ki', '1'),
    ('option2', '52', '0', 'country_mh', '1'),
    ('option2', '53', '0', 'country_mq', '1'),
    ('option2', '54', '0', 'country_na', '1'),
    ('option2', '55', '0', 'country_nr', '1'),
    ('option2', '56', '0', 'country_nu', '1'),
    ('option2', '57', '0', 'country_pm', '1'),
    ('option2', '58', '0', 'country_pn', '1'),
    ('option2', '59', '0', 'country_pw', '1'),
    ('option2', '60', '0', 'country_re', '1'),
    ('option2', '61', '0', 'country_sh', '1'),
    ('option2', '62', '0', 'country_sj', '1'),
    ('option2', '63', '0', 'country_sz', '1'),
    ('option2', '64', '0', 'country_tc', '1'),
    ('option2', '65', '0', 'country_tk', '1'),
    ('option2', '66', '0', 'country_um', '1'),
    ('option2', '67', '0', 'country_un', '1'),
    ('option2', '68', '0', 'country_vu', '1'),
    ('option2', '69', '0', 'country_wafu', '1'),
    ('option2', '70', '0', 'country_eh', '1'),
    ('option2', '71', '0', 'country_yt', '1'),
    ('option2', '72', '0', 'country_afg', '1'),
    ('option2', '73', '0', 'country_atg', '1'),
    ('option2', '74', '0', 'country_alg', '1'),
    ('option2', '75', '0', 'country_nlant', '1'),
    ('option2', '76', '0', 'country_ago', '1'),
    ('option2', '77', '0', 'country_eg', '1'),
    ('option2', '78', '0', 'country_arg', '1'),
    ('option2', '79', '0', 'country_am', '1'),
    ('option2', '80', '0', 'country_aus', '1'),
    ('option2', '81', '0', 'country_abw', '1'),
    ('option2', '82', '0', 'country_aze', '1'),
    ('option2', '83', '0', 'country_bh', '1'),
    ('option2', '84', '0', 'country_brb', '1'),
    ('option2', '85', '0', 'country_bi', '1'),
    ('option2', '86', '0', 'country_bj', '1'),
    ('option2', '87', '0', 'country_bgd', '1'),
    ('option2', '88', '0', 'country_bhs', '1'),
    ('option2', '89', '0', 'country_bt', '1'),
    ('option2', '90', '0', 'country_bf', '1'),
    ('option2', '91', '0', 'country_ber', '1'),
    ('option2', '92', '0', 'country_bur', '1'),
    ('option2', '93', '0', 'country_bo', '1'),
    ('option2', '94', '0', 'country_bw', '1'),
    ('option2', '95', '0', 'country_bra', '1'),
    ('option2', '96', '0', 'country_bz', '1'),
    ('option2', '97', '0', 'country_kh', '1'),
    ('option2', '98', '0', 'country_can', '1'),
    ('option2', '99', '0', 'country_cf', '1'),
    ('option2', '100', '0', 'country_aucc', '1'),
    ('option2', '101', '0', 'country_td', '1'),
    ('option2', '102', '0', 'country_chn', '1'),
    ('option2', '103', '0', 'country_cl', '1'),
    ('option2', '104', '0', 'country_cm', '1'),
    ('option2', '105', '0', 'country_cog', '1'),
    ('option2', '106', '0', 'country_coi', '1'),
    ('option2', '107', '0', 'country_co', '1'),
    ('option2', '108', '0', 'country_cr', '1'),
    ('option2', '109', '0', 'country_cu', '1'),
    ('option2', '110', '0', 'country_cvi', '1'),
    ('option2', '111', '0', 'country_auch', '1'),
    ('option2', '112', '0', 'country_dj', '1'),
    ('option2', '113', '0', 'country_do', '1'),
    ('option2', '114', '0', 'country_dubai', '1'),
    ('option2', '115', '0', 'country_ec', '1'),
    ('option2', '116', '0', 'country_gq', '1'),
    ('option2', '117', '0', 'country_er', '1'),
    ('option2', '118', '0', 'country_et', '1'),
    ('option2', '119', '0', 'country_frgy', '1'),
    ('option2', '120', '0', 'country_fj', '1'),
    ('option2', '121', '0', 'country_gbfi', '1'),
    ('option2', '122', '0', 'country_fro', '1'),
    ('option2', '123', '0', 'country_ga', '1'),
    ('option2', '124', '0', 'country_gm', '1'),
    ('option2', '125', '0', 'country_gw', '1'),
    ('option2', '126', '0', 'country_ge', '1'),
    ('option2', '127', '0', 'country_gh', '1'),
    ('option2', '128', '0', 'country_gbz', '1'),
    ('option2', '129', '0', 'country_gl', '1'),
    ('option2', '130', '0', 'country_gd', '1'),
    ('option2', '131', '0', 'country_gt', '1'),
    ('option2', '132', '0', 'country_gn', '1'),
    ('option2', '133', '0', 'country_gy', '1'),
    ('option2', '134', '0', 'country_ht', '1'),
    ('option2', '135', '0', 'country_hk', '1'),
    ('option2', '136', '0', 'country_heart', '1'),
    ('option2', '137', '0', 'country_hn', '1'),
    ('option2', '138', '0', 'country_ci', '1'),
    ('option2', '139', '0', 'country_in', '1'),
    ('option2', '140', '0', 'country_id', '1'),
    ('option2', '141', '0', 'country_ir', '1'),
    ('option2', '142', '0', 'country_iq', '1'),
    ('option2', '143', '0', 'country_il', '1'),
    ('option2', '144', '0', 'country_jm', '1'),
    ('option2', '145', '0', 'country_jor', '1'),
    ('option2', '146', '0', 'country_jp', '1'),
    ('option2', '147', '0', 'country_kz', '1'),
    ('option2', '148', '0', 'country_ke', '1'),
    ('option2', '149', '0', 'country_kr', '1'),
    ('option2', '150', '0', 'country_nk', '1'),
    ('option2', '151', '0', 'country_kw', '1'),
    ('option2', '152', '0', 'country_ky', '1'),
    ('option2', '153', '0', 'country_kg', '1'),
    ('option2', '154', '0', 'country_la', '1'),
    ('option2', '155', '0', 'country_lr', '1'),
    ('option2', '156', '0', 'country_ly', '1'),
    ('option2', '157', '0', 'country_lc', '1'),
    ('option2', '158', '0', 'country_lb', '1'),
    ('option2', '159', '0', 'country_ls', '1'),
    ('option2', '160', '0', 'country_lk', '1'),
    ('option2', '161', '0', 'country_mg', '1'),
    ('option2', '162', '0', 'country_mr', '1'),
    ('option2', '163', '0', 'country_mv', '1'),
    ('option2', '164', '0', 'country_mx', '1'),
    ('option2', '165', '0', 'country_ml', '1'),
    ('option2', '166', '0', 'country_mys', '1'),
    ('option2', '167', '0', 'country_mw', '1'),
    ('option2', '168', '0', 'country_mn', '1'),
    ('option2', '169', '0', 'country_mo', '1'),
    ('option2', '170', '0', 'country_ma', '1'),
    ('option2', '171', '0', 'country_mz', '1'),
    ('option2', '172', '0', 'country_mu', '1'),
    ('option2', '173', '0', 'country_mm', '1'),
    ('option2', '174', '0', 'country_nh', '1'),
    ('option2', '175', '0', 'country_np', '1'),
    ('option2', '176', '0', 'country_nl', '1'),
    ('option2', '177', '0', 'country_nf', '1'),
    ('option2', '178', '0', 'country_ng', '1'),
    ('option2', '179', '0', 'country_ni', '1'),
    ('option2', '180', '0', 'country_nig', '1'),
    ('option2', '181', '0', 'country_ot', '1'),
    ('option2', '182', '0', 'country_nz', '1'),
    ('option2', '183', '0', 'country_om', '1'),
    ('option2', '184', '0', 'country_pk', '1'),
    ('option2', '185', '0', 'country_pa', '1'),
    ('option2', '186', '0', 'country_py', '1'),
    ('option2', '187', '0', 'country_pe', '1'),
    ('option2', '188', '0', 'country_pf', '1'),
    ('option2', '189', '0', 'country_ph', '1'),
    ('option2', '190', '0', 'country_pg', '1'),
    ('option2', '191', '0', 'country_pr', '1'),
    ('option2', '192', '0', 'country_qa', '1'),
    ('option2', '193', '0', 'country_za', '1'),
    ('option2', '194', '0', 'country_rw', '1'),
    ('option2', '195', '0', 'country_sv', '1'),
    ('option2', '196', '0', 'country_sa', '1'),
    ('option2', '197', '0', 'country_sb', '1'),
    ('option2', '198', '0', 'country_sn', '1'),
    ('option2', '199', '0', 'country_sc', '1'),
    ('option2', '200', '0', 'country_sg', '1'),
    ('option2', '201', '0', 'country_sl', '1'),
    ('option2', '202', '0', 'country_so', '1'),
    ('option2', '203', '0', 'country_vn', '1'),
    ('option2', '204', '0', 'country_st', '1'),
    ('option2', '205', '0', 'country_sd', '1'),
    ('option2', '206', '0', 'country_sr', '1'),
    ('option2', '207', '0', 'country_sy', '1'),
    ('option2', '208', '0', 'country_tz', '1'),
    ('option2', '209', '0', 'country_th', '1'),
    ('option2', '210', '0', 'country_tj', '1'),
    ('option2', '211', '0', 'country_tm', '1'),
    ('option2', '212', '0', 'country_tg', '1'),
    ('option2', '213', '0', 'country_to', '1'),
    ('option2', '214', '0', 'country_tt', '1'),
    ('option2', '215', '0', 'country_tn', '1'),
    ('option2', '216', '0', 'country_tr', '1'),
    ('option2', '217', '0', 'country_tv', '1'),
    ('option2', '218', '0', 'country_tw', '1'),
    ('option2', '219', '0', 'country_ae', '1'),
    ('option2', '220', '0', 'country_ug', '1'),
    ('option2', '221', '0', 'country_xk', '1'),
    ('option2', '222', '0', 'country_uy', '1'),
    ('option2', '223', '0', 'country_us', '1'),
    ('option2', '224', '0', 'country_uz', '1'),
    ('option2', '225', '0', 'country_svin', '1'),
    ('option2', '226', '0', 'country_ve', '1'),
    ('option2', '227', '0', 'country_bvi', '1'),
    ('option2', '228', '0', 'country_wf', '1'),
    ('option2', '229', '0', 'country_ws', '1'),
    ('option2', '230', '0', 'country_ye', '1'),
    ('option2', '231', '0', 'country_zm', '1'),
    ('option2', '232', '0', 'country_zw', '1'),
    ('option2', '233', '0', 'country_zr', '1'),
    ('ext2_option2', '1', '0', 'country_al', '1'),
    ('ext2_option2', '2', '0', 'country_and', '1'),
    ('ext2_option2', '3', '0', 'country_at', '1'),
    ('ext2_option2', '4', '0', 'country_by', '1'),
    ('ext2_option2', '5', '0', 'country_b', '1'),
    ('ext2_option2', '6', '0', 'country_ba', '1'),
    ('ext2_option2', '7', '0', 'country_bg', '1'),
    ('ext2_option2', '8', '0', 'country_hr', '1'),
    ('ext2_option2', '9', '0', 'country_cy', '1'),
    ('ext2_option2', '10', '0', 'country_cz', '1'),
    ('ext2_option2', '11', '0', 'country_dk', '1'),
    ('ext2_option2', '12', '0', 'country_ee', '1'),
    ('ext2_option2', '13', '0', 'country_fi', '1'),
    ('ext2_option2', '14', '0', 'country_fr', '1'),
    ('ext2_option2', '15', '0', 'country_de', '1'),
    ('ext2_option2', '16', '0', 'country_gr', '1'),
    ('ext2_option2', '17', '0', 'country_hu', '1'),
    ('ext2_option2', '18', '0', 'country_is', '1'),
    ('ext2_option2', '19', '0', 'country_ie', '1'),
    ('ext2_option2', '20', '0', 'country_it', '1'),
    ('ext2_option2', '21', '0', 'country_lv', '1'),
    ('ext2_option2', '22', '0', 'country_li', '1'),
    ('ext2_option2', '23', '0', 'country_lt', '1'),
    ('ext2_option2', '24', '0', 'country_lu', '1'),
    ('ext2_option2', '25', '0', 'country_mt', '1'),
    ('ext2_option2', '26', '0', 'country_mol', '1'),
    ('ext2_option2', '27', '0', 'country_mc', '1'),
    ('ext2_option2', '28', '0', 'country_me', '1'),
    ('ext2_option2', '29', '0', 'country_mk', '1'),
    ('ext2_option2', '30', '0', 'country_no', '1'),
    ('ext2_option2', '31', '0', 'country_pl', '1'),
    ('ext2_option2', '32', '0', 'country_pt', '1'),
    ('ext2_option2', '33', '0', 'country_ro', '1'),
    ('ext2_option2', '34', '0', 'country_ru', '1'),
    ('ext2_option2', '35', '0', 'country_sm', '1'),
    ('ext2_option2', '36', '0', 'country_srb', '1'),
    ('ext2_option2', '37', '0', 'country_sk', '1'),
    ('ext2_option2', '38', '0', 'country_si', '1'),
    ('ext2_option2', '39', '0', 'country_es', '1'),
    ('ext2_option2', '40', '0', 'country_se', '1'),
    ('ext2_option2', '41', '0', 'country_ch', '1'),
    ('ext2_option2', '42', '0', 'country_ua', '1'),
    ('ext2_option2', '43', '0', 'country_gbr', '1'),
    ('ext2_option2', '44', '0', 'country_vat', '1'),
    ('ext2_option2', '45', '0', 'country_ai', '1'),
    ('ext2_option2', '46', '0', 'country_aq', '1'),
    ('ext2_option2', '47', '0', 'country_bv', '1'),
    ('ext2_option2', '48', '0', 'country_km', '1'),
    ('ext2_option2', '49', '0', 'country_cv', '1'),
    ('ext2_option2', '50', '0', 'country_io', '1'),
    ('ext2_option2', '51', '0', 'country_ki', '1'),
    ('ext2_option2', '52', '0', 'country_mh', '1'),
    ('ext2_option2', '53', '0', 'country_mq', '1'),
    ('ext2_option2', '54', '0', 'country_na', '1'),
    ('ext2_option2', '55', '0', 'country_nr', '1'),
    ('ext2_option2', '56', '0', 'country_nu', '1'),
    ('ext2_option2', '57', '0', 'country_pm', '1'),
    ('ext2_option2', '58', '0', 'country_pn', '1'),
    ('ext2_option2', '59', '0', 'country_pw', '1'),
    ('ext2_option2', '60', '0', 'country_re', '1'),
    ('ext2_option2', '61', '0', 'country_sh', '1'),
    ('ext2_option2', '62', '0', 'country_sj', '1'),
    ('ext2_option2', '63', '0', 'country_sz', '1'),
    ('ext2_option2', '64', '0', 'country_tc', '1'),
    ('ext2_option2', '65', '0', 'country_tk', '1'),
    ('ext2_option2', '66', '0', 'country_um', '1'),
    ('ext2_option2', '67', '0', 'country_un', '1'),
    ('ext2_option2', '68', '0', 'country_vu', '1'),
    ('ext2_option2', '69', '0', 'country_wafu', '1'),
    ('ext2_option2', '70', '0', 'country_eh', '1'),
    ('ext2_option2', '71', '0', 'country_yt', '1'),
    ('ext2_option2', '72', '0', 'country_afg', '1'),
    ('ext2_option2', '73', '0', 'country_atg', '1'),
    ('ext2_option2', '74', '0', 'country_alg', '1'),
    ('ext2_option2', '75', '0', 'country_nlant', '1'),
    ('ext2_option2', '76', '0', 'country_ago', '1'),
    ('ext2_option2', '77', '0', 'country_eg', '1'),
    ('ext2_option2', '78', '0', 'country_arg', '1'),
    ('ext2_option2', '79', '0', 'country_am', '1'),
    ('ext2_option2', '80', '0', 'country_aus', '1'),
    ('ext2_option2', '81', '0', 'country_abw', '1'),
    ('ext2_option2', '82', '0', 'country_aze', '1'),
    ('ext2_option2', '83', '0', 'country_bh', '1'),
    ('ext2_option2', '84', '0', 'country_brb', '1'),
    ('ext2_option2', '85', '0', 'country_bi', '1'),
    ('ext2_option2', '86', '0', 'country_bj', '1'),
    ('ext2_option2', '87', '0', 'country_bgd', '1'),
    ('ext2_option2', '88', '0', 'country_bhs', '1'),
    ('ext2_option2', '89', '0', 'country_bt', '1'),
    ('ext2_option2', '90', '0', 'country_bf', '1'),
    ('ext2_option2', '91', '0', 'country_ber', '1'),
    ('ext2_option2', '92', '0', 'country_bur', '1'),
    ('ext2_option2', '93', '0', 'country_bo', '1'),
    ('ext2_option2', '94', '0', 'country_bw', '1'),
    ('ext2_option2', '95', '0', 'country_bra', '1'),
    ('ext2_option2', '96', '0', 'country_bz', '1'),
    ('ext2_option2', '97', '0', 'country_kh', '1'),
    ('ext2_option2', '98', '0', 'country_can', '1'),
    ('ext2_option2', '99', '0', 'country_cf', '1'),
    ('ext2_option2', '100', '0', 'country_aucc', '1'),
    ('ext2_option2', '101', '0', 'country_td', '1'),
    ('ext2_option2', '102', '0', 'country_chn', '1'),
    ('ext2_option2', '103', '0', 'country_cl', '1'),
    ('ext2_option2', '104', '0', 'country_cm', '1'),
    ('ext2_option2', '105', '0', 'country_cog', '1'),
    ('ext2_option2', '106', '0', 'country_coi', '1'),
    ('ext2_option2', '107', '0', 'country_co', '1'),
    ('ext2_option2', '108', '0', 'country_cr', '1'),
    ('ext2_option2', '109', '0', 'country_cu', '1'),
    ('ext2_option2', '110', '0', 'country_cvi', '1'),
    ('ext2_option2', '111', '0', 'country_auch', '1'),
    ('ext2_option2', '112', '0', 'country_dj', '1'),
    ('ext2_option2', '113', '0', 'country_do', '1'),
    ('ext2_option2', '114', '0', 'country_dubai', '1'),
    ('ext2_option2', '115', '0', 'country_ec', '1'),
    ('ext2_option2', '116', '0', 'country_gq', '1'),
    ('ext2_option2', '117', '0', 'country_er', '1'),
    ('ext2_option2', '118', '0', 'country_et', '1'),
    ('ext2_option2', '119', '0', 'country_frgy', '1'),
    ('ext2_option2', '120', '0', 'country_fj', '1'),
    ('ext2_option2', '121', '0', 'country_gbfi', '1'),
    ('ext2_option2', '122', '0', 'country_fro', '1'),
    ('ext2_option2', '123', '0', 'country_ga', '1'),
    ('ext2_option2', '124', '0', 'country_gm', '1'),
    ('ext2_option2', '125', '0', 'country_gw', '1'),
    ('ext2_option2', '126', '0', 'country_ge', '1'),
    ('ext2_option2', '127', '0', 'country_gh', '1'),
    ('ext2_option2', '128', '0', 'country_gbz', '1'),
    ('ext2_option2', '129', '0', 'country_gl', '1'),
    ('ext2_option2', '130', '0', 'country_gd', '1'),
    ('ext2_option2', '131', '0', 'country_gt', '1'),
    ('ext2_option2', '132', '0', 'country_gn', '1'),
    ('ext2_option2', '133', '0', 'country_gy', '1'),
    ('ext2_option2', '134', '0', 'country_ht', '1'),
    ('ext2_option2', '135', '0', 'country_hk', '1'),
    ('ext2_option2', '136', '0', 'country_heart', '1'),
    ('ext2_option2', '137', '0', 'country_hn', '1'),
    ('ext2_option2', '138', '0', 'country_ci', '1'),
    ('ext2_option2', '139', '0', 'country_in', '1'),
    ('ext2_option2', '140', '0', 'country_id', '1'),
    ('ext2_option2', '141', '0', 'country_ir', '1'),
    ('ext2_option2', '142', '0', 'country_iq', '1'),
    ('ext2_option2', '143', '0', 'country_il', '1'),
    ('ext2_option2', '144', '0', 'country_jm', '1'),
    ('ext2_option2', '145', '0', 'country_jor', '1'),
    ('ext2_option2', '146', '0', 'country_jp', '1'),
    ('ext2_option2', '147', '0', 'country_kz', '1'),
    ('ext2_option2', '148', '0', 'country_ke', '1'),
    ('ext2_option2', '149', '0', 'country_kr', '1'),
    ('ext2_option2', '150', '0', 'country_nk', '1'),
    ('ext2_option2', '151', '0', 'country_kw', '1'),
    ('ext2_option2', '152', '0', 'country_ky', '1'),
    ('ext2_option2', '153', '0', 'country_kg', '1'),
    ('ext2_option2', '154', '0', 'country_la', '1'),
    ('ext2_option2', '155', '0', 'country_lr', '1'),
    ('ext2_option2', '156', '0', 'country_ly', '1'),
    ('ext2_option2', '157', '0', 'country_lc', '1'),
    ('ext2_option2', '158', '0', 'country_lb', '1'),
    ('ext2_option2', '159', '0', 'country_ls', '1'),
    ('ext2_option2', '160', '0', 'country_lk', '1'),
    ('ext2_option2', '161', '0', 'country_mg', '1'),
    ('ext2_option2', '162', '0', 'country_mr', '1'),
    ('ext2_option2', '163', '0', 'country_mv', '1'),
    ('ext2_option2', '164', '0', 'country_mx', '1'),
    ('ext2_option2', '165', '0', 'country_ml', '1'),
    ('ext2_option2', '166', '0', 'country_mys', '1'),
    ('ext2_option2', '167', '0', 'country_mw', '1'),
    ('ext2_option2', '168', '0', 'country_mn', '1'),
    ('ext2_option2', '169', '0', 'country_mo', '1'),
    ('ext2_option2', '170', '0', 'country_ma', '1'),
    ('ext2_option2', '171', '0', 'country_mz', '1'),
    ('ext2_option2', '172', '0', 'country_mu', '1'),
    ('ext2_option2', '173', '0', 'country_mm', '1'),
    ('ext2_option2', '174', '0', 'country_nh', '1'),
    ('ext2_option2', '175', '0', 'country_np', '1'),
    ('ext2_option2', '176', '0', 'country_nl', '1'),
    ('ext2_option2', '177', '0', 'country_nf', '1'),
    ('ext2_option2', '178', '0', 'country_ng', '1'),
    ('ext2_option2', '179', '0', 'country_ni', '1'),
    ('ext2_option2', '180', '0', 'country_nig', '1'),
    ('ext2_option2', '181', '0', 'country_ot', '1'),
    ('ext2_option2', '182', '0', 'country_nz', '1'),
    ('ext2_option2', '183', '0', 'country_om', '1'),
    ('ext2_option2', '184', '0', 'country_pk', '1'),
    ('ext2_option2', '185', '0', 'country_pa', '1'),
    ('ext2_option2', '186', '0', 'country_py', '1'),
    ('ext2_option2', '187', '0', 'country_pe', '1'),
    ('ext2_option2', '188', '0', 'country_pf', '1'),
    ('ext2_option2', '189', '0', 'country_ph', '1'),
    ('ext2_option2', '190', '0', 'country_pg', '1'),
    ('ext2_option2', '191', '0', 'country_pr', '1'),
    ('ext2_option2', '192', '0', 'country_qa', '1'),
    ('ext2_option2', '193', '0', 'country_za', '1'),
    ('ext2_option2', '194', '0', 'country_rw', '1'),
    ('ext2_option2', '195', '0', 'country_sv', '1'),
    ('ext2_option2', '196', '0', 'country_sa', '1'),
    ('ext2_option2', '197', '0', 'country_sb', '1'),
    ('ext2_option2', '198', '0', 'country_sn', '1'),
    ('ext2_option2', '199', '0', 'country_sc', '1'),
    ('ext2_option2', '200', '0', 'country_sg', '1'),
    ('ext2_option2', '201', '0', 'country_sl', '1'),
    ('ext2_option2', '202', '0', 'country_so', '1'),
    ('ext2_option2', '203', '0', 'country_vn', '1'),
    ('ext2_option2', '204', '0', 'country_st', '1'),
    ('ext2_option2', '205', '0', 'country_sd', '1'),
    ('ext2_option2', '206', '0', 'country_sr', '1'),
    ('ext2_option2', '207', '0', 'country_sy', '1'),
    ('ext2_option2', '208', '0', 'country_tz', '1'),
    ('ext2_option2', '209', '0', 'country_th', '1'),
    ('ext2_option2', '210', '0', 'country_tj', '1'),
    ('ext2_option2', '211', '0', 'country_tm', '1'),
    ('ext2_option2', '212', '0', 'country_tg', '1'),
    ('ext2_option2', '213', '0', 'country_to', '1'),
    ('ext2_option2', '214', '0', 'country_tt', '1'),
    ('ext2_option2', '215', '0', 'country_tn', '1'),
    ('ext2_option2', '216', '0', 'country_tr', '1'),
    ('ext2_option2', '217', '0', 'country_tv', '1'),
    ('ext2_option2', '218', '0', 'country_tw', '1'),
    ('ext2_option2', '219', '0', 'country_ae', '1'),
    ('ext2_option2', '220', '0', 'country_ug', '1'),
    ('ext2_option2', '221', '0', 'country_xk', '1'),
    ('ext2_option2', '222', '0', 'country_uy', '1'),
    ('ext2_option2', '223', '0', 'country_us', '1'),
    ('ext2_option2', '224', '0', 'country_uz', '1'),
    ('ext2_option2', '225', '0', 'country_svin', '1'),
    ('ext2_option2', '226', '0', 'country_ve', '1'),
    ('ext2_option2', '227', '0', 'country_bvi', '1'),
    ('ext2_option2', '228', '0', 'country_wf', '1'),
    ('ext2_option2', '229', '0', 'country_ws', '1'),
    ('ext2_option2', '230', '0', 'country_ye', '1'),
    ('ext2_option2', '231', '0', 'country_zm', '1'),
    ('ext2_option2', '232', '0', 'country_zw', '1'),
    ('ext2_option2', '233', '0', 'country_zr', '1'),
    ('ext2_option12', '1', '0', 'country_al', '1'),
    ('ext2_option12', '2', '0', 'country_and', '1'),
    ('ext2_option12', '3', '0', 'country_at', '1'),
    ('ext2_option12', '4', '0', 'country_by', '1'),
    ('ext2_option12', '5', '0', 'country_b', '1'),
    ('ext2_option12', '6', '0', 'country_ba', '1'),
    ('ext2_option12', '7', '0', 'country_bg', '1'),
    ('ext2_option12', '8', '0', 'country_hr', '1'),
    ('ext2_option12', '9', '0', 'country_cy', '1'),
    ('ext2_option12', '10', '0', 'country_cz', '1'),
    ('ext2_option12', '11', '0', 'country_dk', '1'),
    ('ext2_option12', '12', '0', 'country_ee', '1'),
    ('ext2_option12', '13', '0', 'country_fi', '1'),
    ('ext2_option12', '14', '0', 'country_fr', '1'),
    ('ext2_option12', '15', '0', 'country_de', '1'),
    ('ext2_option12', '16', '0', 'country_gr', '1'),
    ('ext2_option12', '17', '0', 'country_hu', '1'),
    ('ext2_option12', '18', '0', 'country_is', '1'),
    ('ext2_option12', '19', '0', 'country_ie', '1'),
    ('ext2_option12', '20', '0', 'country_it', '1'),
    ('ext2_option12', '21', '0', 'country_lv', '1'),
    ('ext2_option12', '22', '0', 'country_li', '1'),
    ('ext2_option12', '23', '0', 'country_lt', '1'),
    ('ext2_option12', '24', '0', 'country_lu', '1'),
    ('ext2_option12', '25', '0', 'country_mt', '1'),
    ('ext2_option12', '26', '0', 'country_mol', '1'),
    ('ext2_option12', '27', '0', 'country_mc', '1'),
    ('ext2_option12', '28', '0', 'country_me', '1'),
    ('ext2_option12', '29', '0', 'country_mk', '1'),
    ('ext2_option12', '30', '0', 'country_no', '1'),
    ('ext2_option12', '31', '0', 'country_pl', '1'),
    ('ext2_option12', '32', '0', 'country_pt', '1'),
    ('ext2_option12', '33', '0', 'country_ro', '1'),
    ('ext2_option12', '34', '0', 'country_ru', '1'),
    ('ext2_option12', '35', '0', 'country_sm', '1'),
    ('ext2_option12', '36', '0', 'country_srb', '1'),
    ('ext2_option12', '37', '0', 'country_sk', '1'),
    ('ext2_option12', '38', '0', 'country_si', '1'),
    ('ext2_option12', '39', '0', 'country_es', '1'),
    ('ext2_option12', '40', '0', 'country_se', '1'),
    ('ext2_option12', '41', '0', 'country_ch', '1'),
    ('ext2_option12', '42', '0', 'country_ua', '1'),
    ('ext2_option12', '43', '0', 'country_gbr', '1'),
    ('ext2_option12', '44', '0', 'country_vat', '1'),
    ('ext2_option12', '45', '0', 'country_ai', '1'),
    ('ext2_option12', '46', '0', 'country_aq', '1'),
    ('ext2_option12', '47', '0', 'country_bv', '1'),
    ('ext2_option12', '48', '0', 'country_km', '1'),
    ('ext2_option12', '49', '0', 'country_cv', '1'),
    ('ext2_option12', '50', '0', 'country_io', '1'),
    ('ext2_option12', '51', '0', 'country_ki', '1'),
    ('ext2_option12', '52', '0', 'country_mh', '1'),
    ('ext2_option12', '53', '0', 'country_mq', '1'),
    ('ext2_option12', '54', '0', 'country_na', '1'),
    ('ext2_option12', '55', '0', 'country_nr', '1'),
    ('ext2_option12', '56', '0', 'country_nu', '1'),
    ('ext2_option12', '57', '0', 'country_pm', '1'),
    ('ext2_option12', '58', '0', 'country_pn', '1'),
    ('ext2_option12', '59', '0', 'country_pw', '1'),
    ('ext2_option12', '60', '0', 'country_re', '1'),
    ('ext2_option12', '61', '0', 'country_sh', '1'),
    ('ext2_option12', '62', '0', 'country_sj', '1'),
    ('ext2_option12', '63', '0', 'country_sz', '1'),
    ('ext2_option12', '64', '0', 'country_tc', '1'),
    ('ext2_option12', '65', '0', 'country_tk', '1'),
    ('ext2_option12', '66', '0', 'country_um', '1'),
    ('ext2_option12', '67', '0', 'country_un', '1'),
    ('ext2_option12', '68', '0', 'country_vu', '1'),
    ('ext2_option12', '69', '0', 'country_wafu', '1'),
    ('ext2_option12', '70', '0', 'country_eh', '1'),
    ('ext2_option12', '71', '0', 'country_yt', '1'),
    ('ext2_option12', '72', '0', 'country_afg', '1'),
    ('ext2_option12', '73', '0', 'country_atg', '1'),
    ('ext2_option12', '74', '0', 'country_alg', '1'),
    ('ext2_option12', '75', '0', 'country_nlant', '1'),
    ('ext2_option12', '76', '0', 'country_ago', '1'),
    ('ext2_option12', '77', '0', 'country_eg', '1'),
    ('ext2_option12', '78', '0', 'country_arg', '1'),
    ('ext2_option12', '79', '0', 'country_am', '1'),
    ('ext2_option12', '80', '0', 'country_aus', '1'),
    ('ext2_option12', '81', '0', 'country_abw', '1'),
    ('ext2_option12', '82', '0', 'country_aze', '1'),
    ('ext2_option12', '83', '0', 'country_bh', '1'),
    ('ext2_option12', '84', '0', 'country_brb', '1'),
    ('ext2_option12', '85', '0', 'country_bi', '1'),
    ('ext2_option12', '86', '0', 'country_bj', '1'),
    ('ext2_option12', '87', '0', 'country_bgd', '1'),
    ('ext2_option12', '88', '0', 'country_bhs', '1'),
    ('ext2_option12', '89', '0', 'country_bt', '1'),
    ('ext2_option12', '90', '0', 'country_bf', '1'),
    ('ext2_option12', '91', '0', 'country_ber', '1'),
    ('ext2_option12', '92', '0', 'country_bur', '1'),
    ('ext2_option12', '93', '0', 'country_bo', '1'),
    ('ext2_option12', '94', '0', 'country_bw', '1'),
    ('ext2_option12', '95', '0', 'country_bra', '1'),
    ('ext2_option12', '96', '0', 'country_bz', '1'),
    ('ext2_option12', '97', '0', 'country_kh', '1'),
    ('ext2_option12', '98', '0', 'country_can', '1'),
    ('ext2_option12', '99', '0', 'country_cf', '1'),
    ('ext2_option12', '100', '0', 'country_aucc', '1'),
    ('ext2_option12', '101', '0', 'country_td', '1'),
    ('ext2_option12', '102', '0', 'country_chn', '1'),
    ('ext2_option12', '103', '0', 'country_cl', '1'),
    ('ext2_option12', '104', '0', 'country_cm', '1'),
    ('ext2_option12', '105', '0', 'country_cog', '1'),
    ('ext2_option12', '106', '0', 'country_coi', '1'),
    ('ext2_option12', '107', '0', 'country_co', '1'),
    ('ext2_option12', '108', '0', 'country_cr', '1'),
    ('ext2_option12', '109', '0', 'country_cu', '1'),
    ('ext2_option12', '110', '0', 'country_cvi', '1'),
    ('ext2_option12', '111', '0', 'country_auch', '1'),
    ('ext2_option12', '112', '0', 'country_dj', '1'),
    ('ext2_option12', '113', '0', 'country_do', '1'),
    ('ext2_option12', '114', '0', 'country_dubai', '1'),
    ('ext2_option12', '115', '0', 'country_ec', '1'),
    ('ext2_option12', '116', '0', 'country_gq', '1'),
    ('ext2_option12', '117', '0', 'country_er', '1'),
    ('ext2_option12', '118', '0', 'country_et', '1'),
    ('ext2_option12', '119', '0', 'country_frgy', '1'),
    ('ext2_option12', '120', '0', 'country_fj', '1'),
    ('ext2_option12', '121', '0', 'country_gbfi', '1'),
    ('ext2_option12', '122', '0', 'country_fro', '1'),
    ('ext2_option12', '123', '0', 'country_ga', '1'),
    ('ext2_option12', '124', '0', 'country_gm', '1'),
    ('ext2_option12', '125', '0', 'country_gw', '1'),
    ('ext2_option12', '126', '0', 'country_ge', '1'),
    ('ext2_option12', '127', '0', 'country_gh', '1'),
    ('ext2_option12', '128', '0', 'country_gbz', '1'),
    ('ext2_option12', '129', '0', 'country_gl', '1'),
    ('ext2_option12', '130', '0', 'country_gd', '1'),
    ('ext2_option12', '131', '0', 'country_gt', '1'),
    ('ext2_option12', '132', '0', 'country_gn', '1'),
    ('ext2_option12', '133', '0', 'country_gy', '1'),
    ('ext2_option12', '134', '0', 'country_ht', '1'),
    ('ext2_option12', '135', '0', 'country_hk', '1'),
    ('ext2_option12', '136', '0', 'country_heart', '1'),
    ('ext2_option12', '137', '0', 'country_hn', '1'),
    ('ext2_option12', '138', '0', 'country_ci', '1'),
    ('ext2_option12', '139', '0', 'country_in', '1'),
    ('ext2_option12', '140', '0', 'country_id', '1'),
    ('ext2_option12', '141', '0', 'country_ir', '1'),
    ('ext2_option12', '142', '0', 'country_iq', '1'),
    ('ext2_option12', '143', '0', 'country_il', '1'),
    ('ext2_option12', '144', '0', 'country_jm', '1'),
    ('ext2_option12', '145', '0', 'country_jor', '1'),
    ('ext2_option12', '146', '0', 'country_jp', '1'),
    ('ext2_option12', '147', '0', 'country_kz', '1'),
    ('ext2_option12', '148', '0', 'country_ke', '1'),
    ('ext2_option12', '149', '0', 'country_kr', '1'),
    ('ext2_option12', '150', '0', 'country_nk', '1'),
    ('ext2_option12', '151', '0', 'country_kw', '1'),
    ('ext2_option12', '152', '0', 'country_ky', '1'),
    ('ext2_option12', '153', '0', 'country_kg', '1'),
    ('ext2_option12', '154', '0', 'country_la', '1'),
    ('ext2_option12', '155', '0', 'country_lr', '1'),
    ('ext2_option12', '156', '0', 'country_ly', '1'),
    ('ext2_option12', '157', '0', 'country_lc', '1'),
    ('ext2_option12', '158', '0', 'country_lb', '1'),
    ('ext2_option12', '159', '0', 'country_ls', '1'),
    ('ext2_option12', '160', '0', 'country_lk', '1'),
    ('ext2_option12', '161', '0', 'country_mg', '1'),
    ('ext2_option12', '162', '0', 'country_mr', '1'),
    ('ext2_option12', '163', '0', 'country_mv', '1'),
    ('ext2_option12', '164', '0', 'country_mx', '1'),
    ('ext2_option12', '165', '0', 'country_ml', '1'),
    ('ext2_option12', '166', '0', 'country_mys', '1'),
    ('ext2_option12', '167', '0', 'country_mw', '1'),
    ('ext2_option12', '168', '0', 'country_mn', '1'),
    ('ext2_option12', '169', '0', 'country_mo', '1'),
    ('ext2_option12', '170', '0', 'country_ma', '1'),
    ('ext2_option12', '171', '0', 'country_mz', '1'),
    ('ext2_option12', '172', '0', 'country_mu', '1'),
    ('ext2_option12', '173', '0', 'country_mm', '1'),
    ('ext2_option12', '174', '0', 'country_nh', '1'),
    ('ext2_option12', '175', '0', 'country_np', '1'),
    ('ext2_option12', '176', '0', 'country_nl', '1'),
    ('ext2_option12', '177', '0', 'country_nf', '1'),
    ('ext2_option12', '178', '0', 'country_ng', '1'),
    ('ext2_option12', '179', '0', 'country_ni', '1'),
    ('ext2_option12', '180', '0', 'country_nig', '1'),
    ('ext2_option12', '181', '0', 'country_ot', '1'),
    ('ext2_option12', '182', '0', 'country_nz', '1'),
    ('ext2_option12', '183', '0', 'country_om', '1'),
    ('ext2_option12', '184', '0', 'country_pk', '1'),
    ('ext2_option12', '185', '0', 'country_pa', '1'),
    ('ext2_option12', '186', '0', 'country_py', '1'),
    ('ext2_option12', '187', '0', 'country_pe', '1'),
    ('ext2_option12', '188', '0', 'country_pf', '1'),
    ('ext2_option12', '189', '0', 'country_ph', '1'),
    ('ext2_option12', '190', '0', 'country_pg', '1'),
    ('ext2_option12', '191', '0', 'country_pr', '1'),
    ('ext2_option12', '192', '0', 'country_qa', '1'),
    ('ext2_option12', '193', '0', 'country_za', '1'),
    ('ext2_option12', '194', '0', 'country_rw', '1'),
    ('ext2_option12', '195', '0', 'country_sv', '1'),
    ('ext2_option12', '196', '0', 'country_sa', '1'),
    ('ext2_option12', '197', '0', 'country_sb', '1'),
    ('ext2_option12', '198', '0', 'country_sn', '1'),
    ('ext2_option12', '199', '0', 'country_sc', '1'),
    ('ext2_option12', '200', '0', 'country_sg', '1'),
    ('ext2_option12', '201', '0', 'country_sl', '1'),
    ('ext2_option12', '202', '0', 'country_so', '1'),
    ('ext2_option12', '203', '0', 'country_vn', '1'),
    ('ext2_option12', '204', '0', 'country_st', '1'),
    ('ext2_option12', '205', '0', 'country_sd', '1'),
    ('ext2_option12', '206', '0', 'country_sr', '1'),
    ('ext2_option12', '207', '0', 'country_sy', '1'),
    ('ext2_option12', '208', '0', 'country_tz', '1'),
    ('ext2_option12', '209', '0', 'country_th', '1'),
    ('ext2_option12', '210', '0', 'country_tj', '1'),
    ('ext2_option12', '211', '0', 'country_tm', '1'),
    ('ext2_option12', '212', '0', 'country_tg', '1'),
    ('ext2_option12', '213', '0', 'country_to', '1'),
    ('ext2_option12', '214', '0', 'country_tt', '1'),
    ('ext2_option12', '215', '0', 'country_tn', '1'),
    ('ext2_option12', '216', '0', 'country_tr', '1'),
    ('ext2_option12', '217', '0', 'country_tv', '1'),
    ('ext2_option12', '218', '0', 'country_tw', '1'),
    ('ext2_option12', '219', '0', 'country_ae', '1'),
    ('ext2_option12', '220', '0', 'country_ug', '1'),
    ('ext2_option12', '221', '0', 'country_xk', '1'),
    ('ext2_option12', '222', '0', 'country_uy', '1'),
    ('ext2_option12', '223', '0', 'country_us', '1'),
    ('ext2_option12', '224', '0', 'country_uz', '1'),
    ('ext2_option12', '225', '0', 'country_svin', '1'),
    ('ext2_option12', '226', '0', 'country_ve', '1'),
    ('ext2_option12', '227', '0', 'country_bvi', '1'),
    ('ext2_option12', '228', '0', 'country_wf', '1'),
    ('ext2_option12', '229', '0', 'country_ws', '1'),
    ('ext2_option12', '230', '0', 'country_ye', '1'),
    ('ext2_option12', '231', '0', 'country_zm', '1'),
    ('ext2_option12', '232', '0', 'country_zw', '1'),
    ('ext2_option12', '233', '0', 'country_zr', '1'),
    ('ext2_option22', '1', '0', 'country_al', '1'),
    ('ext2_option22', '2', '0', 'country_and', '1'),
    ('ext2_option22', '3', '0', 'country_at', '1'),
    ('ext2_option22', '4', '0', 'country_by', '1'),
    ('ext2_option22', '5', '0', 'country_b', '1'),
    ('ext2_option22', '6', '0', 'country_ba', '1'),
    ('ext2_option22', '7', '0', 'country_bg', '1'),
    ('ext2_option22', '8', '0', 'country_hr', '1'),
    ('ext2_option22', '9', '0', 'country_cy', '1'),
    ('ext2_option22', '10', '0', 'country_cz', '1'),
    ('ext2_option22', '11', '0', 'country_dk', '1'),
    ('ext2_option22', '12', '0', 'country_ee', '1'),
    ('ext2_option22', '13', '0', 'country_fi', '1'),
    ('ext2_option22', '14', '0', 'country_fr', '1'),
    ('ext2_option22', '15', '0', 'country_de', '1'),
    ('ext2_option22', '16', '0', 'country_gr', '1'),
    ('ext2_option22', '17', '0', 'country_hu', '1'),
    ('ext2_option22', '18', '0', 'country_is', '1'),
    ('ext2_option22', '19', '0', 'country_ie', '1'),
    ('ext2_option22', '20', '0', 'country_it', '1'),
    ('ext2_option22', '21', '0', 'country_lv', '1'),
    ('ext2_option22', '22', '0', 'country_li', '1'),
    ('ext2_option22', '23', '0', 'country_lt', '1'),
    ('ext2_option22', '24', '0', 'country_lu', '1'),
    ('ext2_option22', '25', '0', 'country_mt', '1'),
    ('ext2_option22', '26', '0', 'country_mol', '1'),
    ('ext2_option22', '27', '0', 'country_mc', '1'),
    ('ext2_option22', '28', '0', 'country_me', '1'),
    ('ext2_option22', '29', '0', 'country_mk', '1'),
    ('ext2_option22', '30', '0', 'country_no', '1'),
    ('ext2_option22', '31', '0', 'country_pl', '1'),
    ('ext2_option22', '32', '0', 'country_pt', '1'),
    ('ext2_option22', '33', '0', 'country_ro', '1'),
    ('ext2_option22', '34', '0', 'country_ru', '1'),
    ('ext2_option22', '35', '0', 'country_sm', '1'),
    ('ext2_option22', '36', '0', 'country_srb', '1'),
    ('ext2_option22', '37', '0', 'country_sk', '1'),
    ('ext2_option22', '38', '0', 'country_si', '1'),
    ('ext2_option22', '39', '0', 'country_es', '1'),
    ('ext2_option22', '40', '0', 'country_se', '1'),
    ('ext2_option22', '41', '0', 'country_ch', '1'),
    ('ext2_option22', '42', '0', 'country_ua', '1'),
    ('ext2_option22', '43', '0', 'country_gbr', '1'),
    ('ext2_option22', '44', '0', 'country_vat', '1'),
    ('ext2_option22', '45', '0', 'country_ai', '1'),
    ('ext2_option22', '46', '0', 'country_aq', '1'),
    ('ext2_option22', '47', '0', 'country_bv', '1'),
    ('ext2_option22', '48', '0', 'country_km', '1'),
    ('ext2_option22', '49', '0', 'country_cv', '1'),
    ('ext2_option22', '50', '0', 'country_io', '1'),
    ('ext2_option22', '51', '0', 'country_ki', '1'),
    ('ext2_option22', '52', '0', 'country_mh', '1'),
    ('ext2_option22', '53', '0', 'country_mq', '1'),
    ('ext2_option22', '54', '0', 'country_na', '1'),
    ('ext2_option22', '55', '0', 'country_nr', '1'),
    ('ext2_option22', '56', '0', 'country_nu', '1'),
    ('ext2_option22', '57', '0', 'country_pm', '1'),
    ('ext2_option22', '58', '0', 'country_pn', '1'),
    ('ext2_option22', '59', '0', 'country_pw', '1'),
    ('ext2_option22', '60', '0', 'country_re', '1'),
    ('ext2_option22', '61', '0', 'country_sh', '1'),
    ('ext2_option22', '62', '0', 'country_sj', '1'),
    ('ext2_option22', '63', '0', 'country_sz', '1'),
    ('ext2_option22', '64', '0', 'country_tc', '1'),
    ('ext2_option22', '65', '0', 'country_tk', '1'),
    ('ext2_option22', '66', '0', 'country_um', '1'),
    ('ext2_option22', '67', '0', 'country_un', '1'),
    ('ext2_option22', '68', '0', 'country_vu', '1'),
    ('ext2_option22', '69', '0', 'country_wafu', '1'),
    ('ext2_option22', '70', '0', 'country_eh', '1'),
    ('ext2_option22', '71', '0', 'country_yt', '1'),
    ('ext2_option22', '72', '0', 'country_afg', '1'),
    ('ext2_option22', '73', '0', 'country_atg', '1'),
    ('ext2_option22', '74', '0', 'country_alg', '1'),
    ('ext2_option22', '75', '0', 'country_nlant', '1'),
    ('ext2_option22', '76', '0', 'country_ago', '1'),
    ('ext2_option22', '77', '0', 'country_eg', '1'),
    ('ext2_option22', '78', '0', 'country_arg', '1'),
    ('ext2_option22', '79', '0', 'country_am', '1'),
    ('ext2_option22', '80', '0', 'country_aus', '1'),
    ('ext2_option22', '81', '0', 'country_abw', '1'),
    ('ext2_option22', '82', '0', 'country_aze', '1'),
    ('ext2_option22', '83', '0', 'country_bh', '1'),
    ('ext2_option22', '84', '0', 'country_brb', '1'),
    ('ext2_option22', '85', '0', 'country_bi', '1'),
    ('ext2_option22', '86', '0', 'country_bj', '1'),
    ('ext2_option22', '87', '0', 'country_bgd', '1'),
    ('ext2_option22', '88', '0', 'country_bhs', '1'),
    ('ext2_option22', '89', '0', 'country_bt', '1'),
    ('ext2_option22', '90', '0', 'country_bf', '1'),
    ('ext2_option22', '91', '0', 'country_ber', '1'),
    ('ext2_option22', '92', '0', 'country_bur', '1'),
    ('ext2_option22', '93', '0', 'country_bo', '1'),
    ('ext2_option22', '94', '0', 'country_bw', '1'),
    ('ext2_option22', '95', '0', 'country_bra', '1'),
    ('ext2_option22', '96', '0', 'country_bz', '1'),
    ('ext2_option22', '97', '0', 'country_kh', '1'),
    ('ext2_option22', '98', '0', 'country_can', '1'),
    ('ext2_option22', '99', '0', 'country_cf', '1'),
    ('ext2_option22', '100', '0', 'country_aucc', '1'),
    ('ext2_option22', '101', '0', 'country_td', '1'),
    ('ext2_option22', '102', '0', 'country_chn', '1'),
    ('ext2_option22', '103', '0', 'country_cl', '1'),
    ('ext2_option22', '104', '0', 'country_cm', '1'),
    ('ext2_option22', '105', '0', 'country_cog', '1'),
    ('ext2_option22', '106', '0', 'country_coi', '1'),
    ('ext2_option22', '107', '0', 'country_co', '1'),
    ('ext2_option22', '108', '0', 'country_cr', '1'),
    ('ext2_option22', '109', '0', 'country_cu', '1'),
    ('ext2_option22', '110', '0', 'country_cvi', '1'),
    ('ext2_option22', '111', '0', 'country_auch', '1'),
    ('ext2_option22', '112', '0', 'country_dj', '1'),
    ('ext2_option22', '113', '0', 'country_do', '1'),
    ('ext2_option22', '114', '0', 'country_dubai', '1'),
    ('ext2_option22', '115', '0', 'country_ec', '1'),
    ('ext2_option22', '116', '0', 'country_gq', '1'),
    ('ext2_option22', '117', '0', 'country_er', '1'),
    ('ext2_option22', '118', '0', 'country_et', '1'),
    ('ext2_option22', '119', '0', 'country_frgy', '1'),
    ('ext2_option22', '120', '0', 'country_fj', '1'),
    ('ext2_option22', '121', '0', 'country_gbfi', '1'),
    ('ext2_option22', '122', '0', 'country_fro', '1'),
    ('ext2_option22', '123', '0', 'country_ga', '1'),
    ('ext2_option22', '124', '0', 'country_gm', '1'),
    ('ext2_option22', '125', '0', 'country_gw', '1'),
    ('ext2_option22', '126', '0', 'country_ge', '1'),
    ('ext2_option22', '127', '0', 'country_gh', '1'),
    ('ext2_option22', '128', '0', 'country_gbz', '1'),
    ('ext2_option22', '129', '0', 'country_gl', '1'),
    ('ext2_option22', '130', '0', 'country_gd', '1'),
    ('ext2_option22', '131', '0', 'country_gt', '1'),
    ('ext2_option22', '132', '0', 'country_gn', '1'),
    ('ext2_option22', '133', '0', 'country_gy', '1'),
    ('ext2_option22', '134', '0', 'country_ht', '1'),
    ('ext2_option22', '135', '0', 'country_hk', '1'),
    ('ext2_option22', '136', '0', 'country_heart', '1'),
    ('ext2_option22', '137', '0', 'country_hn', '1'),
    ('ext2_option22', '138', '0', 'country_ci', '1'),
    ('ext2_option22', '139', '0', 'country_in', '1'),
    ('ext2_option22', '140', '0', 'country_id', '1'),
    ('ext2_option22', '141', '0', 'country_ir', '1'),
    ('ext2_option22', '142', '0', 'country_iq', '1'),
    ('ext2_option22', '143', '0', 'country_il', '1'),
    ('ext2_option22', '144', '0', 'country_jm', '1'),
    ('ext2_option22', '145', '0', 'country_jor', '1'),
    ('ext2_option22', '146', '0', 'country_jp', '1'),
    ('ext2_option22', '147', '0', 'country_kz', '1'),
    ('ext2_option22', '148', '0', 'country_ke', '1'),
    ('ext2_option22', '149', '0', 'country_kr', '1'),
    ('ext2_option22', '150', '0', 'country_nk', '1'),
    ('ext2_option22', '151', '0', 'country_kw', '1'),
    ('ext2_option22', '152', '0', 'country_ky', '1'),
    ('ext2_option22', '153', '0', 'country_kg', '1'),
    ('ext2_option22', '154', '0', 'country_la', '1'),
    ('ext2_option22', '155', '0', 'country_lr', '1'),
    ('ext2_option22', '156', '0', 'country_ly', '1'),
    ('ext2_option22', '157', '0', 'country_lc', '1'),
    ('ext2_option22', '158', '0', 'country_lb', '1'),
    ('ext2_option22', '159', '0', 'country_ls', '1'),
    ('ext2_option22', '160', '0', 'country_lk', '1'),
    ('ext2_option22', '161', '0', 'country_mg', '1'),
    ('ext2_option22', '162', '0', 'country_mr', '1'),
    ('ext2_option22', '163', '0', 'country_mv', '1'),
    ('ext2_option22', '164', '0', 'country_mx', '1'),
    ('ext2_option22', '165', '0', 'country_ml', '1'),
    ('ext2_option22', '166', '0', 'country_mys', '1'),
    ('ext2_option22', '167', '0', 'country_mw', '1'),
    ('ext2_option22', '168', '0', 'country_mn', '1'),
    ('ext2_option22', '169', '0', 'country_mo', '1'),
    ('ext2_option22', '170', '0', 'country_ma', '1'),
    ('ext2_option22', '171', '0', 'country_mz', '1'),
    ('ext2_option22', '172', '0', 'country_mu', '1'),
    ('ext2_option22', '173', '0', 'country_mm', '1'),
    ('ext2_option22', '174', '0', 'country_nh', '1'),
    ('ext2_option22', '175', '0', 'country_np', '1'),
    ('ext2_option22', '176', '0', 'country_nl', '1'),
    ('ext2_option22', '177', '0', 'country_nf', '1'),
    ('ext2_option22', '178', '0', 'country_ng', '1'),
    ('ext2_option22', '179', '0', 'country_ni', '1'),
    ('ext2_option22', '180', '0', 'country_nig', '1'),
    ('ext2_option22', '181', '0', 'country_ot', '1'),
    ('ext2_option22', '182', '0', 'country_nz', '1'),
    ('ext2_option22', '183', '0', 'country_om', '1'),
    ('ext2_option22', '184', '0', 'country_pk', '1'),
    ('ext2_option22', '185', '0', 'country_pa', '1'),
    ('ext2_option22', '186', '0', 'country_py', '1'),
    ('ext2_option22', '187', '0', 'country_pe', '1'),
    ('ext2_option22', '188', '0', 'country_pf', '1'),
    ('ext2_option22', '189', '0', 'country_ph', '1'),
    ('ext2_option22', '190', '0', 'country_pg', '1'),
    ('ext2_option22', '191', '0', 'country_pr', '1'),
    ('ext2_option22', '192', '0', 'country_qa', '1'),
    ('ext2_option22', '193', '0', 'country_za', '1'),
    ('ext2_option22', '194', '0', 'country_rw', '1'),
    ('ext2_option22', '195', '0', 'country_sv', '1'),
    ('ext2_option22', '196', '0', 'country_sa', '1'),
    ('ext2_option22', '197', '0', 'country_sb', '1'),
    ('ext2_option22', '198', '0', 'country_sn', '1'),
    ('ext2_option22', '199', '0', 'country_sc', '1'),
    ('ext2_option22', '200', '0', 'country_sg', '1'),
    ('ext2_option22', '201', '0', 'country_sl', '1'),
    ('ext2_option22', '202', '0', 'country_so', '1'),
    ('ext2_option22', '203', '0', 'country_vn', '1'),
    ('ext2_option22', '204', '0', 'country_st', '1'),
    ('ext2_option22', '205', '0', 'country_sd', '1'),
    ('ext2_option22', '206', '0', 'country_sr', '1'),
    ('ext2_option22', '207', '0', 'country_sy', '1'),
    ('ext2_option22', '208', '0', 'country_tz', '1'),
    ('ext2_option22', '209', '0', 'country_th', '1'),
    ('ext2_option22', '210', '0', 'country_tj', '1'),
    ('ext2_option22', '211', '0', 'country_tm', '1'),
    ('ext2_option22', '212', '0', 'country_tg', '1'),
    ('ext2_option22', '213', '0', 'country_to', '1'),
    ('ext2_option22', '214', '0', 'country_tt', '1'),
    ('ext2_option22', '215', '0', 'country_tn', '1'),
    ('ext2_option22', '216', '0', 'country_tr', '1'),
    ('ext2_option22', '217', '0', 'country_tv', '1'),
    ('ext2_option22', '218', '0', 'country_tw', '1'),
    ('ext2_option22', '219', '0', 'country_ae', '1'),
    ('ext2_option22', '220', '0', 'country_ug', '1'),
    ('ext2_option22', '221', '0', 'country_xk', '1'),
    ('ext2_option22', '222', '0', 'country_uy', '1'),
    ('ext2_option22', '223', '0', 'country_us', '1'),
    ('ext2_option22', '224', '0', 'country_uz', '1'),
    ('ext2_option22', '225', '0', 'country_svin', '1'),
    ('ext2_option22', '226', '0', 'country_ve', '1'),
    ('ext2_option22', '227', '0', 'country_bvi', '1'),
    ('ext2_option22', '228', '0', 'country_wf', '1'),
    ('ext2_option22', '229', '0', 'country_ws', '1'),
    ('ext2_option22', '230', '0', 'country_ye', '1'),
    ('ext2_option22', '231', '0', 'country_zm', '1'),
    ('ext2_option22', '232', '0', 'country_zw', '1'),
    ('ext2_option22', '233', '0', 'country_zr', '1');

UPDATE `_sql_version` SET `revision`=25, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -25--2024-10-07-13:30---------------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '1',
    `modified_by` = 'SZOF-4156',
    `modified_on` = NOW(),
    `note` = 'Prev value: 0'
WHERE `setting_id` = 'automaticLoadOfExtPositionData';

CREATE TABLE IF NOT EXISTS `ext_position_details` (
`row_id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
`position_name` VARCHAR(128) COMMENT 'Munkakör magyarul',
`position_name_english` VARCHAR(128) COMMENT 'Munkakör angolul',
`position_category_name` VARCHAR(64) COMMENT 'Besorolási kategória megnevezése',
`position_category_code` VARCHAR(32) NULL COMMENT 'Besorolási kategória kódja',
`physical_mental_category` VARCHAR(32) NULL COMMENT 'Fizikai szellemi besorolás',
`accounting_category` VARCHAR(32) NULL COMMENT 'Könyvelési kategória',
`kornferry_grade` VARCHAR(32) NULL COMMENT 'Kornferry grade',
`kornferry_position_code` VARCHAR(32) NULL COMMENT 'Kornferry munkakör kód',
`status` TINYINT NOT NULL COMMENT 'status',
`created_by` VARCHAR(32) COMMENT 'created_by',
`created_on` DATETIME COMMENT 'created_on',
`modified_by` VARCHAR(32) COMMENT 'modified_by',
`modified_on` DATETIME COMMENT 'modified_on',
INDEX (`position_name`),
INDEX (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Munkakörhöz kapcsolt mezők';

INSERT INTO ext_position_details (position_name, position_name_english, position_category_name, position_category_code, physical_mental_category, accounting_category, kornferry_grade, kornferry_position_code, status, created_by, created_on) VALUES
('Kontroling Vezető','Controlling Manager','Executive','E','INDIRECT','Fix','16','FAH',2,'DEV-4156',NOW()),
('Kontroller','Controller','Specialists','S','INDIRECT','Fix','15','FAH',2,'DEV-4156',NOW()),
('Könyvelő','Accountant','Specialists','S','INDIRECT','Fix','13','FAA',2,'DEV-4156',NOW()),
('Pénzügyi és IT igazgató','Finance and IT director','Executive','E','INDIRECT','Fix','18','FAZ',2,'DEV-4156',NOW()),
('Pénzügyi és Számviteli Vezető','Finance and Accounting Manager','Executive','E','INDIRECT','Fix','16','FAA',2,'DEV-4156',NOW()),
('Pénzügyi Ügyintéző','Finance Administrator','Specialists','S','INDIRECT','Fix','13','FAA',2,'DEV-4156',NOW()),
('Csomagoló Áruátvevő','Co-pack Checker','Warehouse Staff','W','DIRECT','Változó','9','LSB',2,'DEV-4156',NOW()),
('Csomagoló Vezető','Co-Pack Leader','Executive','E','INDIRECT','Fix','16','LSB',2,'DEV-4156',NOW()),
('Csomagoló','Co-Pack Worker','Warehouse Staff','W','DIRECT','Változó','6','PRA',2,'DEV-4156',NOW()),
('Adminisztrációs Csoportvezető','Administration Team leader','Admin staff','A','INDIRECT','Fix','13','ASA',2,'DEV-4156',NOW()),
('Adminisztrációs és Reklamációs Csoportvezető','Administration and Claim Team leader','Admin staff','A','INDIRECT','Fix','11','ASA',2,'DEV-4156',NOW()),
('Adminisztrációs Vezető','Administration Manager','Admin staff','A','INDIRECT','Fix','12','CSB',2,'DEV-4156',NOW()),
('Adminisztrátor','Administrator','Admin staff','A','INDIRECT','Fix','11','ASA',2,'DEV-4156',NOW()),
('Áruátvevő','Checker','Warehouse Staff','W','DIRECT','Változó','9','LSB',2,'DEV-4156',NOW()),
('Logisztikai Menedzser','Logistics Manager','Executive','E','INDIRECT','Fix','17','LSB',2,'DEV-4156',NOW()),
('Magasemelésű Targoncavezető','RT driver','Warehouse Staff','W','DIRECT','Változó','10','LSB',2,'DEV-4156',NOW()),
('Operációs Műszakvezető','Shift Leader','Executive','E','INDIRECT','Fix','14','LSB',2,'DEV-4156',NOW()),
('Raktárvezető','Warehouse Manager','Executive','E','INDIRECT','Fix','13','LSB',2,'DEV-4156',NOW()),
('Targoncás Áruátvevő','FT Driver Checker','Warehouse Staff','W','DIRECT','Változó','9','LSB',2,'DEV-4156',NOW()),
('WMS Operátor','WMS Operator','Specialists','S','INDIRECT','Fix','12','LSB',2,'DEV-4156',NOW()),
('Minőségbiztosítási specialista','Quality Specialist','Specialists','S','INDIRECT','Fix','14','QAA',2,'DEV-4156',NOW()),
('Minőségbiztosítási V7ezető','Quality Manager','Executive','E','INDIRECT','Fix','17','QAA',2,'DEV-4156',NOW()),
('Értékesítési Vezető','Sales Manager','Executive','E','INDIRECT','Fix','17','SLZ',2,'DEV-4156',NOW()),
('Értékesítési Specialista','Sales Specialist','Specialists','S','INDIRECT','Fix','14','SLA',2,'DEV-4156',NOW()),
('Számlázási Koordinátor','Invoicing Coordinator','Specialists','S','INDIRECT','Fix','12','FAA',2,'DEV-4156',NOW()),
('IT & SAP key user','IT & SAP key user','Specialists','S','INDIRECT','Fix','12','ITC',2,'DEV-4156',NOW()),
('IT Csoportvezető','IT Team leader','Executive','E','INDIRECT','Fix','16','ITC',2,'DEV-4156',NOW()),
('HR Adminisztrátor','HR Administrator','Admin staff','A','INDIRECT','Fix','12','HRA',2,'DEV-4156',NOW()),
('HR Business Partner','HR Business Partner','Specialists','S','INDIRECT','Fix','15','HRA',2,'DEV-4156',NOW()),
('HR Specialista','HR Specialist','Specialists','S','INDIRECT','Fix','13','HRA',2,'DEV-4156',NOW()),
('HR Vezető','HR Manager','Executive','E','INDIRECT','Fix','17','HRZ',2,'DEV-4156',NOW()),
('Operációs Igazgató','Operation Director','Executive','E','INDIRECT','Fix','19','EMC',2,'DEV-4156',NOW()),
('Raktári tréner','Warehouse Trainer','Specialists','S','INDIRECT','Fix','','',2,'DEV-4156',NOW()),
('Recepciós Ügyintéző','Receptionist','Others','O','DIRECT','Fix','12','ASA',2,'DEV-4156',NOW()),
('Göngyöleg Adminisztrációs Csoportvezető','CSO Team leader','Admin staff','A','INDIRECT','Fix','14','ASA',2,'DEV-4156',NOW()),
('Göngyölegraktáros Targoncavezető','Pallet Forklift driver','Warehouse Staff','W','DIRECT','Változó','9','LSB',2,'DEV-4156',NOW()),
('Raklap és Göngyöleggazdálkodási Vezető','CSO and Pallet Manager','Executive','E','INDIRECT','Fix','17','LSB',2,'DEV-4156',NOW()),
('Akkumulátortöltő terület Koordinátor','Battery Charging Area Coordinator','Others','O','DIRECT','Fix','10','LSB',2,'DEV-4156',NOW()),
('Biztonsági és Karbantartási Koordinátor','Security and Facility Coordinator','Others','O','DIRECT','Fix','11','PTB',2,'DEV-4156',NOW()),
('Biztonsági és Üzemeltetési Vezető','Security and Facility Manager','Executive','E','INDIRECT','Fix','16','PTB',2,'DEV-4156',NOW()),
('CCTV Operátor','CCTV Operator','Others','O','DIRECT','Fix','11','ASA',2,'DEV-4156',NOW()),
('Karbantartó','Maintainer','Others','O','DIRECT','Fix','11','PRB',2,'DEV-4156',NOW()),
('Üzemeltetési Vezető','Facility Manager','Others','O','DIRECT','Fix','15','PTB',2,'DEV-4156',NOW()),
('készletellenőr - készletgazda','Inventory controller','Admin staff','A','INDIRECT','Fix','9','LSB',2,'DEV-4156',NOW()),
('Készletgazda','Inventory controller','Admin staff','A','INDIRECT','Fix','9','LSB',2,'DEV-4156',NOW()),
('Készletgazdálkodási Csoportvezető','Inventory team leader','Admin staff','A','INDIRECT','Fix','13','CSB',2,'DEV-4156',NOW()),
('Logisztikai Támogató Menedzser','Logistics Support Manager','Executive','E','INDIRECT','Fix','17','LSZ',2,'DEV-4156',NOW()),
('Projekt Koordinátor','Project Coordinator','Specialists','S','INDIRECT','Fix','14','ASA',2,'DEV-4156',NOW()),
('Reklamációkezelő','Claim Administrator','Admin staff','A','INDIRECT','Fix','12','CSB',2,'DEV-4156',NOW()),
('Reklamációs Csoportvezető','Claims Team leader','Admin staff','A','INDIRECT','Fix','13','CSB',2,'DEV-4156',NOW()),
('Diszpécser szolgálati Vezető','Dispatcher Team leader','Executive','E','INDIRECT','Fix','15','LSC',2,'DEV-4156',NOW()),
('Diszpécser-Gépjármű üzemeltetési Technikus','Dispatcher-truck Technician','Specialists','S','INDIRECT','Fix','13','LSC',2,'DEV-4156',NOW()),
('Diszpécser-Gépjárművezető','Dispatcher-Driver','Specialists','S','INDIRECT','Fix','14','LSC',2,'DEV-4156',NOW()),
('Fuvarszervező Diszpécser','Dispatcher','Specialists','S','INDIRECT','Fix','14','LSC',2,'DEV-4156',NOW()),
('Gépjárművezető','Driver','Drivers','D','DIRECT','Változó','10','LSC',2,'DEV-4156',NOW()),
('Kiszállítási Koordinátor','Delivery Coordinator','Specialists','S','INDIRECT','Fix','12','LSC',2,'DEV-4156',NOW()),
('Operatív Diszpécser','Operative Dispatcher','Specialists','S','INDIRECT','Fix','13','LSC',2,'DEV-4156',NOW()),
('Rámpa Koordinátor','Delivery Coordinator','Specialists','S','INDIRECT','Fix','12','LSC',2,'DEV-4156',NOW()),
('Senior Diszpécser','Senior Dispatcher','Specialists','S','INDIRECT','Fix','14','LSC',2,'DEV-4156',NOW()),
('Szállítási Adminisztrációs Csoportvezető','Transport Administration Team leader','Admin staff','A','INDIRECT','Fix','14','LSC',2,'DEV-4156',NOW()),
('Szállítási Igazgató','Transport Director','Executive','E','INDIRECT','Fix','18','LSC',2,'DEV-4156',NOW()),
('Szállítási Projekt Kordinátor','Transport Project Coordinator','Specialists','S','INDIRECT','Fix','14','LSC',2,'DEV-4156',NOW()),
('Szállítási Projektvezető','Transport Project Coordinator','Specialists','S','INDIRECT','Fix','14','LSC',2,'DEV-4156',NOW()),
('Ügyvezető Igazgató','CEO','Executive','E','INDIRECT','Fix','','',2,'DEV-4156',NOW()),
('Üzletfejlesztési Vezető','Business Development Manager','Executive','E','INDIRECT','Fix','17','SLZ',2,'DEV-4156',NOW()),
('Komissiós','Picker','Warehouse Staff','W','DIRECT','Változó','9','LSB',2,'DEV-4156',NOW());

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
('option13', '70', '0', 'shift_leader_hu', '1'),
('option14', '70', '0', 'shift_leader_en', '1'),
('option13', '71', '0', 'commissioned_hu', '1'),
('option14', '71', '0', 'commissioned_en', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-base', 'shift_leader_hu', 'Operációs Műszakvezető', 1),
('hu', 'ttwa-base', 'shift_leader_en', 'Shift Leader', 1),
('hu', 'ttwa-base', 'commissioned_hu', 'Komissiós', 1),
('hu', 'ttwa-base', 'commissioned_en', 'Commissioned', 1);

UPDATE `_sql_version` SET `revision`=26, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -26--2024-10-09-13:00---------------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '1',
    `modified_by` = 'SZOF-4196',
    `modified_on` = NOW(),
    `note` = 'Prev value: 0'
WHERE `setting_id` = 'automaticLoadOfExtEngOrgName';

CREATE TABLE IF NOT EXISTS `ext_organisation_details` (
`row_id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
`organisation_name` VARCHAR(128) COMMENT 'Munkakör magyarul',
`organisation_name_english` VARCHAR(128) COMMENT 'Munkakör angolul',
`status` TINYINT NOT NULL COMMENT 'status',
`created_by` VARCHAR(32) COMMENT 'created_by',
`created_on` DATETIME COMMENT 'created_on',
`modified_by` VARCHAR(32) COMMENT 'modified_by',
`modified_on` DATETIME COMMENT 'modified_on',
INDEX (`organisation_name`),
INDEX (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Munkakörhöz kapcsolt mezők';

INSERT INTO ext_organisation_details (organisation_name, organisation_name_english, status, created_by, created_on) VALUES
('Logisztikai Támogatás','Logistic Support',2,'DEV-4196',NOW()),
('Raktári Operáció - Vecsés','WH Operation Vecsés',2,'DEV-4196',NOW()),
('Pénzügy és IT','Finance and IT',2,'DEV-4196',NOW()),
('Kontrolling','Controlling',2,'DEV-4196',NOW()),
('Értékesítés','Sales',2,'DEV-4196',NOW()),
('HR','HR',2,'DEV-4196',NOW()),
('Könyvelés','Accounting',2,'DEV-4196',NOW()),
('Biztonság és Karbantartás','Security and Facility',2,'DEV-4196',NOW()),
('IT','IT',2,'DEV-4196',NOW()),
('Göngyöleg és raklap adminisztráció','CSO and Pallet Management',2,'DEV-4196',NOW()),
('Szállítás','Transport',2,'DEV-4196',NOW()),
('Raktári Operáció - Gyál1','WH Operation Gyál1',2,'DEV-4196',NOW()),
('Raktári Operáció - Gyál2','WH Operation Gyál2',2,'DEV-4196',NOW()),
('Raktári Operáció - Szigetszentmiklós','WH Operation Szigetszentmiklós',2,'DEV-4196',NOW()),
('Minőségbiztosítás','Quality',2,'DEV-4196',NOW()),
('Ügyvezető','CEO',2,'DEV-4196',NOW()),
('Raktári Operáció - Fagyos','WH Operation Frozen',2,'DEV-4196',NOW()),
('Raktári Operáció - VAS','WH Operation VAS',2,'DEV-4196',NOW());

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
('option24', '1', '0','logistic_support_hu', '1'),
('option24', '2', '0','wh_operation_vecses_hu', '1'),
('option24', '3', '0','finance_and_it_hu', '1'),
('option24', '4', '0','controlling_hu', '1'),
('option24', '5', '0','sales_hu', '1'),
('option24', '6', '0','hr_hu', '1'),
('option24', '7', '0','accounting_hu', '1'),
('option24', '8', '0','security_and_facility_hu', '1'),
('option24', '9', '0','it_hu', '1'),
('option24', '10', '0','cso_and_pallet_manag_hu', '1'),
('option24', '11', '0','transport_hu', '1'),
('option24', '12', '0','wh_operation_gyal1_hu', '1'),
('option24', '13', '0','wh_operation_gyal2_hu', '1'),
('option24', '14', '0','wh_operation_szigetsz_hu', '1'),
('option24', '15', '0','quality_hu', '1'),
('option24', '16', '0','ceo_hu', '1'),
('option24', '17', '0','wh_operation_frozen_hu', '1'),
('option24', '18', '0','wh_operation_vas_hu', '1'),
('option25', '1', '0','logistic_support_en', '1'),
('option25', '2', '0','wh_operation_vecses_en', '1'),
('option25', '3', '0','finance_and_it_en', '1'),
('option25', '4', '0','controlling_en', '1'),
('option25', '5', '0','sales_en', '1'),
('option25', '6', '0','hr_en', '1'),
('option25', '7', '0','accounting_en', '1'),
('option25', '8', '0','security_and_facility_en', '1'),
('option25', '9', '0','it_en', '1'),
('option25', '10', '0','cso_and_pallet_manag_en', '1'),
('option25', '11', '0','transport_en', '1'),
('option25', '12', '0','wh_operation_gyal1_en', '1'),
('option25', '13', '0','wh_operation_gyal2_en', '1'),
('option25', '14', '0','wh_operation_szigetsz_en', '1'),
('option25', '15', '0','quality_en', '1'),
('option25', '16', '0','ceo_en', '1'),
('option25', '17', '0','wh_operation_frozen_en', '1'),
('option25', '18', '0','wh_operation_vas_en', '1');

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-base', 'logistic_support_hu','Logisztikai Támogatás', 1),
('hu', 'ttwa-base', 'wh_operation_vecses_hu','Raktári Operáció - Vecsés', 1),
('hu', 'ttwa-base', 'finance_and_it_hu','Pénzügy és IT', 1),
('hu', 'ttwa-base', 'controlling_hu','Kontrolling', 1),
('hu', 'ttwa-base', 'sales_hu','Értékesítés', 1),
('hu', 'ttwa-base', 'hr_hu','HR', 1),
('hu', 'ttwa-base', 'accounting_hu','Könyvelés', 1),
('hu', 'ttwa-base', 'security_and_facility_hu','Biztonság és Karbantartás', 1),
('hu', 'ttwa-base', 'it_hu','IT', 1),
('hu', 'ttwa-base', 'cso_and_pallet_manag_hu','Göngyöleg és raklap adminisztráció', 1),
('hu', 'ttwa-base', 'transport_hu','Szállítás', 1),
('hu', 'ttwa-base', 'wh_operation_gyal1_hu','Raktári Operáció - Gyál1', 1),
('hu', 'ttwa-base', 'wh_operation_gyal2_hu','Raktári Operáció - Gyál2', 1),
('hu', 'ttwa-base', 'wh_operation_szigetsz_hu','Raktári Operáció - Szigetszentmiklós', 1),
('hu', 'ttwa-base', 'quality_hu','Minőségbiztosítás', 1),
('hu', 'ttwa-base', 'ceo_hu','Ügyvezető', 1),
('hu', 'ttwa-base', 'wh_operation_frozen_hu','Raktári Operáció - Fagyos', 1),
('hu', 'ttwa-base', 'wh_operation_vas_hu','Raktári Operáció - VAS', 1),
('hu', 'ttwa-base', 'logistic_support_en','Logistic Support', 1),
('hu', 'ttwa-base', 'wh_operation_vecses_en','WH Operation Vecsés', 1),
('hu', 'ttwa-base', 'finance_and_it_en','Finance and IT', 1),
('hu', 'ttwa-base', 'controlling_en','Controlling', 1),
('hu', 'ttwa-base', 'sales_en','Sales', 1),
('hu', 'ttwa-base', 'hr_en','HR', 1),
('hu', 'ttwa-base', 'accounting_en','Accounting', 1),
('hu', 'ttwa-base', 'security_and_facility_en','Security and Facility', 1),
('hu', 'ttwa-base', 'it_en','IT', 1),
('hu', 'ttwa-base', 'cso_and_pallet_manag_en','CSO and Pallet Management', 1),
('hu', 'ttwa-base', 'transport_en','Transport', 1),
('hu', 'ttwa-base', 'wh_operation_gyal1_en','WH Operation Gyál1', 1),
('hu', 'ttwa-base', 'wh_operation_gyal2_en','WH Operation Gyál2', 1),
('hu', 'ttwa-base', 'wh_operation_szigetsz_en','WH Operation Szigetszentmiklós', 1),
('hu', 'ttwa-base', 'quality_en','Quality', 1),
('hu', 'ttwa-base', 'ceo_en','CEO', 1),
('hu', 'ttwa-base', 'wh_operation_frozen_en','WH Operation Frozen', 1),
('hu', 'ttwa-base', 'wh_operation_vas_en','WH Operation VAS', 1);

UPDATE `_sql_version` SET `revision`=27, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -27--2024-10-09-13:30---------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `cost_details` (
`row_id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
`cost_name` VARCHAR(128) COMMENT 'Költséghely név',
`cost_code` VARCHAR(128) COMMENT 'Költséghely kód',
`status` TINYINT NOT NULL COMMENT 'status',
`created_by` VARCHAR(32) COMMENT 'created_by',
`created_on` DATETIME COMMENT 'created_on',
`modified_by` VARCHAR(32) COMMENT 'modified_by',
`modified_on` DATETIME COMMENT 'modified_on',
INDEX (`cost_name`),
INDEX (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Költséghelyhez kapcsolt mezők';

INSERT INTO `cost_details` (cost_name, cost_code, status, created_by, created_on) VALUES
('Company Head Office','4110AO01',2,'SZOF-4251',NOW()),
('DC Shared Gyál 1','4112SO62',2,'SZOF-4251',NOW()),
('DC Shared Szigetszentmiklós','4117SO62',2,'SZOF-4251',NOW()),
('DC Support Gyál1','4112SO61',2,'SZOF-4251',NOW()),
('DI Plan.disp HHU GY','4112TD02',2,'SZOF-4251',NOW()),
('Drivers HHU Gyál','4112TF21',2,'SZOF-4251',NOW()),
('Fleet Manag HHU GY','4112TF01',2,'SZOF-4251',NOW()),
('Information Technology','4110AO06',2,'SZOF-4251',NOW()),
('Loaders XD HHU GY','4112TF22',2,'SZOF-4251',NOW()),
('Logistics Head Office','4110AO12',2,'SZOF-4251',NOW()),
('OP admin / Pallet mngmnt','4110LA11',2,'SZOF-4251',NOW()),
('Oper. Fulfill Center Szigetszentmiklós','4117FO01',2,'SZOF-4251',NOW()),
('Operations VAS Gyál','4112VC01',2,'SZOF-4251',NOW()),
('Operations VAS Szigetszentmiklós','4117VC01',2,'SZOF-4251',NOW()),
('OperDispatch HHU GY','4112TD00',2,'SZOF-4251',NOW()),
('Pallet warehouse','4110LW10',2,'SZOF-4251',NOW()),
('Transport Admin HHU','4110TA32',2,'SZOF-4251',NOW()),
('Transport Manag HHU','4110TA31',2,'SZOF-4251',NOW()),
('Warehouse CW 2 Dry Gyál','4112LW07',2,'SZOF-4251',NOW()),
('Warehouse CW Dry Szigetszentmiklós','4117LW01',2,'SZOF-4251',NOW()),
('Warehouse CW/XD Chilled Vecsés','4113LW02',2,'SZOF-4251',NOW()),
('Warehouse CW/XD Dry Gyál 2','4112LW13',2,'SZOF-4251',NOW()),
('Warehouse CW/XD Frozen Gyál 1','4112LW12',2,'SZOF-4251',NOW()),
('HH Global Solutions Management&Others','4210HA30',2,'SZOF-4251',NOW()),
('Ocean&Air Office Global HU GY','4212GO10',2,'SZOF-4251',NOW()),
('Road Office Global Solutions HU BU','4215GR10',2,'SZOF-4251',NOW()),
('Road Office Global Solutions HU GY','4212GR10',2,'SZOF-4251',NOW());

TRUNCATE TABLE `cost_center`;

INSERT INTO `cost_center` (`cost_center_id`, `cost_center_name`, `company_id`, `payroll_id`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES
('4110AO01','4110AO01','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112SO62','4112SO62','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4117SO62','4117SO62','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112SO61','4112SO61','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112TD02','4112TD02','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112TF21','4112TF21','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112TF01','4112TF01','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4110AO06','4110AO06','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112TF22','4112TF22','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4110AO12','4110AO12','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4110LA11','4110LA11','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4117FO01','4117FO01','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112VC01','4112VC01','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4117VC01','4117VC01','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112TD00','4112TD00','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4110LW10','4110LW10','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4110TA32','4110TA32','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4110TA31','4110TA31','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112LW07','4112LW07','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4117LW01','4117LW01','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4113LW02','4113LW02','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112LW13','4112LW13','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4112LW12','4112LW12','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4210HA30','4210HA30','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4212GO10','4212GO10','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4215GR10','4215GR10','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW()),
('4212GR10','4212GR10','ALL','ALL','1915-01-01','2038-01-01',2,'SZOF-4251',NOW());

UPDATE employee_cost ec
    JOIN cost c
    ON ec.cost_id = c.cost_id
        AND NOW() BETWEEN c.valid_from AND c.valid_to
        AND c.`status` = 2
    LEFT JOIN cost_details cd
    ON cd.cost_name = c.cost_name
        AND cd.`status` = 2
SET
    ec.cost_center_id = IFNULL(cd.cost_code, '0'),
    ec.modified_by = 'SZOF-4251',
    ec.modified_on = NOW()
WHERE
    ec.`status` = 2
  AND NOW() BETWEEN ec.valid_from AND ec.valid_to;

UPDATE
    dictionary dic
SET
    dic.dict_value = 'Költséghely kód'
WHERE
    dic.dict_id = 'costcenter'
  AND lang = 'hu'
  AND valid = 1;

UPDATE
    dictionary dic
SET
    dic.dict_value = 'Cost code'
WHERE
    dic.dict_id = 'costcenter'
  AND lang = 'en'
  AND valid = 1;

UPDATE
    `app_settings`
SET `setting_value` = '1',
    `modified_by` = 'SZOF-4251',
    `modified_on` = NOW(),
    `note` = 'Prev value: 0'
WHERE `setting_id` = 'automaticLoadOfCostCode';

UPDATE `_sql_version` SET `revision`=28, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -28--2024-10-09-16:30---------------------------------------------------------------

UPDATE
    `option_config`
SET
    `status` = 7,
    `modified_by` = 'SZOF-4251',
    `modified_on` = NOW()
WHERE
    option_id IN ('option22','option23');

UPDATE `_sql_version` SET `revision`=29, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -29--2024-10-09-16:30---------------------------------------------------------------

UPDATE `app_lookup` SET `order` = '1' WHERE `lookup_id` = 'option2' AND `dict_id` = 'country_hu' AND `valid` = '1';
UPDATE `app_lookup` SET `order` = '1' WHERE `lookup_id` = 'option3' AND `dict_id` = 'nat_hun' AND `valid` = '1';
UPDATE `app_lookup` SET `order` = '1' WHERE `lookup_id` = 'option4' AND `dict_id` = 'nat_hun' AND `valid` = '1';
UPDATE `app_lookup` SET `order` = '1' WHERE `lookup_id` = 'ext2_option2' AND `dict_id` = 'country_hu' AND `valid` = '1';
UPDATE `app_lookup` SET `order` = '1' WHERE `lookup_id` = 'ext2_option12' AND `dict_id` = 'country_hu' AND `valid` = '1';
UPDATE `app_lookup` SET `order` = '1' WHERE `lookup_id` = 'ext2_option22' AND `dict_id` = 'country_hu' AND `valid` = '1';

UPDATE `_sql_version` SET `revision`=30, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -30--2024-10-28-11:00---------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '0', `modified_by` = 'SZOF-4391', `modified_on` = NOW(), `note` = 'Prev. val.: 1, Szerződések fülön munkaviszony megszűnése két mező megjelenítése Szerződés tabon 2 mezo munkaviszony megszűnése' WHERE `setting_id` = 'ec_end_on_employeecontracttab';

UPDATE `_sql_version` SET `revision`=31, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -31--2024-11-04-13:00---------------------------------------------------------------

DROP TABLE `ext_position_details`;

CREATE TABLE IF NOT EXISTS `ext_position_details` (
`row_id` INT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
`position_name` VARCHAR(128) COMMENT 'Munkakör magyarul',
`position_name_english` VARCHAR(128) COMMENT 'Munkakör angolul',
`position_category_name` VARCHAR(64) COMMENT 'Besorolási kategória megnevezése',
`position_category_code` VARCHAR(32) NULL COMMENT 'Besorolási kategória kódja',
`physical_mental_category` VARCHAR(32) NULL COMMENT 'Fizikai szellemi besorolás',
`accounting_category` VARCHAR(32) NULL COMMENT 'Könyvelési kategória',
`status` TINYINT NOT NULL COMMENT 'status',
`created_by` VARCHAR(32) COMMENT 'created_by',
`created_on` DATETIME COMMENT 'created_on',
`modified_by` VARCHAR(32) COMMENT 'modified_by',
`modified_on` DATETIME COMMENT 'modified_on',
INDEX (`position_name`),
INDEX (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='Munkakörhöz kapcsolt mezők';

INSERT INTO ext_position_details (position_name, position_name_english, position_category_name, position_category_code, physical_mental_category, accounting_category, status, created_by, created_on) VALUES
('Kontroling Vezető','Controlling Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Kontroller','Controller','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Könyvelő','Accountant','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Pénzügyi és IT igazgató','Finance and IT director','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Pénzügyi és Számviteli Vezető','Finance and Accounting Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Pénzügyi Ügyintéző','Finance Administrator','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Csomagoló Áruátvevő','Co-pack Checker','Warehouse Staff','W','DIRECT','Változó',2,'SZOF-4393',NOW()),
('Csomagoló Vezető','Co-Pack Leader','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Csomagoló','Co-Pack Worker','Warehouse Staff','W','DIRECT','Változó',2,'SZOF-4393',NOW()),
('Adminisztrációs Csoportvezető','Administration Team leader','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Adminisztrációs és Reklamációs Csoportvezető','Administration and Claim Team leader','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Adminisztrációs Vezető','Administration Manager','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Adminisztrátor','Administrator','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Áruátvevő','Checker','Warehouse Staff','W','DIRECT','Változó',2,'SZOF-4393',NOW()),
('Logisztikai Menedzser','Logistics Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Magasemelésű Targoncavezető','RT driver','Warehouse Staff','W','DIRECT','Változó',2,'SZOF-4393',NOW()),
('Operációs Műszakvezető','Shift Leader','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Raktárvezető','Warehouse Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Targoncás Áruátvevő','FT Driver Checker','Warehouse Staff','W','DIRECT','Változó',2,'SZOF-4393',NOW()),
('WMS Operátor','WMS Operator','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Minőségbiztosítási specialista','Quality Specialist','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Minőségbiztosítási V7ezető','Quality Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Értékesítési Vezető','Sales Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Értékesítési Specialista','Sales Specialist','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Számlázási Koordinátor','Invoicing Coordinator','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('IT & SAP key user','IT & SAP key user','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('IT Csoportvezető','IT Team leader','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('HR Adminisztrátor','HR Administrator','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('HR Business Partner','HR Business Partner','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('HR Specialista','HR Specialist','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('HR Vezető','HR Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Operációs Igazgató','Operation Director','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Raktári tréner','Warehouse Trainer','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Recepciós Ügyintéző','Receptionist','Others','O','DIRECT','Fix',2,'SZOF-4393',NOW()),
('Göngyöleg Adminisztrációs Csoportvezető','CSO Team leader','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Göngyölegraktáros Targoncavezető','Pallet Forklift driver','Warehouse Staff','W','DIRECT','Változó',2,'SZOF-4393',NOW()),
('Raklap és Göngyöleggazdálkodási Vezető','CSO and Pallet Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Akkumulátortöltő terület Koordinátor','Battery Charging Area Coordinator','Others','O','DIRECT','Fix',2,'SZOF-4393',NOW()),
('Biztonsági és Karbantartási Koordinátor','Security and Facility Coordinator','Others','O','DIRECT','Fix',2,'SZOF-4393',NOW()),
('Biztonsági és Üzemeltetési Vezető','Security and Facility Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('CCTV Operátor','CCTV Operator','Others','O','DIRECT','Fix',2,'SZOF-4393',NOW()),
('Karbantartó','Maintainer','Others','O','DIRECT','Fix',2,'SZOF-4393',NOW()),
('Üzemeltetési Vezető','Facility Manager','Others','O','DIRECT','Fix',2,'SZOF-4393',NOW()),
('készletellenőr - készletgazda','Inventory controller','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Készletgazda','Inventory controller','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Készletgazdálkodási Csoportvezető','Inventory team leader','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Logisztikai Támogató Menedzser','Logistics Support Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Projekt Koordinátor','Project Coordinator','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Reklamációkezelő','Claim Administrator','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Reklamációs Csoportvezető','Claims Team leader','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Diszpécser szolgálati Vezető','Dispatcher Team leader','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Diszpécser-Gépjármű üzemeltetési Technikus','Dispatcher-truck Technician','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Diszpécser-Gépjárművezető','Dispatcher-Driver','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Fuvarszervező Diszpécser','Dispatcher','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Gépjárművezető','Driver','Drivers','D','DIRECT','Változó',2,'SZOF-4393',NOW()),
('Kiszállítási Koordinátor','Delivery Coordinator','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Operatív Diszpécser','Operative Dispatcher','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Rámpa Koordinátor','Delivery Coordinator','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Senior Diszpécser','Senior Dispatcher','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Szállítási Adminisztrációs Csoportvezető','Transport Administration Team leader','Admin staff','A','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Szállítási Igazgató','Transport Director','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Szállítási Projekt Kordinátor','Transport Project Coordinator','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Szállítási Projektvezető','Transport Project Coordinator','Specialists','S','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Ügyvezető Igazgató','CEO','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Üzletfejlesztési Vezető','Business Development Manager','Executive','E','INDIRECT','Fix',2,'SZOF-4393',NOW()),
('Komissiós','Picker','Warehouse Staff','W','DIRECT','Változó',2,'SZOF-4393',NOW());

UPDATE `_sql_version` SET `revision`=32, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -32--2024-11-04-21:30---------------------------------------------------------------

TRUNCATE TABLE ext_position_details;

INSERT INTO ext_position_details (position_name, position_name_english, position_category_name, position_category_code, physical_mental_category, accounting_category, status, created_by, created_on) VALUES
('Kontroling Vezető','Controlling Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Kontroller','Controller','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Könyvelő','Accountant','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Pénzügyi és IT igazgató','Finance and IT director','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Pénzügyi és Számviteli Vezető','Finance and Accounting Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Pénzügyi Ügyintéző','Finance Administrator','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Csomagoló Áruátvevő','Co-pack Checker','WORK STAFF','W','Direct','variable',2,'SZOF-4393',NOW()),
('Csomagoló Vezető','Co-Pack Leader','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Csomagoló','Co-Pack Worker','WORK STAFF','W','Direct','variable',2,'SZOF-4393',NOW()),
('Adminisztrációs Csoportvezető','Administration Team leader','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Adminisztrációs és Reklamációs Csoportvezető','Administration and Claim Team leader','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Adminisztrációs Vezető','Administration Manager','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Adminisztrátor','Administrator','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Áruátvevő','Checker','WORK STAFF','W','Direct','variable',2,'SZOF-4393',NOW()),
('Logisztikai Menedzser','Logistics Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Magasemelésű Targoncavezető','RT driver','WORK STAFF','W','Direct','variable',2,'SZOF-4393',NOW()),
('Operációs Műszakvezető','Shift Leader','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Raktárvezető','Warehouse Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Targoncás Áruátvevő','FT Driver Checker','WORK STAFF','W','Direct','variable',2,'SZOF-4393',NOW()),
('WMS Operátor','WMS Operator','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Minőségbiztosítási specialista','Quality Specialist','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Minőségbiztosítási V7ezető','Quality Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Értékesítési Vezető','Sales Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Értékesítési Specialista','Sales Specialist','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Számlázási Koordinátor','Invoicing Coordinator','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('IT & SAP key user','IT & SAP key user','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('IT Csoportvezető','IT Team leader','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('HR Adminisztrátor','HR Administrator','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('HR Business Partner','HR Business Partner','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('HR Specialista','HR Specialist','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('HR Vezető','HR Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Operációs Igazgató','Operation Director','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Raktári tréner','Warehouse Trainer','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Recepciós Ügyintéző','Receptionist','OTHERS','O','Direct','fix',2,'SZOF-4393',NOW()),
('Göngyöleg Adminisztrációs Csoportvezető','CSO Team leader','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Göngyölegraktáros Targoncavezető','Pallet Forklift driver','WORK STAFF','W','Direct','variable',2,'SZOF-4393',NOW()),
('Raklap és Göngyöleggazdálkodási Vezető','CSO and Pallet Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Akkumulátortöltő terület Koordinátor','Battery Charging Area Coordinator','OTHERS','O','Direct','fix',2,'SZOF-4393',NOW()),
('Biztonsági és Karbantartási Koordinátor','Security and Facility Coordinator','OTHERS','O','Direct','fix',2,'SZOF-4393',NOW()),
('Biztonsági és Üzemeltetési Vezető','Security and Facility Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('CCTV Operátor','CCTV Operator','OTHERS','O','Direct','fix',2,'SZOF-4393',NOW()),
('Karbantartó','Maintainer','OTHERS','O','Direct','fix',2,'SZOF-4393',NOW()),
('Üzemeltetési Vezető','Facility Manager','OTHERS','O','Direct','fix',2,'SZOF-4393',NOW()),
('készletellenőr - készletgazda','Inventory controller','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Készletgazda','Inventory controller','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Készletgazdálkodási Csoportvezető','Inventory team leader','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Logisztikai Támogató Menedzser','Logistics Support Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Projekt Koordinátor','Project Coordinator','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Reklamációkezelő','Claim Administrator','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Reklamációs Csoportvezető','Claims Team leader','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Diszpécser szolgálati Vezető','Dispatcher Team leader','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Diszpécser-Gépjármű üzemeltetési Technikus','Dispatcher-truck Technician','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Diszpécser-Gépjárművezető','Dispatcher-Driver','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Fuvarszervező Diszpécser','Dispatcher','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Gépjárművezető','Driver','DRIVERS','D','Direct','variable',2,'SZOF-4393',NOW()),
('Kiszállítási Koordinátor','Delivery Coordinator','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Operatív Diszpécser','Operative Dispatcher','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Rámpa Koordinátor','Delivery Coordinator','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Senior Diszpécser','Senior Dispatcher','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Fuvarszervező Diszpécser','Dispatcher','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Szállítási Adminisztrációs Csoportvezető','Transport Administration Team leader','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Szállítási Igazgató','Transport Director','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Szállítási Projekt Kordinátor','Transport Project Coordinator','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Szállítási Projektvezető','Transport Project Coordinator','SPECIALISTS','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Ügyvezető Igazgató','CEO','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Üzletfejlesztési Vezető','Business Development Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Komissiós','Picker','WORK STAFF','W','Direct','variable',2,'SZOF-4393',NOW()),
('Projektvezető','Operation excellence','SPECIALIST','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Regionális kontroller','Regional financial controller','SPECIALIST','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Árazási és projekt specialista','Pricing and project specialist?','SPECIALIST','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Közúti szállítmányozási üzletág vezető','Head of International Road Transport','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Tengeri szállítmányozási ügyintéző','Ocean freight clerk','SPECIALIST','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Tengeri szállítmányozási üzletág vezető','Head of Ocean freight shipping','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Nemzetközi Közúti Szállítmányozási Irodavezető','Freight Teamleader','SPECIALIST','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Nemzetközi szállítmányozási fuvarszervező','Freight forwarder','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Nemzetközi szállítmányozási ügyintéző','Freight administrator','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW()),
('Nemzetközi szállítmányozási üzletág vezető','International Road Transportation Teamleader','EXECUTIVES','E','Indirect','fix',2,'SZOF-4393',NOW()),
('Területi értékesítő','Field Sales Manager','SPECIALIST','S','Indirect','fix',2,'SZOF-4393',NOW()),
('Nemzetközi szállítmányozási adminisztrátor','Freight administrator','ADMIN','A','Indirect','fix',2,'SZOF-4393',NOW());
                                                                                                                                                                                                           
UPDATE `_sql_version` SET `revision`=33, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -33--2024-11-15-10:00---------------------------------------------------------------

UPDATE
    `option_config`
SET
    `status` = 2,
    `modified_by` = 'SZOF-4426',
    `modified_on` = NOW()
WHERE
    `option_id` IN ('option22','option23');

INSERT IGNORE INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `status`, `created_by`, `created_on`)
VALUES
    ('customers/hopi/reportEmployeeListGeneralData', 'date', 'EMPLOYEE_WITH_FROM_TO', 2, 'SZOF-4426', NOW()),
    ('customers/hopi/reportEmployeeListGeneralData', 'group', 'DEFAULT_GROUP_FILTER', 2, 'SZOF-4426', NOW()),
    ('customers/hopi/reportEmployeeListWithAllowancesData', 'date', 'EMPLOYEE_WITH_FROM_TO', 2, 'SZOF-4426', NOW()),
    ('customers/hopi/reportEmployeeListWithAllowancesData', 'group', 'DEFAULT_GROUP_FILTER', 2, 'SZOF-4426', NOW()),
    ('customers/hopi/reportEmployeeListWithEmergencyData', 'date', 'EMPLOYEE_WITH_FROM_TO', 2, 'SZOF-4426', NOW()),
    ('customers/hopi/reportEmployeeListWithEmergencyData', 'group', 'DEFAULT_GROUP_FILTER', 2, 'SZOF-4426', NOW()),
    ('customers/hopi/reportEmployeeListWithLicensesData', 'date', 'EMPLOYEE_WITH_FROM_TO', 2, 'SZOF-4426', NOW()),
    ('customers/hopi/reportEmployeeListWithLicensesData', 'group', 'DEFAULT_GROUP_FILTER', 2, 'SZOF-4426', NOW()),
    ('customers/hopi/reportEmployeeListWithQuitData', 'date', 'EMPLOYEE_WITH_FROM_TO', 2, 'SZOF-4426', NOW()),
    ('customers/hopi/reportEmployeeListWithQuitData', 'group', 'DEFAULT_GROUP_FILTER', 2, 'SZOF-4426', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`)
VALUES
    ('reportEmployeeListGeneralData', 'customers/hopi/reportEmployeeListGeneralData', 'view', '1', '1', 'SZOF-4426', NOW()),
    ('reportEmployeeListWithAllowancesData', 'customers/hopi/reportEmployeeListWithAllowancesData', 'view', '1', '1', 'SZOF-4426', NOW()),
    ('reportEmployeeListWithEmergencyData', 'customers/hopi/reportEmployeeListWithEmergencyData', 'view', '1', '1', 'SZOF-4426', NOW()),
    ('reportEmployeeListWithLicensesData', 'customers/hopi/reportEmployeeListWithLicensesData', 'view', '1', '1', 'SZOF-4426', NOW()),
    ('reportEmployeeListWithQuitData', 'customers/hopi/reportEmployeeListWithQuitData', 'view', '1', '1', 'SZOF-4426', NOW());

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`,`menu_item_name`,`menu_modul`,`menu_label`,`menu_item_css_class`,`menu_url`,
                                      `menu_visible`,`menu_visible_operation`,`menu_item_parent_id`,`menu_order`)
VALUES
    ('menu_item_a_report_employee_list_general_data', 'menu_item_a_report_employee_list_general_data', 'ttwa-base', 'menu_item_a_report_employee_list_general_data',
     'sub', 'customers/hopi/reportEmployeeListGeneralData/index', 'customers/hopi/reportEmployeeListGeneralData', 'view', '82', '1'),
    ('menu_item_b_report_employee_list_with_licenses_data', 'menu_item_b_report_employee_list_with_licenses_data', 'ttwa-base', 'menu_item_b_report_employee_list_with_licenses_data',
     'sub', 'customers/hopi/reportEmployeeListWithLicensesData/index', 'customers/hopi/reportEmployeeListWithLicensesData', 'view', '82', '1'),
    ('menu_item_c_report_employee_list_with_allowances_data', 'menu_item_c_report_employee_list_with_allowances_data', 'ttwa-base', 'menu_item_c_report_employee_list_with_allowances_data',
     'sub', 'customers/hopi/reportEmployeeListWithAllowancesData/index', 'customers/hopi/reportEmployeeListWithAllowancesData', 'view', '82', '1'),
    ('menu_item_d_report_employee_list_with_emergency_data', 'menu_item_d_report_employee_list_with_emergency_data', 'ttwa-base', 'menu_item_d_report_employee_list_with_emergency_data',
     'sub', 'customers/hopi/reportEmployeeListWithEmergencyData/index', 'customers/hopi/reportEmployeeListWithEmergencyData', 'view', '82', '1'),
    ('menu_item_e_report_employee_list_with_quit_data', 'menu_item_e_report_employee_list_with_quit_data', 'ttwa-base', 'menu_item_e_report_employee_list_with_quit_data',
     'sub', 'customers/hopi/reportEmployeeListWithQuitData/index', 'customers/hopi/reportEmployeeListWithQuitData', 'view', '82', '1');

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `description`, `created_by`, `created_on`)
VALUES
    ('reportEmployeeListGeneralData', 'customers/hopi/reportEmployeeListGeneralData', 'Állománylista - általános', 'SZOF-4426', NOW()),
    ('reportEmployeeListWithAllowancesData', 'customers/hopi/reportEmployeeListWithAllowancesData', 'Állománylista - jogosítványok, érvényességek', 'SZOF-4426', NOW()),
    ('reportEmployeeListWithEmergencyData', 'customers/hopi/reportEmployeeListWithEmergencyData', 'Állománylista -juttatásokkal', 'SZOF-4426', NOW()),
    ('reportEmployeeListWithLicensesData', 'customers/hopi/reportEmployeeListWithLicensesData', 'Vészhelyzeti lista', 'SZOF-4426', NOW()),
    ('reportEmployeeListWithQuitData', 'customers/hopi/reportEmployeeListWithQuitData', 'Állománylista- kiléptetéshez', 'SZOF-4426', NOW());

INSERT IGNORE INTO `dictionary`(`lang`, `module`, `dict_id`, `dict_value`, `valid`)
VALUES
    ('hu','ttwa-base','menu_item_a_report_employee_list_general_data','Állománylista - általános','1'),
    ('en','ttwa-base','menu_item_a_report_employee_list_general_data','Employee List - general','1'),
    ('hu','ttwa-base','page_title_report_employee_list_general_data','Állománylista - általános','1'),
    ('en','ttwa-base','page_title_report_employee_list_general_data','Employee List - general','1'),
    ('hu','ttwa-base','menu_item_b_report_employee_list_with_licenses_data','Állománylista - jogosítványok','1'),
    ('en','ttwa-base','menu_item_b_report_employee_list_with_licenses_data','Employee List - licenses','1'),
    ('hu','ttwa-base','page_title_report_employee_list_with_licenses_data','Állománylista - jogosítványok, érvényességek','1'),
    ('en','ttwa-base','page_title_report_employee_list_with_licenses_data','Employee List - licenses, validity','1'),
    ('hu','ttwa-base','menu_item_c_report_employee_list_with_allowances_data','Állománylista -juttatásokkal','1'),
    ('en','ttwa-base','menu_item_c_report_employee_list_with_allowances_data','Employee List - with allowances','1'),
    ('hu','ttwa-base','page_title_report_employee_list_with_allowances_data','Állománylista -juttatásokkal','1'),
    ('en','ttwa-base','page_title_report_employee_list_with_allowances_data','Employee List - with allowances','1'),
    ('hu','ttwa-base','menu_item_d_report_employee_list_with_emergency_data','Vészhelyzeti lista','1'),
    ('en','ttwa-base','menu_item_d_report_employee_list_with_emergency_data','Emergency list','1'),
    ('hu','ttwa-base','page_title_report_employee_list_with_emergency_data','Vészhelyzeti lista','1'),
    ('en','ttwa-base','page_title_report_employee_list_with_emergency_data','Emergency list','1'),
    ('hu','ttwa-base','menu_item_e_report_employee_list_with_quit_data','Állománylista- kiléptetéshez','1'),
    ('en','ttwa-base','menu_item_e_report_employee_list_with_quit_data','Employee List - for quit','1'),
    ('hu','ttwa-base','page_title_report_employee_list_with_quit_data','Állománylista- kiléptetéshez','1'),
    ('en','ttwa-base','page_title_report_employee_list_with_quit_data','Employee List - for quit','1');

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES
     ('customers/hopi/reportEmployeeListGeneralData', 'emp_id', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'fullname', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'birth_date', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'company_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'nameofbirth', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'gender', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ec_valid_from', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ec_valid_to', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'employee_contract_number', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'daily_worktime', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'date_of_birth', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option9', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option10', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option14', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option15', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option16', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option17', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option18', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option19', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option20', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option21', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option24', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option25', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext6_option1', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext6_option2', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'unit_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'emp_id', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'fullname', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'birth_date', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'company_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'gender', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ec_valid_from', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ec_valid_to', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'employee_contract_number', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'daily_worktime', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'date_of_birth', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option9', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option10', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option14', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option15', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option16', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option17', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option18', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option19', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option20', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option21', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option24', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'option25', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option1', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option2', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option3', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option4', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option5', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option6', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option7', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option9', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option10', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option11', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option12', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option14', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option15', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option16', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option17', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option18', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option19', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext3_option20', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option1', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option2', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option3', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option4', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option5', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option6', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option7', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option9', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option10', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option11', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option12', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option14', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option15', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option16', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option17', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option18', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option19', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option20', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option21', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext4_option24', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext5_option15', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext5_option16', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext6_option1', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'ext6_option2', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithLicensesData', 'unit_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'emp_id', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'fullname', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'birth_date', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'company_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'gender', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'ec_valid_from', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'ec_valid_to', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'employee_contract_number', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'daily_worktime', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'personal_month_salary', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'personal_hour_salary', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'shift', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'shift_bonus_in_percent', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option1', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option2', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option3', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option4', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option5', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option6', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option7', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'date_of_birth', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option9', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option10', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option11', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option12', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option14', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option15', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option16', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option17', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option18', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option19', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option20', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option21', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option24', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option25', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option26', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option27', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option28', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option29', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option30', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option31', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option32', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option33', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'option34', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'note', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'ext5_option9', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'ext5_option10', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'ext5_option11', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'ext5_option12', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'ext5_option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'ext6_option1', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'ext6_option2', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithAllowancesData', 'unit_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option21', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option24', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option25', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option26', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option27', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option28', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option29', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option30', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option31', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option32', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option33', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option34', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'emp_id', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'fullname', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'birth_date', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'company_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'nameofbirth', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'gender', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'place_of_birth', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'date_of_birth', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'mothers_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ssn', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option3', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option4', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option6', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option7', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'option24', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option1', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option2', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option3', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option4', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option5', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option6', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option7', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option9', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option10', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option11', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option12', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option14', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option15', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option16', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option17', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option18', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option19', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option20', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option21', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option25', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option26', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option27', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option28', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option29', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option30', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option43', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option44', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithEmergencyData', 'unit_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'emp_id', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'fullname', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'birth_date', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'company_name', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'gender', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ec_valid_from', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ec_valid_to', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'employee_contract_number', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'wage_type', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'daily_worktime', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'personal_month_salary', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'personal_hour_salary', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'shift', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'shift_bonus_in_percent', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'es_option1', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'es_option2', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'es_option3', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'es_option4', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'es_option5', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'es_option6', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'es_option7', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'es_option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'date_of_birth', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option8', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option9', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option10', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option11', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option12', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option14', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option15', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option16', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option17', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option18', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option19', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option20', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option21', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option24', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option25', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option26', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option27', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option28', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option29', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option30', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option31', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option32', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option33', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'option34', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'note', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option21', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option24', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option25', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option26', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option27', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option28', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option29', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option30', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option31', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option32', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option33', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext2_option34', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option9', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option10', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option11', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option12', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option13', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option16', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option17', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option18', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option19', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option20', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option21', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option22', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option23', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option24', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option25', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option26', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option27', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option28', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option29', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option30', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option31', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option32', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option33', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option34', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option35', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option36', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option37', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option38', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option39', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option40', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option41', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option42', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext6_option1', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'ext6_option2', 'ALL', 2, 'SZOF-4426', NOW()),
     ('customers/hopi/reportEmployeeListWithQuitData', 'unit_name', 'ALL', 2, 'SZOF-4426', NOW());

UPDATE `_sql_version` SET `revision`=34, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -34--2024-11-20-10:00---------------------------------------------------------------

INSERT INTO ext_position_details (position_name, position_name_english, position_category_name, position_category_code, physical_mental_category, accounting_category, status, created_by, created_on) VALUES
('Raktári munkatárs','Warehouse worker','WORK STAFF','W','Direct','variable',2,'SZOF-4508',NOW()),
('Raktári adminisztrátor','Administrator','ADMIN','A','Indirect','fix',2,'SZOF-4508',NOW()),
('Co-pack ellenőr','Co-pack checker','WORK STAFF','W','Direct','variable',2,'SZOF-4508',NOW()),
('Kiemelt ügyfélkapcsolati osztályvezető','Warehouse Key Account Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-4508',NOW()),
('Raktári műszakvezető helyettes','Shift Leader Deputy','WORK STAFF','W','Direct','variable',2,'SZOF-4508',NOW()),
('Raktár logisztikai adminisztrátor','Administrator','ADMIN','A','Indirect','fix',2,'SZOF-4508',NOW()),
('Speditör','Forwarder','SPECIALISTS','S','Indirect','fix',2,'SZOF-4508',NOW()),
('Kontrolling csoportvezető','Controlling Teamleader','SPECIALISTS','S','Indirect','fix',2,'SZOF-4508',NOW());

UPDATE `_sql_version` SET `revision`=35, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -35--2024-11-22-10:00---------------------------------------------------------------

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES
('customers/hopi/reportEmployeeListGeneralData', 'payroll_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListGeneralData', 'cost_id', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListGeneralData', 'cost_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithLicensesData', 'payroll_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithLicensesData', 'cost_id', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithLicensesData', 'cost_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'payroll_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'cost_id', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'cost_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option21', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option22', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option23', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option24', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option25', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option26', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option27', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option28', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option29', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option30', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option31', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option32', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option33', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'ext2_option34', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithEmergencyData', 'payroll_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithEmergencyData', 'ext2_option42', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithEmergencyData', 'cost_id', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithEmergencyData', 'cost_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithQuitData', 'payroll_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithQuitData', 'employee_contract_type', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithQuitData', 'ext5_option14', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithQuitData', 'cost_id', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithQuitData', 'cost_name', 'ALL', 2, 'SZOF-4426', NOW());

UPDATE 
  `column_rights`
SET
 `status` = 5,
 `modified_by` = 'SZOF-4426'
WHERE
  `controller_id` = 'customers/hopi/reportEmployeeListWithEmergencyData'
AND `status` = 2
AND `column_id` IN ('ext2_option24','ext2_option31','ext2_option32','ext2_option33','ext2_option34','ext2_option44');

UPDATE 
  `column_rights`
SET
 `status` = 5,
 `modified_by` = 'SZOF-4426'
WHERE
  `controller_id` = 'customers/hopi/reportEmployeeListWithQuitData'
AND `status` = 2
AND `column_id` IN ('ext5_option16');

UPDATE `_sql_version` SET `revision`=36, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -36--2024-12-10-10:00---------------------------------------------------------------

UPDATE `placeholder` SET `placeholder` = 'team_bonus', `modified_by` = 'SZOF-4672', `modified_on` = NOW() WHERE `field_name` = 'es_option2' AND  `status` = '2';
UPDATE `placeholder` SET `placeholder` = 'performance_bonus', `modified_by` = 'SZOF-4672', `modified_on` = NOW() WHERE `field_name` = 'es_option3' AND  `status` = '2';

INSERT IGNORE INTO `placeholder` (`placeholder`, `model`, `field_name`, `placeholder_config`, `status`, `created_by`, `created_on`) VALUES
    ('qualification_bonus', 'EmployeeSalary', 'es_option1', NULL, '2', 'SZOF-4672', NOW()),
    ('termination_period_by_days', 'EmployeeExt', 'option11', NULL, '2', 'SZOF-4672', NOW()),
	('nationality_1', 'EmployeeExt', 'option3', '{"lookup_id": "option3"}', '2', 'SZOF-4672', NOW()),
    ('workplace_location', 'EmployeeExt', 'option8', '{"lookup_id": "option8"}', '2', 'SZOF-4672', NOW()),
    ('end_of_trial_period', 'EmployeeExt', 'option10', '{"lookup_id": "option10"}', '2', 'SZOF-4672', NOW()),
    ('non_competition_clause', 'EmployeeExt', 'option12', '{"lookup_id": "option12"}', '2', 'SZOF-4672', NOW()),
    ('booklet_category_name', 'EmployeeExt', 'option15', '{"lookup_id": "option15"}', '2', 'SZOF-4672', NOW()),
    ('booklet_category_code', 'EmployeeExt', 'option16', '{"lookup_id": "option16"}', '2', 'SZOF-4672', NOW()),
    ('direct_indirect_category', 'EmployeeExt', 'option17', '{"lookup_id": "option17"}', '2', 'SZOF-4672', NOW()),
    ('qualification_level_name', 'EmployeeExt', 'option21', '{"lookup_id": "option21"}', '2', 'SZOF-4672', NOW()),
    ('department_name_hun', 'EmployeeExt', 'option24', '{"lookup_id": "option24"}', '2', 'SZOF-4672', NOW());

UPDATE `_sql_version` SET `revision`=37, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -37--2024-12-16-19:00---------------------------------------------------------------

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES
('customers/hopi/reportEmployeeListGeneralData', 'cost_center_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithLicensesData', 'cost_center_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithAllowancesData', 'cost_center_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithEmergencyData', 'cost_center_name', 'ALL', 2, 'SZOF-4426', NOW()),
('customers/hopi/reportEmployeeListWithQuitData', 'cost_center_name', 'ALL', 2, 'SZOF-4426', NOW());

UPDATE
    `column_rights`
SET
    `status` = 5,
    `modified_by` = 'SZOF-4426'
WHERE
    `controller_id` IN 
    ('customers/hopi/reportEmployeeListGeneralData',
     'customers/hopi/reportEmployeeListWithLicensesData',
     'customers/hopi/reportEmployeeListWithAllowancesData',
     'customers/hopi/reportEmployeeListWithEmergencyData',
     'customers/hopi/reportEmployeeListWithQuitData')
  AND `status` = 2
  AND `column_id` IN ('cost_id');

UPDATE `_sql_version` SET `revision`=38, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -38--2024-12-17-18:00---------------------------------------------------------------

UPDATE `placeholder` SET `model` = 'EmployeeExt', `field_name` = 'option28', `placeholder_config` = '{"lookup_id": "option28"}', `modified_by` = 'SZOF-4687', `modified_on` = NOW() WHERE `placeholder` = 'feor_code' AND  `status` = '2';

INSERT IGNORE INTO `placeholder` (`placeholder`, `model`, `field_name`, `placeholder_config`, `status`, `created_by`, `created_on`) VALUES
    ('name_of_legal_representative', 'Employee', 'company_id', NULL, '2', 'SZOF-4687', NOW()),
    ('private_email_address', 'EmployeeExt', 'option6', NULL, '2', 'SZOF-4687', NOW()),
    ('private_phone_number', 'EmployeeExt', 'option7', NULL, '2', 'SZOF-4687', NOW()),
	('employee_position_in_hungarian', 'EmployeeExt', 'option13', '{"lookup_id": "option13"}', '2', 'SZOF-4687', NOW()),
    ('permanent_residence_address_zip_code', 'EmployeeExt2', 'ext2_option1', NULL, '2', 'SZOF-4687', NOW()),
    ('permanent_residence_address_city', 'EmployeeExt2', 'ext2_option3', NULL, '2', 'SZOF-4687', NOW()),
    ('permanent_residence_address_name_of_public_place', 'EmployeeExt2', 'ext2_option5', NULL, '2', 'SZOF-4687', NOW()),
    ('permanent_residence_address_type_of_public_place', 'EmployeeExt2', 'ext2_option6', NULL, '2', 'SZOF-4687', NOW()),
    ('permanent_residence_address_house_number', 'EmployeeExt2', 'ext2_option7', NULL, '2', 'SZOF-4687', NOW()),
    ('permanent_residence_address_floor', 'EmployeeExt2', 'ext2_option8', NULL, '2', 'SZOF-4687', NOW()),
    ('permanent_residence_address_door', 'EmployeeExt2', 'ext2_option9', NULL, '2', 'SZOF-4687', NOW()),
    ('residence_address_zip_code', 'EmployeeExt2', 'ext2_option11', NULL, '2', 'SZOF-4687', NOW()),
    ('residence_address_city', 'EmployeeExt2', 'ext2_option13', NULL, '2', 'SZOF-4687', NOW()),
    ('residence_address_name_of_public_place', 'EmployeeExt2', 'ext2_option15', NULL, '2', 'SZOF-4687', NOW()),
    ('residence_address_type_of_public_place', 'EmployeeExt2', 'ext2_option16', NULL, '2', 'SZOF-4687', NOW()),
    ('residence_address_house_number', 'EmployeeExt2', 'ext2_option17', NULL, '2', 'SZOF-4687', NOW()),
    ('residence_address_floor', 'EmployeeExt2', 'ext2_option18', NULL, '2', 'SZOF-4687', NOW()),
    ('residence_address_door', 'EmployeeExt2', 'ext2_option19', NULL, '2', 'SZOF-4687', NOW()),
    ('bank_account_number', 'EmployeeExt5', 'ext5_option2', NULL, '2', 'SZOF-4687', NOW());

UPDATE `_sql_version` SET `revision`=39, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -39--2025-01-07-10:00---------------------------------------------------------------

INSERT IGNORE INTO `ext_organisation_details` (`organisation_name`, `organisation_name_english`, `status`, `created_by`, `created_on`) VALUES
    ('CEO Global', 'CEO Global', 2, 'SZOF-4744', NOW()),
    ('HR Global', 'HR Global', 2, 'SZOF-4744', NOW()),
    ('HH Global Solutions Management&Others', 'HH Global Solutions Management&Others', 2, 'SZOF-4744', NOW()),
    ('Ocean&Air Office Global HU GY', 'Ocean&Air Office Global HU GY', 2, 'SZOF-4744', NOW()),
    ('Road Office Global Solutions HU BU', 'Road Office Global Solutions HU BU', 2, 'SZOF-4744', NOW()),
    ('Road Office Global Solutions HU GY', 'Road Office Global Solutions HU GY', 2, 'SZOF-4744', NOW());

UPDATE `_sql_version` SET `revision`=40, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -40--2025-01-15-09:00---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Személyes és munkav. adatok' WHERE `dict_id` = 'tab_employeetabs_employeeext' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Personal and employment data' WHERE `dict_id` = 'tab_employeetabs_employeeext' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Lakcímek és munkábajárás' WHERE `dict_id` = 'tab_employeetabs_employeeext2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Addresses and commute' WHERE `dict_id` = 'tab_employeetabs_employeeext2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyirányú munkábajárás (km)' WHERE `dict_id` = 'ext2_option31' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'One-way commute (km)' WHERE `dict_id` = 'ext2_option31' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogosítványok' WHERE `dict_id` = 'tab_employeetabs_employeeext3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Licenses' WHERE `dict_id` = 'tab_employeetabs_employeeext3' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'EÜ és végzettségi adatok' WHERE `dict_id` = 'tab_employeetabs_employeeext4' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'EU and qualification data' WHERE `dict_id` = 'tab_employeetabs_employeeext4' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Juttatások és levonások' WHERE `dict_id` = 'tab_employeetabs_employeeext5' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Benefits and deductions' WHERE `dict_id` = 'tab_employeetabs_employeeext5' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Jogfoly., egyéb ellátás' WHERE `dict_id` = 'tab_employeetabs_employeeext6' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Legal aid, other benefits' WHERE `dict_id` = 'tab_employeetabs_employeeext6' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Szabadságok' WHERE `dict_id` = 'tab_employeetabs_employeebaseabsence' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Absences' WHERE `dict_id` = 'tab_employeetabs_employeebaseabsence' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `_sql_version` SET `revision`=41, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -41--2025-01-15-10:00---------------------------------------------------------------

UPDATE `option_config` SET `status` = '2', `modified_by` = 'SZOF-4761', `modified_on` = NOW() WHERE `option_id` IN ('es_option9', 'es_option10', 'option35', 'option36', 'option37') AND `status` = '7';

UPDATE `dictionary` SET `dict_value` = 'Személyi alapbér szöveggel' WHERE `dict_id` = 'es_option9' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Personal basic salary with text' WHERE `dict_id` = 'es_option9' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Egyéni teljesítmény bónusz összeg' WHERE `dict_id` = 'es_option10' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Individual performance bonus amount' WHERE `dict_id` = 'es_option10' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Céges email cím' WHERE `dict_id` = 'option35' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Company email address' WHERE `dict_id` = 'option35' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Céges telefonszám' WHERE `dict_id` = 'option36' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Company phone number' WHERE `dict_id` = 'option36' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Próbaidő hossza (napban)' WHERE `dict_id` = 'option37' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Length of probation period (in days)' WHERE `dict_id` = 'option37' AND `module` = 'ttwa-base' AND `lang` = 'en';

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES
    ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option9', 'ALL', 2, 'SZOF-4761', NOW()),
    ('customers/hopi/reportEmployeeListWithQuitData', 'es_option9', 'ALL', 2, 'SZOF-4761', NOW()),
    ('customers/hopi/reportEmployeeListWithAllowancesData', 'es_option10', 'ALL', 2, 'SZOF-4761', NOW()),
    ('customers/hopi/reportEmployeeListWithQuitData', 'es_option10', 'ALL', 2, 'SZOF-4761', NOW());

UPDATE `_sql_version` SET `revision`=42, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -42--2025-01-15-14:00---------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = '1', `note` = 'Prev. val.: 0, Adatokat is mutatunk a dolgozónak kiküldött email értesítésben.', `modified_by` = 'SZOF-4763', `modified_on` = NOW() WHERE `setting_id` = 'showEmployeeDataInNotificationEmail';

INSERT IGNORE INTO `notification_email_config` (`process_id`, `sql`, `addresses`, `day_before_event`, `file_name`, `subject`, `message_text_dict_id`, `data_to_csv`, `rolegroup_ids`, `note`, `status`, `sendToEmployees`, `sendEmptyNotificationEmail`, `process_ids`) VALUES
    (
        'notificationAboutMachineryLicenceHealthValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext3.`ext3_option8` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext3` ext3 ON
                ext3.`employee_id` = e.`employee_id`
            AND ext3.`status` = \"2\"
            AND CURDATE() BETWEEN ext3.`valid_from` AND IFNULL(ext3.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext3_option8\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext3.`ext3_option8`) <= CURDATE() + INTERVAL 30 DAY
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutMachineryLicenceHealthValidity', 'Gépkezelői jogosítvány orvosi érvényesség lejárat',
        'notification_before_one_month_expired', '0', NULL, 'Értesítést küldd a gépkezelői jogosítvány orvosi érvényesség lejárat előtt 30 nappal', '2', 1, '0', 'absenceApprover'
    ),
    (
        'notificationAboutDrivingLicenceCECategoryValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext3.`ext3_option13` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext3` ext3 ON
                ext3.`employee_id` = e.`employee_id`
            AND ext3.`status` = \"2\"
            AND CURDATE() BETWEEN ext3.`valid_from` AND IFNULL(ext3.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext3_option13\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext3.`ext3_option13`) <= CURDATE() + INTERVAL 30 DAY
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutDrivingLicenceCECategoryValidity', 'Jogosítvány CE kategória érvényessége lejár',
        'notification_before_one_month_expired', '0', NULL, 'Értesítést küldd a CE kategória jogosítvány érvényesség lejárat előtt 30 nappal', '2', 1, '0', 'absenceApprover'
    ),
    (
        'notificationAboutGKIlicenceValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext3.`ext3_option15` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext3` ext3 ON
                ext3.`employee_id` = e.`employee_id`
            AND ext3.`status` = \"2\"
            AND CURDATE() BETWEEN ext3.`valid_from` AND IFNULL(ext3.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext3_option15\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext3.`ext3_option15`) <= CURDATE() + INTERVAL 30 DAY
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutGKIlicenceValidity', 'GKI érvényessége lejár',
        'notification_before_one_month_expired', '0', NULL, 'Értesítést küldd a GKI érvényesség lejárat előtt 30 nappal', '2', 1, '0', 'absenceApprover'
    ),
    (
        'notificationAboutDriverCardValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext3.`ext3_option17` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext3` ext3 ON
                ext3.`employee_id` = e.`employee_id`
            AND ext3.`status` = \"2\"
            AND CURDATE() BETWEEN ext3.`valid_from` AND IFNULL(ext3.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext3_option17\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext3.`ext3_option17`) <= CURDATE() + INTERVAL 30 DAY
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutDriverCardValidity', 'Gépjárművezetői kártya érvényessége lejár',
        'notification_before_one_month_expired', '0', NULL, 'Értesítést küldd a gépjárművezetői kártya érvényesség lejárat előtt 30 nappal', '2', 1, '0', 'absenceApprover'
    ),
    (
        'notificationAboutADRCardValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext3.`ext3_option19` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext3` ext3 ON
                ext3.`employee_id` = e.`employee_id`
            AND ext3.`status` = \"2\"
            AND CURDATE() BETWEEN ext3.`valid_from` AND IFNULL(ext3.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext3_option19\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext3.`ext3_option19`) <= CURDATE() + INTERVAL 30 DAY
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutADRCardValidity', 'ADR kártya érvényessége lejár',
        'notification_before_one_month_expired', '0', NULL, 'Értesítést küldd az ADR kártya érvényesség lejárat előtt 30 nappal', '2', 1, '0', 'absenceApprover'
    );

UPDATE `_sql_version` SET `revision`=43, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -43--2025-01-23-09:00---------------------------------------------------------------

INSERT IGNORE INTO `notification_email_config` (`process_id`, `sql`, `addresses`, `day_before_event`, `file_name`, `subject`, `message_text_dict_id`, `data_to_csv`, `rolegroup_ids`, `note`, `status`, `sendToEmployees`, `sendEmptyNotificationEmail`, `process_ids`) VALUES
    (
        'notificationAboutOccupationalHealthCheckValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext4.`ext4_option4` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext4` ext4 ON
                ext4.`employee_id` = e.`employee_id`
            AND ext4.`status` = \"2\"
            AND CURDATE() BETWEEN ext4.`valid_from` AND IFNULL(ext4.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext4_option4\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext4.`ext4_option4`) <= CURDATE() + INTERVAL 30 DAY
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutOccupationalHealthCheckValidity', 'Fogl Eü érvényesség lejár',
        'notification_before_one_month_expired', '0', '65949638ec9f9a87e04f6c526026994e', 'Értesítést küldd a fogl. eü érvényesség lejárat előtt 30 nappal', '2', 1, '0', 'absenceApprover'
    ),
    (
        'notificationAboutPulmonaryFilterExpirationValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext4.`ext4_option5` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext4` ext4 ON
                ext4.`employee_id` = e.`employee_id`
            AND ext4.`status` = \"2\"
            AND CURDATE() BETWEEN ext4.`valid_from` AND IFNULL(ext4.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext4_option5\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext4.`ext4_option5`) <= CURDATE() + INTERVAL 30 DAY
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutPulmonaryFilterExpirationValidity', 'Tüdőszűrő érvényesség lejár',
        'notification_before_one_month_expired', '0', '65949638ec9f9a87e04f6c526026994e', 'Értesítést küldd a tüdőszűrő érvényesség lejárat előtt 30 nappal', '2', 1, '0', 'absenceApprover'
    );

UPDATE `_sql_version` SET `revision`=44, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -44--2025-01-27-16:30---------------------------------------------------------------

INSERT IGNORE INTO `notification_email_config` (`process_id`, `sql`, `addresses`, `day_before_event`, `file_name`, `subject`, `message_text_dict_id`, `data_to_csv`, `rolegroup_ids`, `note`, `status`, `sendToEmployees`, `sendEmptyNotificationEmail`, `process_ids`) VALUES
    (
        'notificationAboutDrivingLicenseBCategoryValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext5.`ext5_option16` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext5` ext5 ON
                ext5.`employee_id` = e.`employee_id`
            AND ext5.`status` = \"2\"
            AND CURDATE() BETWEEN ext5.`valid_from` AND IFNULL(ext5.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext5_option16\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext5.`ext5_option16`) <= CURDATE() + INTERVAL 30 DAY
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutDrivingLicenseBCategoryValidity', 'Jogosítvány B kategória érvényessége lejár',
        'notification_before_one_month_expired', '0', NULL, 'Értesítést küldd a jogosítvány B kategória érvényesség lejárat előtt 30 nappal', '2', 1, '0', 'absenceApprover'
    ),
    (
        'notificationAboutEndOfAdvancePaymentDeductionValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext5.`ext5_option21` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext5` ext5 ON
                ext5.`employee_id` = e.`employee_id`
            AND ext5.`status` = \"2\"
            AND CURDATE() BETWEEN ext5.`valid_from` AND IFNULL(ext5.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext5_option21\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND ext5.`ext5_option21` = DATE_FORMAT(CURDATE(), \"%Y-%m\")
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutEndOfAdvancePaymentDeductionValidity', 'Fizetési előleg levonás vége (hónap)',
        'notification_at_month_expired', '0', '65949638ec9f9a87e04f6c526026994e', 'Értesítést küldd a fizetési előleg levonás vége dátumkor', '2', 1, '0', 'absenceApprover'
    ),
    (
        'notificationAboutEducationalContractDateValidity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext5.`ext5_option35` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext5` ext5 ON
                ext5.`employee_id` = e.`employee_id`
            AND ext5.`status` = \"2\"
            AND CURDATE() BETWEEN ext5.`valid_from` AND IFNULL(ext5.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext5_option35\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext5.`ext5_option35`) = CURDATE()
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutEducationalContractDateValidity', 'Tanulmányi szerződés érvényesség dátuma (lejárat)',
        'notification_at_date_expired', '0', '65949638ec9f9a87e04f6c526026994e', 'Értesítést küldd a tanulmányi szerződés érvényesség dátumkor', '2', 1, '0', 'absenceApprover'
    ),
    (
        'notificationAboutEducationalContractDate2Validity',
        'SELECT
            CONCAT(e.`last_name`,\" \", e.`first_name`) AS fullname,
            lookup_dict_value.`dict_value` AS position_name,
            e.`emp_id` AS emp_id,
            ext5.`ext5_option39` AS field_value,
            d.`dict_value` AS field_name,
            e.`employee_id` AS employee_id
        FROM `employee_ext` ext
       LEFT JOIN `app_lookup` al ON
                al.`lookup_value` = ext.`option13`
            AND al.`lookup_id` = \"option13\"
        LEFT JOIN `employee` e ON
                e.`employee_id` = ext.`employee_id`
            AND e.`status` = \"2\"
            AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, \"2038-01-01\")
       LEFT JOIN `employee_contract` ec ON
                ec.`employee_id` = e.`employee_id`
            AND ec.`status` = \"2\"
            AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, \"2038-01-01\")
			AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_group` eg ON
                eg.`employee_contract_id` = ec.`employee_contract_id`
            AND eg.`status` = \"2\"
            AND eg.`group_id` = \"unit_id\"
            AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, \"2038-01-01\")
        LEFT JOIN `approver` a ON
                a.`related_value` = eg.`group_value`
            AND a.`process_id` = \"absenceApprover\"
            AND a.`related_model` = \"Unit\"
            AND a.`status` = \"2\"
            AND CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, \"2038-01-01\")
        LEFT JOIN `user` ON
                user.`user_id` = a.`approver_user_id`
            AND user.`status` = \"2\"
            AND CURDATE() BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, \"2038-01-01\")
        LEFT JOIN `employee_ext5` ext5 ON
                ext5.`employee_id` = e.`employee_id`
            AND ext5.`status` = \"2\"
            AND CURDATE() BETWEEN ext5.`valid_from` AND IFNULL(ext5.`valid_to`, \"2038-01-01\")
        LEFT JOIN `dictionary` d ON
         		d.`dict_id` = \"ext5_option39\"
         	AND d.`lang` = \"hu\"
         	AND d.`module` = \"ttwa-base\"
        LEFT JOIN `dictionary` lookup_dict_value ON
         		lookup_dict_value.`dict_id` = al.`dict_id`
         	AND lookup_dict_value.`lang` = \"hu\"
         	AND lookup_dict_value.`module` = \"ttwa-base\"
        WHERE
                ext.`status` = \"2\"
            AND CURDATE() BETWEEN ext.`valid_from` AND IFNULL(ext.`valid_to`, \"2038-01-01\")
            AND DATE(ext5.`ext5_option39`) = CURDATE()
            AND ext.`row_id` IS NOT NULL
            AND e.`row_id` IS NOT NULL
            AND ec.`row_id` IS NOT NULL
            AND eg.`row_id` IS NOT NULL
            GROUP BY e.emp_id, al.lookup_value
            ORDER BY fullname',
        '', '0', 'notificationAboutEducationalContractDate2Validity', 'Tanulmányi szerződés érvényesség dátuma (lejárat) 2',
        'notification_at_date_expired', '0', '65949638ec9f9a87e04f6c526026994e', 'Értesítést küldd a tanulmányi szerződés 2 érvényesség dátumkor', '2', 1, '0', 'absenceApprover'
    );

UPDATE `_sql_version` SET `revision`=45, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -45--2025-01-28-10:00---------------------------------------------------------------

UPDATE `dictionary` SET `dict_value` = 'Felettes dolgozó azonosítója' WHERE `dict_id` = 'ext7_option1' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Supervisor employee id' WHERE `dict_id` = 'ext7d_option1' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Felettes dolgozó neve' WHERE `dict_id` = 'ext7_option2' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Supervisor employee name' WHERE `dict_id` = 'ext7_option2' AND `module` = 'ttwa-base' AND `lang` = 'en';
UPDATE `dictionary` SET `dict_value` = 'Van 3 évnél fiatalabb gyermeke' WHERE `dict_id` = 'ext7_option3' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Have a child under 3 years old' WHERE `dict_id` = 'ext7_option3' AND `module` = 'ttwa-base' AND `lang` = 'en';

UPDATE `option_config` SET `status` = '2', `modified_by` = 'SZOF-4823', `modified_on` = NOW() WHERE `option_id` IN ('ext7_option1', 'ext7_option2', 'ext7_option3');
UPDATE `option_config` SET `type` = 'combo', `modified_by` = 'SZOF-4823', `modified_on` = NOW() WHERE `option_id` = 'ext7_option3';

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
('ext7_option3', '0', '0', 'no', '1'),
('ext7_option3', '1', '0', 'yes', '1');

UPDATE `_sql_version` SET `revision`=46, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -46--2025-02-05-19:00---------------------------------------------------------------

INSERT IGNORE INTO `placeholder` (`placeholder`, `model`, `field_name`, `status`, `created_by`, `created_on`) VALUES
    ('individual_performance_bonus_amount', 'EmployeeSalary', 'es_option10', '2', 'SZOF-4871', NOW()),
    ('personal_basic_salary_with_text', 'EmployeeSalary', 'es_option9', '2', 'SZOF-4871', NOW()),
	('length_of_probation_period_in_days', 'EmployeeExt', 'option37', '2', 'SZOF-4871', NOW());

UPDATE `_sql_version` SET `revision`=47, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -47--2025-02-06-11:00---------------------------------------------------------------

UPDATE `app_lookup` SET `valid` = '0' WHERE `lookup_id` = 'option15' AND `lookup_value` = '7' AND `dict_id` = 'option15_20241115132713263';
UPDATE `employee_position` SET `employee_position_name` = 'Árazási és projekt koordinátor', `modified_by` = 'SZOF-4843', `modified_on` = NOW() WHERE `employee_position_name` = 'Árazási és projekt specialista' AND CURDATE() BETWEEN `valid_from` AND `valid_to` AND `status` = 2;
UPDATE `ext_position_details` SET `position_category_name` = 'SPECIALISTS', `modified_by` = 'SZOF-4843', `modified_on` = NOW() WHERE `position_category_name` = 'SPECIALIST' AND `status` = 2;
UPDATE `ext_position_details` SET `position_name` = 'Árazási és projekt koordinátor', `position_name_english` = 'Tender and Project Coordinator', `modified_by` = 'SZOF-4843', `modified_on` = NOW() WHERE `position_name` = 'Árazási és projekt specialista' AND `status` = 2;
UPDATE `ext_position_details` SET `position_name_english` = 'Transport administrator', `modified_by` = 'SZOF-4843', `modified_on` = NOW() WHERE `position_name` = 'Nemzetközi szállítmányozási ügyintéző' AND `status` = 2;
UPDATE `ext_position_details` SET `position_name_english` = 'International forwarder', `modified_by` = 'SZOF-4843', `modified_on` = NOW() WHERE `position_name` = 'Nemzetközi szállítmányozási fuvarszervező' AND `status` = 2;
UPDATE `dictionary` SET `dict_value` = 'Árazási és projekt koordinátor' WHERE `dict_id` = 'option13_20241115105557568' AND `module` = 'ttwa-base' AND `lang` = 'hu';
UPDATE `dictionary` SET `dict_value` = 'Tender and Project Coordinator' WHERE `dict_id` = 'option14_20241115111357417' AND `module` = 'ttwa-base' AND `lang` = 'hu';

INSERT IGNORE INTO `employee_position` (`employee_position_id`, `employee_position_name`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES
    ('103', 'Nemzetközi Szállítmányozási Irodavezető', '1915-01-01','2038-01-01', '2', 'SZOF-4843', NOW()),
    ('104', 'HR Generalista', '1915-01-01','2038-01-01', '2', 'SZOF-4843', NOW()),
    ('105', 'DC Manager', '1915-01-01','2038-01-01', '2', 'SZOF-4843', NOW()),
    ('106', 'Depot Manager', '1915-01-01','2038-01-01', '2', 'SZOF-4843', NOW()),
    ('107', 'Sales Representative', '1915-01-01','2038-01-01', '2', 'SZOF-4843', NOW());

INSERT IGNORE INTO `ext_position_details` (`position_name`, `position_name_english`, `position_category_name`, `position_category_code`, `physical_mental_category`, `accounting_category`, `status`, `created_by`, `created_on`) VALUES
    ('Nemzetközi Szállítmányozási Irodavezető', 'Branch Manager','SPECIALISTS', 'S', 'Indirect', 'fix', '2', 'SZOF-4843', NOW()),
    ('HR Generalista', 'HR Generalist','SPECIALISTS', 'S', 'Indirect', 'fix', '2', 'SZOF-4843', NOW()),
    ('DC Manager', 'DC Manager','EXECUTIVES', 'E', 'Indirect', 'fix', '2', 'SZOF-4843', NOW()),
    ('Depot Manager', 'Depot Manager','EXECUTIVES', 'E', 'Indirect', 'fix', '2', 'SZOF-4843', NOW()),
    ('Sales Representative', 'Sales Representative','SPECIALISTS', 'S', 'Indirect', 'fix', '2', 'SZOF-4843', NOW());

UPDATE `_sql_version` SET `revision`=48, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -48--2025-02-07-14:45---------------------------------------------------------------

INSERT IGNORE INTO `auth_controller` (`controller_id`, `controller_name`, `controller_dict_id`, `created_by`, `created_on`) VALUES
	('customers/hopi/reportBergmannHandover', 'ReportBergmannHandover', 'menu_item_report_bergmann_handover', 'SZOF-4857', NOW());

INSERT IGNORE INTO `auth_acl` (`role_id`, `controller_id`, `operation_id`, `access_right`, `login_need`, `created_by`, `created_on`) VALUES
	('hopiReportBergmannHandover', 'customers/hopi/reportBergmannHandover', 'view', '1', '1', 'SZOF-4857', NOW());

INSERT IGNORE INTO `auth_role` (`role_id`, `role_name`, `customer_visibility`, `description`, `created_by`, `created_on`) VALUES
	('hopiReportBergmannHandover', 'customers/hopi/reportBergmannHandover --- view', '0', 'Bergmann átadás kimutatáshoz ad jogot', 'SZOF-4857', NOW());

INSERT IGNORE INTO `menu_item_table` (`menu_item_id`, `menu_item_name`, `menu_modul`, `menu_label`, `menu_item_css_class`, `menu_url`, `menu_visible`, `menu_visible_operation`, `menu_item_parent_id`, `menu_order`) VALUES
	('customers/hopi/reportBergmannHandover', 'customers/hopi/reportBergmannHandover', 'ttwa-wfm', 'menu_item_report_bergmann_handover', 'sub', '/customers/hopi/reportBergmannHandover/index', 'customers/hopi/reportBergmannHandover', 'view', '82', '217');

INSERT IGNORE INTO `search_filter` (`controller_id`, `filter_type`, `filter_id`, `search_type`, `multi_select`, `status`, `created_by`, `created_on`) VALUES
    ('customers/hopi/reportBergmannHandover', 'date', 'EMPLOYEE_WITH_DATE', 'combo', 0, '2', 'SZOF-4857', NOW()),
    ('customers/hopi/reportBergmannHandover', 'group', 'DEFAULT_GROUP_FILTER', 'combo', 0, '2', 'SZOF-4857', NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-wfm', 'menu_item_report_bergmann_handover', 'Bergmann átadás', '1'),
    ('en', 'ttwa-wfm', 'menu_item_report_bergmann_handover', 'Bergmann handover', '1'),
	('hu', 'ttwa-wfm', 'page_title_report_bergmann_handover', 'Bergmann átadás', '1'),
	('en', 'ttwa-wfm', 'page_title_report_bergmann_handover', 'Bergmann handover', '1'),
    ('hu', 'ttwa-wfm', 'export_file_report_bergmann_handover', 'Bergmann_handover', '1'),
    ('en', 'ttwa-wfm', 'export_file_report_bergmann_handover', 'Bergmann_handover', '1'),
    ('hu', 'ttwa-wfm', 'division_name', 'Divízió megnevezése', '1'),
    ('en', 'ttwa-wfm', 'division_name', 'Division name', '1');

UPDATE `_sql_version` SET `revision`=49, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -49--2025-02-28-13:00---------------------------------------------------------------

INSERT INTO ext_position_details (position_name, position_name_english, position_category_name, position_category_code, physical_mental_category, accounting_category, status, created_by, created_on) VALUES
('Irodavezető','Branch Manager','EXECUTIVES','E','Indirect','fix',2,'SZOF-5237',NOW()),
('Csoportvezető','Team Lead','EXECUTIVES','E','Indirect','fix',2,'SZOF-5237',NOW());

UPDATE `_sql_version` SET `revision`=50, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -50--2025-03-28-13:00---------------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = 'EmployeeExt,EmployeeCost,EmployeeContract',
    `modified_by` = 'SZOF-4762',
    `modified_on` = NOW(),
    `note` = 'Prev value: ""'
WHERE `setting_id` = 'notificationOnChangeModels';

UPDATE
    `app_settings`
SET `setting_value` = '1',
    `modified_by` = 'SZOF-4762',
    `modified_on` = NOW(),
    `note` = 'Prev value: 0'
WHERE `setting_id` = 'emailSendingInPool';

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
    ('hu', 'ttwa-base', 'notification_general_introduction', 'Automatikus értesítés érkezett a Digitális HR rendszerből:', '1'),
    ('en', 'ttwa-base', 'notification_general_introduction', 'Automatic notification has been received from the Digital HR system:', '1'),
    ('hu', 'ttwa-base', 'notification_general_field_text', 'A <strong>{field_name_upper}</strong> módosult, az új érvényes {field_name_lower}: {field_value}', '1'),
    ('en', 'ttwa-base', 'notification_general_field_text', 'The <strong>{field_name_upper}</strong> has been changed, the new {field_name_lower} value is: {field_value}', '1'),
    ('hu', 'ttwa-base', 'employee_id_not_found', 'Dolgozó azonosító nem található.', '1'),
    ('en', 'ttwa-base', 'employee_id_not_found', 'Employee id is not found.', '1'),
    ('hu', 'ttwa-base', 'employee_not_found', 'Dolgozó nem található.', '1'),
    ('en', 'ttwa-base', 'employee_not_found', 'Employee is not found.', '1'),
    ('hu', 'ttwa-base', 'email_address_is_missing', 'Email cím hiányzik. Felhasználó azonosító: {user_id}', '1'),
    ('en', 'ttwa-base', 'email_address_is_missing', 'Email address is missing. User id: {user_id}.', '1');

INSERT INTO `notification_on_change_config` (`entity_name`, `config`, `status`, `created_by`, `created_on`) VALUES
(
       'EmployeeExt',
       '{
           "handler": {
               "namespace": "Components\\\\customers\\\\hopi\\\\Handler",
               "class": "HopiEmailOnDataChangeHandler"
           },
           "fields": ["option8", "option13", "option19"],
           "subject": {
               "option8": "Telephely változás",
               "option13": "Munkakör változás",
               "option19": "Képzettségi kód változás"
           },
           "view": {
               "path": "application.views.customers.hopi",
               "filename": "notificationOnChangeTemplate"
           },
           "params": {
               "introduction": "notification_general_introduction",
               "field_text": "notification_general_field_text",
               "messageText": "notification_at_month_expired"
           },
           "addressByRole": [
               "Raktári tréner",
               "HR Adminisztrátor",
               "Recepciós Ügyintéző",
               "HR Generalista",
               "HR Business Partner"
           ],
            "onEvent": "afterSave"
       }',
       2,
       'system',
       NOW()
),
(
    'EmployeeCost',
    '{
        "handler": {
            "namespace": "Components\\\\customers\\\\hopi\\\\Handler",
            "class": "HopiEmailOnDataChangeHandler"
        },
        "fields": ["cost_id"],
        "subject": {
            "cost_id": "Költséghely változás"
        },
        "view": {
            "path": "application.views.customers.hopi",
            "filename": "notificationOnChangeTemplate"
        },
        "params": {
            "introduction": "notification_general_introduction",
            "field_text": "notification_general_field_text",
            "messageText": "notification_at_month_expired"
        },
        "addressByRole": [
            "Raktári tréner",
            "HR Adminisztrátor",
            "Recepciós Ügyintéző",
            "HR Generalista",
            "HR Business Partner"
        ],
        "onEvent": "afterSave"
    }',
    2,
    'system',
    NOW()
);

UPDATE `_sql_version` SET `revision`=51, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -51--2025-02-10-14:00---------------------------------------------------------------

INSERT INTO `notification_on_change_config` (`entity_name`, `config`, `status`, `created_by`, `created_on`) VALUES
(
    'EmployeeContract',
    '{
        "handler": {
            "namespace": "Components\\\\customers\\\\hopi\\\\Handler",
            "class": "HopiEmailOnDataChangeHandler"
        },
        "fields": ["ec_valid_from"],
        "subject": {
            "ec_valid_from": "Szerződés módosulás"
        },
        "view": {
            "path": "application.views.customers.hopi",
            "filename": "notificationOnChangeTemplate"
        },
        "params": {
            "introduction": "notification_general_introduction",
            "field_text": "notification_general_field_text",
            "messageText": "notification_at_month_expired"
        },
        "addressByRole": [
            "Raktári tréner",
            "HR Adminisztrátor",
            "Recepciós Ügyintéző",
            "HR Generalista",
            "HR Business Partner"
        ],
        "onEvent": "afterSave"
    }',
    2,
    'system',
    NOW()
),
(
    'EmployeeContract',
    '{
        "handler": {
            "namespace": "Components\\\\customers\\\\hopi\\\\Handler",
            "class": "HopiEmailOnDataChangeHandler"
        },
        "fields": ["ec_valid_from"],
        "subject": {
            "ec_valid_from": "Új belépő dolgozó"
        },
        "view": {
            "path": "application.views.customers.hopi",
            "filename": "notificationOnChangeTemplate"
        },
        "params": {
            "introduction": "notification_general_introduction",
            "field_text": "notification_general_field_text",
            "messageText": "notification_at_month_expired"
        },
        "addressByRole": [
            "Raktári tréner",
            "HR Adminisztrátor",
            "Recepciós Ügyintéző",
            "HR Generalista",
            "HR Business Partner"
        ],
        "onEvent": "addEmployee"
    }',
    2,
    'system',
    NOW()
),
(
    'Employee',
    '{
        "handler": {
            "namespace": "Components\\\\customers\\\\hopi\\\\Handler",
            "class": "HopiEmailOnDataChangeHandler"
        },
        "fields": ["valid_to"],
        "subject": {
            "valid_to": "Kilépő dolgozó"
        },
        "view": {
            "path": "application.views.customers.hopi",
            "filename": "notificationOnChangeTemplate"
        },
        "params": {
            "introduction": "notification_general_introduction",
            "field_text": "notification_general_field_text",
            "messageText": "notification_at_month_expired"
        },
        "addressByRole": [
            "Raktári tréner",
            "HR Adminisztrátor",
            "Recepciós Ügyintéző",
            "HR Generalista",
            "HR Business Partner"
        ],
        "onEvent": "lockEmployee"
    }',
    2,
    'system',
    NOW()
);

UPDATE
    `app_settings`
SET `setting_value` = 'EmployeeExt,EmployeeCost,EmployeeContract,Employee',
    `modified_by` = 'SZOF-4762',
    `modified_on` = NOW(),
    `note` = 'Prev value: EmployeeExt,EmployeeCost,EmployeeContract'
WHERE `setting_id` = 'notificationOnChangeModels';

UPDATE `_sql_version` SET `revision`=52, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -52--2025-05-08-14:00---------------------------------------------------------------

UPDATE
    `app_settings`
SET `setting_value` = '0',
    `modified_by` = 'SZOF-5630',
    `modified_on` = NOW(),
    `note` = 'Prev value: 1'
WHERE `setting_id` = 'sendEmptyNotificationEmail';

UPDATE `_sql_version` SET `revision`=53, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -53--2025-05-21-15:45---------------------------------------------------------------

INSERT IGNORE INTO `ext_organisation_details` (`organisation_name`, `organisation_name_english`, `status`, `created_by`, `created_on`) VALUES
('Road Office Global Solutions HU DE', 'Road Office Global Solutions HU DE', 2, 'SZOF-5398', NOW());

INSERT IGNORE INTO ext_position_details (position_name, position_name_english, position_category_name, position_category_code, physical_mental_category, accounting_category, status, created_by, created_on) VALUES
('DC Vezető','DC Manager','EXECUTIVES','E','Indirect','fix',2, 'SZOF-5398', NOW()),
('Értékesítési képviselő','Sales Representative','SPECIALISTS','S','Indirect','fix',2, 'SZOF-5398', NOW()),
('Nemzetközi szállítmányozási csoportvezető','International forwarding Team Lead','SPECIALISTS','S','Indirect','fix',2, 'SZOF-5398', NOW()),
('Nemzetközi szállítmányozási fuvarszervező Junior','International forwarder Junior','SPECIALISTS','S','Indirect','fix',2, 'SZOF-5398', NOW()),
('Nemzetközi szállítmányozási fuvarszervező Senior','International forwarder Senior','SPECIALISTS','S','Indirect','fix',2, 'SZOF-5398', NOW()),
('Árazási és projekt szakértő','Tender & Project Expert','SPECIALISTS','S','Indirect','fix',2, 'SZOF-5398', NOW()),
('Regionális vezető','Country Director','EXECUTIVES','E','Indirect','fix',2, 'SZOF-5398', NOW()),
('Nemzetközi szállítmányozási ügyintéző Senior','Transport administrator Senior','ADMIN','A','Indirect','fix',2, 'SZOF-5398', NOW()),
('Értékesítési támogatási specialista','Sales support specialist','SPECIALISTS','S','Indirect','fix',2, 'SZOF-5398', NOW()),
('Szállítási Támogató Menedzser','Transport Support Manager','SPECIALISTS','S','Indirect','fix',2, 'SZOF-5398', NOW()),
('Gépjárművezető oktató','Driver trainer','DRIVERS','D','Direct','variable',2, 'SZOF-5398', NOW()),
('Logisztikai gyakornok','Logistics trainee','SPECIALISTS','S','Indirect','fix',2, 'SZOF-5398', NOW()),
('Szállítási Értékesítési Szakértő','Transport Sales Specialista ','SPECIALISTS','S','Indirect','fix',2, 'SZOF-5398', NOW()),
('VAS Manager','VAS Manager','EXECUTIVES','E','Indirect','fix',2, 'SZOF-5398', NOW());

UPDATE
    ext_position_details
SET
    position_category_name = CASE
                                 WHEN position_name = 'Üzletfejlesztési Vezető' OR position_name = 'Nemzetközi szállítmányozási fuvarszervező' THEN 'SPECIALISTS'
                                 WHEN position_name = 'Nemzetközi Szállítmányozási Irodavezető' THEN 'EXECUTIVES'
                                 ELSE position_category_name
    END,
    position_category_code = CASE
                                 WHEN position_name = 'Üzletfejlesztési Vezető' OR position_name = 'Nemzetközi szállítmányozási fuvarszervező' THEN 'S'
                                 WHEN position_name = 'Nemzetközi Szállítmányozási Irodavezető' THEN 'E'
                                 ELSE position_category_code
    END,
    modified_by = 'SZOF-5398',
    modified_on = NOW()
WHERE
    position_name IN (
                      'Üzletfejlesztési Vezető',
                      'Nemzetközi szállítmányozási fuvarszervező',
                      'Nemzetközi Szállítmányozási Irodavezető'
    )
  AND status = 2;

INSERT INTO `cost_details` (cost_name, cost_code, status, created_by, created_on) VALUES
    ('Road Office Global Solutions HU DE','4216GR10',2,'SZOF-5398',NOW());

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-base','ceo_director','Vezérigazgató, ügyvezető', 1),
('hu', 'ttwa-base','strategy_lead','Vállalati stratégiatervezési egység vezetője', 1),
('hu', 'ttwa-base','comms_lead','Reklám-, pr- és egyéb kommunikációs tevékenységet folytató egység vezetője', 1),
('hu', 'ttwa-base','accounting_specialist','Egyéb számviteli foglalkozású', 1),
('hu', 'ttwa-base','customer_service_rep','Ügyfélszolgálati központ tájékoztatója', 1),
('hu', 'ttwa-base','safety_specialist','Munkavédelmi és üzembiztonsági foglalkozású', 1),
('hu', 'ttwa-base','service_unit_lead','Egyéb szolgáltatást nyújtó egység vezetője', 1),
('hu', 'ttwa-base','finance_admin','Pénzügyi ügyintéző', 1),
('hu', 'ttwa-base','auto_technician','Gépjármű- és motorkarbantartó, -javító', 1),
('hu', 'ttwa-base','hr_advisor','Személyzeti és pályaválasztási szakértő', 1),
('hu', 'ttwa-base','it_lead','Informatikai egység vezetője', 1),
('hu', 'ttwa-base','mechanical_technician','Mechanikaigép-karbantartó, -javító (műszerész)', 1),
('hu', 'ttwa-base','construction_professional','Egyéb építési, szerelési foglalkozású', 1),
('hu', 'ttwa-base','materials_manager','Anyaggazdálkodó, felvásárló', 1),
('en', 'ttwa-base','ceo_director','Chief Executive Officer (CEO), Managing Director', 1),
('en', 'ttwa-base','strategy_lead','Head of Corporate Strategy Planning Unit', 1),
('en', 'ttwa-base','comms_lead','Head of Advertising, PR, and Other Communication Activities Unit', 1),
('en', 'ttwa-base','accounting_specialist','Other Accounting Professional', 1),
('en', 'ttwa-base','customer_service_rep','Customer Service Center Representative', 1),
('en', 'ttwa-base','safety_specialist','Occupational Safety and Operational Safety Specialist', 1),
('en', 'ttwa-base','service_unit_lead','Head of Other Service-Providing Unit', 1),
('en', 'ttwa-base','finance_admin','Financial Administrator', 1),
('en', 'ttwa-base','auto_technician','Vehicle and Engine Maintenance and Repair Technician', 1),
('en', 'ttwa-base','hr_advisor','Human Resources and Career Advisor', 1),
('en', 'ttwa-base','it_lead','Head of IT Unit', 1),
('en', 'ttwa-base','mechanical_technician','Mechanical Equipment Maintenance and Repair Technician (Instrument Technician)', 1),
('en', 'ttwa-base','construction_professional','Other Construction and Installation Professional', 1),
('en', 'ttwa-base','materials_manager','Materials Manager, Procurement Specialist', 1),
('hu', 'ttwa-base', 'feor_code_1120','1120', 1),
('hu', 'ttwa-base', 'feor_code_1414','1414', 1),
('hu', 'ttwa-base', 'feor_code_1416','1416', 1),
('hu', 'ttwa-base', 'feor_code_4129','4129', 1),
('hu', 'ttwa-base', 'feor_code_4225','4225', 1),
('hu', 'ttwa-base', 'feor_code_3163','3163', 1),
('hu', 'ttwa-base', 'feor_code_1329','1329', 1),
('hu', 'ttwa-base', 'feor_code_3611','3611', 1),
('hu', 'ttwa-base', 'feor_code_7331','7331', 1),
('hu', 'ttwa-base', 'feor_code_2523','2523', 1),
('hu', 'ttwa-base', 'feor_code_1221','1221', 1),
('hu', 'ttwa-base', 'feor_code_7334','7334', 1),
('hu', 'ttwa-base', 'feor_code_7529','7529', 1),
('hu', 'ttwa-base', 'feor_code_3623','3623', 1),
('en', 'ttwa-base', 'feor_code_1120','1120', 1),
('en', 'ttwa-base', 'feor_code_1414','1414', 1),
('en', 'ttwa-base', 'feor_code_1416','1416', 1),
('en', 'ttwa-base', 'feor_code_4129','4129', 1),
('en', 'ttwa-base', 'feor_code_4225','4225', 1),
('en', 'ttwa-base', 'feor_code_3163','3163', 1),
('en', 'ttwa-base', 'feor_code_1329','1329', 1),
('en', 'ttwa-base', 'feor_code_3611','3611', 1),
('en', 'ttwa-base', 'feor_code_7331','7331', 1),
('en', 'ttwa-base', 'feor_code_2523','2523', 1),
('en', 'ttwa-base', 'feor_code_1221','1221', 1),
('en', 'ttwa-base', 'feor_code_7334','7334', 1),
('en', 'ttwa-base', 'feor_code_7529','7529', 1),
('en', 'ttwa-base', 'feor_code_3623','3623', 1);

UPDATE
    app_lookup
SET
    valid = 0
WHERE
    lookup_id IN ('option28','option29')
    AND valid = 1
    AND dict_id LIKE 'option%';

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
('option28','101','0','feor_code_1120', '1'),
('option28','102','0','feor_code_3135', '1'),
('option28','103','0','feor_code_1414', '1'),
('option28','104','0','feor_code_1416', '1'),
('option28','105','0','feor_code_4129', '1'),
('option28','106','0','feor_code_4225', '1'),
('option28','107','0','feor_code_3163', '1'),
('option28','108','0','feor_code_1329', '1'),
('option28','109','0','feor_code_3611', '1'),
('option28','110','0','feor_code_7331', '1'),
('option28','111','0','feor_code_2523', '1'),
('option28','112','0','feor_code_1221', '1'),
('option28','113','0','feor_code_7334', '1'),
('option28','114','0','feor_code_7529', '1'),
('option28','115','0','feor_code_3623', '1'),
('option29','101','0','ceo_director', '1'),
('option29','102','0','qa_technician', '1'),
('option29','103','0','strategy_lead', '1'),
('option29','104','0','comms_lead', '1'),
('option29','105','0','accounting_specialist', '1'),
('option29','106','0','customer_service_rep', '1'),
('option29','107','0','safety_specialist', '1'),
('option29','108','0','service_unit_lead', '1'),
('option29','109','0','finance_admin', '1'),
('option29','110','0','auto_technician', '1'),
('option29','111','0','hr_advisor', '1'),
('option29','112','0','it_lead', '1'),
('option29','113','0','mechanical_technician', '1'),
('option29','114','0','construction_professional', '1'),
('option29','115','0','materials_manager', '1');

UPDATE `_sql_version` SET `revision`=54, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -54--2025-07-04-14:15---------------------------------------------------------------

INSERT IGNORE INTO `dictionary` (`lang`, `module`, `dict_id`, `dict_value`, `valid`) VALUES
('hu', 'ttwa-base','document_template_gdpr_declarations','GDPR nyilatkozatok', 1),
('hu', 'ttwa-base','document_template_onboarding_checklist','Cheklista belépési folyamathoz', 1),
('hu', 'ttwa-base','document_template_data_collection_form','Adatfelvételi lap', 1),
('hu', 'ttwa-base','document_template_employment_contracts','Munkaszerződések', 1),
('hu', 'ttwa-base','document_template_labor_code_information','Mt szerinti tájékoztatók', 1),
('hu', 'ttwa-base','document_template_job_descriptions','Munkaköri leírások', 1),
('hu', 'ttwa-base','document_template_referrals','Beutalók', 1),
('hu', 'ttwa-base','document_template_supplementary_declarations_contract','Kiegészítő nyilatkozatok szerződéskötéshez', 1),
('hu', 'ttwa-base','document_template_declarations_contract','Nyilatkozatok szerződéskötéshez', 1),
('hu', 'ttwa-base','document_template_other_docs_contract','Egyéb dokumentum és tájékoztatók a szerződéskötéshez', 1),
('hu', 'ttwa-base','document_template_termination_documents','Felmondáshoz kapcsolódó dokumentumok', 1),
('hu', 'ttwa-base','document_template_contract_amendments','Szerződés módosítások', 1),
('hu', 'ttwa-base','document_template_study_contracts','Tanulmányi szerződések', 1),
('hu', 'ttwa-base','document_template_employer_certificates','Munkáltatói igazolások', 1),
('hu', 'ttwa-base','document_template_deduction_blocking_declarations','Levonás/letiltás nyilatkozatok', 1),
('en', 'ttwa-base','document_template_gdpr_declarations','GDPR Declarations', 1),
('en', 'ttwa-base','document_template_onboarding_checklist','Onboarding Checklist', 1),
('en', 'ttwa-base','document_template_data_collection_form','Data Collection Form', 1),
('en', 'ttwa-base','document_template_employment_contracts','Employment Contracts', 1),
('en', 'ttwa-base','document_template_labor_code_information','Labor Code Information', 1),
('en', 'ttwa-base','document_template_job_descriptions','Job Descriptions', 1),
('en', 'ttwa-base','document_template_referrals','Referrals', 1),
('en', 'ttwa-base','document_template_supplementary_declarations_contract','Supplementary Declarations for Contract Signing', 1),
('en', 'ttwa-base','document_template_declarations_contract','Declarations for Contract Signing', 1),
('en', 'ttwa-base','document_template_other_docs_contract','Other Documents and Information for Contract Signing', 1),
('en', 'ttwa-base','document_template_termination_documents','Termination-Related Documents', 1),
('en', 'ttwa-base','document_template_contract_amendments','Contract Amendments', 1),
('en', 'ttwa-base','document_template_study_contracts','Study Contracts', 1),
('en', 'ttwa-base','document_template_employer_certificates','Employer Certificates', 1),
('en', 'ttwa-base','document_template_deduction_blocking_declarations','Deduction/Blocking Declarations', 1);

INSERT IGNORE INTO `app_lookup` (`lookup_id`, `lookup_value`, `lookup_default_value`, `dict_id`, `valid`) VALUES
('document_template_category','1','0','document_template_gdpr_declarations', '1'),
('document_template_category','2','0','document_template_onboarding_checklist', '1'),
('document_template_category','3','0','document_template_data_collection_form', '1'),
('document_template_category','4','0','document_template_employment_contracts', '1'),
('document_template_category','5','0','document_template_labor_code_information', '1'),
('document_template_category','6','0','document_template_job_descriptions', '1'),
('document_template_category','7','0','document_template_referrals', '1'),
('document_template_category','8','0','document_template_supplementary_declarations_contract', '1'),
('document_template_category','9','0','document_template_declarations_contract', '1'),
('document_template_category','10','0','document_template_other_docs_contract', '1'),
('document_template_category','11','0','document_template_termination_documents', '1'),
('document_template_category','12','0','document_template_contract_amendments', '1'),
('document_template_category','13','0','document_template_study_contracts', '1'),
('document_template_category','14','0','document_template_employer_certificates', '1'),
('document_template_category','15','0','document_template_deduction_blocking_declarations', '1');

UPDATE `_sql_version` SET `revision`=55, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -55--2025-07-04-16:15---------------------------------------------------------------

INSERT IGNORE INTO `column_rights` (`controller_id`, `column_id`, `rolegroup_id`, `status`, `created_by`, `created_on`) VALUES
     ('customers/hopi/reportEmployeeListGeneralData', 'tax_number', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'employee_contract_type', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option7', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option11', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option28', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option29', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option30', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option31', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option32', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option33', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'option34', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'note', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option2', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option1', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option3', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option4', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option5', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option6', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option7', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option8', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option9', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option10', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option11', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option12', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option13', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option14', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option15', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option16', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option17', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option18', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option19', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext2_option20', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext7_option1', 'ALL', 2, 'SZOF-5733', NOW()),
     ('customers/hopi/reportEmployeeListGeneralData', 'ext7_option2', 'ALL', 2, 'SZOF-5733', NOW());

UPDATE `_sql_version` SET `revision`=56, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -56--2025-07-14-13:00---------------------------------------------------------------

UPDATE `notification_email_config`
SET `sql` = REPLACE(`sql`, '<= CURDATE() + INTERVAL 30 DAY', '= CURDATE() + INTERVAL 30 DAY')
WHERE `process_id` IN (
                       'notificationAboutMachineryLicenceHealthValidity',
                       'notificationAboutDrivingLicenceCECategoryValidity',
                       'notificationAboutGKIlicenceValidity',
                       'notificationAboutDriverCardValidity',
                       'notificationAboutADRCardValidity',
                       'notificationAboutOccupationalHealthCheckValidity',
                       'notificationAboutPulmonaryFilterExpirationValidity',
                       'notificationAboutDrivingLicenseBCategoryValidity'
);

UPDATE `_sql_version` SET `revision`=57, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -57--2025-07-31-13:00---------------------------------------------------------------

UPDATE
    ext_position_details
SET
	position_category_name = 'ADMIN',
	position_category_code = 'A',
	modified_by = 'SZOF-5398',
    modified_on = NOW()
WHERE
    position_name = 'WMS Operátor'
    AND status = 2;

UPDATE `_sql_version` SET `revision`=58, `updated_on`=NOW() WHERE `module` = 'c_hopi';

-- VERSION -58--2025-07-31-14:00---------------------------------------------------------------

UPDATE `app_settings` SET `setting_value` = 'HOPI' WHERE `setting_id` = 'ptr_customer';
UPDATE `app_settings` SET `setting_value` = 'NEXON' WHERE `setting_id` = 'ptr_default_mode';
UPDATE `app_settings` SET `setting_value` = '1' WHERE `setting_id` = 'ptrCorrectLastMonth';
UPDATE `app_settings` SET `setting_value` = '0' WHERE `setting_id` = 'ptr_5_2_mode';
UPDATE `app_settings` SET `setting_value` = '0' WHERE `setting_id` = 'ptr_nexon_payroll';
UPDATE `app_settings` SET `setting_value` = 'employee.tax_number' WHERE `setting_id` = 'ptr_payrollEmpId';
UPDATE `app_settings` SET `setting_value` = '1' WHERE `setting_id` = 'ptrApproverShowPayrollButton';
UPDATE `app_settings` SET `setting_value` = '1' WHERE `setting_id` = 'ptr_OnlyQuitingEmployees';
UPDATE `app_settings` SET `setting_value` = '1' WHERE `setting_id` = 'summarySheet_calculation_sunday_sign';
-- UPDATE `app_settings` SET `setting_value` = '1'  WHERE `setting_id` = 'summarySheet_calculation_natianal_holiday_sign';

UPDATE workgroup set sunday_sign_interval = '00:00:00' WHERE status = 2;

INSERT INTO hopi_ease.payroll_transfer_config
(target, transfer_id, content_type, content_category, content_value, null_value, select_value, select_as, total_sum,
 weekday, is_restday, is_holiday, is_public_holiday, sql_conditions, sql_conditions_2, note, status,
 created_by, created_on, modified_by, modified_on, pre_row_id)
VALUES
    ('NEXON', '5', 'state_type_id', null, 'fceedab36bb56f3a59665972fa0a7d54', 0, null, null, 0,
     null, 0, 0, 0, null, null, 'Rendkívüli szabadság', 2,
     'ptc_sys', null, null, null, null),
    ('NEXON', '10', 'state_type_id', null, '628834878c5bc72f60283e37865678b6', 0, null, null, 0,
     null, 0, 0, 0, null, null, 'Fizetésnélküli szabadság', 2,
     'ptc_sys', null, null, null, null),
    ('NEXON', '23', 'state_type_id', null, '269b59b4efbbb4ef1d527492dc06cb60', 0, null, null, 0,
     null, 0, 0, 0, null, null, 'Igazolt de nem fizetett', 2,
     'ptc_sys', null, null, null, null);

UPDATE payroll_transfer_config SET status=5 WHERE row_id = 6; -- Otthoni munka nem kell
UPDATE payroll_transfer_config
SET status = 5
WHERE row_id IN (7 /*CM*/,8 /*T2*/,9 /*TA*/, 10 /*T1*/, 11 /*CE*/);

INSERT INTO payroll_transfer_config
(target, transfer_id, content_type, content_category, content_value, null_value, select_value, select_as, total_sum,
 weekday, is_restday, is_holiday, is_public_holiday, sql_conditions, sql_conditions_2, note, status,
 created_by, created_on, modified_by, modified_on, pre_row_id)
VALUES
    ( 'NEXON', 'CF', 'inside_type_id', null, null, 0, null, null, 0,
      null, 0, 0, 0, null,
      '(data.`inside_type_id` LIKE "%du2%" OR data.`inside_type_id` LIKE "%ej%")
             AND (data.`dt__schedule_wtdu2_sum` + data.`dt__schedule_wtej_sum`) > 3600
             AND data.`work_order_start_differs` = 1',
      'Műszakpótlék 30%', 2, 'ptc_sys', null, null, null, null
    ),
    ('NEXON', 'CK', 'inside_type_id', NULL, NULL, 0,
     'data.`schedule_standby_full_time` - data.`dt__overtime_st_sum`',
     'new_value_sum_payroll', 0, NULL, 0, 0, 0, NULL,
     'data.`schedule_standby_full_time` > 0',
     'Készenlét', 2, 'ptc_sys', NULL, NULL, NULL, NULL
    ),
    ( 'NEXON', 'TA1 ált. mr.szerint', 'inside_type_id', null, null, 0, null, null, 0,
      null, 0, 0, 0, null,
      '(data.`inside_type_id` LIKE "ot%" OR data.`inside_type_id` LIKE "paidot%")',
      'Túlóra alapidő', 2, 'ptc_sys', null, null, null, null
    ),
    ( 'NEXON', 'T3 pótlék', 'inside_type_id', null, null, 0, null, null, 0,
      null, 0, 0, 0, null,
      '(data.`inside_type_id` LIKE "ot%"
         AND data.`inside_type_id` NOT LIKE "otstw%"
         AND data.`inside_type_id` NOT LIKE "otw%"
         AND data.`inside_type_id` NOT LIKE "paidot%")',
      'Túlóra 50% pótlék hétköznap', 2, 'ptc_sys', null, null, null, null
    ),
    ( 'NEXON', 'T4 pótlék', 'inside_type_id', null, null, 0, null, null, 0,
      null, 0, 0, 0, null,
      '(data.`inside_type_id` LIKE "otw%" OR data.`inside_type_id` LIKE "otstw%")
         AND data.`inside_type_id` NOT LIKE "paidot%")',
      'Túlóra 100% pótlék hétvége', 5, 'ptc_sys', null, null, null, null
    ),
    ( 'NEXON', 'T3 pótlék mik', 'inside_type_id', null, null, 0, null, null, 0,
      null, 0, 0, 0, null,
      'data.`inside_type_id` LIKE "paidot%")',
      'T3 túlóra 50% munkaidőkeret', 5, 'ptc_sys', null, null, null, null
    ),
    ( 'NEXON', 'C24', 'inside_type_id', null, null, 0, null, null, 0,
      null, 0, 0, 0, null,
      'data.`has_absence` = 0
         AND data.cost_id IN ("50017193", "50017193") /* Frozen Gyál1 & H5 */
         AND (data.`inside_type_id` LIKE "wt%" OR data.`inside_type_id` LIKE "ot%")',
      'fagyos pótlék', 2, 'ptc_sys', null, null, null, null
    ),
    ( 'NEXON', 'CV', 'inside_type_id', null, null, 0, null, null, 0,
      null, 0, 0, 0, null,
      '(data.`inside_type_id` LIKE "wt%sun%" OR data.`inside_type_id` LIKE "ot%sun%")',
      'Vasárnapi pótlék 50% beosztás szerinti', 2, 'ptc_sys', null, null, null, null
    );


UPDATE `_sql_version` SET `revision`=59, `updated_on`=NOW() WHERE `module` = 'c_hopi';
-- VERSION -59--2025-09-02-14:00---------------------------------------------------------------

INSERT INTO payroll_transfer_config
(target, transfer_id, content_type, content_category, content_value, null_value, select_value, select_as, total_sum,
 weekday, is_restday, is_holiday, is_public_holiday, sql_conditions, sql_conditions_2, note, status,
 created_by, created_on, modified_by, modified_on, pre_row_id)
VALUES
    ( 'NEXON', 'EE 4  ünnep', 'inside_type_id', null, null, 0, null, null, 0,
      null, 0, 0, 0, null,
      '(data.`inside_type_id` LIKE "wt%" OR data.`inside_type_id` LIKE "ot%")
             AND data.`is_public_holiday` = 1',
      'Műszakpótlék 30%', 2, 'ptc_sys', null, null, null, null
    );

UPDATE `_sql_version` SET `revision`=60, `updated_on`=NOW() WHERE `module` = 'c_hopi';
-- VERSION -60--2025-09-04-09:00---------------------------------------------------------------

UPDATE `app_settings`
SET `setting_value` = '1', `modified_by` = 'SZOF-6086', `modified_on` = NOW()
WHERE `setting_id` = 'standbyUpgradedVersion';

UPDATE `app_settings`
SET `setting_value` = '0', `modified_by` = 'SZOF-6086', `modified_on` = NOW()
WHERE `setting_id` = 'standbyUpgradedVersionInWorktime';

UPDATE `app_settings`
SET `setting_value` = '1', `modified_by` = 'SZOF-6086', `modified_on` = NOW()
WHERE `setting_id` = 'summarySheet_calculation_standby_sign';

UPDATE `app_settings`
SET `setting_value` = '1', `modified_by` = 'SZOF-6086', `modified_on` = NOW()
WHERE `setting_id` = 'summarySheet_calculation_standby_bot_accept_and_change_to_ovetime';

UPDATE `app_settings`
SET `setting_value` = '0', `modified_by` = 'SZOF-6086', `modified_on` = NOW()
WHERE `setting_id` = 'summarySheet_calculation_bot_automaticaly_accept';

UPDATE `payroll_transfer_config`
SET select_value = 'data.`schedule_standby_full_time` - data.`dt__overtime_st_sum` - data.`dt__worktime_st_sum`',
    sql_conditions_2 = 'data.`schedule_standby_full_time` - data.`dt__overtime_st_sum` - data.`dt__worktime_st_sum`'
WHERE transfer_id = 'CK' AND status = 2;

UPDATE `_sql_version` SET `revision`=61, `updated_on`=NOW() WHERE `module` = 'c_hopi';
-- VERSION -61--2025-09-10-09:00---------------------------------------------------------------

UPDATE app_settings SET setting_value = '1' WHERE setting_id = 'standbyUpgradedVersion';

UPDATE `_sql_version` SET `revision`=62, `updated_on`=NOW() WHERE `module` = 'c_hopi';
-- VERSION -62--2025-09-10-11:00---------------------------------------------------------------

UPDATE `payroll_transfer_config`
SET `sql_conditions_2` = '(data.`inside_type_id` LIKE "%du2%" OR data.`inside_type_id` LIKE "%ej%")
AND (data.`dt__schedule_wtdu2_sum` + data.`dt__schedule_wtej_sum`) > 3600'
WHERE `transfer_id` = 'CF' AND `status` = 2;

UPDATE `payroll_transfer_config`
SET `status` = 2
WHERE `transfer_id` IN ('T4 pótlék', 'T3 pótlék mik') AND `status` = 5;

UPDATE `payroll_transfer_config`
SET `sql_conditions_2` = '(data.`inside_type_id` LIKE "otw%" OR data.`inside_type_id` LIKE "otstw%")
         AND data.`inside_type_id` NOT LIKE "paidot%"'
WHERE `transfer_id` = 'T4 pótlék' AND `status` = 2;

UPDATE `payroll_transfer_config`
SET `sql_conditions_2` = 'data.`inside_type_id` LIKE "paidot%"'
WHERE `transfer_id` = 'T3 pótlék mik' AND `status` = 2;

UPDATE `payroll_transfer_config`
SET `sql_conditions_2` = 'data.`has_absence` = 0
         AND data.unit_id IN ("50017193", "50017194") /* Frozen Gyál1 & H5 */
         AND (data.`inside_type_id` LIKE "wt%" OR data.`inside_type_id` LIKE "ot%")'
WHERE `transfer_id` = 'C24' AND `status` = 2;

UPDATE `_sql_version` SET `revision`=63, `updated_on`=NOW() WHERE `module` = 'c_hopi';
-- VERSION -63--2025-09-10-11:00---------------------------------------------------------------

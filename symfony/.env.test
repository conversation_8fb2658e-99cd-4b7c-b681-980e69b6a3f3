# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET=2b509c49f1def33e1d566deb46c3cc4a
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots
DATABASE_URL="mysql://root:testpass@mysql:3306/test_database"

###> lexik/jwt-authentication-bundle ###
JWT_SECRET_KEY=%kernel.project_dir%/config/jwt/private.pem
JWT_PUBLIC_KEY=%kernel.project_dir%/config/jwt/public.pem
JWT_PASSPHRASE=****************************************************************
###< lexik/jwt-authentication-bundle ###

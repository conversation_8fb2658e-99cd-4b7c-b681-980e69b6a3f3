imports:
  - { resource: security.yaml }

parameters:
  env(CUSTOMER): "system"
  customer: '%env(CUSTOMER)%'
  env(JWT_TTL): "1800"
  env(MIGRATION_NAMESPACE): "NO-NAMESPACE"
  env(REQUEST_LOG): "1"
  env(STORAGE_DIR): "../webroot/file_storage/%env(CUSTOMER)%/"
  env(BUILD_ID): "dev"
  env(DEFAULT_VALID_FROM): "1970-01-01"
  env(DEFAULT_VALID_TO): "2038-01-01"
  env(REDIS_URL): "redis://redis"
  env(REDIS_ENABLED): 0
  env(CACHE_ADAPTER): "cache.adapter.array_no_serialize"
  env(MAX_LOG_LINE): "10000"
  env(MAX_LOG_FILE): "10"
  env(DEFAULT_ITEMS_PER_PAGE): "50"
  env(MAX_ITEMS_PER_PAGE): "50"
  env(DOCTRINE_DATABASE_VERSION): "8.0"
  staticOperationIds:
    /api/auth:
      operationId: /api/auth
    /api/health:
      operationId: healthcheck
    /api/token/refresh:
      operationId: /api/token/refresh
    /api/user/login-by-session:
      operationId: api_login_by_session
    /api/_profiler/:
      operationId: api_profiler
    /api/_profiler:
      operationId: api_profiler
    /api/_wdt:
      operationId: api_wdt
    /api/_wdt/:
      operationId: api_wdt
  staticRoles:
    /api/auth:
      requestName: "auth-request"
    api_profiler:
      requestName: "api_profiler"
    api_wdt:
      requestName: "api_wdt"
    /api/token/refresh:
      requestName: "auth-token-refresh"
    _api_/user/logout_post:
      requestName: "logout"
      processIds: [ ]
    _api_/user/logout-all-device_post:
      requestName: "logout-all-device"
      processIds: [ ]
    api_login_by_session:
      requestName: "login-by-session"
      processIds: [ ]
  skipRequestNameCheckOperationIds:
    - _api_/user/logout_post
    - _api_/user/logout-all-device_post
    - _api_/user/login-by-session_post
    - api_profiler
    - api_wdt
  public_dir: "%kernel.project_dir%/public"
  static_dir: "%public_dir%/static"
  common_static_dir: "%static_dir%/common"
  customer_static_dir: "%static_dir%/customer/%customer%"
  translation_dir: "%customer_static_dir%/translations"
  user_interface_dir: "%customer_static_dir%/user_interface"
  metadata_file: "%common_static_dir%/metadata.json"
  permission_rights_file: "%common_static_dir%/permission_rights.json"
  build_identifier: "%env(BUILD_ID)%"

  defaultValidFrom: "%env(DEFAULT_VALID_FROM)%"
  defaultValidTo: "%env(DEFAULT_VALID_TO)%"
  env(DEFAULT_LANGUAGE): "hu"
  defaultLanguage: "%env(DEFAULT_LANGUAGE)%"
  fragment:
    path: '/api/_fragment'

  mobileAgents:
    - '/okhttp\/4.12.0/'
    - '/EasePlusPlus\/1/'
    - '/CFNetwork/i'
services:
  _defaults:
    autowire: true
    autoconfigure: true



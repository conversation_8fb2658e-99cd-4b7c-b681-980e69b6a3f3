<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Guesser\CriterionByQueryGuessers;

use Doctrine\Inflector\InflectorFactory;
use LoginAutonom\CoreBundle\Enum\BuiltinTypeEnum;
use LoginAutonom\CoreBundle\Util\AnnotationHelper;
use LoginAutonom\DatabaseBundle\Builder\SQLQueryDescriptorFromQueryBuilderBuilder;
use LoginAutonom\DatabaseBundle\DTO\CriterionGuessingInfo;
use LoginAutonom\DatabaseBundle\Interfaces\CriterionByQueryGuesserInterface;

final readonly class CriterionByFieldNameGuesser implements CriterionByQueryGuesserInterface
{
    public function __construct(
        private AnnotationHelper $annotationHelper,
    ) {
    }

    public function guess(CriterionGuessingInfo $info): CriterionGuessingInfo
    {
        if (!$info->hasEntityClass()) {
            $info = $this->guessEntity($info);
        } elseif (!$info->hasFieldName()) {
            $info = $this->guessProperty($info);
        }

        return $info;
    }

    public function isSupported(CriterionGuessingInfo $info): bool
    {
        return !$info->hasFieldName() && !$info->isSkip();
    }

    public static function priority(): int
    {
        return 0;
    }

    private function guessEntity(CriterionGuessingInfo $info): CriterionGuessingInfo
    {
        [$singular, $plural] = $this->guessPropertyNames($info);
        $isSingle = $singular === $info->getPropertyName();
        $sqlQueryDescriptor = (new SQLQueryDescriptorFromQueryBuilderBuilder())
            ->reset()
            ->setQueryBuilder($info->getQueryBuilder())
            ->build();
        foreach ($sqlQueryDescriptor->getEntitiesWithAlias() as $entityClass) {
            $reflection = new \ReflectionClass($entityClass);
            if ($reflection->hasProperty($singular)) {
                $info->setEntityClass($entityClass);
                $info->setFieldName($singular);
                $isSingle ? $info->setSingleValue() : $info->setMultipleValue();

                return $info;
            }
            if ($reflection->hasProperty($plural)) {
                $info->setEntityClass($entityClass);
                $info->setFieldName($plural);
                $isSingle ? $info->setSingleValue() : $info->setMultipleValue();

                return $info;
            }
        }

        return $info;
    }

    private function guessProperty(CriterionGuessingInfo $info): CriterionGuessingInfo
    {
        [$singular, $plural] = $this->guessPropertyNames($info);
        $entityClass = $info->getEntityClass();
        $reflection = new \ReflectionClass($entityClass);
        if ($reflection->hasProperty($singular)) {
            $info->setEntityClass($entityClass);
            $info->setFieldName($singular);
        } elseif ($reflection->hasProperty($plural)) {
            $info->setEntityClass($entityClass);
            $info->setFieldName($plural);
        }

        return $info;
    }

    private function guessPropertyNames(CriterionGuessingInfo $info): array
    {
        $inflector = InflectorFactory::create()->build();
        $query = $info->getQuery();
        $propertyName = $info->getPropertyName();
        $isArray = $this->annotationHelper->isPropertyType(
            $query,
            $propertyName,
            BuiltinTypeEnum::ARRAY
        );
        if ($isArray) {
            $singular = $inflector->singularize($propertyName);
            $plural = $propertyName;
        } else {
            $singular = $propertyName;
            $plural = $inflector->pluralize($propertyName);
        }

        return [$singular, $plural];
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Guesser;

use LoginAutonom\CoreBundle\Util\AnnotationHelper;
use LoginAutonom\DatabaseBundle\Builder\SQLQueryDescriptorFromQueryBuilderBuilder;
use LoginAutonom\DatabaseBundle\DTO\CriterionGuessingInfo;
use LoginAutonom\DatabaseBundle\Guess\CriterionGuess;
use LoginAutonom\DatabaseBundle\Interfaces\CriterionByQueryGuesserInterface;
use Symfony\Component\DependencyInjection\Attribute\TaggedIterator;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

final readonly class CriterionByQueryGuesser
{
    public function __construct(
        private AnnotationHelper $annotationHelper,
        #[TaggedIterator(CriterionByQueryGuesserInterface::TAG, defaultPriorityMethod: 'priority')]
        private iterable $guessers,
        private PropertyAccessorInterface $propertyAccessor,
        private UniqueDoctrineParameterNameGuesser $uniqueDoctrineParameterNameGuesser,
    ) {
    }

    public function guess(CriterionGuessingInfo $info): CriterionGuess
    {
        /** @var CriterionByQueryGuesserInterface $guesser */
        foreach ($this->guessers as $guesser) {
            if ($guesser->isSupported($info)) {
                $info = $guesser->guess($info);
            }
        }
        $guess = new CriterionGuess();
        if (!$info->hasEntityClass() || !$info->hasFieldName() || $info->isSkip()) {
            $guess->setSkip(true);
            return $guess;
        }
        $guess->setNot($info->isNot());
        $guess->setLike($info->isLike());
        $guess->setEntityClass($info->getEntityClass());
        $guess->setFieldName($info->getFieldName());
        $sqlQueryDescriptor = (new SQLQueryDescriptorFromQueryBuilderBuilder())
            ->reset()
            ->setQueryBuilder($info->getQueryBuilder())
            ->build();
        $guess->setEntityAlias(
            $sqlQueryDescriptor->getAliasForEntityClass($info->getEntityClass())
        );
        $guess->setMultipleValue($info->isMultipleValue());
        $guess->setParameterName(":" .
            $this->uniqueDoctrineParameterNameGuesser->guess(
                $info->getFieldName(),
                $info->getQueryBuilder()
            ));
        $guess->setParameterValue(
            $this->propertyAccessor->getValue($info->getQuery(), $info->getPropertyName())
        );

        return $guess;
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Guesser;

use LoginAutonom\CoreBundle\Exception\NotImplementedException;
use LoginAutonom\CoreBundle\Util\AnnotationHelper;
use LoginAutonom\DatabaseBundle\Attribute\NameField;

final readonly class FieldNameByNameFieldAttributeGuesser
{
    public function __construct(
        private AnnotationHelper $annotationHelper
    ) {
    }

    /**
     * @throws NotImplementedException
     */
    public function guess(string $entityClass): string
    {
        $attributeInstances = $this->annotationHelper->findPropertiesByAttribute(
            $entityClass,
            NameField::class
        );

        foreach ($attributeInstances as $fieldName => $attributeInstanceByField) {
            return $fieldName;
        }
        throw new NotImplementedException("NameField attribute missing in: {$entityClass}!");
    }
}

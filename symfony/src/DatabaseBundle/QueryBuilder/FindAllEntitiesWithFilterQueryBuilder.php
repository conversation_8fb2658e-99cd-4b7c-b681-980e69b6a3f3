<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\QueryBuilder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;
use LoginAutonom\DatabaseBundle\Message\Query\FindAllEntitiesWithFilterQuery;

final class FindAllEntitiesWithFilterQueryBuilder extends AbstractQueryBuilder
{
    public const ALIAS = 'entityClass';

    public function build(DatabaseQueryMessageInterface $query): QueryBuilder
    {
        /** @var FindAllEntitiesWithFilterQuery $query */
        $qb = $this->util->createQueryBuilder();
        $qb->select(self::ALIAS)
            ->from($query->getEntityClass(), self::ALIAS);
        $qb = $this->util->addSimpleCriteria(
            $query->getFilters(),
            $query->getEntityClass(),
            $qb
        );

        return $qb;
    }

    public static function getHandledQueryClass(): string
    {
        return FindAllEntitiesWithFilterQuery::class;
    }
}

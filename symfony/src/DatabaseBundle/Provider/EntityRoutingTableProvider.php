<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Provider;

use LoginAutonom\DatabaseBundle\Descriptor\EntityRoutingTable;
use LoginAutonom\DatabaseBundle\Generator\EntityRoutingTableGenerator;
use Symfony\Contracts\Cache\CacheInterface;

final readonly class EntityRoutingTableProvider
{
    public const CACHE_KEY = 'cache';

    public function __construct(
        private EntityRoutingTableGenerator $entityRoutingTableGenerator,
        private CacheInterface $entityRoutingTableCache,
    ) {
    }

    public function provide(): EntityRoutingTable
    {
        return $this->entityRoutingTableCache->get(
            self::CACHE_KEY,
            fn() => $this->doProvide()
        );
    }

    public function reset(): void
    {
        $this->entityRoutingTableCache->delete(self::CACHE_KEY);
    }

    private function doProvide(): EntityRoutingTable
    {
        return $this->entityRoutingTableGenerator->generate();
    }
}

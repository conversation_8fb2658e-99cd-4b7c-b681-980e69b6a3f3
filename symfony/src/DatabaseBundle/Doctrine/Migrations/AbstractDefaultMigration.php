<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Doctrine\Migrations;

use Doctrine\Migrations\AbstractMigration;
use Doctrine\Migrations\Exception\AbortMigration;
use Doctrine\ORM\Mapping\ClassMetadata;
use Doctrine\ORM\Tools\SchemaTool;
use LoginAutonom\DatabaseBundle\Descriptor\DBCHSQLVersionDescriptor;
use LoginAutonom\DatabaseBundle\Descriptor\DBCHSQLVersionsDescriptor;
use LoginAutonom\DatabaseBundle\Provider\EntityManagerProvider;
use Ramsey\Uuid\Uuid;
use Symfony\Component\String\ByteString;

abstract class AbstractDefaultMigration extends AbstractMigration
{
    private EntityManagerProvider $entityManagerProvider;
    private ?DBCHSQLVersionsDescriptor $DBCHSQLVersions = null;

    public function setEntityManagerProvider(EntityManagerProvider $entityManagerProvider): void
    {
        $this->entityManagerProvider = $entityManagerProvider;
    }

    protected function createTableIfNotExists(string $entityFQCN): void
    {
        if (!$this->isTableExists($entityFQCN)) {
            $this->createTableByEntityClass($entityFQCN);
        }
    }

    protected function isTableExists(string $entityFQCN): bool
    {
        return $this->isTableExistsFromSchema(
            $this->getTableNameByEntity($entityFQCN)
        );
    }

    protected function isTableExistsFromSchema(string $tableName): bool
    {
        $schemaManager = $this->connection->createSchemaManager();
        return $schemaManager->tablesExist([$tableName]);
    }

    protected function getTableNameByEntity(string $entityFQCN): string
    {
        $classMetadata = $this->getClassMetadata($entityFQCN);

        return $classMetadata->getTableName();
    }

    protected function createTableByEntityClass(string $entityFQCN): void
    {
        $createSchemaSQL = $this->getCreateSchemaSQL($entityFQCN);
        foreach ($createSchemaSQL as $sql) {
            $this->addSql($sql);
        }
    }

    protected function getCreateSchemaSQL(string $entityFQCN): array
    {
        $classMetadata = $this->getClassMetadata($entityFQCN);
        $schemaTool = new SchemaTool($this->entityManagerProvider->provideByClass($entityFQCN));

        return $schemaTool->getCreateSchemaSql([$classMetadata]);
    }

    protected function recreateTableByEntityClass(string $entityFQCN): void
    {
        $schemaManager = $this->connection->createSchemaManager();
        if (
            $schemaManager->tablesExist([
            $this->getTableNameByEntity($entityFQCN),
            ])
        ) {
            $this->dropTableByEntityClass($entityFQCN);
        }
        $this->createTableByEntityClass($entityFQCN);
    }

    protected function dropTableByEntityClass(string $entityFQCN): void
    {
        $dropSchemaSQL = $this->getDropSchemaSQL($entityFQCN);
        foreach ($dropSchemaSQL as $sql) {
            $this->addSql($sql);
        }
    }

    protected function getDropSchemaSQL(string $entityFQCN): array
    {
        $classMetadata = $this->getClassMetadata($entityFQCN);
        $schemaTool = new SchemaTool($this->entityManagerProvider->provideByClass($entityFQCN));

        return $schemaTool->getDropSchemaSQL([$classMetadata]);
    }

    protected function hasModule(string $moduleName): bool
    {
        return $this->getDBCHSQLVersionDescriptor()->hasVersionByModule($moduleName);
    }

    protected function getDBCHSQLVersionDescriptor(): DBCHSQLVersionsDescriptor
    {
        if (isset($this->DBCHSQLVersions)) {
            return $this->DBCHSQLVersions;
        }
        $rawLines = $this->connection->fetchAllAssociative('SELECT * FROM _sql_version');
        $sqlVersions = new DBCHSQLVersionsDescriptor();
        foreach ($rawLines as $rawLine) {
            $sqlVersions->addVersion(
                new DBCHSQLVersionDescriptor(
                    $rawLine['id'],
                    $rawLine['module'],
                    $rawLine['major_minor'],
                    $rawLine['revision'],
                    (bool)$rawLine['upgrade_block'],
                    \DateTime::createFromFormat('Y-m-d H:i:s', $rawLine['updated_on']),
                )
            );
        }
        $this->DBCHSQLVersions = $sqlVersions;

        return $this->DBCHSQLVersions;
    }

    public function isNullable(string $entityClass, string $fieldName): bool
    {
        $schemaManager = $this->connection->createSchemaManager();
        $columns = $schemaManager->listTableColumns($this->getTableNameByEntity($entityClass));
        if (!array_key_exists($fieldName, $columns)) {
            throw new AbortMigration(
                "Column '$fieldName' does not exist in table '$entityClass'."
            );
        }
        $column = $columns[$fieldName];
        return !$column->getNotnull();
    }

    private function getClassMetadata(string $entityFQCN): ClassMetadata
    {
        return $this->entityManagerProvider->provideByClass($entityFQCN)->getClassMetadata($entityFQCN);
    }

    protected function generateRandomString(int $length = 16): string
    {
        return ByteString::fromRandom($length)->toString();
    }

    protected function generateUUID7(): string
    {
        return Uuid::uuid7()->toString();
    }
}

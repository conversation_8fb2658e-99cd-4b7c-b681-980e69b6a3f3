<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Guess;

final class CriterionGuess
{
    private string $entityClass;
    private string $entityAlias;
    private string $fieldName;
    private string $parameterName;
    private mixed $parameterValue;
    private bool $multipleValue = false;
    private bool $skip = false;
    private bool $not = false;
    private bool $like = false;

    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    public function hasEntityClass(): bool
    {
        return isset($this->entityClass);
    }

    public function setEntityClass(string $entityClass): void
    {
        $this->entityClass = $entityClass;
    }

    public function getEntityAlias(): string
    {
        return $this->entityAlias;
    }

    public function hasEntityAlias(): bool
    {
        return isset($this->entityAlias);
    }

    public function setEntityAlias(string $entityAlias): void
    {
        $this->entityAlias = $entityAlias;
    }

    public function getFieldName(): string
    {
        return $this->fieldName;
    }

    public function hasFieldName(): bool
    {
        return isset($this->fieldName);
    }

    public function setFieldName(string $fieldName): void
    {
        $this->fieldName = $fieldName;
    }

    public function getParameterName(): string
    {
        return $this->parameterName;
    }

    public function hasParameterName(): bool
    {
        return isset($this->parameterName);
    }

    public function setParameterName(string $parameterName): void
    {
        $this->parameterName = $parameterName;
    }

    public function getParameterValue(): mixed
    {
        return $this->parameterValue;
    }

    public function hasParameterValue(): bool
    {
        return isset($this->parameterValue);
    }

    public function setParameterValue(mixed $parameterValue): void
    {
        $this->parameterValue = $parameterValue;
    }

    public function isMultipleValue(): bool
    {
        return $this->multipleValue;
    }

    public function hasIsMultipleValue(): bool
    {
        return isset($this->multipleValue);
    }

    public function setMultipleValue(bool $multipleValue): void
    {
        $this->multipleValue = $multipleValue;
    }

    public function isSkip(): bool
    {
        return $this->skip;
    }

    public function hasSkip(): bool
    {
        return isset($this->skip);
    }

    public function setSkip(bool $skip): void
    {
        $this->skip = $skip;
    }

    public function isNot(): bool
    {
        return $this->not;
    }

    public function setNot(bool $not): void
    {
        $this->not = $not;
    }

    public function isLike(): bool
    {
        return $this->like;
    }

    public function hasLike(): bool
    {
        return isset($this->like);
    }

    public function setLike(bool $like): void
    {
        $this->like = $like;
    }
}

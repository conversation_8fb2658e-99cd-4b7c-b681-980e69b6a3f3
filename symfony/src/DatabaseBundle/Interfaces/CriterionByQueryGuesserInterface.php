<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Interfaces;

use LoginAutonom\DatabaseBundle\DTO\CriterionGuessingInfo;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(CriterionByQueryGuesserInterface::TAG)]
interface CriterionByQueryGuesserInterface
{
    public const TAG = 'criterion.by.query.guesser';

    public function guess(CriterionGuessingInfo $info): CriterionGuessingInfo;
    public function isSupported(CriterionGuessingInfo $info): bool;

    public static function priority(): int;
}

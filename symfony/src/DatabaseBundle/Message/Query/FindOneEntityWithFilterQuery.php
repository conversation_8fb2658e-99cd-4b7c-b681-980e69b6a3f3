<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Message\Query;

use LoginAutonom\DatabaseBundle\Interfaces\FindOneDatabaseQueryMessageInterface;

final readonly class FindOneEntityWithFilterQuery implements FindOneDatabaseQueryMessageInterface
{
    public function __construct(
        private string $entityClass,
        private array $filters = [],
    ) {
    }

    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    public function hasEntityClass(): bool
    {
        return isset($this->entityClass);
    }

    public function getFilters(): array
    {
        return $this->filters;
    }

    public function hasFilters(): bool
    {
        return isset($this->filters);
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Message\Query;

use LoginAutonom\DatabaseBundle\Interfaces\FindAllArrayDatabaseQueryMessageInterface;

final readonly class FindAllEntitiesWithoutFilterQuery implements FindAllArrayDatabaseQueryMessageInterface
{
    public function __construct(
        private string $entityClass,
    ) {
    }

    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    public function hasEntityClass(): bool
    {
        return isset($this->entityClass);
    }
}

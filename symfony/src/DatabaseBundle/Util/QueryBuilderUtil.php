<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Util;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\AuthSecurityBundle\DTO\SimpleRequestContext;
use LoginAutonom\CoreBundle\Storage\ContextStorage;
use LoginAutonom\DatabaseBundle\Builder\AddJoinBuilder;
use LoginAutonom\DatabaseBundle\Builder\CriteriaByQueryObjectBuilder;
use LoginAutonom\DatabaseBundle\Builder\CriteriaBySimpleArrayBuilder;
use LoginAutonom\DatabaseBundle\Builder\OrderByQueryBuilder;
use LoginAutonom\DatabaseBundle\Builder\SQLQueryDescriptorFromQueryBuilderBuilder;
use LoginAutonom\DatabaseBundle\Descriptor\EntityMetadata;
use LoginAutonom\DatabaseBundle\Descriptor\SQLQueryDescriptorModifyDescriptor;
use LoginAutonom\DatabaseBundle\DTO\JoinDTO;
use LoginAutonom\DatabaseBundle\Handler\AddJoinWithAssociationGuessHandler;
use LoginAutonom\DatabaseBundle\Handler\AddJoinWithSecurityPermissionHandler;
use LoginAutonom\DatabaseBundle\Handler\SQLQueryDescriptorModifyDescriptorHandler;
use LoginAutonom\DatabaseBundle\Provider\EntityMetadataProvider;
use LoginAutonom\DatabaseBundle\Provider\QueryBuilderProvider;

final readonly class QueryBuilderUtil
{
    public const SIMPLE_QUERYBUILDER_ALIAS = 'ec';

    public function __construct(
        private QueryBuilderProvider $queryBuilderProvider,
        private EntityMetadataProvider $entityMetadataProvider,
        private SQLQueryDescriptorModifyDescriptorHandler $queryBuilderModifyHandler,
        private AddJoinWithAssociationGuessHandler $addJoinHandler,
        private ContextStorage $contextStorage,
        private AddJoinWithSecurityPermissionHandler $addJoinWithSecurityPermissionHandler,
        private CriteriaByQueryObjectBuilder $criteriaByQueryObjectBuilder,
        private CriteriaBySimpleArrayBuilder $criteriaBySimpleArrayBuilder,
        private OrderByQueryBuilder $orderByQueryBuilder,
        private AddJoinBuilder $addJoinBuilder,
    ) {
    }

    public function createSimpleQueryBuilder(
        string $entityClass,
        string $alias = self::SIMPLE_QUERYBUILDER_ALIAS
    ): QueryBuilder {
        $qb = $this->createQueryBuilder();
        $qb->select($alias)
            ->from($entityClass, $alias);

        return $qb;
    }

    public function createQueryBuilder(): QueryBuilder
    {
        return $this->queryBuilderProvider->createQueryBuilder();
    }

    public function createQueryBuilderWithSecurityPermission(
        string $entityClass,
        string $alias = self::SIMPLE_QUERYBUILDER_ALIAS
    ): QueryBuilder {
        $qb = $this->createQueryBuilder();
        $qb->from($entityClass, $alias);
        $this->addJoinWithSecurityPermissionHandler->handle(
            new SimpleRequestContext(
                $this->contextStorage->getLoggedUserId(),
                $this->contextStorage->getOperationId(),
                $this->contextStorage->getRequestName()
            ),
            $qb
        );

        return $qb;
    }

    public function getAliasForEntityClass(string $entityClass, QueryBuilder $qb): string
    {
        $sqlQueryDescriptor = (new SQLQueryDescriptorFromQueryBuilderBuilder())
            ->reset()
            ->setQueryBuilder($qb)
            ->build();

        return $sqlQueryDescriptor->getAliasForEntityClass($entityClass);
    }

    public function getMetadata(string $entityClass): EntityMetadata
    {
        return $this->entityMetadataProvider->provide($entityClass);
    }

    public function handleQueryBuilderModifier(SQLQueryDescriptorModifyDescriptor $descriptor): void
    {
        $this->queryBuilderModifyHandler->handle($descriptor);
    }

    public function addJoin(JoinDTO $joinDTO): QueryBuilder
    {
        $infos = $this->addJoinHandler->handle($joinDTO);
        $info = reset($infos);
        return $info->getQueryBuilder();
    }

    public function addObviousCriteria(object $query, QueryBuilder $qb): QueryBuilder
    {
        return $this->criteriaByQueryObjectBuilder->reset()
            ->setQueryBuilder($qb)
            ->setQuery($query)
            ->build();
    }

    public function addSimpleCriteria(array $criteriaByFields, string $entityFQCN, QueryBuilder $qb): QueryBuilder
    {
        return $this->criteriaBySimpleArrayBuilder->reset()
            ->setQueryBuilder($qb)
            ->setEntityFQCN($entityFQCN)
            ->setCriteria($criteriaByFields)
            ->build();
    }

    public function addOrderByQuery(object $query, QueryBuilder $qb): QueryBuilder
    {
        if (!method_exists($query, 'getOrder')) {
            return $qb;
        }
        return $this->orderByQueryBuilder->reset()
            ->setQuery($query)
            ->setQueryBuilder($qb)
            ->build();
    }

    public function createSelectWithDTO(string $dtoClass, array $fieldNames): string
    {
        $fields = array_fill(0, count($fieldNames), '%s');
        $format = 'NEW %s(' . implode(', ', $fields) . ')';
        $params = [];
        $params[] = $format;
        $params[] = $dtoClass;
        $params = array_merge($params, $fieldNames);

        return sprintf(...$params);
    }

    public function getAddJoinBuilder(): AddJoinBuilder
    {
        return $this->addJoinBuilder;
    }
}

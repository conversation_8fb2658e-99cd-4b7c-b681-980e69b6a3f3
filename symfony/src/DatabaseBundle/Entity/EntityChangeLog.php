<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use LoginAutonom\CoreBundle\Enum\ChangeLogActionEnum;
use LoginAutonom\DatabaseBundle\Attribute\NoChangeLog;
use LoginAutonom\DatabaseBundle\Attribute\NoDatabasePersistPermission;
use LoginAutonom\DatabaseBundle\Attribute\NoVisibilityPermission;
use LoginAutonom\DatabaseBundle\Attribute\StatusColumn;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\HistoryFieldsEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\RowIdEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\StatusEmbeddable;

#[ORM\Entity]
#[StatusColumn]
#[NoVisibilityPermission]
#[NoChangeLog]
#[NoDatabasePersistPermission]
final class EntityChangeLog
{
    #[ORM\Embedded(class: RowIdEmbeddable::class, columnPrefix: false)]
    private RowIdEmbeddable $row;

    #[ORM\Embedded(class: StatusEmbeddable::class, columnPrefix: false)]
    private StatusEmbeddable $status;

    #[ORM\Embedded(class: HistoryFieldsEmbeddable::class, columnPrefix: false)]
    private HistoryFieldsEmbeddable $history;

    #[ORM\Column(type: "string", length: 128)]
    private string $entityFQCN;

    #[ORM\Column(type: "integer")]
    private int $changedRowId;

    #[ORM\Column(type: "string", length: 32, enumType: ChangeLogActionEnum::class)]
    private ChangeLogActionEnum $action;

    #[ORM\Column(type: "json")]
    private array $changes = [];

    #[ORM\Column(type: "string", length: 128)]
    private string $reason;

    #[ORM\Column(type: "string", length: 32)]
    private string $correlationId;

    public function __construct()
    {
        $this->row = new RowIdEmbeddable();
        $this->status = new StatusEmbeddable();
        $this->history = new HistoryFieldsEmbeddable();
    }

    public function getRow(): RowIdEmbeddable
    {
        return $this->row;
    }

    public function hasRow(): bool
    {
        return isset($this->row);
    }

    public function setRow(RowIdEmbeddable $row): void
    {
        $this->row = $row;
    }

    public function getStatus(): StatusEmbeddable
    {
        return $this->status;
    }

    public function hasStatus(): bool
    {
        return isset($this->status);
    }

    public function setStatus(StatusEmbeddable $status): void
    {
        $this->status = $status;
    }

    public function getHistory(): HistoryFieldsEmbeddable
    {
        return $this->history;
    }

    public function hasHistory(): bool
    {
        return isset($this->history);
    }

    public function setHistory(HistoryFieldsEmbeddable $history): void
    {
        $this->history = $history;
    }

    public function getEntityFQCN(): string
    {
        return $this->entityFQCN;
    }

    public function hasEntityFQCN(): bool
    {
        return isset($this->entityFQCN);
    }

    public function setEntityFQCN(string $entityFQCN): void
    {
        $this->entityFQCN = $entityFQCN;
    }

    public function getChangedRowId(): int
    {
        return $this->changedRowId;
    }

    public function hasChangedRowId(): bool
    {
        return isset($this->changedRowId);
    }

    public function setChangedRowId(int $changedRowId): void
    {
        $this->changedRowId = $changedRowId;
    }

    public function getAction(): ChangeLogActionEnum
    {
        return $this->action;
    }

    public function hasAction(): bool
    {
        return isset($this->action);
    }

    public function setAction(ChangeLogActionEnum $action): void
    {
        $this->action = $action;
    }

    public function getChanges(): array
    {
        return $this->changes;
    }

    public function hasChanges(): bool
    {
        return isset($this->changes);
    }

    public function setChanges(array $changes): void
    {
        $this->changes = $changes;
    }

    public function getReason(): string
    {
        return $this->reason;
    }

    public function hasReason(): bool
    {
        return isset($this->reason);
    }

    public function setReason(string $reason): void
    {
        $this->reason = $reason;
    }

    public function getCorrelationId(): string
    {
        return $this->correlationId;
    }

    public function hasCorrelationId(): bool
    {
        return isset($this->correlationId);
    }

    public function setCorrelationId(string $correlationId): void
    {
        $this->correlationId = $correlationId;
    }
}

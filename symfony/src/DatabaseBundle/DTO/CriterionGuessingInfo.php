<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\DTO;

use Doctrine\ORM\QueryBuilder;

final class CriterionGuessingInfo
{
    private string $entityClass;
    private string $fieldName;
    private bool $isMultipleValue = false;
    private bool $skip = false;
    private bool $not = false;
    private bool $like = false;

    public function __construct(
        private QueryBuilder $queryBuilder,
        private object $query,
        private string $propertyName,
    ) {
    }

    public function getQueryBuilder(): QueryBuilder
    {
        return $this->queryBuilder;
    }

    public function hasQueryBuilder(): bool
    {
        return isset($this->queryBuilder);
    }

    public function getQuery(): object
    {
        return $this->query;
    }

    public function hasQuery(): bool
    {
        return isset($this->query);
    }

    public function getPropertyName(): string
    {
        return $this->propertyName;
    }

    public function hasPropertyName(): bool
    {
        return isset($this->propertyName);
    }

    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    public function hasEntityClass(): bool
    {
        return isset($this->entityClass);
    }

    public function setEntityClass(string $entityClass): void
    {
        $this->entityClass = $entityClass;
    }

    public function getFieldName(): string
    {
        return $this->fieldName;
    }

    public function hasFieldName(): bool
    {
        return isset($this->fieldName);
    }

    public function setFieldName(string $fieldName): void
    {
        $this->fieldName = $fieldName;
    }

    public function isMultipleValue(): bool
    {
        return $this->isMultipleValue;
    }

    public function hasIsMultipleValue(): bool
    {
        return isset($this->isMultipleValue);
    }

    public function setSingleValue(): void
    {
        $this->isMultipleValue = false;
    }

    public function setMultipleValue(): void
    {
        $this->isMultipleValue = true;
    }

    public function setSkip(): void
    {
        $this->skip = true;
    }

    public function isSkip(): bool
    {
        return $this->skip;
    }

    public function hasSkip(): bool
    {
        return isset($this->skip);
    }

    public function isNot(): bool
    {
        return $this->not;
    }

    public function setNot(bool $not): void
    {
        $this->not = $not;
    }

    public function isLike(): bool
    {
        return $this->like;
    }

    public function setLike(bool $like): void
    {
        $this->like = $like;
    }

    public function hasLike(): bool
    {
        return isset($this->like);
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Command;

use LoginAutonom\DatabaseBundle\Provider\EntityRoutingTableProvider;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(GenerateEntityRoutingTableCommand::NAME)]
final class GenerateEntityRoutingTableCommand extends Command
{
    public const NAME = 'entity-routing:generate-table';

    public function __construct(
        private readonly EntityRoutingTableProvider $entityRoutingTableProvider,
    ) {
        parent::__construct();
    }


    protected function configure()
    {
        $this->setDescription('');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->entityRoutingTableProvider->reset();
        $table = $this->entityRoutingTableProvider->provide();

        return Command::SUCCESS;
    }
}

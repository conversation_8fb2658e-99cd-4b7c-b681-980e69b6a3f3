<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Builder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\CoreBundle\Util\AnnotationHelper;
use LoginAutonom\DatabaseBundle\DTO\CriterionGuessingInfo;
use LoginAutonom\DatabaseBundle\Guesser\CriterionByQueryGuesser;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

#[Autoconfigure(shared: false)]
final class CriteriaByQueryObjectBuilder
{
    private QueryBuilder $queryBuilder;
    private object $query;

    public function __construct(
        private readonly AnnotationHelper $annotationHelper,
        private readonly CriterionByQueryGuesser $criterionByQueryGuesser,
    ) {
    }

    public function build(): QueryBuilder
    {
        $qb = $this->queryBuilder;

        $reflection = new \ReflectionClass($this->query);
        $criteria = [];
        foreach ($reflection->getProperties() as $property) {
            if (!$property->isInitialized($this->query)) {
                continue;
            }
            $info = new CriterionGuessingInfo($qb, $this->query, $property->getName());
            $guess = $this->criterionByQueryGuesser->guess($info);
            if ($guess->isSkip()) {
                continue;
            }
            if ($guess->isMultipleValue()) {
                $criterion = $qb->expr()->in(
                    $guess->getEntityAlias() . '.' . $guess->getFieldName(),
                    $guess->getParameterName()
                );
            } elseif ($guess->isLike()) {
                $criterion = $qb->expr()->like(
                    $guess->getEntityAlias() . '.' . $guess->getFieldName(),
                    $guess->getParameterName()
                );
            } else {
                $criterion = $qb->expr()->eq(
                    $guess->getEntityAlias() . '.' . $guess->getFieldName(),
                    $guess->getParameterName()
                );
            }
            if ($guess->isNot()) {
                $criterion = $qb->expr()->not($criterion);
            }
            $criteria[] = $criterion;
            if ($guess->isLike()) {
                $qb->setParameter($guess->getParameterName(), '%' . mb_strtolower($guess->getParameterValue()) . '%');
            } else {
                $qb->setParameter($guess->getParameterName(), $guess->getParameterValue());
            }
        }
        if ($criteria !== []) {
            $qb->andWhere(
                $qb->expr()->andX(...$criteria)
            );
        }

        return $qb;
    }

    public function reset(): self
    {
        unset(
            $this->queryBuilder,
            $this->query,
        );

        return $this;
    }

    public function setQueryBuilder(QueryBuilder $queryBuilder): self
    {
        $this->queryBuilder = $queryBuilder;
        return $this;
    }

    public function setQuery(object $query): self
    {
        $this->query = $query;
        return $this;
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\DevelopmentToolsBundle\Maker;

use Doctrine\DBAL\Exception;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Mapping\Driver\DatabaseDriver;
use Doctrine\ORM\Tools\DisconnectedClassMetadataFactory;
use Doctrine\Persistence\Mapping\ClassMetadata;
use LoginAutonom\DatabaseBundle\Guesser\TableInfoGuesser;
use LoginAutonom\DevelopmentToolsBundle\Doctrine\EntityClassGenerator;
use Symfony\Bundle\MakerBundle\ConsoleStyle;
use Symfony\Bundle\MakerBundle\DependencyBuilder;
use Symfony\Bundle\MakerBundle\Doctrine\DoctrineHelper;
use Symfony\Bundle\MakerBundle\FileManager;
use Symfony\Bundle\MakerBundle\Generator;
use Symfony\Bundle\MakerBundle\InputConfiguration;
use Symfony\Bundle\MakerBundle\Maker\AbstractMaker;
use Symfony\Bundle\MakerBundle\Str;
use Symfony\Bundle\MakerBundle\Util\ClassNameDetails;
use Symfony\Bundle\MakerBundle\Util\PhpCompatUtil;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\DependencyInjection\Attribute\AsTaggedItem;
use Symfony\Component\DependencyInjection\Attribute\Autowire;
use Symfony\Component\Filesystem\Filesystem;

/**
 * @method string getCommandDescription()
 */
#[AsTaggedItem('maker.command')]
final class EntityFromTableMaker extends AbstractMaker
{
    public const TABLE = 'table';
    public const FORCE = 'force';
    public const FORCE_SHORT = 'f';

    public function __construct(
        #[Autowire(service: 'maker.file_manager')] private FileManager $fileManager,
        #[Autowire(service: 'maker.doctrine_helper')] private DoctrineHelper $doctrineHelper,
        #[Autowire(service: 'maker.generator')] private Generator $generator,
        #[Autowire(service: 'maker.php_compat_util')] private PhpCompatUtil $phpCompatUtil,
        private EntityManagerInterface $entityManager,
        private TableInfoGuesser $guesser
    ) {
    }

    public static function getCommandName(): string
    {
        return 'database:entity:from_table';
    }

    public function configureCommand(Command $command, InputConfiguration $inputConfig)
    {
        $command->addArgument(self::TABLE, InputArgument::REQUIRED, 'table name');
        $command->addArgument('bundle', InputArgument::REQUIRED, 'bundle name (eg: EmployeeBundle)');
        $command->setDescription('entity maker from existing table');
        $command->addOption(self::FORCE, self::FORCE_SHORT, InputOption::VALUE_NONE, 'Remove file before generate');
    }

    public function generate(InputInterface $input, ConsoleStyle $io, Generator $generator)
    {
        $force = $input->getOption(self::FORCE);
        $tableName = $input->getArgument(self::TABLE);
        $entityBundle = $input->getArgument('bundle');
        $namespace = "LoginAutonom\\{$entityBundle}\\Entity\\";

        $metadata = $this->generateMetadata($tableName, $namespace);

        $className = Str::asCamelCase($tableName);
        $entityClassDetails = new ClassNameDetails(
            $namespace . $className,
            $namespace
        );
        $entityClassGenerator = new EntityClassGenerator(
            $generator,
            $this->doctrineHelper
        );
        $guess = $this->guesser->guess($metadata);
        $fileName = $this->fileManager->getRelativePathForFutureClass(
            $entityClassDetails->getFullName()
        );
        if ($force && $this->fileManager->fileExists($fileName)) {
            $filesystem = new Filesystem();
            $filesystem->remove($fileName);
        }
        $entityPath = $entityClassGenerator->generateEntityClass(
            $entityClassDetails,
            $guess
        );
        $generator->writeChanges();
    }

    /**
     * @throws Exception
     */
    private function generateMetadata(string $tableName, string $namespace): ClassMetadata
    {
        $schemaManager = $this->entityManager->getConnection()->getSchemaManager();
        $table = $schemaManager->introspectTable($tableName);
        $databaseDriver = new DatabaseDriver($schemaManager);
        $databaseDriver->setNamespace($namespace);
        $databaseDriver->setTables([$table], []);

        $this->entityManager->getConfiguration()->setMetadataDriverImpl($databaseDriver);
        $cmf = new DisconnectedClassMetadataFactory();
        $cmf->setEntityManager($this->entityManager);
        $metadatas = $cmf->getAllMetadata();

        return reset($metadatas);
    }

    public function __call(string $name, array $arguments)
    {
    }

    public function configureDependencies(DependencyBuilder $dependencies)
    {
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use LoginAutonom\CoreBundle\DTO\EntityUniqueHashConfiguration;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\DatabaseBundle\DTO\ValidityIntervalBasedEntity;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\ValidityEmbeddable;
use LoginAutonom\DatabaseBundle\Generator\UUIDGeneratorToEntityField;
use LoginAutonom\EtlBundle\Builder\EtlFlowBuilder;
use LoginAutonom\EtlBundle\Builder\EtlMappingBuilder;
use LoginAutonom\EtlBundle\DTO\EtlValidityBasedEntityCacheWithMapping;
use LoginAutonom\EtlBundle\Handler\EntityUniqueHashHandler;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowOneRowTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

final class CreateDefaultEntityIfNotExistsTransformer implements
    FlowOneRowTransformerInterface,
    LoggerAwareInterface,
    EtlCacheAwareInterface
{
    use EtlCacheAwareTrait;
    use LoggerAwareTrait;
    use EtlSerializableTrait;
    use EtlCommonFunctionsTrait;
    use FlowTransformerTrait;

    public function __construct(
        private readonly string $targetField,
        private readonly string $entityClass,
        private readonly array $defaultValues,
        private readonly array $copyFromRow,
        private readonly array $copyFromEntity,
        private readonly PropertyAccessorInterface $propertyAccessor,
        private readonly UUIDGeneratorToEntityField $uuidGenerator,
        private readonly EtlMappingBuilder $etlMappingBuilder,
        private readonly EntityUniqueHashHandler $entityUniqueHashHandler,
        private readonly ?string $uuidField = null,
        private readonly ?string $validityField = null,
        private readonly ?EntityUniqueHashConfiguration $entityUniqueHashConfiguration = null,
    ) {
    }

    public function transform(Row $row, FlowContext $context): Row
    {
        if ($row->has($this->targetField)) {
            return $row;
        }

        try {
            $entity = $this->getEntity($row, $context);
            return $row->set(
                $context->entryFactory()->create($this->targetField, $entity)
            );
        } catch (\InvalidArgumentException $e) {
            return $row;
        }
    }

    public function getEntity(Row $row, FlowContext $context): object
    {
        if (isset($this->entityUniqueHashConfiguration)) {
            if ($this->entityUniqueHashHandler->handle($row, $this->entityUniqueHashConfiguration)) {
                throw new \InvalidArgumentException('Entity with this unique hash already exists');
            }
        }

        if (!isset($this->validityField)) {
            return $this->createEntity($row, $context);
        }

        try {
            $validityIntervalBasedEntity = $this->getValidityBasedObjectByRow($row, $context);
            $validity = $row->get($this->validityField)->value();
            return $validityIntervalBasedEntity->get($validity);
        } catch (NotFoundException $e) {
        }

        return $this->createEntity($row, $context);
    }

    private function getValidityBasedObjectByRow(Row $row, FlowContext $context): ValidityIntervalBasedEntity
    {
        $entityCache = $this->getEntityCache($context);
        return $entityCache->get($row, $this->entityClass);
    }

    private function getEntityCache(FlowContext $context): EtlValidityBasedEntityCacheWithMapping
    {
        $cache = $this->cache->flow($context->config->id());
        return $cache->get(EtlFlowBuilder::ENTITY_CACHE);
    }

    private function createEntity(Row $row, FlowContext $context): object
    {
        $entityCache = $this->getEntityCache($context);
        $entity = $this->createEntityObjectFromRow($row);
        $changeStorage = $this->getChangesStorage($row);
        $changeStorage->addNew($entity);
        $entityCache->add($entity);
        $entityCache->generateMapping($entity);

        if ($entityCache->isCreateEtlMappingEntity($this->entityClass)) {
            $this->createEtlMapping($row, $entity, $context);
        }

        return $entity;
    }

    private function createEntityObjectFromRow(Row $row): mixed
    {
        $entity = new ($this->entityClass)();
        if (method_exists($entity, 'setValidity')) {
            $entity->setValidity(new ValidityEmbeddable());
        }
        foreach ($this->defaultValues as $fieldName => $defaultValue) {
            $this->propertyAccessor->setValue($entity, $fieldName, $defaultValue);
        }
        foreach ($this->copyFromRow as $rowField => $entityField) {
            if (!$row->has($rowField)) {
                throw new \InvalidArgumentException();
            }
            $this->propertyAccessor->setValue(
                $entity,
                $entityField,
                $row->get($rowField)->value()
            );
        }
        foreach (
            $this->copyFromEntity as ['fieldName' => $fieldName,
            'entityField' => $entityField,
            'targetField' => $targetField]
        ) {
            if (!$row->has($fieldName)) {
                throw new \InvalidArgumentException();
            }
            $copyEntity = $row->get($fieldName)->value();
            if (!$this->propertyAccessor->isReadable($copyEntity, $entityField)) {
                throw new \InvalidArgumentException();
            }
            $this->propertyAccessor->setValue(
                $entity,
                $targetField,
                $this->propertyAccessor->getValue($copyEntity, $entityField)
            );
        }
        if (isset($this->uuidField)) {
            $this->propertyAccessor->setValue(
                $entity,
                $this->uuidField,
                $this->uuidGenerator->generate($entity, $this->uuidField)
            );
        }

        return $entity;
    }

    private function createEtlMapping(Row $row, object $entity, FlowContext $context): void
    {
        $entityCache = $this->getEntityCache($context);
        $mappingType = $entityCache->getMappingType($this->entityClass);

        try {
            $sourceMapping = $entityCache->getSourceMapping($mappingType);
            $identifiers = $entityCache->collectIdentifiers($row, $sourceMapping);
            $entityCache->findMapping($identifiers);
        } catch (NotFoundException) {
            $etlMapping = $this->etlMappingBuilder->reset()
                ->setEntity($entity)
                ->setMappingType($mappingType)
                ->setWorkflow($context->config->id())
                ->setRow($row)
                ->setSourceFields($entityCache->getSourceMapping($mappingType))
                ->setTargetFields($entityCache->getTargetMapping($mappingType))
                ->build();
            $changeStorage = $this->getChangesStorage($row);
            $changeStorage->addNew($etlMapping);
            $entityCache->add($etlMapping);
        }
    }
}

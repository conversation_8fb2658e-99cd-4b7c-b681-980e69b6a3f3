<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Factory\Flow\Transformer;

use LoginAutonom\CoreBundle\DTO\EntityUniqueHashConfiguration;
use LoginAutonom\DatabaseBundle\Generator\UUIDGeneratorToEntityField;
use LoginAutonom\EtlBundle\Builder\EtlMappingBuilder;
use LoginAutonom\EtlBundle\Flow\Transformer\CreateDefaultEntityIfNotExistsTransformer;
use LoginAutonom\EtlBundle\Interfaces\FlowTransformerFactoryInterface;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

final readonly class CreateDefaultEntityIfNotExistsTransformerFactory implements FlowTransformerFactoryInterface
{
    public const TARGET_FIELD = 'targetField';
    public const ENTITY_CLASS = 'entityClass';
    public const DEFAULT_VALUES = 'defaultValues';
    public const UUID_FIELD = 'uuidField';
    public const COPY_FROM_ROW = 'copyFromRow';
    public const COPY_FROM_ENTITY = 'copyFromEntity';
    public const VALIDITY_FIELD = 'validityField';
    public const UNIQUE_HASH_MODE = 'uniqueHashMode';
    public const UNIQUE_HASH_TYPE = 'uniqueHashType';

    public function __construct(
        private PropertyAccessorInterface $propertyAccessor,
        private UUIDGeneratorToEntityField $UUIDGeneratorToEntityField,
        private EtlMappingBuilder $etlMappingBuilder,
    ) {
    }

    public function build(array $config): object
    {
        $targetField = $config[self::TARGET_FIELD];
        $entityClass = $config[self::ENTITY_CLASS];
        $defaultValues = $config[self::DEFAULT_VALUES];
        $uuidField = $config[self::UUID_FIELD] ?? null;
        $copyFromRow = $config[self::COPY_FROM_ROW] ?? [];
        $copyFromEntity = $config[self::COPY_FROM_ENTITY] ?? [];
        $validityField = $config[self::VALIDITY_FIELD] ?? null;
        $entityUniqueHashConfig = null;

        if (isset($config[self::UNIQUE_HASH_MODE]) && isset($config[self::UNIQUE_HASH_TYPE])) {
            $entityUniqueHashConfig = new EntityUniqueHashConfiguration(
                $config[self::UNIQUE_HASH_MODE],
                $config[self::UNIQUE_HASH_TYPE],
                $entityClass
            );
        }

        return new CreateDefaultEntityIfNotExistsTransformer(
            $targetField,
            $entityClass,
            $defaultValues,
            $copyFromRow,
            $copyFromEntity,
            $this->propertyAccessor,
            $this->UUIDGeneratorToEntityField,
            $this->etlMappingBuilder,
            $uuidField,
            $validityField,
            $entityUniqueHashConfig
        );
    }

    public static function getName(): string
    {
        return 'create-default-entity-if-not-exists-transformer';
    }
}

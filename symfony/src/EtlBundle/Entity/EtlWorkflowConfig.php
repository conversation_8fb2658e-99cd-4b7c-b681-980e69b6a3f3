<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use LoginAutonom\DatabaseBundle\Attribute\NoVisibilityPermission;
use LoginAutonom\DatabaseBundle\Attribute\StatusColumn;
use LoginAutonom\DatabaseBundle\Attribute\ValidityColumns;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\HistoryFieldsEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\RowIdEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\StatusEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\ValidityEmbeddable;

#[ORM\Entity]
#[StatusColumn]
#[ValidityColumns]
#[ORM\Index(columns: [
    'workflow',
    'valid_from',
    'valid_to',
    'status',
], name: 'IDX')]
#[NoVisibilityPermission]
final class EtlWorkflowConfig
{
    #[ORM\Embedded(class: RowIdEmbeddable::class, columnPrefix: false)]
    private RowIdEmbeddable $row;

    #[ORM\Embedded(class: StatusEmbeddable::class, columnPrefix: false)]
    private StatusEmbeddable $status;

    #[ORM\Embedded(class: HistoryFieldsEmbeddable::class, columnPrefix: false)]
    private HistoryFieldsEmbeddable $history;

    #[ORM\Embedded(class: ValidityEmbeddable::class, columnPrefix: false)]
    private ValidityEmbeddable $validity;

    #[ORM\Column(type: 'string', length: 255)]
    private string $workflow;

    #[ORM\Column(type: 'json')]
    private array $config;

    public function __construct()
    {
        $this->row = new RowIdEmbeddable();
        $this->status = new StatusEmbeddable();
        $this->history = new HistoryFieldsEmbeddable();
        $this->validity = new ValidityEmbeddable();
    }

    public function getRow(): RowIdEmbeddable
    {
        return $this->row;
    }

    public function setRow(RowIdEmbeddable $row): void
    {
        $this->row = $row;
    }

    public function hasRow(): bool
    {
        return isset($this->row);
    }

    public function getPreRow(): PreRowIdEmbeddable
    {
        return $this->preRow;
    }

    public function setPreRow(PreRowIdEmbeddable $preRow): void
    {
        $this->preRow = $preRow;
    }

    public function hasPreRow(): bool
    {
        return isset($this->preRow);
    }

    public function getStatus(): StatusEmbeddable
    {
        return $this->status;
    }

    public function setStatus(StatusEmbeddable $status): void
    {
        $this->status = $status;
    }

    public function hasStatus(): bool
    {
        return isset($this->status);
    }

    public function getHistory(): HistoryFieldsEmbeddable
    {
        return $this->history;
    }

    public function setHistory(HistoryFieldsEmbeddable $history): void
    {
        $this->history = $history;
    }

    public function hasHistory(): bool
    {
        return isset($this->history);
    }

    public function getValidity(): ValidityEmbeddable
    {
        return $this->validity;
    }

    public function setValidity(ValidityEmbeddable $validity): void
    {
        $this->validity = $validity;
    }

    public function hasValidity(): bool
    {
        return isset($this->validity);
    }

    public function getWorkflow(): string
    {
        return $this->workflow;
    }

    public function setWorkflow(string $workflow): void
    {
        $this->workflow = $workflow;
    }

    public function hasWorkflow(): bool
    {
        return isset($this->workflow);
    }

    public function getConfig(): array
    {
        return $this->config;
    }

    public function setConfig(array $config): void
    {
        $this->config = $config;
    }

    public function hasConfig(): bool
    {
        return isset($this->config);
    }
}

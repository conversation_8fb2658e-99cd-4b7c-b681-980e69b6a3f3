<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Handler;

use Flow\ETL\Row;
use LoginAutonom\CoreBundle\DTO\EntityUniqueHashConfiguration;
use LoginAutonom\CoreBundle\Interfaces\EntityUniqueHashByFieldsGuesserInterface;
use LoginAutonom\CoreBundle\Interfaces\QueryBusInterface;
use LoginAutonom\CoreBundle\Message\Query\EntityUniqueHashQuery;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Component\DependencyInjection\ServiceLocator;

final readonly class EntityUniqueHashHandler
{
    public function __construct(
        #[TaggedLocator(EntityUniqueHashByFieldsGuesserInterface::TAGGED_LOCATOR_NAME)]
        private ServiceLocator $guessers,
        private QueryBusInterface $queryBus,
    ) {
    }

    public function handle(Row $row, EntityUniqueHashConfiguration $configuration): bool
    {
        $guesser = $this->getGuesser($row, $configuration);
        $uniqueHashes = $guesser->guess($row, $configuration);
        $entityUniqueHashes = $this->queryBus->query(
            new EntityUniqueHashQuery(
                $configuration->getEntityFQCN(),
                $configuration->getHashType(),
                $uniqueHashes
            )
        );

        return !empty($entityUniqueHashes);
    }

    private function getGuesser(
        Row $row,
        EntityUniqueHashConfiguration $configuration
    ): EntityUniqueHashByFieldsGuesserInterface {
        /** @var EntityUniqueHashByFieldsGuesserInterface $guesser */
        foreach ($this->guessers as $guesser) {
            if (!$guesser->isSupported($row, $configuration)) {
                continue;
            }

            return $guesser;
        }

        throw new \InvalidArgumentException(sprintf('No guesser found for mode "%s"', $configuration->getMode()));
    }
}
<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Handler;

use Flow\ETL\Row;
use LoginAutonom\CoreBundle\DTO\EntityUniqueHashConfiguration;
use LoginAutonom\CoreBundle\Interfaces\EntityUniqueHashByFieldsGuesserInterface;
use LoginAutonom\CoreBundle\Interfaces\QueryBusInterface;
use LoginAutonom\CoreBundle\Message\Query\EntityUniqueHashQuery;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Component\DependencyInjection\ServiceLocator;

final readonly class EntityUniqueHashHandler
{
    public function __construct(
        #[TaggedLocator(EntityUniqueHashByFieldsGuesserInterface::TAGGED_LOCATOR_NAME)]
        private ServiceLocator $guessers,
        private QueryBusInterface $queryBus,
    ) {
    }

    public function handle(Row $row, EntityUniqueHashConfiguration $configuration): bool
    {
        $guesser = $this->getGuesser($row, $configuration);
        $uniqueHashes = $guesser->guess($row, $configuration);
        $entityUniqueHashes = $this->queryBus->query(
            new EntityUniqueHashQuery(
                $configuration->getEntityFQCN(),
                $configuration->getHashType(),
                $uniqueHashes
            )
        );

        return !empty($entityUniqueHashes);
    }

    /**
     * Handle multiple rows at once and return array of existing hashes
     * @param Row[] $rows
     * @param EntityUniqueHashConfiguration $configuration
     * @return array Array of existing unique hashes
     */
    public function handleBatch(array $rows, EntityUniqueHashConfiguration $configuration): array
    {
        if (empty($rows)) {
            return [];
        }

        $allUniqueHashes = [];
        $guesser = $this->getGuesser(reset($rows), $configuration);

        foreach ($rows as $row) {
            $uniqueHashes = $guesser->guess($row, $configuration);
            if (is_array($uniqueHashes)) {
                $allUniqueHashes = array_merge($allUniqueHashes, $uniqueHashes);
            } else {
                $allUniqueHashes[] = $uniqueHashes;
            }
        }

        if (empty($allUniqueHashes)) {
            return [];
        }

        $allUniqueHashes = array_unique($allUniqueHashes);
        $entityUniqueHashes = $this->queryBus->query(
            new EntityUniqueHashQuery(
                $configuration->getEntityFQCN(),
                $configuration->getHashType(),
                $allUniqueHashes
            )
        );

        // Since EntityUniqueHashQuery implements FindAllArrayDatabaseQueryMessageInterface,
        // it returns arrays of arrays, so we need to extract the uniqueHash values
        $existingHashes = [];
        foreach ($entityUniqueHashes as $hashData) {
            if (is_array($hashData) && isset($hashData['uniqueHash'])) {
                $existingHashes[] = $hashData['uniqueHash'];
            }
        }

        return $existingHashes;
    }

    /**
     * Check if a single row's hash exists in the provided existing hashes array
     * @param Row $row
     * @param EntityUniqueHashConfiguration $configuration
     * @param array $existingHashes Array of existing hashes from batch query
     * @return bool
     */
    public function isHashExistsInBatch(Row $row, EntityUniqueHashConfiguration $configuration, array $existingHashes): bool
    {
        if (empty($existingHashes)) {
            return false;
        }

        $guesser = $this->getGuesser($row, $configuration);
        $uniqueHashes = $guesser->guess($row, $configuration);

        if (is_array($uniqueHashes)) {
            foreach ($uniqueHashes as $hash) {
                if (in_array($hash, $existingHashes, true)) {
                    return true;
                }
            }
        } else {
            return in_array($uniqueHashes, $existingHashes, true);
        }

        return false;
    }

    private function getGuesser(
        Row $row,
        EntityUniqueHashConfiguration $configuration
    ): EntityUniqueHashByFieldsGuesserInterface {
        /** @var EntityUniqueHashByFieldsGuesserInterface $guesser */
        foreach ($this->guessers as $guesser) {
            if (!$guesser->isSupported($row, $configuration)) {
                continue;
            }

            return $guesser;
        }

        throw new \InvalidArgumentException(sprintf('No guesser found for mode "%s"', $configuration->getMode()));
    }
}
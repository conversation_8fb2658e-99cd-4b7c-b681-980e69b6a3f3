<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Guesser;

use Flow\ETL\Row;
use LoginAutonom\CoreBundle\DTO\EntityUniqueHashConfiguration;
use LoginAutonom\CoreBundle\Enum\EntityUniqueHashModeEnum;
use LoginAutonom\CoreBundle\Interfaces\EntityUniqueHashByFieldsGuesserInterface;

final readonly class Sha256EntityUniqueHashByFieldsGuesser implements EntityUniqueHashByFieldsGuesserInterface
{
    public function guess(Row $row, EntityUniqueHashConfiguration $configuration): string
    {
        $values = [];
        
        foreach ($configuration->getFields() as $fieldName) {
            if (!$row->has($fieldName)) {
                throw new \InvalidArgumentException(sprintf('Required field "%s" is missing from row', $fieldName));
            }
            
            $values[] = (string) $row->get($fieldName)->value();
        }
        
        $concatenatedValues = implode('|', $values);
        
        return hash('sha256', $concatenatedValues);
    }

    public function isSupported(Row $row, EntityUniqueHashConfiguration $configuration): bool
    {
        return $configuration->getMode() == EntityUniqueHashModeEnum::DYNAMIC->value;
    }
}
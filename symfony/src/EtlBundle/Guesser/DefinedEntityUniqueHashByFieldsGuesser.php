<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Guesser;

use Flow\ETL\Row;
use LoginAutonom\CoreBundle\DTO\EntityUniqueHashConfiguration;
use LoginAutonom\CoreBundle\Enum\EntityUniqueHashModeEnum;
use LoginAutonom\CoreBundle\Interfaces\EntityUniqueHashByFieldsGuesserInterface;

final readonly class DefinedEntityUniqueHashByFields<PERSON>uesser implements EntityUniqueHashByFieldsGuesserInterface
{
    public function guess(Row $row, EntityUniqueHashConfiguration $configuration): array
    {
        $fields = $configuration->getFields();

        if (empty($fields)) {
            throw new \InvalidArgumentException('At least one field must be specified for defined unique hash mode');
        }

        $hashFieldName = reset($fields);

        if (!$row->has($hashFieldName)) {
            throw new \InvalidArgumentException(
                sprintf('Required unique hash field "%s" is missing from row', $hashFieldName)
            );
        }

        $uniqueHash = $row->get($hashFieldName)->value();

        if (empty($uniqueHash)) {
            throw new \InvalidArgumentException(sprintf('Unique hash field "%s" cannot be empty', $hashFieldName));
        }

        return [$uniqueHash];
    }

    public function isSupported(Row $row, EntityUniqueHashConfiguration $configuration): bool
    {
        return $configuration->getMode() === EntityUniqueHashModeEnum::DEFINED->value;
    }
}
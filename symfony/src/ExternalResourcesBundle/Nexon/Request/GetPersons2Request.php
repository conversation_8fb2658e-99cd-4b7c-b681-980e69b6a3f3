<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Request;

use LoginAutonom\ExternalResourcesBundle\Builder\APIAdapterBuilder;
use LoginAutonom\ExternalResourcesBundle\Builder\RequestDataBuilder;
use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiRequestParameterKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Enum\HttpMethodEnum;
use LoginAutonom\ExternalResourcesBundle\Interfaces\ApiRequestInterface;
use LoginAutonom\ExternalResourcesBundle\Interfaces\RequestHandlerInterface;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class GetPersons2Request implements RequestHandlerInterface, ApiRequestInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;

    public const TARGET_PATH = "/OdataApi/v5.0/GetPersons2(TaxNumber='')";

    public function __construct(
        private readonly APIAdapterBuilder $adapterBuilder,
        private readonly RequestDataBuilder $requestDataBuilder,
    ) {
    }

    public function handle(ApiConfigurationDTO $apiConfigurationDTO, array $params = []): mixed
    {
        try {
            $requestBody = $this->buildRequestBody($params);
            $requestHeaders = $this->buildRequestHeaders($params);
            $adapter = $this->adapterBuilder->reset()
                ->setApiConfigurationDTO($apiConfigurationDTO)
                ->build();
            $targetPath = $this->buildTargetPath($params);
            $requestDataDTO = $this->requestDataBuilder->reset()
                ->setPath($targetPath)
                ->setMethod(HttpMethodEnum::GET)
                ->setHeaders($requestHeaders)
                ->setBodyData($requestBody)
                ->setApiConfigurationDTO($apiConfigurationDTO)
                ->build();

            $result = $adapter->get($requestDataDTO, $apiConfigurationDTO->getOptions());
            return (string) $result->getBody();
        } catch (\Throwable $e) {
            $this->logger->error('Error in GetPersons2Request: {message}', [
                'message' => $e->getMessage(),
                'params' => $params,
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    public function buildRequestBody(array $params = []): array
    {
        $body = [];

        if (
            isset($params[ApiRequestParameterKeyEnum::EXTRA_BODY->value]) &&
            is_array($params[ApiRequestParameterKeyEnum::EXTRA_BODY->value])
        ) {
            $body = array_merge($body, $params[ApiRequestParameterKeyEnum::EXTRA_BODY->value]);
        }

        return $body;
    }

    public function buildRequestHeaders(array $params = []): array
    {
        $headers = [];
        if (
            isset($params[ApiRequestParameterKeyEnum::EXTRA_HEADERS->value]) &&
            is_array($params[ApiRequestParameterKeyEnum::EXTRA_HEADERS->value])
        ) {
            $headers = array_merge($headers, $params[ApiRequestParameterKeyEnum::EXTRA_HEADERS->value]);
        }

        return $headers;
    }

    private function buildTargetPath(array $params = []): string
    {
        $targetPath = self::TARGET_PATH;

        if (!empty($params['tax_number'])) {
            $taxNumber = (string) $params['tax_number'];
            $targetPath = str_replace("TaxNumber=''", "TaxNumber='{$taxNumber}'", $targetPath);
        }

        return $targetPath;
    }

    public static function type(): string
    {
        return 'get-persons-2';
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Enum;

use LoginAutonom\ExternalResourcesBundle\Enum\HttpMethodEnum;

enum ApiEndpointEnum: string
{
    case GET_PERSONS_2 = 'GET_PERSONS_2';
    case GET_DAILY_WORK_SCHEDULE_2 = 'GET_DAILY_WORK_SCHEDULE_2';

    public function getPath(): string
    {
        return match ($this) {
            self::GET_PERSONS_2 => "/OdataApi/v5.0/GetPersons2(TaxNumber='')",
            self::GET_DAILY_WORK_SCHEDULE_2 => '/OdataApi/v5.0/GetDailyWorkSchedules2',
        };
    }

    public function getMethod(): HttpMethodEnum
    {
        return match ($this) {
            self::GET_PERSONS_2 => HttpMethodEnum::GET,
            self::GET_DAILY_WORK_SCHEDULE_2 => HttpMethodEnum::POST,
        };
    }
}

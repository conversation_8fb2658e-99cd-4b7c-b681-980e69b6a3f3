<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Enum;

enum ApiHeaderEnum: string
{
    case CONTENT_TYPE = 'Content-Type';
    case ACCEPT = 'Accept';
    case CONNECTION = 'Connection';
    case KEEP_ALIVE = 'Keep-Alive';
    case CACHE_CONTROL = 'Cache-Control';

    public function getDefaultValue(): string
    {
        return match ($this) {
            self::CONTENT_TYPE => 'application/json',
            self::ACCEPT => '*/*',
            self::CONNECTION => 'keep-alive',
            self::KEEP_ALIVE => 'timeout=300, max=1000',
            self::CACHE_CONTROL => 'no-cache',
        };
    }

    public static function getDefaultHeaders(): array
    {
        return [
            self::CONTENT_TYPE->value => self::CONTENT_TYPE->getDefaultValue(),
            self::ACCEPT->value => self::ACCEPT->getDefaultValue(),
            self::CONNECTION->value => self::CONNECTION->getDefaultValue(),
            self::KEEP_ALIVE->value => self::KEEP_ALIVE->getDefaultValue(),
            self::CACHE_CONTROL->value => self::CACHE_CONTROL->getDefaultValue(),
        ];
    }
}

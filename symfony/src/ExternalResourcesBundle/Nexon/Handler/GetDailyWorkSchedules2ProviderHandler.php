<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Nexon\Handler;

use LoginAutonom\CoreBundle\Interfaces\QueryBusInterface;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoVisibilityPermissionStamp;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\SingleValidityStamp;
use LoginAutonom\EmployeeBundle\Message\Query\EmployeesByEmpIdQuery;
use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\ApiConfigEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\CommonResponseFieldNameEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Enum\RequestProviderNameEnum;
use LoginAutonom\ExternalResourcesBundle\Nexon\Interfaces\ApiRequestProviderInterface;
use LoginAutonom\ExternalResourcesBundle\Nexon\Processor\DailyWorkSchedule2ResponseProcessor;
use LoginAutonom\ExternalResourcesBundle\Nexon\Provider\ApiRequest\GetDailyWorkSchedule2Provider;
use LoginAutonom\ExternalResourcesBundle\Nexon\Provider\ApiRequest\GetPersons2Provider;

final readonly class GetDailyWorkSchedules2ProviderHandler implements ApiRequestProviderInterface
{
    public function __construct(
        private GetPersons2Provider $personsProvider,
        private GetDailyWorkSchedule2Provider $workScheduleProvider,
        private DailyWorkSchedule2ResponseProcessor $workScheduleProcessor,
        private QueryBusInterface $queryBus,
    ) {
    }

    public function provideData(ApiConfigurationDTO $config): \Generator
    {
        $persons = $this->personsProvider->provideData($config);
        $personIds = $this->getPersonIdsFromPersons($persons);

        if (empty($personIds)) {
            yield [];
            return;
        }

        foreach ($this->createDescriptorsIterator($config, $persons, $personIds) as $employeeWorkSchedules) {
            yield [$employeeWorkSchedules];
        }
    }

    private function createDescriptorsIterator(
        ApiConfigurationDTO $config,
        array $persons,
        array $personIds
    ): \Generator {
        $personIdChunks = array_chunk($personIds, ApiConfigEnum::CHUNK_SIZE->value);
        foreach ($personIdChunks as $personIdChunk) {
            $personsChunk = $this->filterPersonsByPersonIds($persons, $personIdChunk);
            $filteredPersonsChunk = $this->filterPersonsByExistingEmployees($personsChunk);
            if (empty($filteredPersonsChunk)) {
                continue;
            }
            $workSchedules = $this->workScheduleProvider->provideData($config, $personIdChunk);
            $employeeWorkScheduleDescriptors = $this->workScheduleProcessor->process(
                $filteredPersonsChunk,
                $workSchedules
            );

            yield $employeeWorkScheduleDescriptors;
        }
    }

    private function getPersonIdsFromPersons(array $persons): array
    {
        return array_filter(
            array_unique(
                array_column($persons, CommonResponseFieldNameEnum::PERSON_ID->value)
            )
        );
    }

    private function filterPersonsByPersonIds(array $persons, array $personIds): array
    {
        return array_filter($persons, function ($person) use ($personIds) {
            $personId = $person[CommonResponseFieldNameEnum::PERSON_ID->value] ?? null;
            return $personId !== null && in_array($personId, $personIds, true);
        });
    }

    private function filterPersonsByExistingEmployees(array $persons): array
    {
        $empIdsFromPersons = $this->getEmployeeNumbersFromPersons($persons);

        if (empty($empIdsFromPersons)) {
            return [];
        }

        $employeeEntitiesFromDb = $this->queryBus->query(
            new EmployeesByEmpIdQuery($empIdsFromPersons),
            [
                new SingleValidityStamp(),
                new NoVisibilityPermissionStamp('External resources import'),
            ]
        );

        $empIdsFromDb = array_map(function ($employee) {
            return $employee->getEmpId();
        }, $employeeEntitiesFromDb);

        return array_filter($persons, function ($person) use ($empIdsFromDb) {
            $employeeNumber = $person[CommonResponseFieldNameEnum::EMPLOYEE_NUMBER->value] ?? null;
            return $employeeNumber !== null && in_array($employeeNumber, $empIdsFromDb, true);
        });
    }

    private function getEmployeeNumbersFromPersons(array $persons): array
    {
        return array_filter(
            array_unique(
                array_column($persons, CommonResponseFieldNameEnum::EMPLOYEE_NUMBER->value)
            )
        );
    }

    public static function getName(): string
    {
        return RequestProviderNameEnum::GET_DAILY_WORK_SCHEDULES_2->value;
    }
}

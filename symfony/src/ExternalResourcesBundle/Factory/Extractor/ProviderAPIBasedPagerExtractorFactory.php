<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Factory\Extractor;

use InvalidArgumentException;
use LoginAutonom\EtlBundle\DTO\ExtractorFactoryParams;
use LoginAutonom\EtlBundle\Interfaces\FlowExtractorFactoryInterface;
use LoginAutonom\ExternalResourcesBundle\Builder\ApiConfigurationBuilder;
use LoginAutonom\ExternalResourcesBundle\Flow\Extractor\ProviderAPIBasedPagerExtractor;
use LoginAutonom\ExternalResourcesBundle\Handler\MultiRequestHandler;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(FlowExtractorFactoryInterface::TAG)]
final readonly class ProviderAPIBasedPagerExtractorFactory implements FlowExtractorFactoryInterface
{
    private const ROW_LIMIT = 'rowLimit';
    private const API_CONFIG = 'apiConfig';
    private const AUTH_CONFIGS = 'authConfigs';
    private const OPTIONS = 'options';
    private const PROTOCOL = 'protocol';
    private const HOSTNAME = 'hostname';
    private const PORT = 'port';
    private const PATH = 'path';

    public function __construct(
        private ApiConfigurationBuilder $apiConfigurationBuilder,
        private MultiRequestHandler $multiRequestHandler,
    ) {
    }

    public function build(ExtractorFactoryParams $factoryParams): object
    {
        $config = $factoryParams->getConfig();
        $rowLimit = $config[self::ROW_LIMIT] ?? 1000;
        $apiConfigData = $config[self::API_CONFIG] ?? throw new InvalidArgumentException(
            'API configuration is required'
        );

        $apiConfiguration = $this->apiConfigurationBuilder
            ->reset()
            ->setProtocol($apiConfigData[self::PROTOCOL] ?? 'https')
            ->setHostname($apiConfigData[self::HOSTNAME] ?? throw new InvalidArgumentException('Hostname is required'))
            ->setPort($apiConfigData[self::PORT] ?? null)
            ->setPath($apiConfigData[self::PATH] ?? null)
            ->setAuthConfigs($apiConfigData[self::AUTH_CONFIGS] ?? [])
            ->setOptions($apiConfigData[self::OPTIONS] ?? [])
            ->build();

        $multiRequestHandler = $this->multiRequestHandler;

        return new ProviderAPIBasedPagerExtractor(
            options: $config,
            rowLimit: $rowLimit,
            multiRequestHandler: $multiRequestHandler,
            apiConfiguration: $apiConfiguration,
        );
    }

    public static function getName(): string
    {
        return 'nexon-provider-api-based-extractor';
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Flow\Extractor;

use Flow\ETL\FlowContext;
use LoginAutonom\EtlBundle\DTO\ExtractedData;
use LoginAutonom\EtlBundle\Enum\EtlCommonFieldNamesEnum;
use LoginAutonom\EtlBundle\Interfaces\FlowExtractorInterface;
use LoginAutonom\ExternalResourcesBundle\DTO\ApiConfigurationDTO;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiExtractorConfigurationKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiMetadataKeyEnum;
use LoginAutonom\ExternalResourcesBundle\Handler\MultiRequestHandler;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class ProviderAPIBasedPagerExtractor implements FlowExtractorInterface, LoggerAwareInterface
{
    use LoggerAwareTrait;

    public const OFFSET = ApiExtractorConfigurationKeyEnum::OFFSET->value;

    public function __construct(
        private readonly array $options,
        private readonly int $rowLimit,
        private readonly MultiRequestHandler $multiRequestHandler,
        private readonly ApiConfigurationDTO $apiConfiguration
    ) {
    }

    public function extract(FlowContext $context): \Generator
    {
        $requests = $this->options[ApiExtractorConfigurationKeyEnum::REQUESTS->value];

        $finalResults = $this->multiRequestHandler->handle(
            $requests,
            $this->options,
            $this->apiConfiguration,
        );

        $extraFields = $this->options[ApiExtractorConfigurationKeyEnum::EXTRA_FIELDS->value] ?? [];
        $offset = 0;

        foreach (array_chunk($finalResults, $this->rowLimit) as $batch) {
            $rows = array_map(static fn($item) => array_merge($item, $extraFields), $batch);
            $extractedData = new ExtractedData(
                $rows,
                [
                    EtlCommonFieldNamesEnum::OFFSET => $offset,
                    EtlCommonFieldNamesEnum::METADATA => [ApiMetadataKeyEnum::MULTI_RESULTS->value => true],
                ],
            );

            yield $extractedData;
            $offset += count($rows);
        }
    }
}

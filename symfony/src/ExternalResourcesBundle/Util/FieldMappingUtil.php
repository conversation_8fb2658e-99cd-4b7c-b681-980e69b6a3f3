<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Util;

final class FieldMappingUtil
{
    public function applyFieldMappings(array $data, array $mappings): array
    {
        return array_map(function ($row) use ($mappings) {
            $mappedRow = [];
            foreach ($mappings as $sourceField => $targetField) {
                if (!array_key_exists($sourceField, $row)) {
                    continue;
                }
                $mappedRow[$targetField] = $row[$sourceField];
            }
            return $mappedRow;
        }, $data);
    }
}

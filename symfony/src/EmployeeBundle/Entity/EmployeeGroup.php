<?php

namespace LoginAutonom\EmployeeBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use LoginAutonom\DatabaseBundle\Attribute\EntityAssociation;
use LoginAutonom\DatabaseBundle\Attribute\NoVisibilityPermission;
use LoginAutonom\DatabaseBundle\Attribute\StatusColumn;
use LoginAutonom\DatabaseBundle\Attribute\ValidityColumns;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\HistoryFieldsEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\NoteEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\RowIdEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\StatusEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\ValidityEmbeddable;
use LoginAutonom\EmployeeBundle\Enum\EmployeeContractEntityFieldEnum;

#[ORM\Entity]
#[StatusColumn]
#[ValidityColumns]
#[NoVisibilityPermission]
final class EmployeeGroup
{
    #[ORM\Embedded(class: RowIdEmbeddable::class, columnPrefix: false)]
    private RowIdEmbeddable $row;

    #[ORM\Embedded(class: NoteEmbeddable::class, columnPrefix: false)]
    private NoteEmbeddable $note;

    #[ORM\Embedded(class: StatusEmbeddable::class, columnPrefix: false)]
    private StatusEmbeddable $status;

    #[ORM\Embedded(class: HistoryFieldsEmbeddable::class, columnPrefix: false)]
    private HistoryFieldsEmbeddable $history;

    #[ORM\Embedded(class: ValidityEmbeddable::class, columnPrefix: false)]
    private ValidityEmbeddable $validity;

    #[ORM\Column(type: 'string', length: 32)]
    #[EntityAssociation(
        targetEntity: EmployeeContract::class,
        targetField: EmployeeContractEntityFieldEnum::EMPLOYEE_CONTRACT_ID
    )]
    private string $employeeContractId;

    #[ORM\Column(type: 'string', length: 32)]
    private string $groupId;

    #[ORM\Column(type: 'string', length: 64)]
    private string $groupValue;

    public function __construct()
    {
        $this->row = new RowIdEmbeddable();
        $this->note = new NoteEmbeddable();
        $this->status = new StatusEmbeddable();
        $this->history = new HistoryFieldsEmbeddable();
        $this->validity = new ValidityEmbeddable();
    }

    public function getRow(): RowIdEmbeddable
    {
        return $this->row;
    }

    public function setRow(RowIdEmbeddable $row): void
    {
        $this->row = $row;
    }

    public function getNote(): NoteEmbeddable
    {
        return $this->note;
    }

    public function setNote(NoteEmbeddable $note): void
    {
        $this->note = $note;
    }

    public function getStatus(): StatusEmbeddable
    {
        return $this->status;
    }

    public function setStatus(StatusEmbeddable $status): void
    {
        $this->status = $status;
    }

    public function getHistory(): HistoryFieldsEmbeddable
    {
        return $this->history;
    }

    public function setHistory(HistoryFieldsEmbeddable $history): void
    {
        $this->history = $history;
    }

    public function getValidity(): ValidityEmbeddable
    {
        return $this->validity;
    }

    public function setValidity(ValidityEmbeddable $validity): void
    {
        $this->validity = $validity;
    }

    public function getEmployeeContractId(): string
    {
        return $this->employeeContractId;
    }

    public function setEmployeeContractId(string $employeeContractId): void
    {
        $this->employeeContractId = $employeeContractId;
    }

    public function hasEmployeeContractId(): bool
    {
        return isset($this->employeeContractId);
    }

    public function getGroupId(): string
    {
        return $this->groupId;
    }

    public function setGroupId(string $groupId): void
    {
        $this->groupId = $groupId;
    }

    public function hasGroupId(): bool
    {
        return isset($this->groupId);
    }

    public function getGroupValue(): string
    {
        return $this->groupValue;
    }

    public function setGroupValue(string $groupValue): void
    {
        $this->groupValue = $groupValue;
    }

    public function hasGroupValue(): bool
    {
        return isset($this->groupValue);
    }
}

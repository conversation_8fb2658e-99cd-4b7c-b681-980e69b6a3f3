<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Message\Query;

use LoginAutonom\DatabaseBundle\Interfaces\FindAllArrayDatabaseQueryMessageInterface;

final readonly class EntityUniqueHashQuery implements FindAllArrayDatabaseQueryMessageInterface
{
    public function __construct(
        private string $entityFQCN,
        private string $hashType,
        private array $uniqueHashes,
    ) {
    }

    public function getEntityFQCN(): string
    {
        return $this->entityFQCN;
    }

    public function getHashType(): string
    {
        return $this->hashType;
    }

    public function getUniqueHashes(): array
    {
        return $this->uniqueHashes;
    }
}

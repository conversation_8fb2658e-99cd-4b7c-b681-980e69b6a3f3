<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\DTO;

final readonly class EntityUniqueHashConfiguration
{
    public function __construct(
        private string $mode,
        private string $hashType,
        private string $entityFQCN,
    ) {
    }

    public function getMode(): string
    {
        return $this->mode;
    }

    public function hasMode(): bool
    {
        return isset($this->mode);
    }

    public function getHashType(): string
    {
        return $this->hashType;
    }

    public function hasHashType(): bool
    {
        return isset($this->hashType);
    }

    public function getEntityFQCN(): string
    {
        return $this->entityFQCN;
    }

    public function hasEntityFQCN(): bool
    {
        return isset($this->entityFQCN);
    }
}
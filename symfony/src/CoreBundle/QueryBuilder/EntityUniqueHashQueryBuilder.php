<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\QueryBuilder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\CoreBundle\Entity\EntityUniqueHash;
use LoginAutonom\CoreBundle\Message\Query\EntityUniqueHashQuery;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;
use LoginAutonom\DatabaseBundle\QueryBuilder\AbstractQueryBuilder;

final class EntityUniqueHashQueryBuilder extends AbstractQueryBuilder
{
    public const ALIAS = 'euh';

    public function build(DatabaseQueryMessageInterface $query): QueryBuilder
    {
        /** @var EntityUniqueHashQuery $query */
        $qb = $this->util->createSimpleQueryBuilder(
            EntityUniqueHash::class,
            self::ALIAS
        );

        $this->util->addObviousCriteria($query, $qb);

        return $qb;
    }

    public static function getHandledQueryClass(): string
    {
        return EntityUniqueHashQuery::class;
    }
}
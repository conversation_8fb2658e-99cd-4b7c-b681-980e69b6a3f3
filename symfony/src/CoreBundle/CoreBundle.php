<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle;

use LoginAutonom\CoreBundle\DependencyInjection\Compiler\ApiResourceClassesPathCompilerPass;
use Symfony\Component\DependencyInjection\Compiler\PassConfig;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\Configurator\ContainerConfigurator;
use Symfony\Component\HttpKernel\Bundle\AbstractBundle;

final class CoreBundle extends AbstractBundle
{
    public function build(ContainerBuilder $container): void
    {
        $container->addCompilerPass(
            new ApiResourceClassesPathCompilerPass(),
            PassConfig::TYPE_BEFORE_OPTIMIZATION,
            200
        );
    }

    public function loadExtension(array $config, ContainerConfigurator $container, ContainerBuilder $builder): void
    {
        $container->import('Resources/config/services.yaml');
    }
}

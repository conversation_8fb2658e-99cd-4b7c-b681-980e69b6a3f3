<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Entity;

use Doctrine\ORM\Mapping as ORM;
use LoginAutonom\DatabaseBundle\Attribute\StatusColumn;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\HistoryFieldsEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\RowIdEmbeddable;
use LoginAutonom\DatabaseBundle\Entity\Embeddable\StatusEmbeddable;

#[ORM\Entity]
#[StatusColumn]
#[ORM\Index(columns: [
    'unique_hash',
    'hash_type',
    'status',
    'entity_fqcn',
], name: 'idx_uq_hash_type_status_fqcn')]
final class EntityUniqueHash
{
    #[ORM\Embedded(class: RowIdEmbeddable::class, columnPrefix: false)]
    private RowIdEmbeddable $row;

    #[ORM\Embedded(class: StatusEmbeddable::class, columnPrefix: false)]
    private StatusEmbeddable $status;

    #[ORM\Embedded(class: HistoryFieldsEmbeddable::class, columnPrefix: false)]
    private HistoryFieldsEmbeddable $history;

    #[ORM\Column(type: 'string', length: 128)]
    private string $entityFQCN;

    #[ORM\Column(type: 'string', length: 128)]
    private string $uniqueHash;

    #[ORM\Column(type: 'string', length: 32)]
    private string $hashType;

    public function __construct()
    {
        $this->row = new RowIdEmbeddable();
        $this->status = new StatusEmbeddable();
        $this->history = new HistoryFieldsEmbeddable();
    }

    public function getRow(): RowIdEmbeddable
    {
        return $this->row;
    }

    public function setRow(RowIdEmbeddable $row): void
    {
        $this->row = $row;
    }

    public function hasRow(): bool
    {
        return isset($this->row);
    }

    public function getStatus(): StatusEmbeddable
    {
        return $this->status;
    }

    public function setStatus(StatusEmbeddable $status): void
    {
        $this->status = $status;
    }

    public function hasStatus(): bool
    {
        return isset($this->status);
    }

    public function getHistory(): HistoryFieldsEmbeddable
    {
        return $this->history;
    }

    public function setHistory(HistoryFieldsEmbeddable $history): void
    {
        $this->history = $history;
    }

    public function hasHistory(): bool
    {
        return isset($this->history);
    }

    public function getEntityFQCN(): string
    {
        return $this->entityFQCN;
    }

    public function setEntityFQCN(string $entityFQCN): void
    {
        $this->entityFQCN = $entityFQCN;
    }

    public function hasEntityFQCN(): bool
    {
        return isset($this->entityFQCN);
    }

    public function getUniqueHash(): string
    {
        return $this->uniqueHash;
    }

    public function setUniqueHash(string $uniqueHash): void
    {
        $this->uniqueHash = $uniqueHash;
    }

    public function hasUniqueHash(): bool
    {
        return isset($this->uniqueHash);
    }

    public function getHashType(): string
    {
        return $this->hashType;
    }

    public function setHashType(string $hashType): void
    {
        $this->hashType = $hashType;
    }

    public function hasHashType(): bool
    {
        return isset($this->hashType);
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Interfaces;

use Flow\ETL\Row;
use LoginAutonom\CoreBundle\DTO\EntityUniqueHashConfiguration;
use Symfony\Component\DependencyInjection\Attribute\AutoconfigureTag;

#[AutoconfigureTag(EntityUniqueHashByFieldsGuesserInterface::TAGGED_LOCATOR_NAME)]
interface EntityUniqueHashByFieldsGuesserInterface
{
    public const TAGGED_LOCATOR_NAME = 'core.entity_unique_hash_guesser';

    public function guess(Row $row, EntityUniqueHashConfiguration $configuration): array;

    public function isSupported(Row $row, EntityUniqueHashConfiguration $configuration): bool;
}

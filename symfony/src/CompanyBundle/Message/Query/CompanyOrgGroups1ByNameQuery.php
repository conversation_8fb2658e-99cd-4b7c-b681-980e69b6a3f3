<?php

declare(strict_types=1);

namespace LoginAutonom\CompanyBundle\Message\Query;

use LoginAutonom\DatabaseBundle\Interfaces\FindAllDatabaseQueryMessageInterface;

final class CompanyOrgGroups1ByNameQuery implements FindAllDatabaseQueryMessageInterface
{
    private array $names;

    public function getNames(): array
    {
        return $this->names;
    }

    public function hasNames(): bool
    {
        return isset($this->names);
    }

    public function setNames(array $names): self
    {
        $this->names = $names;

        return $this;
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\CompanyBundle\Message\Query;

use LoginAutonom\DatabaseBundle\Interfaces\FindAllDatabaseQueryMessageInterface;

final class CompanyOrgGroups3ByNameQuery implements FindAllDatabaseQueryMessageInterface
{
    private array $companyOrgGroupNames;

    public function getCompanyOrgGroupNames(): array
    {
        return $this->companyOrgGroupNames;
    }

    public function hasCompanyOrgGroupNames(): bool
    {
        return isset($this->companyOrgGroupNames);
    }

    public function setCompanyOrgGroupNames(array $companyOrgGroupNames): self
    {
        $this->companyOrgGroupNames = $companyOrgGroupNames;

        return $this;
    }
}

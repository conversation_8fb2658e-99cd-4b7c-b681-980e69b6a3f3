<?php

declare(strict_types=1);

namespace LoginAutonom\CompanyBundle\Provider\ValidityIntervalBased;

use Carbon\CarbonImmutable;
use LoginAutonom\CompanyBundle\Message\Query\CompanyOrgGroups3ByNameQuery;
use LoginAutonom\CoreBundle\Interfaces\QueryBusInterface;
use LoginAutonom\DatabaseBundle\DTO\IdsWithValidityInterval;
use LoginAutonom\DatabaseBundle\DTO\ValidityIntervalBasedEntities;
use LoginAutonom\DatabaseBundle\Interfaces\ValidityIntervalBasedEntitiesProviderInterface;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoValidityStamp;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\ValidityIntervalStamp;

final readonly class CompanyOrgGroups3ByNameProvider implements ValidityIntervalBasedEntitiesProviderInterface
{
    public function __construct(
        private QueryBusInterface $queryBus
    ) {
    }

    public function provide(
        IdsWithValidityInterval $dto,
        array $stamps,
        ValidityIntervalBasedEntities $entities
    ): ValidityIntervalBasedEntities {
        if ($dto->hasFrom()) {
            $stamps[] = new ValidityIntervalStamp(
                $dto->getFrom(),
                $dto->getTo() ?? CarbonImmutable::now(),
            );
        } else {
            $stamps[] = new NoValidityStamp();
        }
        $entitiesFromQuery = $this->queryBus->query(
            (new CompanyOrgGroups3ByNameQuery())
                ->setCompanyOrgGroupNames($dto->getIds()),
            $stamps
        );
        $entities->addMultiple($entitiesFromQuery);

        return $entities;
    }

    public static function getName(): string
    {
        return 'company-org-groups3-by-name';
    }
}

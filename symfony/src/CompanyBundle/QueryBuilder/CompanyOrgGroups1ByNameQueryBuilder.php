<?php

declare(strict_types=1);

namespace LoginAutonom\CompanyBundle\QueryBuilder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\CompanyBundle\Entity\CompanyOrgGroup1;
use LoginAutonom\CompanyBundle\Enum\CompanyOrgGroup1EntityFieldEnum;
use LoginAutonom\CompanyBundle\Message\Query\CompanyOrgGroups1ByNameQuery;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;
use LoginAutonom\DatabaseBundle\QueryBuilder\AbstractQueryBuilder;

final class CompanyOrgGroups1ByNameQueryBuilder extends AbstractQueryBuilder
{
    public const ALIAS = 'cog1';
    public const NAMES = ':names';

    public function build(DatabaseQueryMessageInterface $query): QueryBuilder
    {
        /** @var CompanyOrgGroups1ByNameQuery $query */
        $qb = $this->util->createSimpleQueryBuilder(
            CompanyOrgGroup1::class,
            self::ALIAS
        );

        if ($query->hasNames()) {
            $qb->where(
                $qb->expr()->in(
                    self::ALIAS . '.' . CompanyOrgGroup1EntityFieldEnum::COMPANY_ORG_GROUP_NAME,
                    self::NAMES
                )
            )->setParameter(self::NAMES, $query->getNames());
        }

        return $qb;
    }

    public static function getHandledQueryClass(): string
    {
        return CompanyOrgGroups1ByNameQuery::class;
    }
}

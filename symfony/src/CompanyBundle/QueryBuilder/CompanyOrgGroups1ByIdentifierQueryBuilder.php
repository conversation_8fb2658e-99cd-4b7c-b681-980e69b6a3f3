<?php

declare(strict_types=1);

namespace LoginAutonom\CompanyBundle\QueryBuilder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\CompanyBundle\Entity\CompanyOrgGroup1;
use LoginAutonom\CompanyBundle\Enum\CompanyOrgGroup1EntityFieldEnum;
use LoginAutonom\CompanyBundle\Message\Query\CompanyOrgGroups1ByIdentifierQuery;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;
use LoginAutonom\DatabaseBundle\QueryBuilder\AbstractQueryBuilder;

final class CompanyOrgGroups1ByIdentifierQueryBuilder extends AbstractQueryBuilder
{
    public const ALIAS = 'cog1';
    public const IDENTIFIERS = ':identifiers';

    public function build(DatabaseQueryMessageInterface $query): QueryBuilder
    {
        /** @var CompanyOrgGroups1ByIdentifierQuery $query */
        $qb = $this->util->createSimpleQueryBuilder(
            CompanyOrgGroup1::class,
            self::ALIAS
        );

        if ($query->hasIdentifiers()) {
            $qb->where(
                $qb->expr()->in(
                    self::ALIAS . '.' . CompanyOrgGroup1EntityFieldEnum::IDENTIFIER,
                    self::IDENTIFIERS
                )
            )->setParameter(self::IDENTIFIERS, $query->getIdentifiers());
        }

        return $qb;
    }

    public static function getHandledQueryClass(): string
    {
        return CompanyOrgGroups1ByIdentifierQuery::class;
    }
}

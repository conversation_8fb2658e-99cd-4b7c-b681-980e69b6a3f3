<?php

declare(strict_types=1);

namespace LoginAutonom\CompanyBundle\QueryBuilder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\CompanyBundle\Entity\Company;
use LoginAutonom\CompanyBundle\Enum\CompanyEntityFieldEnum;
use LoginAutonom\CompanyBundle\Message\Query\CompaniesByCompanyNameQuery;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;
use LoginAutonom\DatabaseBundle\QueryBuilder\AbstractQueryBuilder;

final class CompaniesByCompanyNameQueryBuilder extends AbstractQueryBuilder
{
    public const ALIAS = 'c';
    public const COMPANY_NAMES = ':companyNames';

    public function build(DatabaseQueryMessageInterface $query): QueryBuilder
    {
        /** @var CompaniesByCompanyNameQuery $query */
        $qb = $this->util->createSimpleQueryBuilder(
            Company::class,
            self::ALIAS
        );

        if ($query->hasCompanyNames()) {
            $qb->where(
                $qb->expr()->in(
                    self::ALIAS . '.' . CompanyEntityFieldEnum::COMPANY_NAME,
                    self::COMPANY_NAMES
                )
            )->setParameter(self::COMPANY_NAMES, $query->getCompanyNames());
        }

        return $qb;
    }

    public static function getHandledQueryClass(): string
    {
        return CompaniesByCompanyNameQuery::class;
    }
}

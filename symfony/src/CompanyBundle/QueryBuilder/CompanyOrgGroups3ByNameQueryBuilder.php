<?php

declare(strict_types=1);

namespace LoginAutonom\CompanyBundle\QueryBuilder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\CompanyBundle\Entity\CompanyOrgGroup3;
use LoginAutonom\CompanyBundle\Enum\CompanyOrgGroup3EntityFieldEnum;
use LoginAutonom\CompanyBundle\Message\Query\CompanyOrgGroups3ByNameQuery;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;
use LoginAutonom\DatabaseBundle\QueryBuilder\AbstractQueryBuilder;

final class CompanyOrgGroups3ByNameQueryBuilder extends AbstractQueryBuilder
{
    public const ALIAS = 'cog3';
    public const NAMES = ':names';

    public function build(DatabaseQueryMessageInterface $query): QueryBuilder
    {
        /** @var CompanyOrgGroups3ByNameQuery $query */
        $qb = $this->util->createSimpleQueryBuilder(
            CompanyOrgGroup3::class,
            self::ALIAS
        );

        $this->util->addObviousCriteria($query, $qb);

        return $qb;
    }

    public static function getHandledQueryClass(): string
    {
        return CompanyOrgGroups3ByNameQuery::class;
    }
}

<?php

declare(strict_types=1);

namespace LoginAutonom\CompanyBundle\QueryBuilder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\CompanyBundle\Entity\CompanyOrgGroup2;
use LoginAutonom\CompanyBundle\Enum\CompanyOrgGroup2EntityFieldEnum;
use LoginAutonom\CompanyBundle\Message\Query\CompanyOrgGroups2ByNameQuery;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;
use LoginAutonom\DatabaseBundle\QueryBuilder\AbstractQueryBuilder;

final class CompanyOrgGroups2ByNameQueryBuilder extends AbstractQueryBuilder
{
    public const ALIAS = 'cog2';
    public const NAMES = ':names';

    public function build(DatabaseQueryMessageInterface $query): QueryBuilder
    {
        /** @var CompanyOrgGroups2ByNameQuery $query */
        $qb = $this->util->createSimpleQueryBuilder(
            CompanyOrgGroup2::class,
            self::ALIAS
        );

        if ($query->hasNames()) {
            $qb->where(
                $qb->expr()->in(
                    self::ALIAS . '.' . CompanyOrgGroup2EntityFieldEnum::COMPANY_ORG_GROUP_NAME,
                    self::NAMES
                )
            )->setParameter(self::NAMES, $query->getNames());
        }

        return $qb;
    }

    public static function getHandledQueryClass(): string
    {
        return CompanyOrgGroups2ByNameQuery::class;
    }
}

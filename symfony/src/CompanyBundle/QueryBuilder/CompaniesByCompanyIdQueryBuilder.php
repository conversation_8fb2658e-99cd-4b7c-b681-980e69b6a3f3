<?php

declare(strict_types=1);

namespace LoginAutonom\CompanyBundle\QueryBuilder;

use Doctrine\ORM\QueryBuilder;
use LoginAutonom\CompanyBundle\Entity\Company;
use LoginAutonom\CompanyBundle\Enum\CompanyEntityFieldEnum;
use LoginAutonom\CompanyBundle\Message\Query\CompaniesByCompanyIdQuery;
use LoginAutonom\DatabaseBundle\Interfaces\DatabaseQueryMessageInterface;
use LoginAutonom\DatabaseBundle\QueryBuilder\AbstractQueryBuilder;

final class CompaniesByCompanyIdQueryBuilder extends AbstractQueryBuilder
{
    public const ALIAS = 'c';
    public const COMPANY_IDS = ':companyIds';

    public function build(DatabaseQueryMessageInterface $query): QueryBuilder
    {
        /** @var CompaniesByCompanyIdQuery $query */
        $qb = $this->util->createSimpleQueryBuilder(
            Company::class,
            self::ALIAS
        );

        if ($query->hasCompanyIds()) {
            $qb->where(
                $qb->expr()->in(
                    self::ALIAS . '.' . CompanyEntityFieldEnum::COMPANY_ID,
                    self::COMPANY_IDS
                )
            )->setParameter(self::COMPANY_IDS, $query->getCompanyIds());
        }

        return $qb;
    }

    public static function getHandledQueryClass(): string
    {
        return CompaniesByCompanyIdQuery::class;
    }
}

<?php

declare(strict_types=1);

namespace Tests\PHPUnit\EtlBundle\Flow\Transformer;

use Flow\ETL\Config;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use Flow\ETL\Row\Factory\NativeEntryFactory;
use LoginAutonom\CoreBundle\DTO\EntityUniqueHashConfiguration;
use LoginAutonom\CoreBundle\Enum\EntityUniqueHashModeEnum;
use LoginAutonom\DatabaseBundle\Generator\UUIDGeneratorToEntityField;
use LoginAutonom\EtlBundle\Builder\EtlMappingBuilder;
use LoginAutonom\EtlBundle\Flow\Transformer\CreateDefaultEntityIfNotExistsTransformer;
use LoginAutonom\EtlBundle\Handler\EntityUniqueHashHandler;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\PropertyAccess\PropertyAccessorInterface;

class CreateDefaultEntityIfNotExistsTransformerTest extends TestCase
{
    private MockObject $propertyAccessor;
    private MockObject $uuidGenerator;
    private MockObject $etlMappingBuilder;
    private MockObject $entityUniqueHashHandler;
    private NativeEntryFactory $entryFactory;
    private FlowContext $context;

    protected function setUp(): void
    {
        $this->propertyAccessor = $this->createMock(PropertyAccessorInterface::class);
        $this->uuidGenerator = $this->createMock(UUIDGeneratorToEntityField::class);
        $this->etlMappingBuilder = $this->createMock(EtlMappingBuilder::class);
        $this->entityUniqueHashHandler = $this->createMock(EntityUniqueHashHandler::class);
        $this->entryFactory = new NativeEntryFactory();
        $this->context = new FlowContext(Config::default());
    }

    public function testCollectInformationWithoutHashConfiguration(): void
    {
        $transformer = new CreateDefaultEntityIfNotExistsTransformer(
            'targetField',
            'TestEntity',
            [],
            [],
            [],
            $this->propertyAccessor,
            $this->uuidGenerator,
            $this->etlMappingBuilder,
            $this->entityUniqueHashHandler
        );

        $row = Row::create($this->entryFactory->create('test', 'value'));
        $info = new \ArrayObject();

        $transformer->collectInformation($row, $this->context, $info);

        $this->assertArrayNotHasKey(CreateDefaultEntityIfNotExistsTransformer::ROWS_WITH_HASH_CONFIG, $info);
    }

    public function testCollectInformationWithHashConfiguration(): void
    {
        $hashConfig = new EntityUniqueHashConfiguration(
            EntityUniqueHashModeEnum::DYNAMIC->value,
            'sha256',
            'TestEntity'
        );

        $transformer = new CreateDefaultEntityIfNotExistsTransformer(
            'targetField',
            'TestEntity',
            [],
            [],
            [],
            $this->propertyAccessor,
            $this->uuidGenerator,
            $this->etlMappingBuilder,
            $this->entityUniqueHashHandler,
            null,
            null,
            $hashConfig
        );

        $row = Row::create($this->entryFactory->create('test', 'value'));
        $info = new \ArrayObject();

        $transformer->collectInformation($row, $this->context, $info);

        $this->assertArrayHasKey(CreateDefaultEntityIfNotExistsTransformer::ROWS_WITH_HASH_CONFIG, $info);
        $this->assertCount(1, $info[CreateDefaultEntityIfNotExistsTransformer::ROWS_WITH_HASH_CONFIG]);
        $this->assertSame($row, $info[CreateDefaultEntityIfNotExistsTransformer::ROWS_WITH_HASH_CONFIG][0]);
    }

    public function testCollectInformationSkipsRowsWithTargetField(): void
    {
        $hashConfig = new EntityUniqueHashConfiguration(
            EntityUniqueHashModeEnum::DYNAMIC->value,
            'sha256',
            'TestEntity'
        );

        $transformer = new CreateDefaultEntityIfNotExistsTransformer(
            'targetField',
            'TestEntity',
            [],
            [],
            [],
            $this->propertyAccessor,
            $this->uuidGenerator,
            $this->etlMappingBuilder,
            $this->entityUniqueHashHandler,
            null,
            null,
            $hashConfig
        );

        $row = Row::create(
            $this->entryFactory->create('test', 'value'),
            $this->entryFactory->create('targetField', 'existing')
        );
        $info = new \ArrayObject();

        $transformer->collectInformation($row, $this->context, $info);

        $this->assertArrayNotHasKey(CreateDefaultEntityIfNotExistsTransformer::ROWS_WITH_HASH_CONFIG, $info);
    }

    public function testExecuteActionWithBatchProcessing(): void
    {
        $hashConfig = new EntityUniqueHashConfiguration(
            EntityUniqueHashModeEnum::DYNAMIC->value,
            'sha256',
            'TestEntity'
        );

        $transformer = new CreateDefaultEntityIfNotExistsTransformer(
            'targetField',
            'TestEntity',
            [],
            [],
            [],
            $this->propertyAccessor,
            $this->uuidGenerator,
            $this->etlMappingBuilder,
            $this->entityUniqueHashHandler,
            null,
            null,
            $hashConfig
        );

        $rows = [
            Row::create($this->entryFactory->create('test1', 'value1')),
            Row::create($this->entryFactory->create('test2', 'value2'))
        ];

        $info = new \ArrayObject();
        $info[CreateDefaultEntityIfNotExistsTransformer::ROWS_WITH_HASH_CONFIG] = $rows;

        $expectedHashes = ['hash1', 'hash2'];
        $this->entityUniqueHashHandler
            ->expects($this->once())
            ->method('handleBatch')
            ->with($rows, $hashConfig)
            ->willReturn($expectedHashes);

        $transformer->executeAction($info, $this->context);

        $this->assertArrayHasKey(CreateDefaultEntityIfNotExistsTransformer::EXISTING_HASHES, $info);
        $this->assertSame($expectedHashes, $info[CreateDefaultEntityIfNotExistsTransformer::EXISTING_HASHES]);
    }

    public function testExecuteActionWithoutRowsToProcess(): void
    {
        $transformer = new CreateDefaultEntityIfNotExistsTransformer(
            'targetField',
            'TestEntity',
            [],
            [],
            [],
            $this->propertyAccessor,
            $this->uuidGenerator,
            $this->etlMappingBuilder,
            $this->entityUniqueHashHandler
        );

        $info = new \ArrayObject();

        $this->entityUniqueHashHandler
            ->expects($this->never())
            ->method('handleBatch');

        $transformer->executeAction($info, $this->context);

        $this->assertArrayNotHasKey(CreateDefaultEntityIfNotExistsTransformer::EXISTING_HASHES, $info);
    }
}

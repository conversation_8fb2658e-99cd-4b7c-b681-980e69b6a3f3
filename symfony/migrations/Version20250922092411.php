<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

final class Version20250922092411 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Create entity_unique_hash table';
    }

    public function up(Schema $schema): void
    {
        $this->addSql('CREATE TABLE `entity_unique_hash` (
            `row_id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `entity_fqcn` varchar(128) NOT NULL,
            `unique_hash` varchar(64) NOT NULL,
            `hash_type` varchar(32) NOT NULL,
            `status` tinyint(3) UNSIGNED NOT NULL,
            `created_by` varchar(32) NOT NULL,
            `created_on` datetime NOT NULL,
            `modified_by` varchar(32) NULL,
            `modified_on` datetime NULL,
            PRIMARY KEY (`row_id`),
            INDEX idx_uq_hash_type_status_fqcn (unique_hash, hash_type, status, entity_fqcn)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE `entity_unique_hash`');
    }
}

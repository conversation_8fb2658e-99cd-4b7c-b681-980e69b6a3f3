<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use LoginAutonom\DatabaseBundle\Doctrine\Migrations\AbstractDefaultMigration;
use LoginAutonom\DatabaseBundle\Entity\EntityChangeLog;

final class Version20240405064831 extends AbstractDefaultMigration
{
    public function getDescription(): string
    {
        return 'Create entity change log table.';
    }

    public function up(Schema $schema): void
    {
        if (!$this->isTableExists(EntityChangeLog::class)) {
            $this->addSql(
                'CREATE TABLE entity_change_log (row_id INT UNSIGNED AUTO_INCREMENT NOT NULL, entity_fqcn VARCHAR(128) NOT NULL, changed_row_id INT NOT NULL, action VARCHAR(32) NOT NULL, changes JSON NOT NULL, reason VARCHAR(128) NOT NULL, correlation_id VARCHAR(32) NOT NULL, status TINYINT UNSIGNED NOT NULL, created_by VARCHAR(32) NOT NULL, created_on DATETIME NOT NULL, modified_by VARCHAR(32) DEFAULT NULL, modified_on DATETIME DEFAULT NULL, PRIMARY KEY(row_id)) DEFAULT CHARACTER SET utf8mb3 COLLATE `utf8mb3_unicode_ci` ENGINE = InnoDB'
            );
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE entity_change_log');
    }
}

<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use LoginAutonom\DatabaseBundle\Doctrine\Migrations\AbstractDefaultMigration;

final class Version20230830093443 extends AbstractDefaultMigration
{
    public function getDescription(): string
    {
        return 'Create media_object table';
    }

    public function up(Schema $schema): void
    {
        if (!$this->isTableExistsFromSchema('media_object')) {
            $this->addSql(
                'CREATE TABLE media_object (id INT AUTO_INCREMENT NOT NULL, file_path VARCHAR(255) DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB'
            );
        }
    }

    public function down(Schema $schema): void
    {
        $this->addSql('DROP TABLE media_object');
    }
}
